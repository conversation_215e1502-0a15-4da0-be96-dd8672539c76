package com.trs.gov.externalSystem.service;

import com.trs.gov.externalSystem.DTO.RightSearchDTO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/25 18:16
 * @version 1.0
 * @since  1.0
 */
public interface RightService {

     /**
      * 获取操作权限按钮<BR>
      * <AUTHOR> lan.xin E-mail: <EMAIL>
      * 创建时间：2020/10/20 17:16
      * @param  dto 请求参数
      * @throws
      * @return
      */
     RestfulResults<List<String>> getMyRightOper(RightSearchDTO dto) throws Exception;
}
