package com.trs.gov.externalSystem.service;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.externalSystem.DTO.GetAllSiteDTO;
import com.trs.gov.externalSystem.DTO.HySiteDTO;
import com.trs.gov.externalSystem.DTO.MonitorSiteDTO;
import com.trs.gov.externalSystem.VO.MonitorSiteVO;
import com.trs.gov.externalSystem.VO.SiteVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 海云服务接口
 */

public interface SitesService {

    /**
     * 获取站点信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:31
     * @param   dto  请求参数
     * @throws  ServiceException 服务异常
     * @return  查询结果
     */
    RestfulResults<List<SiteVO>> getHySites(HySiteDTO dto) throws ServiceException;

    /**
     * 异常性获取站点信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:31
     * @param   dto  请求参数
     * @throws  ServiceException 服务异常
     * @return  查询结果
     */
    RestfulResults<List<SiteVO>> getAllHySites(GetAllSiteDTO dto) throws ServiceException;

    /**
     * 获取监控云站点信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/23 11:06
     * @param   dto 请求参数
     * @throws
     * @return
     */
    RestfulResults<List<MonitorSiteVO>> getMonitorSites(MonitorSiteDTO dto);


}

