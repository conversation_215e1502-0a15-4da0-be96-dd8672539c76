package com.trs.gov.externalSystem.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * @auther
 * @description
 * @date
 **/
@Data
public class GetAllSiteDTO extends BaseDTO {
    private Integer pageIndex;

    private Integer pageSize;

    @Override
    public boolean isValid() throws ServiceException {

        if (pageIndex==null){
            pageIndex = 1;
        }
        if (pageSize==null){
            pageSize = 10000;
        }
        return true;
    }
}
