package com.trs.gov.externalSystem.util;

import com.trs.common.base.Defaults;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.StringUtils;
import com.trs.log.exception.RecordableException;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.OkHttpClient;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> admin E-mail: <EMAIL>
 *         创建时间：2019/6/10 11:21
 * @version 1.0
 */
public class HttpUtil {

    private String url;

    private Map parameter;

    private Headers headers;

    private static HttpUtil httpUtil = new HttpUtil();
    //http请求工具
    private HttpRequest httpRequest = new HttpRequest(
            new OkHttpClient.Builder().connectTimeout(4, TimeUnit.MINUTES).
                    readTimeout(4, TimeUnit.MINUTES).
                    writeTimeout(4, TimeUnit.MINUTES),
            false
    );

    /**
     * @Description: 单例获取HttpUtil实例
     * @Param: []
     * @return: com.trs.psp.core.util.HttpUtil
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 17:24
     */
    public static HttpUtil newBuilder() {
        if (httpUtil == null) {
            httpUtil = new HttpUtil();
        }
        return httpUtil;
    }

    /**
     * @Description: 设置访问地址
     * @Param: [url]
     * @return: com.trs.psp.core.util.HttpUtil
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 17:26
     */
    public HttpUtil setUrl(String url) {
        this.url = url;
        return this;
    }

    /**
     * @Description: 设置请求参数
     * @Param: [parameter]
     * @return: com.trs.psp.core.util.HttpUtil
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 17:26
     */
    public HttpUtil setParamter(Map parameter) {
        this.parameter = parameter;
        //this.parameter.put("notLoginName","trsAdmin");
        return this;
    }

    /**
     * @Description: 设置头
     * @Param: [headers]
     * @return: com.trs.fsp.utils.HttpUtil
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/7/10 20:09
     */
    public HttpUtil setHeaders(Headers headers) {
        this.headers = headers;
        return this;
    }

    /**
     * @Description: POST请求
     * @Param: []
     * @return: java.lang.String
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 17:54
     */
    public String httpSendPost() throws RecordableException {
        if (StringUtils.isNullOrEmpty(this.url) || this.parameter.isEmpty()) {
            return Defaults.defaultValue(String.class);
        }
        FormBody.Builder bodybuilder = new FormBody.Builder();
        Iterator<Map.Entry<String, String>> iterator = parameter.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String key = String.valueOf(entry.getKey());
            String value = String.valueOf(entry.getValue());
            bodybuilder.add(key, value);
        }
        FormBody body = bodybuilder.build();
        //com.trs.common.http2.HttpRequest httpRequest = new com.trs.common.http2.HttpRequest();
        if (headers != null) {
            return httpRequest.doPost(url, body, headers);
        }
        return httpRequest.doPost(url, body);
    }

    /**
     * @Description: PUT请求
     * @Param: []
     * @return: java.lang.String
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 17:54
     */
    public String httpSendPut() throws RecordableException {
        if (StringUtils.isNullOrEmpty(this.url) || this.parameter.isEmpty()) {
            return Defaults.defaultValue(String.class);
        }
        FormBody.Builder bodybuilder = new FormBody.Builder();
        Iterator<Map.Entry<String, String>> iterator = parameter.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            String key = String.valueOf(entry.getKey());
            String value = String.valueOf(entry.getValue());
            bodybuilder.add(key, value);
        }
        FormBody body = bodybuilder.build();
        //com.trs.common.http2.HttpRequest httpRequest = new com.trs.common.http2.HttpRequest();
        if (headers != null) {
            return httpRequest.doPut(url, body, headers);
        }
        return httpRequest.doPut(url, body);
    }

    /**
     * @Description: DELETE请求
     * @Param: []
     * @return: java.lang.String
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 17:56
     */
    public String httpSendDelete() throws RecordableException {
        if (StringUtils.isNullOrEmpty(this.url)) {
            return Defaults.defaultValue(String.class);
        }
        StringBuffer sbUrl = new StringBuffer(url);
        if (parameter != null && !parameter.isEmpty()) {
            sbUrl.append("?");
            Iterator<Map.Entry<String, String>> iterator = parameter.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, String> entry = iterator.next();
                String key = String.valueOf(entry.getKey());
                String value = String.valueOf(entry.getValue());
                sbUrl.append(key).append("=").append(value).append("&");
            }
            sbUrl.setLength(sbUrl.length() - 1);
        }
        //com.trs.common.http2.HttpRequest httpRequest = new com.trs.common.http2.HttpRequest();
        if (headers != null) {
            return httpRequest.doDelete(sbUrl.toString(), null, headers);
        }
        return httpRequest.doDelete(sbUrl.toString());
    }

    /**
     * @Description: GET方法请求
     * @Param: []
     * @return: java.lang.String
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 17:56
     */
    public String httpSendGet() throws RecordableException {
        if (StringUtils.isNullOrEmpty(this.url)) {
            return Defaults.defaultValue(String.class);
        }
        StringBuffer sbUrl = new StringBuffer(url);
        if (parameter != null && !parameter.isEmpty()) {
            sbUrl.append("?");
            Iterator<Map.Entry<String, String>> iterator = parameter.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, String> entry = iterator.next();
                String key = String.valueOf(entry.getKey());
                String value = String.valueOf(entry.getValue());
                sbUrl.append(key).append("=").append(value).append("&");
            }
            sbUrl.setLength(sbUrl.length() - 1);
        }
        //com.trs.common.http2.HttpRequest httpRequest = new com.trs.common.http2.HttpRequest();

        if (headers != null) {
            return httpRequest.doGet(sbUrl.toString(), headers);
        }
        return httpRequest.doGet(sbUrl.toString());
    }

    /**
     * @Description: post请求
     * @Param: [url, body]
     * @return: java.lang.String
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 11:22
     */
    public static String httpSendPost(String url, FormBody body) throws RecordableException {
        HttpRequest httpRequest = new HttpRequest();
        return httpRequest.doPost(url, body);
    }

    /**
     * @Description: get请求
     * @Param: [url]
     * @return: java.lang.String
     * @Author: fu.gang Email : <EMAIL>
     * @Date: 2019/6/10 11:22
     */
    public static String httpSendGet(String url) throws RecordableException {
        HttpRequest httpRequest = new HttpRequest();
        return httpRequest.doGet(url);
    }
}
