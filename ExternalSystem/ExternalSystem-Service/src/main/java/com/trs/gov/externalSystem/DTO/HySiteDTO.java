package com.trs.gov.externalSystem.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class HySiteDTO extends BaseDTO {

    /**
     * 渠道类型id
     */
    private String mediaType;

    /**
     * 站点名称
     */
    private String siteName;

    private Integer pageNum;

    private Integer pageSize;

    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isEmpty(mediaType)){
            throw new ParamInvalidException("渠道类型id不能为空");
        }
        if (pageNum ==null){
            pageNum = 1;
        }
        if (pageSize==null){
            pageSize = 10000;
        }
        return true;
    }
}
