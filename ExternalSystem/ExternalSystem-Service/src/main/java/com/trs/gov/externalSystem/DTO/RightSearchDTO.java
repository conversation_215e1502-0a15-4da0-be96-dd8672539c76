package com.trs.gov.externalSystem.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.CoreConstant;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import lombok.Data;

/**
 * @auther
 * @description
 * @date
 **/
@Data
public class RightSearchDTO extends BaseDTO {

    /**
     * 当前用户名
     */
    private String userName;

    /**
     * 操作资源id
     */
    private String operId;
    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isEmpty(userName)||"null".equals(userName)){
            throw new ParamInvalidException("当前用户名不能为空");
        }
        if (StringUtils.isEmpty(operId)||"null".equals(userName)){
            throw new ParamInvalidException("操作资源Id不能为空");
        }
        return true;
    }
    public String getUserName() {
        if (userName == null) {
            userName = ContextHelper.getLoginUser().orElse(CoreConstant.SYSTEM_USER_NAME);
        }
        return userName;
    }
}
