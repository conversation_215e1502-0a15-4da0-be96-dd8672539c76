package com.trs.gov.externalSystem.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * @auther
 * @description
 * @date
 **/
@Data
public class MonitorSiteDTO extends BaseDTO {


    /**
     * 监控云站点id
     */
    private String id;

    /**
     * 监控云站点名称
     */
    private String name;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 当前页
     */
    private Integer pageNum;

    @Override
    public boolean isValid() throws ServiceException {
        if (pageNum==null){
            this.pageNum=1;
        }
        if (pageSize==null){
            this.pageSize=10;
        }
        return true;
    }
}
