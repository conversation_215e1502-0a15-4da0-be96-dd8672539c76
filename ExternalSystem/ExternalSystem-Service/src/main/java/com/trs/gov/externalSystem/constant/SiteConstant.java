package com.trs.gov.externalSystem.constant;

/**
 * @auther
 * @description
 * @date
 **/
public class SiteConstant {

    /**
     * 海云请求方法名字段名称
     */
    public final static String HY_PARAM_METHODNAME="methodname";

    /**
     * 海云请求服务名字段名称
     */
    public final static String HY_PARAM_SERVICEID="serviceid";

    /**
     * 海云请求白名单用户名字段名称
     */
    public final static String HY_PARAM_CURRUSERNAME="CurrUserName";

    /**
     * 海云请求当前页参数名
     */
    public final static String HY_PARAM_PAGEINDEX="PageIndex";

    /**
     * 海云请求分页大小参数名
     */
    public final static String HY_PARAM_PAGESIZE="PageSize";

    /**
     * 海云请求渠道类型参数名
     */
    public final static String HY_PARAM_MEDIATYPE="MediaType";

    /**
     * 海云请求站点名称参数名
     */
    public final static String HY_PARAM_SITENAME="SITENAME";

    /**
     * 海云请求站点名称参数名
     */
    public final static String HY_PARAM_KEY="key";

    /**
     * 监测云站点请求参数名
     */
    public final static String MONITORSITE_PARAM_ISPH = "isp_h";

    /**
     * 监测云站点请求参数名
     */
    public final static String MONITORSITE_PARAM_ISPA = "isp_a";

    /**
     * 监测云站点请求参数名
     */
    public final static String MONITORSITE_PARAM_ISPS = "isp_s";

    /**
     * 监测云站点请求参数名
     */
    public final static String MONITORSITE_PARAM_ISPV = "isp_v";

    /**
     * 监测云站点请求参数名(监测云租户id）
     */
    public final static String MONITORSITE_PARAM_TENANID = "tenantId";

    /**
     * 监测云站点请求参数名(监测云中的用户名）
     */
    public final static String MONITORSITE_PARAM_USERNAME = "userName";

    /**
     * 监测云站点请求参数名(监测云租户id）
     */
    public final static String MONITORSITE_PARAM_PHONE = "phone";

    /**
     * 监测云站点请求参数名(分页大小)
     */
    public final static String MONITORSITE_PARAM_PAGESIZE = "pageSize";

    /**
     * 监测云站点请求参数名(当前页)
     */
    public final static String MONITORSITE_PARAM_PAGEINDEX = "pageIndex";

    /**
     * 监测云站点请求参数名(站点名称)
     */
    public final static String MONITORSITE_PARAM_NAME = "name";

}
