package com.trs.gov.externalSystem.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.externalSystem.DTO.GetAllSiteDTO;
import com.trs.gov.externalSystem.DTO.HySiteDTO;
import com.trs.gov.externalSystem.DTO.MonitorSiteDTO;
import com.trs.gov.externalSystem.VO.MonitorSiteVO;
import com.trs.gov.externalSystem.VO.SiteVO;
import com.trs.gov.externalSystem.mgr.SiteMgr;
import com.trs.gov.externalSystem.service.SitesService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @auther
 * @description
 * @date
 **/
@Service
@Slf4j
public class SiteServiceImpl implements SitesService {
    @Autowired
    private SiteMgr siteMgr;

    @Override
    public RestfulResults<List<SiteVO>> getHySites(HySiteDTO dto) throws ServiceException {
        List<SiteVO> allSites;
        String  itemCount;
        try {
            BaseUtils.checkDTO(dto);
            String  data = siteMgr.getHySites(dto);
            String results = JSONObject.parseObject(data).getString("DATA");
            String pager = JSONObject.parseObject(data).getString("PAGER");
            itemCount= JSONObject.parseObject(pager).getString("ITEMCOUNT");
            allSites = JSONObject.parseArray(results, SiteVO.class);
        } catch (Exception e) {
            log.error("获取数据异常", e);
            return RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(allSites)
                .addMsg("获取数据成功")
                .addTotalCount(Long.parseLong(itemCount))
                .addPageSize(dto.getPageSize())
                .addPageNum(dto.getPageNum());
    }

    @Override
    public RestfulResults<List<SiteVO>> getAllHySites(GetAllSiteDTO dto) throws ServiceException {
        List<SiteVO> allSites;
        try {
            BaseUtils.checkDTO(dto);
            allSites = siteMgr.getAllHySites(dto);
        }catch (Exception e){
            log.error("获取数据异常",e);
            return RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(allSites)
                .addMsg("获取数据成功")
                .addTotalCount(Long.parseLong(String.valueOf(allSites.size())))
                .addPageSize(dto.getPageSize())
                .addPageNum(dto.getPageIndex());
    }
    @Override
    public RestfulResults<List<MonitorSiteVO>> getMonitorSites(MonitorSiteDTO dto) {

        List<MonitorSiteVO> monitorSiteVOS = null;
        Long total = 0L;
        try {
            BaseUtils.checkDTO(dto);
            JSONObject monitorSites = siteMgr.getMonitorSites(dto);
            if (monitorSites!=null){
                JSONObject jsonObject1 = JSONObject.parseObject(monitorSites.getString("data"));
                JSONObject jsonObject2 = JSONObject.parseObject(jsonObject1.getString("data"));
                monitorSiteVOS = JSONObject.parseArray(jsonObject2.getString("data"), MonitorSiteVO.class);
                total = Long.parseLong(JSONObject.parseObject(jsonObject2.getString("pager")).getString("itemCount"));
            }
        }catch (Exception e){
            log.error("获取数据异常",e);
            return RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(monitorSiteVOS)
                .addMsg("获取数据成功")
                .addTotalCount(total)
                .addPageSize(dto.getPageSize())
                .addPageNum(dto.getPageNum());
    }

}
