package com.trs.gov.externalSystem.mgr;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.gov.externalSystem.DTO.GetAllSiteDTO;
import com.trs.gov.externalSystem.DTO.HySiteDTO;
import com.trs.gov.externalSystem.DTO.MonitorSiteDTO;
import com.trs.gov.externalSystem.VO.SiteVO;
import com.trs.gov.externalSystem.constant.SiteConstant;
import com.trs.gov.externalSystem.util.HttpUtil;
import com.trs.gov.management.base.enums.MediaType;
import com.trs.tcache.util.redis.JedisPoolUtils;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @auther
 * @description
 * @date
 **/
@Component
@Slf4j
public class SiteMgr {

    @Value("${hycloud.currUserName}")
    private String currUserName;

    @Value("${hycloud.url}")
    private String url;

    @Value("${hycloud.serviceid}")
    private String serviceId;

    @Value("${hycloud.methodname}")
    private String methodName;

    @Value("${hycloud.key}")
    private String hyKey;

    @Value("${externalSys.SiteService.redisIndex:6}")
    private int siteRedisIndex;

    @Value("${externalSys.SiteService.redisExpireTime:7200}")
    private int redisExpireTime;

    @Value("${monitorSite.url}")
    private String monitorSiteUrl;

    @Value("${monitorSite.param.isp_h}")
    private String ispH;

    @Value("${monitorSite.param.isp_a}")
    private String ispA;

    @Value("${monitorSite.param.isp_s}")
    private String ispS;

    @Value("${monitorSite.param.isp_v}")
    private String ispV;

    @Value("${monitorSite.param.tenantId}")
    private String tenantId;

    @Value("${monitorSite.param.userName}")
    private String userName;

    @Value("${monitorSite.param.phone}")
    private String phone;

    @Value("${monitorSite.access_token.url}")
    private String accessTokenUrl;

    @Value("${externalSys.SiteService.monitorRedisIndex:8}")
    private int monitorRedisIndex;

    private static String MEDIATYPE="mediaType";

    public List<SiteVO> getHySites_old(HySiteDTO dto) throws Exception{
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        //先从redis缓存获取
        String redisKey = MEDIATYPE+dto.getMediaType()+"_"+StringUtils.toStringValue(dto.getSiteName())+"_"+StringUtils.toStringValue(dto.getPageNum())+"_"+StringUtils.toStringValue(dto.getPageSize());
        if (jedisPoolUtils.exists(redisKey,siteRedisIndex)){
            String results = jedisPoolUtils.get(redisKey, siteRedisIndex);
            if (!StringUtils.isEmpty(results)){
                return JsonUtils.toOptionObject(results,List.class,SiteVO.class).get();
            }
        }
        //构造请求参数
        HashMap<String,Object> map = new HashMap<>();
        map.put(SiteConstant.HY_PARAM_METHODNAME,methodName);
        map.put(SiteConstant.HY_PARAM_SERVICEID,serviceId);
        map.put(SiteConstant.HY_PARAM_CURRUSERNAME,currUserName);
        map.put(SiteConstant.HY_PARAM_MEDIATYPE,dto.getMediaType());
        map.put(SiteConstant.HY_PARAM_PAGEINDEX,dto.getPageNum());
        map.put(SiteConstant.HY_PARAM_PAGESIZE,dto.getPageSize());
        map.put(SiteConstant.HY_PARAM_SITENAME,StringUtils.toStringValue(dto.getSiteName()));
        //调用海云接口查询站点列表
        String result = HttpUtil.newBuilder().setUrl(url).setParamter(map).httpSendGet();
        String data1 = JSONObject.parseObject(result).getString("DATA");
        String data2 = JSONObject.parseObject(data1).getString("DATA");
        List<SiteVO> list = JSONObject.parseArray(data2, SiteVO.class);
        //存入缓存
        jedisPoolUtils.set(redisKey,JsonUtils.toOptionalJson(list).get(),redisExpireTime,siteRedisIndex);
        return list;
    }

    public String getHySites(HySiteDTO dto) throws Exception{
        //构造请求参数
        Map<String,Object> map = new LinkedHashMap<>();
        map.put(SiteConstant.HY_PARAM_METHODNAME,methodName);
        map.put(SiteConstant.HY_PARAM_SERVICEID,serviceId);
        map.put(SiteConstant.HY_PARAM_CURRUSERNAME,currUserName);
        map.put(SiteConstant.HY_PARAM_MEDIATYPE,dto.getMediaType());
        map.put(SiteConstant.HY_PARAM_PAGEINDEX,dto.getPageNum());
        map.put(SiteConstant.HY_PARAM_PAGESIZE,dto.getPageSize());
        map.put(SiteConstant.HY_PARAM_SITENAME,StringUtils.toStringValue(dto.getSiteName()));
        map.put(SiteConstant.HY_PARAM_KEY,hyKey);
        //调用海云接口查询站点列表
        String result = HttpUtil.newBuilder().setUrl(url).setParamter(map).httpSendGet();
        if ("false".equalsIgnoreCase(JSONObject.parseObject(result).getString("ISSUCCESS"))){
            throw new Exception(JSONObject.parseObject(result).getString("MSG"));
        }
        String data = JSONObject.parseObject(result).getString("DATA");
        //存入缓存
        return data;
    }


    public List<SiteVO> getAllHySites(GetAllSiteDTO dto) throws Exception{
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        //先从redis缓存获取
        String redisKey = "ALL";
        if (jedisPoolUtils.exists(redisKey,siteRedisIndex)){
            String results = jedisPoolUtils.get(redisKey, siteRedisIndex);
            if (!StringUtils.isEmpty(results)){
                return JsonUtils.toOptionObject(results,List.class,SiteVO.class).get();
            }
        }
        //构造请求参数
        HashMap<String,Object> map = new HashMap<>();
        map.put(SiteConstant.HY_PARAM_METHODNAME,methodName);
        map.put(SiteConstant.HY_PARAM_SERVICEID,serviceId);
        map.put(SiteConstant.HY_PARAM_CURRUSERNAME,currUserName);
        map.put(SiteConstant.HY_PARAM_PAGEINDEX,dto.getPageIndex());
        map.put(SiteConstant.HY_PARAM_PAGESIZE,dto.getPageSize());
        Map<Integer, String> mediaType = MediaType.getMediaType();
        List<SiteVO> list = new ArrayList<>();
        for (Map.Entry<Integer, String> mediaTypes : mediaType.entrySet()) {
            if (mediaTypes.getKey()!=0){
                map.put(SiteConstant.HY_PARAM_MEDIATYPE,mediaTypes.getKey());
                //调用海云接口查询站点列表
                String result = HttpUtil.newBuilder().setUrl(url).setParamter(map).httpSendGet();
                String data1 = JSONObject.parseObject(result).getString("DATA");
                String data2 = JSONObject.parseObject(data1).getString("DATA");
                list.addAll(JSONObject.parseArray(data2, SiteVO.class));
            }
        }
        //调用海云接口查询站点列表
        //存入缓存
        jedisPoolUtils.set(redisKey,JsonUtils.toOptionalJson(list).get(),redisExpireTime,siteRedisIndex);
        return list;
    }

    public JSONObject getMonitorSites(MonitorSiteDTO dto) throws Exception{
        String access_token;
        String ACCESS_TOKEN = "access_token";
        //监测云配置信息未配置时返回空
        if (StringUtils.isEmpty(accessTokenUrl)||"null".equals(accessTokenUrl)||
                StringUtils.isEmpty(monitorSiteUrl)||"null".equals(monitorSiteUrl)){
            return null;
        }
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        //先从缓存中查找是否存在access_token
        if (jedisPoolUtils.exists(ACCESS_TOKEN,monitorRedisIndex)&&!StringUtils.isEmpty(jedisPoolUtils.get(ACCESS_TOKEN,monitorRedisIndex))){
            access_token = jedisPoolUtils.get(ACCESS_TOKEN,monitorRedisIndex);
        }
        //通过请求获取access_token
        else {
            access_token = Try.of(() -> {
                JSONObject jsonObject = JSONObject.parseObject(HttpUtil.httpSendGet(accessTokenUrl));
                String token;
                if ("true".equalsIgnoreCase(jsonObject.getString("isSuccess"))) {
                    token = JSONObject.parseObject(jsonObject.getString("data")).getString(ACCESS_TOKEN);
                    jedisPoolUtils.set(ACCESS_TOKEN, token, redisExpireTime, monitorRedisIndex);
                } else {
                    throw new Exception(jsonObject.getString("msg"));
                }
                return token;
            }).onFailure(err -> log.error("调用三方服务获取access_token失败！", err)).getOrElseThrow(err -> new Exception("三方服务异常,监控云站点数据暂不可获取", err));
        }
        //封装请求参数is
        Map<String,Object> map = new HashMap<>();
        if (!StringUtils.isEmpty(dto.getName())){
            map.put(SiteConstant.MONITORSITE_PARAM_NAME,dto.getName());
        }
        map.put(SiteConstant.MONITORSITE_PARAM_PAGESIZE,dto.getPageSize());
        map.put(SiteConstant.MONITORSITE_PARAM_PAGEINDEX,dto.getPageNum());
        map.put(SiteConstant.MONITORSITE_PARAM_ISPH,ispH);
        map.put(SiteConstant.MONITORSITE_PARAM_ISPA,ispA);
        map.put(SiteConstant.MONITORSITE_PARAM_ISPS,ispS);
        map.put(SiteConstant.MONITORSITE_PARAM_ISPV,ispV);
        map.put(SiteConstant.MONITORSITE_PARAM_TENANID,tenantId);
        map.put(SiteConstant.MONITORSITE_PARAM_USERNAME,userName);
        map.put(SiteConstant.MONITORSITE_PARAM_PHONE,phone);
        map.put(ACCESS_TOKEN,access_token);
        return Try.of(()->{
            String result = HttpUtil.newBuilder().setUrl(monitorSiteUrl).setParamter(map).httpSendGet();
            JSONObject jsonObject1 = JSONObject.parseObject(result);
            if (!"true".equalsIgnoreCase(jsonObject1.getString("isSuccess"))){
                throw new Exception(jsonObject1.getString("msg"));
            }
            return jsonObject1;
        }).onFailure(err -> log.error("获取监控云站点失败！", err)).getOrElseThrow(err->new Exception("获取监控云站点失败", err));
    }
}
