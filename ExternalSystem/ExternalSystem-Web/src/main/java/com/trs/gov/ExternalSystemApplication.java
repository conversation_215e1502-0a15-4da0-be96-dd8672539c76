package com.trs.gov;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.trs")
@Slf4j
//@EnableSwagger2
public class ExternalSystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(ExternalSystemApplication.class, args);
        log.info("ExternalSystem-Service模块启动成功!");
    }
}
