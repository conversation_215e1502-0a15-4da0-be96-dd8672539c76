package com.trs.gov.externalSystem.covert;


import com.trs.upms.client.config.Operation;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/10/20 17:44
 * @version 1.0
 * @since  1.0
 */
public class RightCovert {
    /**
     * 转义操作权限
     * @param operList
     * @return
     */
    public static List<String> convert(List<Operation> operList){
        List<String> resultList = new ArrayList<>();
        if(operList==null || operList.size()==0){
            return resultList;
        }
        for (Operation oper:operList
        ) {
            if (oper!=null){
                resultList.add(oper.getKey());
            }
        }
        return resultList;
    }

}
