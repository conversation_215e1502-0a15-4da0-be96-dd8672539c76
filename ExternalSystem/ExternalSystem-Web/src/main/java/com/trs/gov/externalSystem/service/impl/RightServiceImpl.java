package com.trs.gov.externalSystem.service.impl;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.externalSystem.DTO.RightSearchDTO;
import com.trs.gov.externalSystem.covert.RightCovert;
import com.trs.gov.externalSystem.mgr.RightMgr;
import com.trs.gov.externalSystem.service.RightService;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.base.constant.UnitConstant;
import com.trs.gov.management.service.UnitService;
import com.trs.upms.client.config.Operation;
import com.trs.upms.client.mgr.UpmsRightMgr;
import com.trs.upms.client.po.Resource;
import com.trs.upms.client.service.impl.UpmsRightServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/25 18:16
 * @version 1.0
 * @since  1.0
 */
@Slf4j
@Service
public class RightServiceImpl extends UpmsRightServiceImpl implements RightService {


    @Value("${upms.client.resourcetype}")
    private String resourcetype;

    @Reference(check = false, timeout = 60000)
    private UnitService unitService;

    @Autowired
    private UpmsRightMgr upmsRightMgr;

    @Autowired
    private RightMgr rightMgr;

    @Override
    public List<Resource> queryRightResources(String unitType, String userName) throws Exception {
        if (StringUtils.isEmpty(unitType)) {
            unitType = "ALL";
        }
        List<Resource> resources = new ArrayList<>();
        UnitSearchDTO unitSearchDTO = new UnitSearchDTO();
        switch (unitType) {
            case "ALL":
                for (Map.Entry<String, String> type : UnitConstant.unitTypeConstant.entrySet()) {
                    Resource resource = new Resource();
                    resource.setResourceId("DEPTTYPE" + type.getKey());
                    resource.setResourceName(type.getValue());
                    resource.setResourceType(resourcetype);
                    resource.setEntity(true);
                    resource.setShouQuan(true);
                    unitSearchDTO.setUnitType(type.getKey());
                    BaseUtils.setUserInfoToDTO(unitSearchDTO);
                    this.doUnit(resource, unitService.queryUnitByType(unitSearchDTO).getDatas());
                    resources.add(resource);
                }
                break;
            default:
                Resource resource = new Resource();
                resource.setResourceId("DEPTTYPE" + unitType);
                resource.setResourceName(UnitConstant.unitTypeConstant.get(unitType));
                resource.setResourceType(resourcetype);
                resource.setEntity(true);
                resource.setShouQuan(true);
                unitSearchDTO.setUnitType(unitType);
                BaseUtils.setUserInfoToDTO(unitSearchDTO);
                this.doUnit(resource, unitService.queryUnitByType(unitSearchDTO).getDatas());
                resources.add(resource);
        }
        return resources;
    }
    private void doUnit(Resource resource, List<UnitVO> unitVOS) {
        List<String> grandIdList = new ArrayList<>();
        List<Resource> resources = new ArrayList<>();
        for (UnitVO unitVO : unitVOS) {
            Resource resourceSon = new Resource();
            resourceSon.setResourceId(Long.toString(unitVO.getId()));
            resourceSon.setResourceName(unitVO.getUnitName());
            resourceSon.setResourceType(resourcetype);
            resourceSon.setShouQuan(true);
            resourceSon.setEntity(true);
            resources.add(resourceSon);
            grandIdList.add(Long.toString(unitVO.getId()));
        }
        resource.setGrandIdList(grandIdList);
        resource.setChildren(resources);
    }

    @Override
    public RestfulResults<List<String>> getMyRightOper(RightSearchDTO dto) throws Exception{
        //  构造返回对象
        RestfulResults restfulResults = null;
        try {
            //  调用业务方法
            List<Operation> myRightOper = rightMgr.getMyRightOper(dto);
            restfulResults = RestfulResults.ok(RightCovert.convert(myRightOper))
                    .addMsg("获取数据成功")
                    .addTotalCount(Long.parseLong(String.valueOf(myRightOper.size())));
        }catch (Exception e){
            e.printStackTrace();
            restfulResults = RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return restfulResults;
    }
}
