package com.trs.gov.externalSystem.controller;

import com.trs.gov.externalSystem.DTO.RightSearchDTO;
import com.trs.gov.externalSystem.service.impl.RightServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/10/20 17:13
 * @version 1.0
 * @since  1.0
 */
@RestController
@RequestMapping("externalSystem")
public class RightController {
    @Autowired
    private RightServiceImpl rightService;

   /**
    * 获取操作权限按钮<BR>
    * <AUTHOR> lan.xin E-mail: <EMAIL>
    * 创建时间：2020/10/20 17:16
    * @param  dto 请求参数
    * @throws
    * @return
    */
    @RequestMapping(value = "getMyRightOper",method = {RequestMethod.GET})
    public RestfulResults getMyRightOper(RightSearchDTO dto) throws Exception{
        return rightService.getMyRightOper(dto);
    }

}
