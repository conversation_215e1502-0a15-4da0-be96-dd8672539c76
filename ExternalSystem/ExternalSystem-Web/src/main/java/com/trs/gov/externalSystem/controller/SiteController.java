package com.trs.gov.externalSystem.controller;

import com.trs.gov.externalSystem.DTO.GetAllSiteDTO;
import com.trs.gov.externalSystem.DTO.HySiteDTO;
import com.trs.gov.externalSystem.DTO.MonitorSiteDTO;
import com.trs.gov.externalSystem.VO.MonitorSiteVO;
import com.trs.gov.externalSystem.VO.SiteVO;
import com.trs.gov.externalSystem.service.impl.SiteServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/23 11:32
 * @version 1.0
 * @since  1.0
 */
@RestController
@RequestMapping("externalSystem")
public class SiteController {

    @Autowired
    private SiteServiceImpl siteService;

    /**
     * 从外部系统获取站点信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/23 11:32
     * @param
     * @throws
     * @return
     */
    @RequestMapping(value = "getHySites",method = {RequestMethod.GET})
    public RestfulResults<List<SiteVO>> getAllSites(HySiteDTO dto) throws Exception{
       return siteService.getHySites(dto);
    }

    /**
     * 一次性获取海云站点信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/23 11:32
     * @param
     * @throws
     * @return
     */
    @RequestMapping(value = "getAllHySites",method = {RequestMethod.GET})
    public RestfulResults<List<SiteVO>> getAllHySites(GetAllSiteDTO dto) throws Exception{
        return siteService.getAllHySites(dto);
    }


    /**
     * 获取监控云站点信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/23 11:06
     * @param   dto 请求参数
     * @throws
     * @return
     */
    @RequestMapping(value = "getMonitorSites",method = {RequestMethod.GET})
    public RestfulResults<List<MonitorSiteVO>> getMonitorSites(MonitorSiteDTO dto){
        return siteService.getMonitorSites(dto);
    }
}
