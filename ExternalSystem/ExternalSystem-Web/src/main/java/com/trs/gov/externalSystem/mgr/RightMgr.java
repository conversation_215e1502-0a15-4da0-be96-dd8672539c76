package com.trs.gov.externalSystem.mgr;

import com.trs.gov.externalSystem.DTO.RightSearchDTO;
import com.trs.gov.management.service.UnitService;
import com.trs.upms.client.config.Operation;
import com.trs.upms.client.mgr.UpmsRightMgr;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @auther
 * @description
 * @date
 **/
@Component
public class RightMgr {

    private static final String OPERLISTKEY = "operList";

    @Reference(check = false, timeout = 60000)
    private UnitService unitService;

    @Autowired
    private UpmsRightMgr upmsRightMgr;

    @Value("${externalSys.RightService.redisIndex:7}")
    private int rightIndex;

    @Value("${externalSys.RightService.redisExpireTime:3600}")
    private int redisExpireTime;

    public List<Operation> getMyRightOper(RightSearchDTO dto) throws Exception {
        // 为0且非管理员的直接返回空
        if ("0".equalsIgnoreCase(dto.getOperId()) && !upmsRightMgr.isAdmin(dto.getUserName())) {
            return new ArrayList<>();
        }
        // 获取权限数据
        return upmsRightMgr.getMyOprRight(dto.getUserName(), dto.getOperId());
    }
}
