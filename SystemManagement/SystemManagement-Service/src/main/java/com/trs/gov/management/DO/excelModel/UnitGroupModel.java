package com.trs.gov.management.DO.excelModel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/30 17:35 
 * @version 1.0  
 * @since  1.0
 */
@Data
public class UnitGroupModel extends BaseRowModel {

    /**
     * 序号
     */
    @ExcelProperty(value = "序号",index = 0)
    private Integer id;

    /**
     * 分组名称
     */
    @ExcelProperty(value = "分组名称",index = 1)
    private String groupName;

    /**
     * 单位数量
     */
    @ExcelProperty(value = "单位数量",index = 2)
    private Long unitCounts;

    /**
     * 1,启用;-1停用
     */
    @ExcelProperty(value = "启用状态",index = 3)
    private String status;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人",index = 4)
    private String crUser;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间",index = 5)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人",index = 6)
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "更新时间",index = 7)
    private Date updateTime;

    public void setStatus(Integer status){
        if (status==1){
            this.status="启用中";
        }
        if (status==-1){
            this.status="停用中";
        }
    }
}
