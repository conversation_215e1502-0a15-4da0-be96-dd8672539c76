package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.VO.FileVO;
import lombok.Data;

import java.util.Date;

@Data
public class UnitExportDTO extends BaseDTO {

    /**
     * 单位id(新建给0，编辑给对应id)
     */
    private String oldUnitId;

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    private String unitType;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 单位负责人
     */
    private String unitMaster;
    /**
     * 单位负责人真实姓名
     */
    private String unitMasterTrueName;

    /**
     * 运维单位负责人电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 单位地址
     */
    private String unitAddr;

    /**
     * 单位简介
     */
    private String unitDesc;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 业务领域
     */
    private String businessArea;

    /**
     * 商标
     */
    private FileVO logo;

    /**
     * 创建人
     */
    private String crUser;

    /**
     * 创建时间
     */
    private Date crTime;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if(CMyString.isEmpty(oldUnitId)){
            throw new ParamInvalidException("老工单Id不能为空!");
        }
        if(CMyString.isEmpty(unitType)){
            throw new ParamInvalidException("单位类型不能为空!");
        }
        if(CMyString.isEmpty(unitName)){
            throw new ParamInvalidException("单位名称不能为空!");
        }
        if(CMyString.isEmpty(unitMaster)){
            throw new ParamInvalidException("单位负责人名称不能为空!");
        }
        if(CMyString.isEmpty(email)){
            throw new ParamInvalidException("单位负责人邮箱不能为空!");
        }
        if(crTime == null){
            throw new ParamInvalidException("创建时间不能为空!");
        }
        return true;
    }
}
