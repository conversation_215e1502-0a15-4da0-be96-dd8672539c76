package com.trs.gov.management.base.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/10/22 15:18
 * @version 1.0
 * @since  1.0
 */
public enum MediaType {

    ALL(0,"全部"),APP(1,"网站"),INTERNET(2,"APP"),
    WECHAT(3,"微信"),WEBO(4,"微博");
    private static final Map<Integer, String> mappings = new HashMap<>(16);
    static {
        for (MediaType value : values()) {
            mappings.put(value.typeId,value.value);
        }
    }
    public Integer getTypeId() {
        return typeId;
    }

    public String getValue() {
        return value;
    }

    private final Integer typeId;
    private final String value;

    MediaType(Integer typeId,String value){
        this.typeId = typeId;
        this.value = value;
    }

    public static Map<Integer,String> getMediaType(){
        return mappings;
    }
}
