package com.trs.gov.management.VO;

import lombok.Data;

import java.util.Objects;

/**
 * Description:  <BR>
 * Copyright: Copyright (c) 2004-2016 拓尔思信息技术股份有限公司 <BR>
 * Company: www.trs.com.cn <BR>
 *
 * <AUTHOR>
 * @version 1.0
 * @createTime 2022/11/28 14:14
 */
@Data
public class GroupUnitVO {


    private Integer groupuserid;
    private Integer  groupid;
    private String  groupname;
    private Integer userid;
    private String  tenant;
    private String username;
    private Integer  status;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GroupUnitVO that = (GroupUnitVO) o;
        return groupid.equals(that.groupid) && groupname.equals(that.groupname);
    }

    @Override
    public int hashCode() {
        return Objects.hash(groupid, groupname);
    }
}
