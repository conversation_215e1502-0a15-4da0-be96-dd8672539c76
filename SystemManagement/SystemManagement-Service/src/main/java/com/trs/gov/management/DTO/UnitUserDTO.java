package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class UnitUserDTO extends BasePageDTO {

    /**
     * 组织单位id
     */
    private String unitId;
    /**
     * 单位编码
     */
    private String groupId;
    /**
     * 单位用户名
     */
    private String userName;

    /**
     *用户状态(1 启用,0停用)
     */
    private Integer status = 1;

    @Override
    public boolean isValid() throws ServiceException {

        return true;
    }
}
