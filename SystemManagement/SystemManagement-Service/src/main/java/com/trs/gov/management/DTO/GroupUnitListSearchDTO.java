package com.trs.gov.management.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * @auther
 * @description
 * @date
 **/
@Data
public class GroupUnitListSearchDTO extends BasePageDTO {

    /**
     * 单位分组id
     */
    private String groupId;

    /**
     * 1启用，-1停用
     */
    private Integer status;

    /**
     * 是否获取所有
     */
    private String isAll;

    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isEmpty(groupId)||"null".equalsIgnoreCase(groupId))
        {
            throw new ParamInvalidException("分组id不能为空");
        }
        return true;
    }
}
