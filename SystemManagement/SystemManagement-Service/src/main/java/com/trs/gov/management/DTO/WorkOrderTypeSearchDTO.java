package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;


@Data
public class WorkOrderTypeSearchDTO extends BaseDTO {

    /**
     * 工单类型id
     */
    private String id;

    /**
     * 工单类型父级id
     */
    private String parentId;

    /**
     * 工单类型状态（1,启用;-1,停用)
     */
    private String status;

    /**
     * 工单类型等级
     */
    private Integer level;

    /**
     * 顶级父类key
     **/
    private String rootKey;

    private boolean isOpenApi = false;

    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
