package com.trs.gov.management.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-09 14:19
 * @version 1.0
 * @since 1.0
 * 单位实体对象
 */
@Data
@TableName("unit")
public class UnitDO extends BaseDO {

    public static final String OBJ_TYPE = "unit";
    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    @ApiModelProperty(value = "单位类型编号", required = true)
    @Column(name = "unit_type")
    private String unitType;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称", required = true)
    @Column(name = "unit_name")
    private String unitName;

    /**
     * 单位负责人
     */
    @ApiModelProperty(value = "单位负责人", required = true)
    @Column(name = "unit_master")
    private String unitMaster;

    /**
     * 运维单位负责人电话
     */
    @ApiModelProperty(value = "运维单位负责人电话", required = true)
    @Column(name = "phone")
    private String phone;

    /**
     * 联系邮箱
     */
    @ApiModelProperty(value = "联系邮箱", required = true)
    @Column(name = "email")
    private String email;

    /**
     * 单位地址
     */
    @ApiModelProperty(value = "单位地址", required = false)
    @Column(name = "unit_addr")
    private String unitAddr;

    /**
     * 单位简介
     */
    @ApiModelProperty(value = "单位简介", required = false)
    @Column(name = "unit_desc")
    private String unitDesc;

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位编码", required = false)
    @Column(name = "unit_code")
    private String unitCode;

    /**
     * 业务领域
     */
    @ApiModelProperty(value = "业务领域", required = false)
    @Column(name = "business_area")
    private String businessArea;

    /**
     * 单位人员数量
     */
    @ApiModelProperty(value = "单位人员数量", required = true)
    @Column(name = "person_count")
    private Integer personCount;

    /**
     * 单位状态（1,启用;-1,停用)
     */
    @ApiModelProperty(value = "单位状态（1,启用;-1,停用)", required = true)
    @Column(name = "status")
    private Integer status;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = true)
    @Column(name = "update_time")
    private Date updateTime;

    /**
     *单位负责人用于显示的名称
     */
    @ApiModelProperty(value = "负责人用于显示的名称", required = true)
    @Column(name = "unit_master_true_name")
    private String unitMasterTrueName;

    /**
     * 创建人用于显示的名称
     */
    @ApiModelProperty(value = "创建人用于显示的名称", required = true)
    @Column(name = "true_name")
    private String trueName;

    @Version
    private Integer version;

    /**
     * 历史绩效值：
     * 满分条件（35分）：工单实际完成时间与创建时间间隔≤72 小时，且完成结果经审核符合验收要求（以工单验收标准为准）
     * 非满分得分规则：
     * 72 小时＜完成时间间隔≤96 小时（3-4 个自然日）：得分 28 分
     * 96 小时＜完成时间间隔≤120 小时（4-5 个自然日）：得分 21 分
     * 120 小时＜完成时间间隔≤168 小时（5-7 个自然日）：得分 14 分
     * 168 小时＜完成时间间隔≤240 小时（7-10 个自然日）：得分 7 分
     * 完成时间间隔＞240 小时（10 个自然日以上）：得分 0 分
     */
    @ApiModelProperty(value = "历史绩效值")
    @Column(name = "performance")
    private Integer performance;

    /**
     * 资源负载
     * 各部门工单积压量分之一乘以25（0,1都算出来的值都是25）
     */
    @ApiModelProperty(value = "资源负载")
    @Column(name = "resource_load")
    private Integer resourceLoad;

    public Date getUpdateTime() {
        if (updateTime == null) {
            updateTime = new Date();
        }
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
