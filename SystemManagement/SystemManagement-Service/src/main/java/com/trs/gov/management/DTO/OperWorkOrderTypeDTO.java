package com.trs.gov.management.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class OperWorkOrderTypeDTO extends BaseDTO {

    /**
     * 工单类型id
     */
    private String id;

    /**
     * 工单类型状态（1,启用;-1,停用)
     */
    private Integer status;

    /**
     * 是否删除(0:未删除，1:删除)
     */
    private Integer isDel;

    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isEmpty(id)||"null".equals(id)) {
            throw new ParamInvalidException("类型id不能为空！");
        }
        return true;
    }
}
