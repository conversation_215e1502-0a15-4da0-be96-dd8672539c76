package com.trs.gov.management.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/10/9 10:04
 * @version 1.0
 * @since  1.0
 * 工单类型实体对象
 */
@Data
@TableName("work_order_type")
public class WorkOrderTypeDO extends BaseDO {

    /**
     * 工单类型名称
     */
    @ApiModelProperty(value = "工单类型名称", required = true)
    @Column(name = "type_name")
    private String typeName;

    /**
     * 工单类型描述
     */
    @ApiModelProperty(value = "工单类型描述", required = true)
    @Column(name = "type_desc")
    private String typeDesc;

    /**
     * 父级id
     */
    @ApiModelProperty(value = "父级id", required = true)
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 类型等级
     */
    @ApiModelProperty(value = "类型等级", required = true)
    @Column(name = "level")
    private Integer level;

    /**
     * 是否删除(0:未删除，1:删除)
     */
    @ApiModelProperty(value = "是否删除(0:未删除，1:删除)", required = true)
    @Column(name = "is_del")
    private Integer isDel;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = true)
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 顶级字段的key
     */
    @ApiModelProperty(value = "顶级字段的rootKey", required = true)
    @Column(name = "root_key")
    private String rootKey;

    /**
     * 受理单位id
     */
    @ApiModelProperty(value = "受理单位id", required = false)
    @Column(name = "accept_unit_id")
    private Long acceptUnitId;

    public Date getUpdateTime() {
        if (updateTime == null) {
            updateTime = new Date();
        }
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
