package com.trs.gov.management.base.constant;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @auther
 * @description
 * @date
 **/
public class UnitConstant extends BaseConstant {

    /**
     * 请求方法参数名
     */
    public static final String POST_METHOD = "method";

    /**
     * 请求方法值
     */
    public static final String POST_METHOD_VALUE = "queryGroupAndUser";

    /**
     * 参数--资源ID
     */
    public static final String PARAM_GROUPID = "groupId";

    /**
     * 用户名
     */
    public static final String PARAM_USERNAME = "userName";

    /**
     * 用户名状态
     */
    public static final String PARAM_STATUS = "status";

    /**
     * 主办主管单位导出表表名
     */
    public final static String MASTERHOST_UNIT_FILENAME="主办主管关系管理表";

    /**
     * 主办主管单位表脚标名
     */
    public final static String MASTERHOST_UNIT_SHEETNAME="主办主管关系管理表";

    /**
     * 服务单位导出表表名
     */
    public final static String OPERATION_UNIT_FILENAME="服务单位管理表";

    /**
     * 服务单位表脚标名
     */
    public final static String OPERATION_UNIT_SHEETNAME="服务单位管理表";

    /**
     * 单位用户缓存redisKey关键词
     */
    public final static String USERS_REDIS_KEY="_users";

    /**
     * 单位logo缓存redisKey关键词
     */
    public final static String LOGO_REDIS_KEY="_logo";

    /**
     * 主管单位
     */
    public static final String PARAM_MASTERUNIT_ID= "1";
    public static final String PARAM_MASTERUNIT_NAME= "主管单位";

    /**
     * 主办单位
     */
    public static final String PARAM_HOSTUNIT_ID = "2";
    public static final String PARAM_HOSTUNIT_NAME = "主办单位";

    /**
     * 主办主管单位
     */
    public static final String PARAM_MASTERHOSTUNIT_ID = "1,2";
    public static final String PARAM_MASTERHOSTUNIT_NAME = "主管主办单位";
    /**
     * 运维单位
     */
    public static final String PARAM_OPERATION_ID = "3";
    public static final String PARAM_OPERATIONUNIT_NAME = "运维单位";

    /**
     * 厂商单位
     */
    public static final String PARAM_FACTORY_ID = "4";
    public static final String PARAM_FACTOR_NAME = "厂商单位";

    /**
     * 服务单位
     */
    public static final String PARAM_SERVICEUNIT_ID = "3,4";

    public static final Map<String,String> unitTypeConstant = new HashMap<>();

    static {
        unitTypeConstant.put(PARAM_MASTERHOSTUNIT_ID,PARAM_MASTERHOSTUNIT_NAME);
        unitTypeConstant.put(PARAM_HOSTUNIT_ID,PARAM_HOSTUNIT_NAME);
        unitTypeConstant.put(PARAM_MASTERUNIT_ID,PARAM_MASTERUNIT_NAME);
        unitTypeConstant.put(PARAM_OPERATION_ID,PARAM_OPERATIONUNIT_NAME);
        unitTypeConstant.put(PARAM_FACTORY_ID,PARAM_FACTOR_NAME);
    }

    public static Optional<String> getUnitTypeDesc(String id){
        return Optional.ofNullable(unitTypeConstant.get(id));
    }

    public static final Map<String,String> unitExcelFileName = new HashMap<>();
    static {
        unitExcelFileName.put(PARAM_HOSTUNIT_ID,MASTERHOST_UNIT_FILENAME);
        unitExcelFileName.put(PARAM_MASTERUNIT_ID,MASTERHOST_UNIT_FILENAME);
        unitExcelFileName.put(PARAM_MASTERHOSTUNIT_ID,MASTERHOST_UNIT_FILENAME);
        unitExcelFileName.put(PARAM_OPERATION_ID,OPERATION_UNIT_FILENAME);
        unitExcelFileName.put(PARAM_FACTORY_ID,OPERATION_UNIT_FILENAME);
        unitExcelFileName.put(PARAM_SERVICEUNIT_ID,OPERATION_UNIT_FILENAME);
    }
    public static Optional<String> getFileName(String id){
        return Optional.ofNullable(unitExcelFileName.get(id));
    }

}
