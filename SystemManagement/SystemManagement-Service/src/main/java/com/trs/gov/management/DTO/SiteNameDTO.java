package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class SiteNameDTO extends BaseDTO {

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 渠道类型
     */
    private Integer mediaType;

    /**
     * 监控云站点名称
     */
    private String monitorSite;

    /**
     * 监控云站点id
     */
    private Long monitorSiteId;
    @Override
    public boolean isValid() throws ServiceException {

        return true;
    }
}
