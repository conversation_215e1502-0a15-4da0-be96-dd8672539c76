package com.trs.gov.management.VO;


import com.trs.gov.core.VO.BaseVO;
import lombok.Data;
@Data
public class WorkOrderTypeVO extends BaseVO {

    /**
     * 工单类型id
     */
    private Long id;

    /**
     * 工单类型名称
     */
    private String typeName;

    /**
     * 工单类型描述
     */
    private String typeDesc;

    /**
     * 工单类型状态 1启用，停用
     */
    private Integer status;

    /**
     * 是否有子级
     */
    private String hasChild;

    /**
     * 受理单位名称
     */
    private String acceptUnit;

    /**
     * 受理单位id
     */
    private String acceptUnitId;

}
