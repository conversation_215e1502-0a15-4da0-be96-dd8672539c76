package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

import java.util.Date;

@Data
public class SiteRelationExportDTO extends BaseDTO {

    /**
     * 旧站点关系ID
     */
    private String oldSiteRelationId;
    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 渠道类型
     */
    private Integer mediaType;

    /**
     * 渠道类型名称
     */
    private String mediaName;

    /**
     * 唯一标识
     */
    private String uniqueId;

    /**
     * 备案时间
     */
    private Date filingTime;

    /**
     * 建设时间
     */
    private Date constructionTime;

    /**
     * 主办单位id
     */
    private Long hostUnitId;

    /**
     * 主办单位名称
     */
    private String hostUnit;

    /**
     * 主管单位id
     */
    private Long masterUnitId;

    /**
     * 主管单位名称
     */
    private String masterUnit;
    /**
     * 运维单位id
     */
    private Long operationUnitId;

    /**
     * 运维单位名称
     */
    private String operationUnit;

    /**
     * 运维单位负责人
     */
    private String operationHost;

    /**
     * 运维单位负责人电话
     */
    private String phone;

    /**
     * 运维周期 开始
     */
    private Date operationStartTime;

    /**
     * 运维周期 结束
     */
    private Date operationEndTime;

    /**
     * 监控云站点名称
     */
    private String monitorSite;

    /**
     * 监控云站点ID
     */
    private Long monitorSiteId;

    /**
     * 创建人
     */
    private String crUser;

    /**
     * 创建时间
     */
    private Date crTime;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (CMyString.isEmpty(oldSiteRelationId)) {
            throw new ParamInvalidException("老工单oldSiteRelationId不能为空!");
        }
        if (siteId == null) {
            throw new ParamInvalidException("站点siteId不能为空!");
        }
        if (CMyString.isEmpty(siteName)) {
            throw new ParamInvalidException("站点名称不能为空!");
        }
        if (filingTime == null) {
            throw new ParamInvalidException("备案时间不能为空!");
        }
        if (constructionTime == null) {
            throw new ParamInvalidException("建设时间不能为空!");
        }
        if (hostUnitId == null) {
            throw new ParamInvalidException("主办单位id不能为空!");
        }
        if (CMyString.isEmpty(hostUnit)) {
            throw new ParamInvalidException("主办单位名称不能为空!");
        }
        if (masterUnitId == null) {
            throw new ParamInvalidException("主管单位id不能为空!");
        }
        if (CMyString.isEmpty(masterUnit)) {
            throw new ParamInvalidException("主管单位名称不能为空!");
        }
        if (operationUnitId == null) {
            throw new ParamInvalidException("运维单位id不能为空!");
        }
        if (CMyString.isEmpty(operationUnit)) {
            throw new ParamInvalidException("运维单位名称不能为空!");
        }
        if (mediaType == null) {
            throw new ParamInvalidException("媒体类型不能为空!");
        }
        if (CMyString.isEmpty(mediaName)) {
            throw new ParamInvalidException("媒体名称不能为空!");
        }
        if (CMyString.isEmpty(operationHost)) {
            throw new ParamInvalidException("运维单位负责人不能为空!");
        }
        if (operationStartTime == null) {
            throw new ParamInvalidException("运维周期开始时间不能为空!");
        }
        if (operationEndTime == null) {
            throw new ParamInvalidException("运维周期结束时间不能为空!");
        }
        if (CMyString.isEmpty(uniqueId)) {
            throw new ParamInvalidException("站点的唯一标识不能为空!");
        }
        return true;
    }
}
