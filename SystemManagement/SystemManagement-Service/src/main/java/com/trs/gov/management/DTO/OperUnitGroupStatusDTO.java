package com.trs.gov.management.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 10:00
 * @version 1.0
 * @since  1.0
 */
@Data
public class OperUnitGroupStatusDTO extends BaseDTO {

    /**
     * 单位分组id
     */
    private String groupIds;

    /**
     * 启用停用状态1,启用；-1,停用
     */
    private Integer status;

    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isEmpty(groupIds)||"null".equalsIgnoreCase(groupIds)){
            throw new ParamInvalidException("单位分组id不能为空");
        }
        if (status ==null){
            throw new ParamInvalidException("操作状态不能为空");
        }
        return true;
    }
}
