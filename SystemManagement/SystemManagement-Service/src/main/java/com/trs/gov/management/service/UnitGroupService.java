package com.trs.gov.management.service;

import com.trs.common.base.Report;
import com.trs.common.base.Reports;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DO.excelModel.UnitGroupModel;
import com.trs.gov.management.DTO.*;
import com.trs.gov.management.VO.GroupUserCountVO;
import com.trs.gov.management.VO.UnitGroupVO;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 9:44
 * @version 1.0
 * @since  1.0
 * 单位分组配置服务
 */
public interface UnitGroupService {


    /**
     * 新建/编辑单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 9:54
     * @param  dto 传递参数
     * @throws
     * @return 保存结果
     */
    RestfulResults<Report> saveOrUpdateUnitGroup(UnitGroupDTO dto) throws ServiceException;

    /**
     * 删除分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 9:57
     * @param  groupIds  分组id串
     * @throws
     * @return 删除结果
     */
    RestfulResults<Reports> deleteUnitGroup(String groupIds);

    /**
     * 启用/停用单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 10:03
     * @param  dto  传递参数
     * @throws
     * @return 操作结果
     */
    RestfulResults<List<Report>> operUnitGroupStatus(OperUnitGroupStatusDTO dto);

    /**
     * 配置单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 10:30
     * @param  dto  传递参数
     * @throws
     * @return 配置结果
     */
    RestfulResults<Report> addUnitsToGroup(AddUnitsToGroupDTO dto);


    /**
     * 查询单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 10:52
     * @param  dto 传递参数
     * @throws
     * @return 查询结果
     */
    RestfulResults<List<UnitGroupVO>> queryUnitGroupList(UnitGroupSearchDTO dto);


    /**
     * 获取单位分组下单位信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 17:52
     * @param  dto 分组id
     * @throws
     * @return 查询结果
     */
    RestfulResults<List<UserCountVO>> getGroupUnitInfoListByGroupId(GroupUnitListSearchDTO dto);

    /**
     * 导出单位分组数据<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/30 17:48
     * @param  dto 传递参数
     * @throws
     * @return
     */
    List<UnitGroupModel> exportUnitGroup(UnitGroupSearchDTO dto);

    /**
     * 获取单位分组下单位信息(不做权限过滤)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 17:52
     * @param  dto 分组id
     * @throws
     * @return 查询结果
     */
    RestfulResults<List<UserCountVO>> getGroupUnitInfoListByGroupIdNotRight(GroupUnitListSearchDTO dto);

    /**
     * 查询单位分组(不做权限过滤)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 10:52
     * @param  dto 传递参数
     * @throws
     * @return 查询结果
     */
    RestfulResults<List<UnitGroupVO>> queryUnitGroupNotRight(UnitGroupSearchDTO dto) throws ServiceException;

    /**
     * 批量获取单位分组下单位信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 17:52
     * @param  dto 分组id
     * @throws
     * @return 查询结果
     */
    RestfulResults<List<UserCountVO>> getGroupUnitInfoListByGroupIds(GroupUnitListSearchDTO dto) throws Exception;

    /**
     * 获取分组下用户数量<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2021/1/11 15:41
     * @param  dto  传入参数
     * @throws
     * @return 查询结果
     */
    RestfulResults<List<GroupUserCountVO>> getGroupUserCount(GroupUnitListSearchDTO dto) throws Exception;
}
