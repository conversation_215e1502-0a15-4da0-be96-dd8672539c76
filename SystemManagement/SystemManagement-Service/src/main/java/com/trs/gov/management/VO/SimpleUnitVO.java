package com.trs.gov.management.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.Date;

@Data
public class SimpleUnitVO extends BaseVO {

    /**
     * 单位id
     */
    private Long id;

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    private String unitType;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 单位负责人
     */
    private String unitMaster;

    /**
     * 单位负责人真实名称
     */
    private String unitMasterTrueName;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 单位地址
     */
    private String unitAddr;

    /**
     * 单位状态（1,启用;-1,停用)
     */
    private Integer status;

    /**
     * 创建人
     */
    private String crUser;

    /**
     * 创建人真实姓名
     */
    private String trueName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 是否为当前单位负责人
     */
    private Boolean isUnitMaster;

}
