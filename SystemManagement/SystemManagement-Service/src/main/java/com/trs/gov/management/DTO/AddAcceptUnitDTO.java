package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * @auther
 * @description
 * @date
 **/
@Data
public class AddAcceptUnitDTO extends BaseDTO {

    /**
     * 工单类型id
     */
    private Long id;

    /**
     * 受理单位id
     */
    private Long acceptUnitId;


    @Override
    public boolean isValid() throws ServiceException {
        if (id==null){
            throw new ParamInvalidException("工单类型id不能为空");
        }
        if (acceptUnitId==null){
            throw new ParamInvalidException("受理单位不能id不能为空");
        }
        return true;
    }
}
