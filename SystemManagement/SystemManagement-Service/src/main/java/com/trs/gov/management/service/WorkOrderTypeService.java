package com.trs.gov.management.service;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DTO.AddAcceptUnitDTO;
import com.trs.gov.management.DTO.OperWorkOrderTypeDTO;
import com.trs.gov.management.DTO.WorkOrderTypeDTO;
import com.trs.gov.management.DTO.WorkOrderTypeSearchDTO;
import com.trs.gov.management.VO.ParentWorkOrderTypeVO;
import com.trs.gov.management.VO.WorkOrderTypeForNoticeVO;
import com.trs.gov.management.VO.WorkOrderTypeLevelVO;
import com.trs.gov.management.VO.WorkOrderTypeVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 工单类型配置服务接口
 */
public interface WorkOrderTypeService {

    /**
     * 获取工单类型<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:21
     * @throws  ServiceException 服务异常
     * @return  查询结果
     */
    RestfulResults<List<WorkOrderTypeVO>> getWorkOrderTypes(WorkOrderTypeSearchDTO dto) ;

    /**
     * 保存工单类型(新建/编辑)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:21
     * @param   dto 请求参数
     * @throws  ServiceException 服务异常
     * @return  保存结果
     */
    RestfulResults<Report> saveOrUpdateWorkOrderType(WorkOrderTypeDTO dto) ;

    /**
     * 删除、启用停用工单类型<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:22
     * @param   dto 请求参数
     * @throws  ServiceException 服务异常
     * @return  保存结果
     */
    RestfulResults<Report> operWorkOrderType(OperWorkOrderTypeDTO dto);


    /**
     * 根据当前类型id获取其它模块所需类型信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 14:25
     * @param  id  当前类型id
     * @throws
     * @return
     */
    RestfulResults<WorkOrderTypeForNoticeVO> getTypeInfoForOtherPart(String id);

    /**
     * 根据类型等级获取工单类型<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/25 12:50
     * @param  dto  请求参数
     * @throws
     * @return
     */
    RestfulResults<List<WorkOrderTypeLevelVO>> queryWorkOrderTypeByLevel(WorkOrderTypeSearchDTO dto);

    /**
     * @Description  根据顶级类型key获取该类型以及该类型所有下的工单类型id
     * @Param [workOrderTypeSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/29 17:41
     **/
    RestfulResults<List<Long>> listAllRelateTypeByRootType(WorkOrderTypeSearchDTO workOrderTypeSearchDTO);

    /**
     * @Description  根据工单类型id获取该类型以及该类型下所有的工单类型id
     * @Param [workOrderTypeSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/10 11:36
     **/
    RestfulResults<List<Long>> listAllRelateTypeById(WorkOrderTypeSearchDTO workOrderTypeSearchDTO);

    /**
     * 获取父级工单类型<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:21
     * @return  查询结果
     */
    RestfulResults<List<ParentWorkOrderTypeVO>> getParentWorkOrderTypes(WorkOrderTypeSearchDTO dto);

    /**
     * 添加投诉的受理单位<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/27 9:51
     * @param  dto   请求参数
     * @throws
     * @return
     */
    RestfulResults<Report> addAcceptUnit(AddAcceptUnitDTO dto);

}
