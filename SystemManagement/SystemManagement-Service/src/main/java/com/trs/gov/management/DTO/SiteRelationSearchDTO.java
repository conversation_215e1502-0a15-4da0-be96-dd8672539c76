package com.trs.gov.management.DTO;


import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

import java.util.List;

@Data
public class SiteRelationSearchDTO extends BasePageDTO {

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 渠道类型
     */
    private String mediaType;

    /**
     * 站点id
     */
    private String siteId;

    /**
     * 备案开始时间
     */
    private String filingStartTime;

    /**
     * 备案结束时间
     */
    private String filingEndTime;

    /**
     * 建设开始时间
     */
    private String constructionStartTime;

    /**
     * 建设结束时间
     */
    private String constructionEndTime;

    /**
     * 主办单位id
     */
    private String hostUnitId;

    /**
     * 主办单位名称
     */
    private String hostUnitName;

    /**
     * 主管单位id
     */
    private String masterUnitId;

    /**
     * 主管单位名称
     */
    private String masterUnitName;
    /**
     * 运维单位id
     */
    private String operationUnitId;

    /**
     * 运维单位名称
     */
    private String operationUnitName;

    /**
     * 运维周期 开始
     */
    private String operationStartTime;

    /**
     * 运维周期 结束
     */
    private String operationEndTime;

    /**
     * 是否获取所有
     */
    private String isAll;

    /**
     * 当前用户当前单位作为运维单位di
     */
    private String unitIdAsOperUnitId;

    /**
     * 当前用户当前单位作为主办di
     */
    private String unitIdAsHostUnitId;

    /**
     * @Description  获取热点站点数，默认为三个
     * @Param
     * @return
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/28 17:32
     **/
    private Integer hotSiteCount = 3;

    private String operationTime;

    private List<String> distinctField;

    /**
     * 内部服务调用标识true为内部服务调用
     */
    private Boolean rpcTag = false;



    @Override
    public boolean isValid() throws ServiceException {
        //默认分页
        if (getPageNum()==null){
            setPageNum(0);
        }
        if (getPageSize()==null){
            setPageSize(10);
        }
        if ("0".equals(mediaType)){
            this.mediaType=null;
        }
        return true;
    }
}
