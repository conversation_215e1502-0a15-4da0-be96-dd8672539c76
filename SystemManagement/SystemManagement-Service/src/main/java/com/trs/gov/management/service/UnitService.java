package com.trs.gov.management.service;

import com.alibaba.fastjson.JSONArray;
import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.management.DO.excelModel.UnitModel;
import com.trs.gov.management.DTO.*;
import com.trs.gov.management.VO.*;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 单位配置服务接口
 */
public interface UnitService {

    /**
     * 查询单位<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:15
     * @param  dto 请求参数
     * @throws ServiceException 服务异常
     * @return 查询结果
     */
    RestfulResults<List<UnitVO>> queryUnitList(UnitSearchDTO dto);

    /**
     * 查询单位基础信息，不包含单位用户和logo<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:15
     * @param  dto 请求参数
     * @throws ServiceException 服务异常
     * @return 查询结果
     */
    RestfulResults<List<UnitVO>> queryBaseUnitList(UnitSearchDTO dto);

    /**
     * 查询下属单位的基础信息，不包含单位用户和logo<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-05 18:11
     */
    RestfulResults<List<UnitVO>> queryBaseChildrenUnitList(UnitSearchDTO dto);

    /**
     * 保存单位(新建/编辑)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:15
     * @param   dto 请求参数
     * @throws  ServiceException 服务异常
     * @return  保存结果
     */
    RestfulResults<Report> saveOrUpdateUnit(UnitDTO dto);

    /**
     * 操作单位状态(启用/停用)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 21:15
     * @param   dto  请求参数
     * @throws  ServiceException 服务异常
     * @return  保存结果
     */
    RestfulResults<Report> operUnitState(UnitStateDTO dto) ;

    /**
     * 获取组织单位下的用户<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 17:32
     * @param  dto  请求参数
     * @throws
     * @return
     */
    RestfulResults<JSONArray> queryUnitUserList(UnitUserDTO dto);

    /**
     * 查询用户列表<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 20:27
     * @param  dto  请求参数
     * @throws
     * @return
     */
    RestfulResults<List<UserVO>> getAllUser(UserSearchDTO dto);

    /**
     * 根据单位类型查询单位(只查等值的)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/27 18:52
     * @param
     * @throws
     * @return
     */
    RestfulResults<List<UnitVO>> queryUnitByType(UnitSearchDTO dto);

    /**
     * 根据单位类型查询单位(只查等值的)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/27 18:52
     * @param
     * @throws
     * @return
     */
    RestfulResults<List<SimpleUnitVO>> queryUnitByUser(UserSearchDTO dto);


  /**
   * 获取单位绑定站点关系信息<BR>
   * <AUTHOR> lan.xin E-mail: <EMAIL>
   * 创建时间：2020/10/16 16:32
   * @param
   * @throws
   * @return
   */
    RestfulResults<List<UnitAndSiteRelationsVO>> getUnitAndSiteRelations(UnitSearchDTO dto);

    /**
     * 获取单位详情<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/23 16:49
     * @param  dto  传递参数
     * @throws
     * @return
     */
    RestfulResults<UnitVO> getUnitDetailById(UnitSearchDTO dto) throws ServiceException;

    /**
     * 导出单位列表<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/23 17:56
     * @param
     * @throws
     * @return
     */
    List<UnitModel> exportUnit(UnitSearchDTO dto) throws ServiceException;


    UnitVO exportUnit(UnitExportDTO dto) throws ServiceException;

    /**
     * 获取检索所需单位信息接口<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/2 15:54
     * @param  dto 请求参数
     * @throws
     * @return
     */
    RestfulResults<List<UnitSearchVO>> getAllUnitForSearch(UnitSearchDTO dto);

    /**
     * 根据单位id获取用户数量<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/16 21:07
     * @param
     * @throws
     * @return
     */
    RestfulResults<List<UserCountVO>> getUserCountByUnitIds(String ids);

    /**
     * 根据单位类型查询单位(只查等值的)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/27 18:52
     * @param
     * @throws
     * @return
     */
    RestfulResults<List<BaseUnitVO>> queryHasRightUnitByType(UnitSearchDTO dto);

    /**
     * 获取检索所需单位信息接口(通知组件使用)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/2 15:54
     * @param  dto 请求参数
     * @throws
     * @return
     */
    RestfulResults<List<UnitSearchVO>> getAllUnitForTongZhiSearch(UnitSearchDTO dto);

    /**
     * 1.通过类型和名称获取单位
     * 2. 存在->更新
     * 3. 不存在->新增
     * @param unitName
     * @param type
     * @return
     */
    UnitDO getUnitByTypeAndUnitName(String unitName, String type,SponsorInfoVO sponsorInfoVO, InitInfoVO initInfoVO, OperationUnitInfoVO operationUnitInfoVO) throws Exception;
}
