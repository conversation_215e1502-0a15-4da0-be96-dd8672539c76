package com.trs.gov.management.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-10 13:21
 * @version 1.0
 * @since 1.0
 * 站点关系实体对象
 */
@Data
@TableName("site_relation")
public class SiteRelationDO extends BaseDO {

    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id", required = true)
    @Column(name = "site_id")
    private Long siteId;

    /**
     * 站点名称
     */
    @ApiModelProperty(value = "站点名称", required = true)
    @Column(name = "site_name")
    private String siteName;

    /**
     * 渠道类型
     */
    @ApiModelProperty(value = "渠道类型", required = true)
    @Column(name = "media_type")
    private Integer mediaType;

    /**
     * 渠道类型名称
     */
    @ApiModelProperty(value = "渠道类型", required = true)
    @Column(name = "media_name")
    private String mediaName;

    /**
     * 唯一标识
     */
    @ApiModelProperty(value = "唯一标识", required = true)
    @Column(name = "unique_id")
    private String uniqueId;

    /**
     * 备案时间
     */
    @ApiModelProperty(value = "备案时间", required = true)
    @Column(name = "filing_time")
    private Date filingTime;

    /**
     * 建设时间
     */
    @ApiModelProperty(value = "建设时间", required = true)
    @Column(name = "construction_time")
    private Date constructionTime;

    /**
     * 主办单位id
     */
    @ApiModelProperty(value = "主办单位id", required = true)
    @Column(name = "host_unit_id")
    private Long hostUnitId;

    /**
     * 主办单位名称
     */
    @ApiModelProperty(value = "主办单位名称", required = true)
    @Column(name = "host_unit")
    private String hostUnit;

    /**
     * 主管单位id
     */
    @ApiModelProperty(value = "主管单位id", required = true)
    @Column(name = "master_unit_id")
    private Long masterUnitId;

    /**
     * 主管单位名称
     */
    @ApiModelProperty(value = "主管单位名称", required = true)
    @Column(name = "master_unit")
    private String masterUnit;

    /**
     * 运维单位id
     */
    @ApiModelProperty(value = "运维单位id", required = true)
    @Column(name = "operation_unit_id")
    private Long operationUnitId;

    /**
     * 运维单位名称
     */
    @ApiModelProperty(value = "运维单位名称", required = true)
    @Column(name = "operation_unit")
    private String operationUnit;

    /**
     * 运维单位负责人
     */
    @ApiModelProperty(value = "运维单位负责人", required = true)
    @Column(name = "operation_host")
    private String operationHost;

    /**
     * 运维单位负责人电话
     */
    @ApiModelProperty(value = "运维单位负责人电话", required = true)
    @Column(name = "phone")
    private String phone;

    /**
     * 运维周期 开始
     */
    @ApiModelProperty(value = "运维周期 开始", required = true)
    @Column(name = "operation_start_time")
    private Date operationStartTime;

    /**
     * 运维周期 结束
     */
    @ApiModelProperty(value = "运维周期 结束", required = true)
    @Column(name = "operation_end_time")
    private Date operationEndTime;

    /**
     * 监控云站点
     */
    @ApiModelProperty(value = "监控云站点", required = true)
    @Column(name = "monitor_site")
    private String monitorSite;

    /**
     * 监控云站点ID
     */
    @ApiModelProperty(value = "监控云站点ID", required = true)
    @Column(name = "monitor_site_id")
    private Long monitorSiteId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = true)
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除(0:未删除，1:删除)
     */
    @ApiModelProperty(value = "是否删除(0:未删除，1:删除)", required = false)
    @Column(name = "is_del")
    private Integer isDel;

    /**
     * 创建人用于显示的名称
     */
    @ApiModelProperty(value = "创建人用于显示的名称", required = true)
    @Column(name = "true_name")
    private String trueName;

}
