package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class UserUnitMappingSearchDTO extends BasePageDTO {

    /**
     * 单位类型串（多个使用英文逗号分隔）
     */
    private String unitTypes;

    private String userName;

    /**
     * 单位ID串（多个使用英文逗号分隔）
     */
    private String unitIds;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 是否是单位负责人
     * */
    private Boolean isMaster;

    /**
     * 单位停用启用状态（1启用，-1停用）
     */
    private String unitStatus = "1";

    /**
     * 检索关键词
     */
    private String keywords;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
