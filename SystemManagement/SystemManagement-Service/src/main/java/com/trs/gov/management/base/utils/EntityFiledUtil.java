package com.trs.gov.management.base.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * @auther
 * @description
 * @date
 **/
public class EntityFiledUtil {

    /**
     * 获取属性名数组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/17 12:28
     * @param  object 实体对象
     * @throws
     * @return 实体对象属性名
     */
    public static String[] getFiledName(Object object){
        Field[] fields=object.getClass().getDeclaredFields();
        String[] fieldNames=new String[fields.length];
        for(int i=0;i<fields.length;i++){
            fieldNames[i]=fields[i].getName();
        }
        return fieldNames;
    }

  /**
   * 根据属性名获取属性值<BR>
   * <AUTHOR> lan.xin E-mail: <EMAIL>
   * 创建时间：2020/9/17 12:29
   * @param  fieldName  字段名
   * @param  object  实体对象
   * @throws
   * @return  实体对象对象字段值
   */
    public static Object getFieldValueByName(String fieldName, Object object) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = object.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(object, new Object[] {});
            return value;
        } catch (Exception e) {
            return null;
        }
    }

  /**
   * 获取实体属性名-属性值map集合<BR>
   * <AUTHOR> lan.xin E-mail: <EMAIL>
   * 创建时间：2020/9/17 12:37
   * @param    object 实体对象
   * @throws
   * @return   结果map
   */
    public static Map<String,Object> getFileMap(Object object){
        String[] fieldNames = getFiledName(object);
        Map<String,Object> companyWantMap =new HashMap<>();
        //遍历所有属性
        for(int j=0 ; j<fieldNames.length ; j++){
            //获取属性的名字
            String name = fieldNames[j];
            Object value =getFieldValueByName(name,object);
            if (value == null){
                value = "";
            }
            companyWantMap.put(name,value);
        }
        return companyWantMap;
    }
}
