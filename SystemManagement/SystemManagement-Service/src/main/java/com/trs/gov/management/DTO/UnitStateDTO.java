package com.trs.gov.management.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class UnitStateDTO extends BaseDTO {

    /**
     * 单位id
     */
    private String id;

    /**
     * 单位状态（1,启用;-1,停用)
     */
    private Integer status;

    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isEmpty(id)||"null".equals("id")){
            throw new ParamInvalidException("单位id不能为空！");
        }
        if (status==null){
            throw new ParamInvalidException("修改状态不能为空！");
        }
        return true;
    }
}
