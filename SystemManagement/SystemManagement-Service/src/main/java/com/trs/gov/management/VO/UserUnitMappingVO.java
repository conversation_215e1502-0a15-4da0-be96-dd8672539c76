package com.trs.gov.management.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户单位映射的VO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2021-01-05 15:44
 * @version 1.0
 * @since 1.0
 */
@Data
public class UserUnitMappingVO extends BaseVO {

    private Long id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 真实姓名
     */
    private String trueName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 单位ID
     */
    private String unitId;

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    private String unitType;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 是否是单位负责人
     */
    private Boolean isMaster;
}
