package com.trs.gov.management.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class WorkOrderTypeDTO extends BaseDTO {

    /**
     * 工单id
     */
    private String id;
    /**
     * 工单类型名称
     */
    private String typeName;

    /**
     * 工单类型描述
     */
    private String typeDesc;

    /**
     * 父级id
     */
    private Long parentId;

    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isEmpty(id)||"null".equals(id)) {
            throw new ParamInvalidException("类型id不能为空！");
        }
        if(StringUtils.isEmpty(typeName)||"null".equals(typeName)){
            throw new ParamInvalidException("类型名称不能为空");
        }
        if (StringUtils.isEmpty(typeDesc)||"null".equals(typeDesc)){
            throw new ParamInvalidException("类型描述不能为空");
        }
        if (parentId==0L){
            throw new ParamInvalidException("顶级工单类型不允许新建或编辑");
        }
        if (parentId==null){
            throw new ParamInvalidException("父级id不能为空！");
        }
        if (parentId.longValue()==3){
            throw new ParamInvalidException("通知工单类型不允许新建");
        }
        if (typeName.length()>64){
            throw new ParamInvalidException("类型名称最长为64位");
        }
        if (typeDesc.length()>1024){
            throw new ParamInvalidException("类型描述最长为1024位");
        }
        return true;
    }
}
