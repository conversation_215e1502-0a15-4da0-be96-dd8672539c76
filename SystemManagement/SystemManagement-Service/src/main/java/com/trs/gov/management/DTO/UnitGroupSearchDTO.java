package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 10:46
 * @version 1.0
 * @since  1.0
 */
@Data
public class UnitGroupSearchDTO extends BasePageDTO {

    /**
     * 分组id
     */
    private String id;

    /**
     * 分组名称
     */
    private String keyWord;

    /**
     * 1,启用；-1,停用
     */
    private String status;

    /**
     * 创建-开始时间
     */
    private String createTimeStart;

    /**
     * 创建-结束时间
     */
    private String createTimeEnd;

    /**
     * 更新-开始时间
     */
    private String updateTimeStart;

    /**
     * 更新-结束时间
     */
    private String updateTimeEnd;

    /**
     * 是否获取全部
     */
    private String isAll;

    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
