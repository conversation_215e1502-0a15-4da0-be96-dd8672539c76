package com.trs.gov.management.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 11:15
 * @version 1.0
 * @since  1.0
 */
@Data
@TableName("unit_group")
public class UnitGroupDO extends BaseDO {

    /**
     * 单位分组名称
     */
    @ApiModelProperty(value = "单位分组名称", required = true)
    @Column(name = "group_name")
    private String groupName;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "人", required = true)
    @Column(name = "update_user")
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", required = true)
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除(0:未删除，1:删除)
     */
    @ApiModelProperty(value = "是否删除(0:未删除，1:删除)", required = false)
    @Column(name = "is_del")
    private Integer isDel;

}
