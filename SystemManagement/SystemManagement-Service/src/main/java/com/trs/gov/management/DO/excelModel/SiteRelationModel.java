package com.trs.gov.management.DO.excelModel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.util.Date;

/**
 * @auther
 * @description
 * @date
 **/
@Data
public class SiteRelationModel extends BaseRowModel {

    /**
     * 站点id
     */
    @ExcelProperty(value = "站点ID",index = 0)
    private Long siteId;

    /**
     * 站点名称
     */
    @ExcelProperty(value = "站点名称",index = 1)
    private String siteName;

    /**
     * 站点类别名称
     */
    @ExcelProperty(value = "站点类别",index = 2)
    private String mediaName;

    /**
     * 主办单位名称
     */
    @ExcelProperty(value = "主办单位",index = 3)
    private String hostUnit;

    /**
     * 运维单位名称
     */
    @ExcelProperty(value = "运维单位",index = 4)
    private String operationUnit;

    /**
     * 备案时间
     */
    @ExcelProperty(value = "备案时间",index = 5)
    private String filingTime;

    /**
     * 建设时间
     */
    @ExcelProperty(value = "建设时间",index = 6)
    private String constructionTime;

    /**
     * 运维周期 开始
     */
    @ExcelProperty(value = "运维周期",index = 7)
    private String operationTime;

    /**
     * 运维单位负责人
     */
    @ExcelProperty(value = "站点运维负责人",index = 8)
    private String operationHost;

    /**
     * 主管单位名称
     */
    @ExcelProperty(value = "主管单位",index = 9)
    private String masterUnit;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人",index = 10)
    private String crUser;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间",index = 11)
    private Date crTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间",index = 12)
    private Date updateTime;

}
