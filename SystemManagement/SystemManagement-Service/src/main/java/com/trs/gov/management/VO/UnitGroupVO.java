package com.trs.gov.management.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 10:37
 * @version 1.0
 * @since  1.0
 */
@Data
public class UnitGroupVO extends BaseVO {

    /**
     * 单位分组id
     */
    private Long id;

    /**
     * 单位分组名称
     */
    private String groupName;

    /**
     * 1,启用;-1停用
     */
    private Integer status;

    /**
     * 创建人
     */
    private String crUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 单位数量
     */
    private Long unitCounts;

}
