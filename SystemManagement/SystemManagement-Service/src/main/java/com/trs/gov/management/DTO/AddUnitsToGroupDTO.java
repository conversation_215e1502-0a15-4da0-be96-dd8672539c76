package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 10:27
 * @version 1.0
 * @since  1.0
 */
@Data
public class AddUnitsToGroupDTO extends BaseDTO {

    /**
     * 单位id串
     */
    private String unitIds;
    /**
     * 单位分组id
     */
    private Long groupId;

    @Override
    public boolean isValid() throws ServiceException {
        if (groupId==null){
            throw new ParamInvalidException("单位分组id不能为空");
        }
        return true;
    }
}
