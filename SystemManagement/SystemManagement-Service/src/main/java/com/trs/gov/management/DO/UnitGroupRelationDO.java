package com.trs.gov.management.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 11:31
 * @version 1.0
 * @since  1.0
 * 单位和单位分组关系表
 */
@Data
@TableName("unit_group_relation")
public class UnitGroupRelationDO {

    @ApiModelProperty(value = "单位和分组关系ID", required = true)
    @TableId(type = IdType.AUTO)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "单位分组id",required = true)
    @Column(name = "group_id")
    private Long groupId;

    @ApiModelProperty(value = "单位id",required = true)
    @Column(name = "unit_id")
    private Long unitId;

}
