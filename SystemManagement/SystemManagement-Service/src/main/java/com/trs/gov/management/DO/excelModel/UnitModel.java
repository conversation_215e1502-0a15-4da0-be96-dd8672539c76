package com.trs.gov.management.DO.excelModel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @auther
 * @description
 * @date
 **/
@Data
public class UnitModel extends BaseRowModel {

    /**
     * 序号
     */
    @ExcelProperty(value = "序号",index = 0)
    private Integer id;

    /**
     * 单位名称
     */
    @ExcelProperty(value = "单位名称",index = 1)
    private String unitName;

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    @ExcelProperty(value = "单位类别",index = 2)
    private String unitTypeName;

    /**
     * 单位状态（1,启用;-1,停用)
     */
    @ExcelProperty(value = "启用状态",index = 3)
    private String status;

    /**
     * 单位地址
     */
    @ExcelProperty(value = "单位地址",index = 4)
    private String unitAddr;

    /**
     * 单位人员
     */
    @ExcelProperty(value = "单位人员",index = 5)
    private String unitUsers;

    /**
     * 单位负责人
     */
    @ExcelProperty(value = "单位负责人",index = 6)
    private String unitMaster;

    /**
     * 单位编码
     */
    @ExcelProperty(value = "单位编码",index = 7)
    private String unitCode;

    /**
     * 联系邮箱
     */
    @ExcelProperty(value = "联系邮箱",index = 8)
    private String email;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人",index = 9)
    private String crUser;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间",index = 10)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间",index = 11)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    public void setStatus(Integer status){
        if (status==1){
            this.status="启用中";
        }
        if (status==-1){
            this.status="停用中";
        }
    }

}
