package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

@Data
public class UnitDTO extends BasePageDTO {

    /**
     * 单位id(新建给0，编辑给对应id)
     */
    private String id;

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    private String unitType;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 单位负责人
     */
    private String unitMaster;

    /**
     * 运维单位负责人电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 单位地址
     */
    private String unitAddr;

    /**
     * 单位简介
     */
    private String unitDesc;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 业务领域
     */
    private String businessArea;

    /**
     * 商标
     */
    private String logo;
    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {

        if (StringUtils.isEmpty(id)||"null".equals(id)) {
            throw new ParamInvalidException("单位id不能为空！");
        }
        if (StringUtils.isEmpty(unitType)||"null".equals(unitType)){
            throw new ParamInvalidException("单位类型不能为空！");
        }
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        if (!StringUtils.isEmpty(unitCode)&&!pattern.matcher(unitCode).matches()){
            throw new ParamInvalidException("单位编码只能为数字类型");
        }
        if (!StringUtils.isEmpty(unitCode)&&unitCode.length()>10){
            throw new ParamInvalidException("单位编码最长为10位");
        }
        if (StringUtils.isEmpty(unitCode)){
            this.unitCode = null;
        }
        return true;
    }
    public void setUnitType(String unitType){
        List<String> list = Arrays.asList(unitType.split(","));
        Collections.sort(list);
        this.unitType = String.valueOf(list).replaceAll("(?:\\[|null|\\]| +)", "");
    }

}
