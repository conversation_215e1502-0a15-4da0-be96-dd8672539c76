package com.trs.gov.management.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class SiteRelationDTO extends BaseDTO {

    /**
     * 站点关系id(新建给0，编辑给对应id)
     */
    private String id;
    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 渠道类型
     */
    private Integer mediaType;

    /**
     * 渠道类型名称
     */
    private String mediaName;

    /**
     * 唯一标识
     */
    private String uniqueId;

    /**
     * 备案时间
     */
    private String filingTime;

    /**
     * 建设时间
     */
    private String constructionTime;

    /**
     * 主办单位id
     */
    private Long hostUnitId;

    /**
     * 主办单位名称
     */
    private String hostUnit;

    /**
     * 主管单位id
     */
    private Long masterUnitId;

    /**
     * 主管单位名称
     */
    private String masterUnit;
    /**
     * 运维单位id
     */
    private Long operationUnitId;

    /**
     * 运维单位名称
     */
    private String operationUnit;

    /**
     * 运维单位负责人
     */
    private String operationHost;

    /**
     * 运维单位负责人电话
     */
    private String phone;

    /**
     * 运维周期 开始
     */
    private String operationStartTime;

    /**
     * 运维周期 结束
     */
    private String operationEndTime;

    /**
     * 监控云站点名称
     */
    private String monitorSite;

    /**
     * 监控云站点ID
     */
    private Long monitorSiteId;

    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isEmpty(id)||"null".equals(id)) {
            throw new ParamInvalidException("站点关系id不能为空！");
        }
        if (StringUtils.isEmpty(siteName)) {
            throw new ParamInvalidException("站点名称不能为空！");
        }
        if (StringUtils.isEmpty(uniqueId)) {
            throw new ParamInvalidException("唯一标识不能为空！");
        }
        if (uniqueId.length()>50){
            throw new ParamInvalidException("唯一标识最长为50！");
        }
        if (StringUtils.isEmpty(filingTime)) {
            throw new ParamInvalidException("备案时间不能为空！");
        }
        if (StringUtils.isEmpty(constructionTime)) {
            throw new ParamInvalidException("建设时间不能为空！");
        }
        if (StringUtils.isEmpty(hostUnit)) {
            throw new ParamInvalidException("主办单位不能为空！");
        }
        if (StringUtils.isEmpty(masterUnit)) {
            throw new ParamInvalidException("主管单位不能为空！");
        }
        if (StringUtils.isEmpty(operationUnit)) {
            throw new ParamInvalidException("运维单位不能为空！");
        }
        if (StringUtils.isEmpty(operationHost)) {
            throw new ParamInvalidException("运维单位负责人不能为空！");
        }
        return true;
    }
}
