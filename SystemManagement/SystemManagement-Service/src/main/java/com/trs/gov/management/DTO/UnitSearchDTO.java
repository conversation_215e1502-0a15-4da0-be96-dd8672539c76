package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import lombok.Data;

@Data
public class UnitSearchDTO extends BasePageDTO {

    /**
     * 单位id
     */
    private String id;

    /**
     *单位名称
     */
    private String unitName;

    /**
     * 单位类型
     */
    private String unitType;

    /**
     * 单位状态
     */
    private String status;

    /**
     * 创建开始时间
     */
    private String crTimeStart;

    /**
     * 创建结束时间
     */
    private String crTimeEnd;

    /**
     * 更新开始时间
     */
    private String updateTimeStart;

    /**
     * 更新结束时间
     */
    private String updateTimeEnd;

    /**
     * 是否关联
     */
    private String isConnect;

    /**
     * 是否获取全部
     */
    private String isAll;

    /**
     * 当前用户当前单位id
     */
    private String nowUserUnitId;

    /**
     * 内部服务调用标识true为内部服务调用
     */
    private Boolean rpcTag = false;

    @Override
    public boolean isValid() {
        //默认分页
        if (getPageNum()==null){
            setPageNum(0);
        }
        if (getPageSize()==null){
            setPageSize(10);
        }
        return true;
    }
}
