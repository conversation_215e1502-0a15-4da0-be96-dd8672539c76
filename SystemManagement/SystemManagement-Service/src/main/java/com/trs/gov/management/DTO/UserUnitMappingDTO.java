package com.trs.gov.management.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.management.VO.UnitVO;
import com.trs.user.VO.UserVO;
import lombok.Data;

@Data
public class UserUnitMappingDTO extends BaseDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 真实姓名
     */
    private String trueName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    private String unitType;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 单位停用启用的状态
     */
    private Integer status;

    /**
     * 是否是单位负责人
     */
    private Boolean isMaster;

    public static UserUnitMappingDTO of(UnitVO unit, UserVO user) throws ServiceException {
        UserUnitMappingDTO dto = new UserUnitMappingDTO();
        dto.setUserName(user.getUserName());
        dto.setTrueName(CMyString.showEmpty(user.getTrueName(), user.getUserName()));
        dto.setEmail(CMyString.showEmpty(user.getEmail()));
        dto.setPhone(CMyString.showEmpty(user.getPhone()));
        dto.setUnitId(Long.toString(unit.getId()));
        dto.setStatus(unit.getStatus());
        dto.setUnitName(unit.getUnitName());
        dto.setIsMaster(user.getUserName().equals(unit.getUnitMaster()));
        dto.setUnitType(CMyString.showEmpty(unit.getUnitType()));
        BaseUtils.checkDTO(dto);
        return dto;
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
