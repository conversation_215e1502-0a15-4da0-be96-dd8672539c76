package com.trs.gov.management.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DO.UnitGroupRelationDO;
import com.trs.gov.management.DTO.GroupUnitListSearchDTO;
import com.trs.gov.management.VO.UnitGroupRelationVO;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 15:40
 * @version 1.0
 * @since  1.0
 */
public interface UnitGroupRelationService extends IService<UnitGroupRelationDO> {

    /**
     * 根据单位id查询所在单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2021/1/4 9:36
     * @param
     * @throws
     * @return
     */
    List<Long> getGroupByUnitId(String unitIds) throws ServiceException;

    /**
     * 根据分组id获取分组与分组单位关系列表<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2021/1/12 15:30
     * @param  searchDTO
     * @throws
     * @return
     */
    List<UnitGroupRelationVO> getUnitGroupRelationList(GroupUnitListSearchDTO searchDTO) throws ServiceException ;
}
