package com.trs.gov.management.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户单位映射关系
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2021-01-05 14:54
 * @version 1.0
 * @since  1.0
 */
@Data
@TableName("user_unit_mapping")
public class UserUnitMappingDO extends BaseDO {

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 真实姓名
     */
    @TableField("true_name")
    private String trueName;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 单位ID
     * */
    @TableField("unit_id")
    private String unitId;

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    @TableField("unit_type")
    private String unitType;

    /**
     * 单位名称
     */
    @TableField("unit_name")
    private String unitName;

    /**
     * 是否是单位负责人
     * */
    @TableField("is_master")
    private Boolean isMaster;

}
