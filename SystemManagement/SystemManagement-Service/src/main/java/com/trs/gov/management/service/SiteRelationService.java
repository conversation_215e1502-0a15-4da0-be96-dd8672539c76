package com.trs.gov.management.service;

import com.trs.common.base.Report;
import com.trs.common.base.Reports;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DO.excelModel.SiteRelationModel;
import com.trs.gov.management.DTO.SiteNameDTO;
import com.trs.gov.management.DTO.SiteRelationDTO;
import com.trs.gov.management.DTO.SiteRelationExportDTO;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.VO.GetAllSitesByTypeVO;
import com.trs.gov.management.VO.HotSiteVO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;
import java.util.Map;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 站点配置服务接口
 */
public interface SiteRelationService {

    /**
     * 新建/编辑站点关系<BR>
     *
     * @param dto 请求参数
     * @return 保存结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:55
     */
     RestfulResults<Report> saveOrUpdateSiteRelation(SiteRelationDTO dto);

    /**
     * 查询站点关系列表<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:55
     */
    RestfulResults<List<SiteRelationVO>> querySiteRelationList(SiteRelationSearchDTO dto);

    /**
     * 删除站点关系<BR>
     *
     * @param id 站点关系id
     * @return 保存结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:57
     */
    RestfulResults<Reports> deleteSiteRelation(String id);

    /**
     * 查询站点或监控云名称是否存在<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 11:49
     */
    RestfulResults<String> nameIsExist(SiteNameDTO dto);

    /**
     * 获取站点渠道类型关系<BR>
     *
     * @param
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 13:37
     */
    RestfulResults<List<Map>> getMediaTypeInfo() throws ServiceException;

    /**
     * 根据站点分类获取所有站点信息<BR>
     *
     * @param
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/27 21:27
     */
    RestfulResults<List<GetAllSitesByTypeVO>> getWorkOrderSysSites();

    /**
     * 导出站点关系表<BR>
     * @param
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/23 13:49
     */
    List<SiteRelationModel> exportSiteRelation(SiteRelationSearchDTO dto) throws ServiceException;


    SiteRelationVO exportSiteRelation(SiteRelationExportDTO dto) throws Exception;

    /**
     * @Description  获取热点
     * @Param []
     * @return java.util.List<com.trs.gov.management.VO.HotSiteVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/28 17:26
     **/
    RestfulResults<List<HotSiteVO>> listHotSiteRelation(SiteRelationSearchDTO dto) throws ServiceException;


    /**
     * 获取政府与非政府占比<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2021/10/9 9:38
     * @param
     * @throws
     * @return 查询结果
     */
    Map<String,Double> getGovNatureRate(SiteRelationSearchDTO dto) throws ServiceException;

    /**
     * 从海云获取站点信息更新同步到工单系统中
     * @param siteRelationDTO
     */
    void saveOrUpdateSiteRelationByHy(SiteRelationDTO siteRelationDTO) throws Exception;
}
