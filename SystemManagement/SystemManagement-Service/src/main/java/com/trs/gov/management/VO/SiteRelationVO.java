package com.trs.gov.management.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.Date;

@Data
public class SiteRelationVO extends BaseVO {

    /**
     * 站点关系id
     */
    private Long id;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点类别
     */
    private Integer mediaType;

    /**
     * 站点类别名称
     */
    private String mediaName;

    /**
     * 备案时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date filingTime;

    /**
     * 建设时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date constructionTime;

    /**
     * 主办单位id
     */
    private Long hostUnitId;

    /**
     * 主办单位名称
     */
    private String hostUnit;

    /**
     * 主管单位id
     */
    private Long masterUnitId;

    /**
     * 主管单位名称
     */
    private String masterUnit;
    /**
     * 运维单位id
     */
    private Long operationUnitId;

    /**
     * 运维单位名称
     */
    private String operationUnit;

    /**
     * 运维单位负责人
     */
    private String operationHost;

    /**
     * 唯一标识
     */
    private String uniqueId;

    /**
     * 负责人电话
     */
    private String phone;

    /**
     * 运维周期 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date operationStartTime;

    /**
     * 运维周期 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date operationEndTime;

    /**
     * 创建人
     */
    private String crUser;

    /**
     * 创建人真实姓名
     */
    private String trueName;

    /**
     * 监控云站点
     */
    private String monitorSite;

    /**
     * 监控云站点ID
     */
    private Long monitorSiteId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
