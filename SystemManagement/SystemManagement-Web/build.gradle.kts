/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    `java-library`
    id("org.springframework.boot")
}

dependencies {
    api(project(":SystemManagement-Service"))
    api(project(":User-Service"))
    api(project(":WorkOrder-Service"))
    implementation("com.trs.upms:upms-client:[1.2,)")
    api(project(":ExternalSystem-Service"))
    implementation("com.trs:timing_scheduler_client:0.1.3")
}

description = "SystemManagement-Web"
