<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.management.mapper.SiteRelationMapper">

    <select id="selectHotSite" resultType="com.trs.gov.management.VO.HotSiteVO">
        SELECT
        CONCAT(sr.site_id,sr.media_type) as distinctField,
        sr.id as id,
        sr.site_id as siteId,
        sr.site_name as siteName,
        sr.media_type as mediaType,
        sr.media_name as mediaName,
        MAX(wo.cr_time) as maxTime
        FROM
        work_order wo
        LEFT JOIN site_relation sr ON wo.site_id = sr.site_id
        WHERE
        wo.site_id IS NOT NULL
        AND is_del = 0
        AND ( sr.host_unit_id = #{dto.unitId} OR sr.master_unit_id = #{dto.unitId} OR sr.operation_unit_id = #{dto.unitId} )
        AND sr.operation_start_time &lt;= #{dto.operationTime}
        AND sr.operation_end_time &gt;= #{dto.operationTime}
        GROUP BY
        distinctField
        ORDER BY
        maxTime DESC
        LIMIT #{dto.hotSiteCount}
    </select>

    <select id="selectHotSiteByCrTime" resultType="com.trs.gov.management.VO.HotSiteVO">
        SELECT
        sr.id as id,
        sr.site_id as siteId,
        sr.site_name as siteName,
        sr.media_type as mediaType,
        sr.media_name as mediaName
        FROM site_relation sr
        WHERE sr.is_del = 0
        AND ( sr.host_unit_id = #{dto.unitId} OR sr.master_unit_id = #{dto.unitId} OR sr.operation_unit_id = #{dto.unitId} )
        AND sr.operation_start_time &lt;= #{dto.operationTime}
        AND sr.operation_end_time &gt;= #{dto.operationTime}
        <if test="dto.distinctField != null and dto.distinctField.size() > 0">
            AND CONCAT(sr.site_id,sr.media_type) not in
            <foreach collection="dto.distinctField" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY
        sr.cr_time DESC
        LIMIT #{dto.hotSiteCount}
    </select>

</mapper>