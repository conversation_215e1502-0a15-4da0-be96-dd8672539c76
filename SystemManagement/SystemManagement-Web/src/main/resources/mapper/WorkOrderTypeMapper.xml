<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.management.mapper.WorkOrderTypeMapper">

    <select id="selectParent" resultType="com.trs.gov.management.VO.ParentWorkOrderTypeVO">
        select
            a.id childId,
            a.type_name childName,
            b.*
        from
            work_order_type a, work_order_type b
        where
            a.parent_id = b.id
            and a.id in
            <foreach collection="ids" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>
</mapper>