package com.trs.gov.management.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Report;
import com.trs.common.base.Reports;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.externalSystem.DTO.RightSearchDTO;
import com.trs.gov.externalSystem.service.RightService;
import com.trs.gov.management.DO.UnitGroupDO;
import com.trs.gov.management.DO.UnitGroupRelationDO;
import com.trs.gov.management.DTO.*;
import com.trs.gov.management.VO.GroupUserCountVO;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.gov.management.base.constant.BaseConstant;
import com.trs.gov.management.mapper.UnitGroupMapper;
import com.trs.gov.management.mapper.UnitGroupRelationMapper;
import com.trs.gov.management.mapper.UnitMapper;
import com.trs.gov.management.service.impl.UnitGroupRelationServiceImpl;
import com.trs.upms.client.mgr.UpmsRightMgr;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 11:14
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
public class UnitGroupMgr {

    @Autowired
    private UnitGroupMapper groupMapper;

    @Autowired
    private UnitGroupRelationMapper relationMapper;

    @Autowired
    private UnitMapper unitMapper;

    @Autowired
    private UnitGroupRelationServiceImpl relationService;

    @Autowired
    private UpmsRightMgr upmsRightMgr;

    @Autowired
    private UnitMgr unitMgr;

    @Reference(check = false, timeout = 60000)
    private RightService rightService;

    public Report saveUnitGroup(UnitGroupDTO dto) throws ServiceException {
        List<UnitGroupDO> unitGroupDOS = groupMapper.selectList(new QueryWrapper<UnitGroupDO>()
                .eq("group_name", dto.getName())
                .eq("is_del", 0));
        if (unitGroupDOS.size() > 0) {
            throw new ServiceException(String.format("【%s】单位名称分组已存在", dto.getName()));
        }
        //构造保存DO
        UnitGroupDO unitGroupDO = createUnitGroupDO(dto,new UnitGroupDO());
        //保存单位分组
        groupMapper.insert(unitGroupDO);

        if (!StringUtils.isEmpty(dto.getUnitIds()) && !"null".equalsIgnoreCase(dto.getUnitIds())) {
            //构造添加单位到分组的dto
            AddUnitsToGroupDTO addUnitDTO = new AddUnitsToGroupDTO();
            addUnitDTO.setGroupId(unitGroupDO.getId());
            addUnitDTO.setUnitIds(dto.getUnitIds());
            //添加单位到分组
            addUnitsToGroup(addUnitDTO);
        }
        return new Report("新建单位分组", "新建单位分组成功");
    }

    private static UnitGroupDO createUnitGroupDO(UnitGroupDTO dto,UnitGroupDO groupDO) {
        if (groupDO!=null) {
            BaseUtils.copyProperties(dto,groupDO);
        }
        groupDO.setGroupName(dto.getName());
        groupDO.setUpdateUser(ContextHelper.getLoginUser().get());
        groupDO.setUpdateTime(new Date());
        return groupDO;
    }

    public Report updateUnitGroup(UnitGroupDTO dto) throws ServiceException {
        List<UnitGroupDO> unitGroupDOS = groupMapper.selectList(new QueryWrapper<UnitGroupDO>()
                .eq("group_name", dto.getName())
                .ne("id", dto.getId())
                .eq("is_del", 0));
        if (unitGroupDOS.size() > 0) {
            throw new ServiceException(String.format("【%s】单位名称分组已存在", dto.getName()));
        }
        UnitGroupDO groupDO = groupMapper.selectOne(new QueryWrapper<UnitGroupDO>()
                .eq("is_del", 0)
                .eq("id", dto.getId()));
        //构造保存DO
        UnitGroupDO unitGroupDO = createUnitGroupDO(dto,groupDO);
        groupMapper.updateById(unitGroupDO);

        //构造添加单位到分组的dto
        AddUnitsToGroupDTO addUnitDTO = new AddUnitsToGroupDTO();
        addUnitDTO.setGroupId(unitGroupDO.getId());
        addUnitDTO.setUnitIds(dto.getUnitIds());
        //添加单位到分组
        addUnitsToGroup(addUnitDTO);

        return new Report("编辑单位分组", "编辑单位分组成功");
    }

    public Reports deleteUnitGroup(String groupIds) throws ServiceException {
        Reports reports = Reports.createNewReports("删除单位分组");
        Optional.ofNullable(groupIds).orElseThrow(() -> new ServiceException("分组id不能为空"));
        //获取待删除分组
        List<UnitGroupDO> doList = groupMapper.selectList(new QueryWrapper<UnitGroupDO>()
                .in("id", Arrays.asList(groupIds.split(",")))
                .eq("is_del", 0));
        //遍历删除，以构造每个分组删除结果
        doList.forEach(item -> {
            try {
                item.setIsDel(1);
                groupMapper.updateById(item);
                reports.success(String.format("删除单位分组【%s】成功", item.getGroupName()));
            } catch (Exception e) {
                reports.error(String.format("删除单位分组【%s】失败", item.getGroupName()));
            }
        });
        return reports;
    }

    public List<Report> operUnitGroupStatus(OperUnitGroupStatusDTO dto) throws ServiceException {
        //构造返回结果
        List<Report> reports = new ArrayList<>();
        List<UnitGroupDO> unitGroupDOS = groupMapper.selectList(new QueryWrapper<UnitGroupDO>()
                .in("id", Arrays.asList(dto.getGroupIds().split(",")))
                .eq("is_del", 0));
        if (CollectionUtils.isEmpty(unitGroupDOS)) {
            throw new ServiceException(String.format("操作数据异常"));
        }
        unitGroupDOS.forEach(item->{
            item.setStatus(dto.getStatus());
            groupMapper.updateById(item);
            reports.add(new Report("启用/停用", "启用/停用【" + item.getGroupName() + "】成功"));
        });
        return reports;
    }

    public Report addUnitsToGroup(AddUnitsToGroupDTO dto) throws ServiceException {
        List<UnitGroupDO> unitGroupDOS = groupMapper.selectList(new QueryWrapper<UnitGroupDO>().eq("id", dto.getGroupId()).eq("is_del", 0));
        if (unitGroupDOS.size() == 0) {
            throw new ServiceException(String.format("id为【%s】的单位分组不存在", dto.getGroupId()));
        }
        UnitGroupDO unitGroupDO = unitGroupDOS.get(0);
        if (unitGroupDO.getStatus() == -1) {
            throw new ServiceException(String.format("该单位分组已停用"));
        }
        List<UnitGroupRelationDO> list = new ArrayList<>();
        //删除之前单位分组的配置
        relationMapper.delete(new QueryWrapper<UnitGroupRelationDO>().eq("group_id", dto.getGroupId()));
        //是否存在添加的单位
        if (!StringUtils.isEmpty(dto.getUnitIds()) || "null".equalsIgnoreCase(dto.getUnitIds())) {
            //构造DO集合
            for (String unitId : dto.getUnitIds().split(",")) {
                UnitGroupRelationDO relationDO = new UnitGroupRelationDO();
                relationDO.setGroupId(dto.getGroupId());
                relationDO.setUnitId(Long.valueOf(unitId));
                list.add(relationDO);
            }
            //批量保存
            relationService.saveBatch(list);
        }
        return new Report("配置单位分组", String.format("配置单位分组【%s】成功", unitGroupDO.getGroupName()));
    }

    public Page<UnitGroupDO> queryUnitGroupList(UnitGroupSearchDTO dto) {

        QueryWrapper<UnitGroupDO> wrapper = new QueryWrapper<>();
        //构造查询条件
        Optional.ofNullable(dto.getId()).ifPresent(ids -> {
            wrapper.in("id", Arrays.asList(ids.split(",")));
        });
        wrapper.eq(!StringUtils.isEmpty(dto.getStatus()), "status", dto.getStatus())
                .between(!StringUtils.isEmpty(dto.getCreateTimeStart()) && !StringUtils.isEmpty(dto.getCreateTimeEnd()), "cr_time", dto.getCreateTimeStart(), dto.getCreateTimeEnd() + BaseConstant.TIME_MODULE)
                .between(!StringUtils.isEmpty(dto.getUpdateTimeStart()) && !StringUtils.isEmpty(dto.getUpdateTimeEnd()), "update_time", dto.getUpdateTimeStart(), dto.getUpdateTimeEnd() + BaseConstant.TIME_MODULE)
                .like(!StringUtils.isEmpty(dto.getKeyWord()), "group_name", dto.getKeyWord())
                .eq("is_del", 0);
        wrapper.orderByDesc("cr_time");
        //构造分页
        Page<UnitGroupDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        if ("1".equals(dto.getIsAll())){
            List<UnitGroupDO> unitGroupDOS = groupMapper.selectList(wrapper);
            page.setRecords(unitGroupDOS);
            page.setTotal(unitGroupDOS.size());
            return page;
        }
        Page<UnitGroupDO> pageResult = groupMapper.selectPage(page, wrapper);
        return pageResult;
    }

    //获取分组id和单位数量关系
    public Map<Long, Long> getGroupUnitCountsMap() throws Exception {
        Map<Long, Long> groupCountsMap = new HashMap<>();
        //权限过滤
        List<Long> unitIds = getMyRightUnitIds();
        if (CollectionUtils.isEmpty(unitIds)){
            return groupCountsMap;
        }
        List<Map<String, Long>> groupUnitCounts = relationMapper.getGroupUnitCounts(unitIds);
        //获取单位组织和单位数量关系
        groupUnitCounts.forEach(item -> {
            groupCountsMap.put(item.get("groupId"), item.get("unitCounts"));
        });
        return groupCountsMap;
    }

    public Page<UnitGroupRelationDO> getGroupUnitListForRight(GroupUnitListSearchDTO dto, List<Long> unitIds) throws Exception {
        Page<UnitGroupRelationDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        if (CollectionUtils.isEmpty(unitIds)) {
           return page;
        }
        Page<UnitGroupRelationDO> relationDOS = relationMapper.selectPage(page,
                new QueryWrapper<UnitGroupRelationDO>()
                        .eq("group_id", dto.getGroupId())
                        .in("unit_id", unitIds)
        );
        return relationDOS;
    }
    public List<UnitGroupRelationDO> getGroupsUnitListForRight(GroupUnitListSearchDTO dto, List<Long> unitIds) throws Exception {
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.EMPTY_LIST;
        }
        List<UnitGroupRelationDO> relationDOS = relationMapper.selectList(
                new QueryWrapper<UnitGroupRelationDO>()
                        .in("group_id", Arrays.asList(dto.getGroupId().split(",")))
                        .in("unit_id", unitIds)
        );
        return relationDOS;
    }

    //获取单位用户统计相关信息
    public List<UserCountVO> getUnitListCountInfo(List<UnitGroupRelationDO> relationDOS,GroupUnitListSearchDTO dto) throws Exception {
        if (CollectionUtils.isEmpty(relationDOS)){
            return Collections.EMPTY_LIST;
        }
        //获取添加到当前分组的单位id
        List<String> ids = relationDOS.stream().map(unitGroupRelationDO -> String.valueOf(unitGroupRelationDO.getUnitId())).collect(Collectors.toList());
        //返回单位列表信息
        return unitMgr.getUserCountByIds(String.valueOf(ids).replaceAll("(?:\\[|null|\\]| +)", ""), dto.getStatus());
    }

    //获取当前用户所在当前单位可获取的单位数据
    public List<Long> getMyRightUnitIds() throws Exception {
        RightSearchDTO rightSearchDTO = new RightSearchDTO();
        String userName = ContextHelper.getLoginUser().orElseThrow(() -> new Exception("当前用户不能为空"));
        String unitId = ContextHelper.getLoginUnitId().orElseThrow(() -> new Exception("当前用户单位id不能为空"));
        rightSearchDTO.setUserName(userName);
        rightSearchDTO.setOperId(unitId);
        //获取当前用户当前单位权限位
        List<String> myRightOper = rightService.getMyRightOper(rightSearchDTO).getDatas();
        List<Long> unitIds;
        //数据过滤
        if (CollectionUtils.isEmpty(myRightOper)) {
            unitIds = Collections.EMPTY_LIST;
        } else if (upmsRightMgr.isAdmin(userName) || myRightOper.contains(BaseConstant.ALLUNIT)) {
            unitIds = unitMapper.getAllHasRightUnits();
        } else if (myRightOper.contains(BaseConstant.OWNUNIT)) {
            unitIds = Arrays.asList(Long.valueOf(unitId));
        } else {
            unitIds = Collections.EMPTY_LIST;
        }
        return unitIds;
    }

    public Page<UnitGroupRelationDO> getGroupUnitListNotRight(GroupUnitListSearchDTO dto) throws Exception {
        Page<UnitGroupRelationDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        //构造查询条件
        QueryWrapper<UnitGroupRelationDO> wrapper = new QueryWrapper<UnitGroupRelationDO>()
                .in("group_id", Arrays.asList(dto.getGroupId().split(",")))
                .inSql(dto.getStatus() != null, "unit_id", "select id from unit where status = " + dto.getStatus());
        //是否获取所有
        if ("1".equals(dto.getIsAll())){
            List<UnitGroupRelationDO> relationDOS = relationMapper.selectList(wrapper);
            page.setRecords(relationDOS);
            page.setTotal(relationDOS.size());
            return page;
        }
        Page<UnitGroupRelationDO> relationDOPage = relationMapper.selectPage(page, wrapper);
        return relationDOPage;
    }

    public List<UnitGroupDO> queryUnitGroupNotRight(UnitGroupSearchDTO dto) {
        QueryWrapper<UnitGroupDO> wrapper = new QueryWrapper<>();
        //构造查询条件
        Optional.ofNullable(dto.getId()).ifPresent(ids -> {
            wrapper.in("id", Arrays.asList(ids.split(",")));
        });
        wrapper.eq(!StringUtils.isEmpty(dto.getStatus()),"status",dto.getStatus())
                .eq("is_del",0);
        return groupMapper.selectList(wrapper);
    }

    public List<GroupUserCountVO> getGroupUserCount(GroupUnitListSearchDTO dto) throws Exception {
        List<GroupUserCountVO> list = new ArrayList<>();
        //获取分组和单位关系实体
        List<UnitGroupRelationDO> relationDOs= relationMapper.selectList(
                new QueryWrapper<UnitGroupRelationDO>()
                        .in("group_id", Arrays.asList(dto.getGroupId().split(",")))
                        .inSql(dto.getStatus()!=null,"unit_id","select id from unit where status = "+dto.getStatus())
        );
        List<Long> unitIds = relationDOs.stream().map(UnitGroupRelationDO::getUnitId).distinct().collect(Collectors.toList());
        List<Long> groupIds = relationDOs.stream().map(UnitGroupRelationDO::getGroupId).distinct().collect(Collectors.toList());
        //获取单位和用户数量关系
        List<UserCountVO> userCountByIds = unitMgr.getUserCountByIds(String.valueOf(unitIds).replaceAll("(?:\\[|null|\\]| +)", ""), dto.getStatus());
        Map<Long, Object> collect = userCountByIds.stream().distinct().collect(Collectors.toMap(UserCountVO::getUnitId, UserCountVO::getUserCount));
        //遍历分组id
        for (Long groupId : groupIds) {
            int count = 0;
            //遍历分组和单位关系实体数据，统计分组用户数量
            for (UnitGroupRelationDO relationDO : relationDOs) {
                if (relationDO.getGroupId().longValue()==groupId.longValue()){
                    count += Integer.parseInt(collect.getOrDefault(relationDO.getUnitId(),0).toString());
                }
            }
            GroupUserCountVO userCountVO = new GroupUserCountVO();
            userCountVO.setId(groupId);
            userCountVO.setUserCount(count);
            list.add(userCountVO);
        }
        return list;
    }
}
