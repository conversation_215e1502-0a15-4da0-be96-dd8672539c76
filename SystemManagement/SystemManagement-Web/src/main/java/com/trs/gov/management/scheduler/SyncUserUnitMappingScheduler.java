package com.trs.gov.management.scheduler;

import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.management.mgr.IUserUnitMappingMgr;
import com.trs.scheduler.Parameters;
import com.trs.scheduler.SchedulerRunnable;
import com.trs.user.DTO.UserDTO;
import com.trs.user.service.IUserService;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 人员单位映射同步器
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2021-01-05 15:36
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class SyncUserUnitMappingScheduler implements SchedulerRunnable {

    @Scheduled(fixedDelayString = "${SystemManagementService.SyncUserUnitMapping.Interval:600000}")
    public void run() {
        this.run(new Parameters());
    }

    @Resource(name = "userUnitMappingMgr")
    private IUserUnitMappingMgr mgr;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    private static final String SESSIONID = "SYNC_SESSIONID_" + java.util.UUID.randomUUID();

    @Override
    public void run(Parameters parameters) {
        Try.of(() -> {
            String uuid = java.util.UUID.randomUUID().toString();
            log.info("开始同步人员单位映射关系,flag={}", uuid);
            Try.of(() -> {
                userService.saveLoginInfo(SESSIONID, SESSIONID, UserDTO.of("system"));
                ContextHelper.initContext(SESSIONID, "system");
                return 0;
            });
            mgr.syncUserUnitMapping();
            Try.of(() -> {
                userService.logout(SESSIONID);
                return 0;
            });
            log.info("完成同步人员单位映射关系,flag={}", uuid);
            return 0;
        }).onFailure(err -> log.error("同步人员单位映射关系出错！", err));
    }
}
