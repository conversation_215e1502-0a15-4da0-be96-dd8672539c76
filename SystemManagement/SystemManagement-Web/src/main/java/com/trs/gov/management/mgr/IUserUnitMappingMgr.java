package com.trs.gov.management.mgr;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DTO.UserUnitMappingDTO;
import com.trs.gov.management.DTO.UserUnitMappingSearchDTO;
import com.trs.gov.management.VO.UserUnitMappingBySearchVO;
import com.trs.gov.management.VO.UserUnitMappingVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * IUserUnitMappingMgr
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2021-01-05 15:36
 * @version 1.0
 * @since 1.0
 */
public interface IUserUnitMappingMgr {

    /**
     * 获取用户单位映射列表<BR>
     *
     * @param dto 请求参数
     * @return 用户单位映射列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-06 15:04
     */
    public RestfulResults<List<UserUnitMappingBySearchVO>> queryUserUnitMappingList(UserUnitMappingSearchDTO dto) throws ServiceException;

    /**
     * 同步映射关系<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 15:37
     */
    public void syncUserUnitMapping();

    /**
     * 更新映射关系<BR>
     *
     * @param list 用户单位列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 15:47
     */
    public void updateMapping(List<UserUnitMappingDTO> list) throws ServiceException;

    /**
     * 根据单位ID查询相关映射关系列表<BR>
     *
     * @param unitId 单位ID
     * @return 映射关系列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 15:47
     */
    public List<UserUnitMappingVO> findMappingByUnitId(String unitId) throws ServiceException;

    /**
     * 删除相关单位的映射<BR>
     *
     * @param unitId 单位ID
     * @return 清除情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 15:52
     */
    public boolean deleteMappingByUnitId(String unitId) throws ServiceException;

}
