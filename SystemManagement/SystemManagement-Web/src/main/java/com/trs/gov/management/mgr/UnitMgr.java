package com.trs.gov.management.mgr;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.externalSystem.DTO.RightSearchDTO;
import com.trs.gov.externalSystem.service.RightService;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.file.util.FileUtils;
import com.trs.gov.management.DO.SiteRelationDO;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.management.DTO.*;
import com.trs.gov.management.VO.*;
import com.trs.gov.management.base.constant.BaseConstant;
import com.trs.gov.management.base.constant.UnitConstant;
import com.trs.gov.management.base.utils.HttpUtil;
import com.trs.gov.management.mapper.SiteRelationMapper;
import com.trs.gov.management.mapper.UnitMapper;
import com.trs.log.exception.RecordableException;
import com.trs.scheduler.Parameters;
import com.trs.tcache.util.redis.JedisPoolUtils;
import com.trs.upms.client.mgr.UpmsRightMgr;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.DTO.UserDTO;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.GroupVO;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IGroupService;
import com.trs.user.service.IUserService;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.trs.gov.management.base.constant.UnitConstant.PARAM_USERNAME;
import static com.trs.gov.management.base.constant.UnitConstant.POST_METHOD_VALUE;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/16 11:38
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class UnitMgr {

    @Autowired
    private UnitMapper unitMapper;

    @Autowired
    private SiteRelationMapper siteRelationMapper;

    @Autowired
    private UpmsRightMgr upmsRightMgr;

    @Autowired
    private UnitGroupMgr groupMgr;

    @Value("${upmsApiUrl.vaule}")
    private String upmsApiUrlVaule;

    @Reference(check = false, timeout = 60000)
    private IUserService service;

    @Reference(check = false, timeout = 60000)
    private IGroupService iGroupService;

    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileService;

    @Reference(check = false, timeout = 60000)
    private RightService rightService;

    @Value("${management.unitMgr.redisExpireTime:1800}")
    private int redisExpireTime;

    @Value("${management.unitMgr.redisIndex:9}")
    private int redisIndex;

    @Value("${upms.service.url}")
    private String upmsServiceUrl;

    public Page<UnitDO> queryChildrenUnitList(UnitSearchDTO dto) throws ServiceException {
        String unitId = dto.getUnitId();
        if (CMyString.isEmpty(unitId)) {
            throw new ServiceException("单位ID不能为空！");
        }
        UnitDO unitDO = unitMapper.selectById(Long.valueOf(unitId));
        if (unitDO == null) {
            throw new ServiceException("根据ID[" + unitId + "]无法找到相关单位！");
        }
        Page<UnitDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        String unitCode = unitDO.getUnitCode();
        if (CMyString.isEmpty(unitCode)) {
            return page;
        }
        List<String> groupKeyList = iGroupService.findChildrenGroupKeyListByGroupKey(GroupDTO.of(unitCode)).getDatas();
        if (groupKeyList == null || groupKeyList.isEmpty()) {
            return page;
        }
        QueryWrapper<UnitDO> wrapper = new QueryWrapper<>();
        Optional.ofNullable(dto.getUnitType()).ifPresent(unitType -> {
            wrapper.and(wrapper_ -> {
                for (String type : unitType.split(",")) {
                    wrapper_.or().like("unit_type", type);
                }
            });
        });
        wrapper.eq("status", CMyString.showEmpty(Optional.ofNullable(dto.getStatus()).orElse("1"), "1"));
        wrapper.in("unit_code", groupKeyList);
        List<UnitDO> unitDOS = unitMapper.selectList(wrapper);
        if (dto.getPageSize() == -1) {
            page.setSize(unitDOS.size());
        }
        page.setRecords(unitDOS);
        page.setTotal(unitDOS.size());
        return page;
    }

    public Page<UnitDO> queryUnitList(UnitSearchDTO dto) throws Exception {
        //过滤用户单位
        if (!unitRightFilter(dto)){
            return new Page<>();
        }
        QueryWrapper<UnitDO> wrapper = new QueryWrapper<>();
        //构造查询
        Optional.ofNullable(dto.getUnitType()).ifPresent(unitType -> {
            wrapper.and(wrapper_ -> {
                for (String type : unitType.split(",")) {
                    wrapper_.or().like("unit_type", type);
                }
            });
        });
        Optional.ofNullable(dto.getId()).ifPresent(id -> {
            wrapper.in("id", Arrays.asList(id.split(",")));
        });
        wrapper.like(!StringUtils.isEmpty(dto.getUnitName()), "unit_name", dto.getUnitName())
                .eq(!StringUtils.isEmpty(dto.getStatus()), "status", dto.getStatus())
                .isNotNull("1".equals(dto.getIsConnect()), "unit_code")
                .isNull("0".equals(dto.getIsConnect()), "unit_code")
                .between(!(StringUtils.isEmpty(dto.getCrTimeStart()) && StringUtils.isEmpty(dto.getCrTimeEnd())), "cr_time", dto.getCrTimeStart(), dto.getCrTimeEnd() + BaseConstant.TIME_MODULE)
                .between(!(StringUtils.isEmpty(dto.getUpdateTimeStart()) && StringUtils.isEmpty(dto.getUpdateTimeEnd())), "update_time", dto.getUpdateTimeStart(), dto.getUpdateTimeEnd() + BaseConstant.TIME_MODULE)
                .eq("is_del", 0);
        wrapper.eq(!StringUtils.isEmpty(dto.getNowUserUnitId()),"id",dto.getNowUserUnitId());
        wrapper.orderByDesc("cr_time");
        //构造分页
        Page<UnitDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        //执行查询
        if ("1".equals(dto.getIsAll())) {
            List<UnitDO> unitDOS = unitMapper.selectList(wrapper);
            page.setRecords(unitDOS);
            page.setTotal(unitDOS.size());
            return page;
        }
        Page<UnitDO> unitPage = unitMapper.selectPage(page, wrapper);
        if (unitPage.getRecords().size() == 1) {
            //保存当前用户要操作的单位的version版本
            JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
            jedisPoolUtils.set(dto.getToken(), String.valueOf(page.getRecords().get(0).getVersion()), redisExpireTime, redisIndex);
        }
        return unitPage;
    }

    //过滤用户单位
    public Boolean unitRightFilter(UnitSearchDTO dto) throws Exception {
        //如果为内部其他服务调用则不用过滤
        if (dto.getRpcTag()){
            return true;
        }
        //当前用户所在当前单位是否有关于单位的任何数据范围权限
        Boolean hasAnyRight = false;
        String userName = ContextHelper.getLoginUser().orElseThrow(() -> new Exception("当前用户不能为空"));
        String unitId = ContextHelper.getLoginUnitId().orElseThrow(() -> new Exception("当前用户单位id不能为空"));
        RightSearchDTO rightSearchDTO = new RightSearchDTO();
        rightSearchDTO.setUserName(userName);
        rightSearchDTO.setOperId(unitId);
        //获取权限位
        List<String> datas = rightService.getMyRightOper(rightSearchDTO).getDatas();
        //权限过滤
        if (datas.size()==0
                ||(!datas.contains(BaseConstant.OWNUNIT)
                &&!datas.contains(BaseConstant.ALLUNIT))){
            hasAnyRight = false;
        }else if (upmsRightMgr.isAdmin(userName)||datas.contains(BaseConstant.ALLUNIT)){
            hasAnyRight = true;
        }else if (datas.contains(BaseConstant.OWNUNIT)){
            hasAnyRight = true;
            dto.setNowUserUnitId(unitId);
        }
        return hasAnyRight;
    }

    public void addUnitUser(String unitCode, UnitUserDTO userDTO, UnitVO unitVO) throws Exception {
        List<UserVO> userVOS = new ArrayList<>();
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        String usersName = null;
        String redisKey = unitVO.getUnitName() + UnitConstant.USERS_REDIS_KEY;
        //现在缓存中查找
        if (jedisPoolUtils.exists(redisKey, redisIndex)) {
            List list = JsonUtils.toOptionObject(jedisPoolUtils.get(redisKey, redisIndex), List.class, UserVO.class).getOrElse(Collections.emptyList());
            unitVO.setUserVOS(list);
            return;
        }
        //判断编码是否为空
        if (StringUtils.isEmpty(unitCode)) {
            userVOS = Collections.emptyList();
        } else {
            userDTO.setGroupId(unitCode);
            userDTO.setPageSize(9999);
            JSONArray jsonArray = queryUnitUserList(userDTO);
            //如果返回用户数据为0，则返回空
            if (jsonArray.size() == 0) {
                userVOS = Collections.emptyList();
            } else {
                //获取用户名串
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < jsonArray.size(); i++) {
                    sb.append(jsonArray.getJSONObject(i).getString("username"));
                    sb.append(",");
                }
                usersName = String.valueOf(sb);
                UserSearchDTO searchDTO = new UserSearchDTO();
                searchDTO.setUserName(usersName);
                searchDTO.setPageSize(9999);
                //查询用户信息
                RestfulResults<List<UserVO>> allUser = service.getAllUser(searchDTO);
                userVOS = allUser.getDatas();
            }
        }
        jedisPoolUtils.set(redisKey, JsonUtils.toOptionalJson(userVOS).getOrElse(""), redisExpireTime, redisIndex);
        unitVO.setUserVOS(userVOS);
    }

    @Transactional(rollbackFor = Exception.class)
    public Report saveUnit(UnitDTO dto) throws Exception {
        //判断单位名称是否重复
        UnitDO unitDO = unitMapper.selectOne(new QueryWrapper<UnitDO>()
                .eq("unit_name", dto.getUnitName())
                .eq("is_del", "0"));
        if (unitDO != null) {
            throw new Exception("该单位名称已存在");
        }
        //设置属性值
        unitDO = new UnitDO();
        BaseUtils.copyProperties(dto, unitDO);
        this.setOtherProperties(unitDO);
        //向ids添加运维、厂商单位
        addUnitToIds(dto, unitDO);
        //保存
        unitMapper.insert(unitDO);
        //保存logo信息
        try {
            if (!StringUtils.isEmpty(dto.getLogo()) && !JSONObject.parseArray(dto.getLogo()).isEmpty() && !JSONObject.parseArray(dto.getLogo()).getJSONObject(0).isEmpty()) {
                FileUtils.saveFile(fileService, dto.getLogo(), UnitDO.OBJ_TYPE, Long.toString(unitDO.getId()), "picList");
            }
        } catch (Exception e) {
            Long id = unitDO.getId();
            if (id != null) {
                Try.of(() -> {
                    FileUtils.deleteFile(fileService, UnitDO.OBJ_TYPE, Long.toString(id), "picList");
                    return 0;
                }).onFailure(err -> log.warn("清除文件出错！", err));
            }
            throw new Exception("保存logo失败");
        }
        return new Report("新建单位", "新建单位成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public Report updateUnit(UnitDTO dto) throws Exception {
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        //查询单位是否存在
        UnitDO unitDO = unitMapper.selectOne(new QueryWrapper<UnitDO>()
                .eq("id", dto.getId())
                .eq("is_del", "0"));
        if (unitDO == null) {
            throw new Exception("单位不存在");
        }
        //判断单位名称是否重复
        UnitDO unitDO2 = unitMapper.selectOne(new QueryWrapper<UnitDO>()
                .eq("unit_name", dto.getUnitName())
                .eq("is_del", "0")
                .ne("id", dto.getId()));
        if (unitDO2 != null) {
            throw new Exception("该单位名称已存在");
        }
        //修改属性值
        BaseUtils.copyProperties(dto, unitDO);
        this.setOtherProperties(unitDO);
        //取出用户要操作的单位的version版本
        String version = jedisPoolUtils.get(dto.getToken(), redisIndex);
        unitDO.setVersion(Integer.valueOf(version));
        //保存
        int result = unitMapper.updateById(unitDO);
        if (result == 0) {
            throw new Exception("当前数据已被更新，请关闭窗口重新获取最新数据");
        }
        //保存文logo信息
        try {
            if (!StringUtils.isEmpty(dto.getLogo()) && !JSONObject.parseArray(dto.getLogo()).isEmpty() && !JSONObject.parseArray(dto.getLogo()).getJSONObject(0).isEmpty()) {
                FileUtils.saveFile(fileService, dto.getLogo(), UnitDO.OBJ_TYPE, Long.toString(unitDO.getId()), "picList");
            }
        } catch (Exception e) {
            Long id = unitDO.getId();
            if (id != null) {
                Try.of(() -> {
                    FileUtils.deleteFile(fileService, UnitDO.OBJ_TYPE, Long.toString(id), "picList");
                    return 0;
                }).onFailure(err -> log.warn("清除文件出错！", err));
            }
            throw new Exception("保存logo失败");
        }
        //单位跟新后清除之前单位的缓存信息
        jedisPoolUtils.del(dto.getUnitName() + UnitConstant.USERS_REDIS_KEY, redisIndex);
        jedisPoolUtils.del(dto.getUnitName() + UnitConstant.LOGO_REDIS_KEY, redisIndex);
        return new Report("编辑单位", "编辑单位成功");
    }

    public List<Report> operUnitState(UnitStateDTO dto) {
        //构造返回结果
        List<Report> reports = new ArrayList<>();
        List<UnitDO> unitDOS = unitMapper.selectBatchIds(Arrays.asList(dto.getId().split(",")));
        for (UnitDO unitDO : unitDOS) {
            //修改状态
            unitDO.setStatus(dto.getStatus());
            //更新
            unitMapper.updateById(unitDO);
            reports.add(new Report("启用/停用", "启用/停用【" + unitDO.getUnitName() + "】成功"));
        }
        return reports;
    }

    //设置额外实体属性
    private void setOtherProperties(UnitDO unitDO) throws ServiceException {
        unitDO.setUpdateTime(new Date());
        UserDTO userDTO = new UserDTO();
        userDTO.setUserName(unitDO.getCrUser());
        unitDO.setTrueName(service.getBaseUserInfo(userDTO).getTrueName());
        userDTO.setUserName(unitDO.getUnitMaster());
        unitDO.setUnitMasterTrueName(service.getBaseUserInfo(userDTO).getTrueName());
    }

    public JSONArray queryUnitUserList(UnitUserDTO dto) throws Exception {

        Map<String, Object> map = new LinkedHashMap<>();
        //设置参数
        map.put(UnitConstant.POST_METHOD, UnitConstant.POST_METHOD_VALUE);
        Optional.ofNullable(dto.getUnitId()).ifPresent(id -> Optional.ofNullable(unitMapper.selectById(id)).ifPresent(unitDo -> map.put(UnitConstant.PARAM_GROUPID, unitDo.getUnitCode())));
        Optional.ofNullable(dto.getGroupId()).ifPresent(groupId -> map.put(UnitConstant.PARAM_GROUPID, groupId));
        Optional.ofNullable(dto.getPageNum()).ifPresent(pageNum -> map.put(UnitConstant.PAGE_NUM, pageNum.toString()));
        Optional.ofNullable(dto.getPageSize()).ifPresent(pageSize -> map.put(UnitConstant.PAGE_SIZE, pageSize.toString()));
        Optional.ofNullable(dto.getUserName()).ifPresent(userName -> map.put(UnitConstant.PARAM_USERNAME, userName));
        Optional.ofNullable(dto.getStatus()).ifPresent(status -> map.put(UnitConstant.PARAM_STATUS, status));
        //向权限系统发送请求
        String result = HttpUtil.newBuilder().setUrl(upmsApiUrlVaule).setParamter(map).httpSendGet();
        //解析结果
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject == null || !jsonObject.getString("code").equals("200")) {
            throw new Exception("获取单位用户信息异常，异常信息" + jsonObject.getString("msg"));
        }
        JSONArray datas = JSONObject.parseArray(jsonObject.getString("datas"));
        return datas;
    }

    public List<UnitVO> queryUnitByType(UnitSearchDTO dto) throws Exception {
        List<UnitDO> unitDOS = unitMapper.selectList(new QueryWrapper<UnitDO>()
                .eq("unit_type", dto.getUnitType())
                .eq("status", "1")
                .eq("is_del", 0));
        UnitUserDTO userDTO = new UnitUserDTO();
        List<UnitVO> unitVOS = new ArrayList<>();
        for (UnitDO unitDO : unitDOS) {
            UnitVO unitVO = new UnitVO();
            BaseUtils.copyProperties(unitDO, unitVO);
            //添加用户信息
            addUnitUser(unitDO.getUnitCode(), userDTO, unitVO);
            unitVOS.add(unitVO);
        }
        return unitVOS;
    }

    public List<SimpleUnitVO> queryUnitByUser(UserSearchDTO dto) throws Exception {
        //构造返回
        List<SimpleUnitVO> unitVOS = new ArrayList<>();
        if (StringUtils.isEmpty(dto.getUserName())) {
            return unitVOS;
        }
        //判断用户在权限系统状态是否正常
        if (checkUserStatus(dto.getUserName())){
            //超管返回所有单位
            if (upmsRightMgr.isAdmin(dto.getUserName())){
                List<UnitDO> unitDOS = unitMapper.selectList(new QueryWrapper<UnitDO>().eq("is_del", 0).eq("status", "1"));
                return unitDoToSimpleUnitVO(unitDOS,unitVOS,dto);
            }
        }else {
            throw new Exception("用户【"+dto.getUserName()+"】处于停用或其他异常状态，无法登录");
        }
        //获取当前用户为负责人的单位
        List<UnitDO> unitDOSMaster = unitMapper.selectList(new QueryWrapper<UnitDO>().eq("is_del", 0)
                .eq("status", "1")
                .eq("unit_master", dto.getUserName()));
        unitDoToSimpleUnitVO(unitDOSMaster,unitVOS,dto);
        //构造参数dto
        UnitUserDTO unitUserDTO = new UnitUserDTO();
        //查询组织
        unitUserDTO.setPageSize(100);
        unitUserDTO.setUserName(dto.getUserName());
        //获取权限系统用户所在单位编码
        JSONArray jsonArray = queryUnitUserList(unitUserDTO);
        if (jsonArray.size() == 0) {
            return unitVOS;
        }
        List<String> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            list.add(jsonArray.getJSONObject(i).getString("groupid"));
        }
        //根据单位编码获取当前系统注册的单位信息
        List<UnitDO> unitDOS = unitMapper.selectList(new QueryWrapper<UnitDO>()
                .eq("is_del", 0)
                .eq("status", "1")
                .in("unit_code", list));
        return unitDoToSimpleUnitVO(unitDOS,unitVOS,dto).stream().distinct().collect(Collectors.toList());
    }

    private List<SimpleUnitVO> unitDoToSimpleUnitVO(List<UnitDO> unitDOS, List<SimpleUnitVO> unitVOS,UserSearchDTO dto) {
        unitDOS.forEach(unitDO -> {
            SimpleUnitVO unitVO = new SimpleUnitVO();
            BaseUtils.copyProperties(unitDO, unitVO);
            if (dto.getUserName().equals(unitDO.getUnitMaster())){
                unitVO.setIsUnitMaster(true);
            }else {
                unitVO.setIsUnitMaster(false);
            }
            unitVOS.add(unitVO);
        });
        return unitVOS;
    }

    public List<UnitAndSiteRelationsVO> getUnitAndSiteRelations(UnitSearchDTO dto) {

        QueryWrapper<SiteRelationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper.eq("host_unit_id", dto.getId())
                .or().eq("master_unit_id", dto.getId())
                .or().eq("operation_unit_id", dto.getId()));
        queryWrapper.eq("is_del", 0);

        List<SiteRelationDO> siteRelationDOS = siteRelationMapper.selectList(queryWrapper);
        List<UnitAndSiteRelationsVO> results = new ArrayList<>();
        //得出单位与站点关系分类
        for (SiteRelationDO siteRelationDO : siteRelationDOS) {
            if (dto.getId().equals(Long.toString(siteRelationDO.getHostUnitId()))) {
                UnitAndSiteRelationsVO relationsVO = new UnitAndSiteRelationsVO();
                relationsVO.setMediaName(siteRelationDO.getMediaName());
                relationsVO.setSiteName(siteRelationDO.getSiteName());
                relationsVO.setUnitType(UnitConstant.PARAM_HOSTUNIT_NAME);
                results.add(relationsVO);
            }
            if (dto.getId().equals(Long.toString(siteRelationDO.getMasterUnitId()))) {
                UnitAndSiteRelationsVO relationsVO = new UnitAndSiteRelationsVO();
                relationsVO.setMediaName(siteRelationDO.getMediaName());
                relationsVO.setSiteName(siteRelationDO.getSiteName());
                relationsVO.setUnitType(UnitConstant.PARAM_MASTERUNIT_NAME);
                results.add(relationsVO);
            }
            if (dto.getId().equals(Long.toString(siteRelationDO.getOperationUnitId()))) {
                UnitAndSiteRelationsVO relationsVO = new UnitAndSiteRelationsVO();
                relationsVO.setMediaName(siteRelationDO.getMediaName());
                relationsVO.setSiteName(siteRelationDO.getSiteName());
                relationsVO.setUnitType(UnitConstant.PARAM_OPERATIONUNIT_NAME);
                results.add(relationsVO);
            }
        }
        return results;
    }

    public void addUnitToIds(UnitDTO dto, UnitDO unitDTO) throws ServiceException {
        if (UnitConstant.PARAM_OPERATION_ID.equals(dto.getUnitType()) || UnitConstant.PARAM_FACTOR_NAME.equals(dto.getUnitType())) {
            GroupDTO groupDTO = new GroupDTO();
            groupDTO.setGroupName(dto.getUnitName());
            groupDTO.setGroupDesc(dto.getUnitDesc());
            groupDTO.setId(Long.parseLong("0"));
            groupDTO.setGroupKey(dto.getUnitCode());
            BaseUtils.setUserInfoToDTO(groupDTO);
            RestfulResults<GroupVO> groupVORestfulResults = iGroupService.editGroup(groupDTO);
            if (groupVORestfulResults.getDatas().getGroupKey() != null) {
                unitDTO.setUnitCode(groupVORestfulResults.getDatas().getGroupKey());
            }
        }
    }

    public UnitVO getUnitDetailById(UnitSearchDTO dto) throws Exception {
        if (StringUtils.isEmpty(dto.getId()) || "null".equalsIgnoreCase(dto.getId())) {
            throw new Exception("单位id不能为空");
        }
        UnitDO unitDO = unitMapper.selectById(dto.getId());
        if (unitDO == null) {
            throw new Exception("该单位信息不存在");
        }
        UnitVO unitVO = new UnitVO();
        BaseUtils.copyProperties(unitDO, unitVO);
        //获取logo
        ObjDTO logoDTO = ObjDTO.of(UnitDO.OBJ_TYPE, String.valueOf(unitDO.getId()), "picList");
        BaseUtils.setUserInfoToDTO(logoDTO);
        List<FileVO> logo = fileService.getFileListOfObj(logoDTO);
        unitVO.setLogo(logo);
        //添加单位用户
        UnitUserDTO userDTO = new UnitUserDTO();
        addUnitUser(unitDO.getUnitCode(), userDTO, unitVO);
        return unitVO;
    }

    public void addUnitLogo(UnitVO unitVO) throws Exception {
        List logo = new ArrayList<>();
        String redisKey = unitVO.getUnitName() + UnitConstant.LOGO_REDIS_KEY;
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        if (jedisPoolUtils.exists(redisKey, redisIndex)) {
            logo = JsonUtils.toOptionObject(jedisPoolUtils.get(redisKey, redisIndex), List.class, FileVO.class).getOrElse(Collections.emptyList());
        } else {
            ObjDTO logoDTO = ObjDTO.of(UnitDO.OBJ_TYPE, String.valueOf(unitVO.getId()), "picList");
            BaseUtils.setUserInfoToDTO(logoDTO);
            logo = fileService.getFileListOfObj(logoDTO);
            jedisPoolUtils.set(redisKey, JsonUtils.toOptionalJson(logo).getOrElse(""), redisExpireTime, redisIndex);
        }
        unitVO.setLogo(logo);
    }

    public Optional<UnitVO> exportUnit(UnitExportDTO dto) throws ServiceException {
        UnitDO unitDO = new UnitDO();
        BaseUtils.copyProperties(dto, unitDO);
        //从其它地方迁移过来
        unitDO.setExportFromOther(1);
        int result = unitMapper.insert(unitDO);
        if (result <= 0) {
            throw new ServiceException("保存单位oldUnitId[" + dto.getOldUnitId() + "]的数据失败!");
        }
        if (dto.getLogo() != null) {
            try {
                FileVO[] fileVOS = new FileVO[]{dto.getLogo()};
                FileUtils.saveFile(fileService, JSONObject.toJSONString(fileVOS), UnitDO.OBJ_TYPE, Long.toString(unitDO.getId()), "picList");
            } catch (Exception e) {
                unitMapper.deleteById(unitDO.getId());
                throw new ServiceException(e.getMessage(), e);
            }
        }
        UnitVO unitVO = new UnitVO();
        BaseUtils.copyProperties(unitDO, unitVO);
        return Optional.of(unitVO);
    }

    public List<UnitSearchVO> getAllUnitForSearch(UnitSearchDTO dto) {
        if (StringUtils.isEmpty(dto.getStatus()) || "null".equalsIgnoreCase(dto.getStatus())) {
            dto.setStatus("1");
        }
        List<UnitSearchVO> allUnitForSearch = unitMapper.getAllUnitForSearch(dto.getStatus(), dto.getUnitType(), dto.getUnitName());
        return allUnitForSearch;
    }

    public List<UserCountVO> getUserCountByIds(String ids,Integer status) throws Exception {
        List<UnitDO> unitDOS;
        List<UserCountVO> resultMapList = new ArrayList<>();
        //获取单位信息
        //为空获取所有
        if (StringUtils.isEmpty(ids) || "null".equalsIgnoreCase(ids)) {
            unitDOS = unitMapper.selectList(new QueryWrapper<UnitDO>().eq("is_del", 0)
                    .eq(status!=null,"status", status));
        } else {
            unitDOS = unitMapper.selectList(new QueryWrapper<UnitDO>().eq("is_del", 0)
                    .eq(status!=null,"status", status)
                    .in("id", Arrays.asList(ids.split(","))));
        }
        //获取非空单位编码
        List<String> unitCodes = unitDOS.stream()
                .filter(unitDO -> !StringUtils.isEmpty(unitDO.getUnitCode()) && !"null".equalsIgnoreCase(unitDO.getUnitCode()))
                .map(UnitDO::getUnitCode).collect(Collectors.toList());
        //构造编码和数量映射关系
        Map<String, Object> idCodeMapping = new HashMap<>();
        //构造请求参数
        if (!CollectionUtils.isEmpty(unitCodes)){
            Map<String, Object> mapParams = new LinkedHashMap<>();
            mapParams.put(UnitConstant.POST_METHOD, "getUserCountByGroupIds");
            mapParams.put("groupIds", String.valueOf(unitCodes).replaceAll("(?:\\[|null|\\]| +)", ""));
            //向权限系统发送请求
            String getResult = HttpUtil.newBuilder().setParamter(mapParams).setUrl(upmsApiUrlVaule).httpSendGet();
            //解析结果
            JSONObject jsonObject = JSONObject.parseObject(getResult);
            if (jsonObject == null || !jsonObject.getString("code").equals("200")) {
                throw new Exception("获取用户信息异常，异常信息" + jsonObject.getString("msg"));
            }
            List<Map<String, Object>> dataListMap = JSONArray.parseObject(jsonObject.getString("datas"), List.class);

            dataListMap.forEach(item -> {
                idCodeMapping.put(String.valueOf(item.get("groupId")), item.get("userCount"));
            });
        }
        for (UnitDO unitDO : unitDOS) {
            UserCountVO userCountVO = new UserCountVO();
            //将未关联单位用户数量设为0
            if (StringUtils.isEmpty(unitDO.getUnitCode()) || "null".equalsIgnoreCase(unitDO.getUnitCode())) {
                userCountVO.setUnitId(unitDO.getId());
                userCountVO.setUnitName(unitDO.getUnitName());
                userCountVO.setUnitType(unitDO.getUnitType());
                userCountVO.setStatus(unitDO.getStatus());
                userCountVO.setUserCount(0);
            }
            //已关联的获取相应用户数量
            else {
                userCountVO.setUnitId(unitDO.getId());
                userCountVO.setUnitName(unitDO.getUnitName());
                userCountVO.setUnitType(unitDO.getUnitType());
                userCountVO.setStatus(unitDO.getStatus());
                userCountVO.setUserCount(idCodeMapping.get(unitDO.getUnitCode()) == null ? 0 : idCodeMapping.get(unitDO.getUnitCode()));
            }
            resultMapList.add(userCountVO);
        }
        return resultMapList;
    }

    public List<BaseUnitVO> queryHasRightUnitByType(UnitSearchDTO dto) throws Exception {
        List<Long> myRightUnitIds = groupMgr.getMyRightUnitIds();
        if (CollectionUtils.isEmpty(myRightUnitIds)){
            return Collections.EMPTY_LIST;
        }
        List<UnitDO> unitDOS = unitMapper.selectList(new QueryWrapper<UnitDO>()
                .eq("unit_type", dto.getUnitType())
                .in("id",myRightUnitIds)
                .eq("status", "1")
                .eq("is_del", 0));
        List<BaseUnitVO> unitVOS = new ArrayList<>();
        for (UnitDO unitDO : unitDOS) {
            BaseUnitVO unitVO = new BaseUnitVO();
            BaseUtils.copyProperties(unitDO, unitVO);
            unitVOS.add(unitVO);
        }
        return unitVOS;
    }

    public Page<UnitDO> getAllUnitForTongZhiSearch(UnitSearchDTO dto) {
        if (StringUtils.isEmpty(dto.getStatus()) || "null".equalsIgnoreCase(dto.getStatus())) {
            dto.setStatus("1");
        }
        QueryWrapper<UnitDO> wrapper = new QueryWrapper<>();
        Optional.ofNullable(dto.getUnitType()).ifPresent(unitType->{
            wrapper.and(wrapper_->{
                for (String s : unitType.split(",")) {
                    wrapper_.or().like("unit_type",s);
                }
            });
        });
        Page<UnitDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        wrapper.eq("is_del", 0)
                .eq("status", dto.getStatus())
                .like(!StringUtils.isEmpty(dto.getUnitName()), "unit_name", dto.getUnitName());
        Page<UnitDO> unitDOPage = unitMapper.selectPage(page, wrapper);
        return unitDOPage;
    }

    private boolean checkUserStatus(String userName) throws Exception {
        PreConditionCheck.checkNotNull(userName,"登录用户名不能为空");
        Map<String, Object> map = new LinkedHashMap<>();
        map.put(UnitConstant.POST_METHOD, "getUserInfoByUserName");
        map.put(UnitConstant.PARAM_USERNAME,userName);
        //向权限系统发送请求
        String result = HttpUtil.newBuilder().setUrl(upmsServiceUrl.concat("/user.do")).setParamter(map).httpSendGet();
        //解析结果
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (jsonObject == null || !jsonObject.getString("code").equals("200")) {
            throw new Exception("获取单位用户信息异常，异常信息" + jsonObject.getString("msg"));
        }
        Integer status =(Integer)jsonObject.getJSONObject("datas").getOrDefault("status",0);
        return 1==status;
    }

    public UnitDO getUnitByTypeAndUnitName(String unitName, String type, SponsorInfoVO sponsorInfoVO, InitInfoVO initInfoVO, OperationUnitInfoVO operationUnitInfoVO) throws Exception {
       log.warn("type：{}; unitName：{};",type,unitName);
        UnitDO unitDO = unitMapper.selectOne(new QueryWrapper<UnitDO>()
                .in("unit_type", type)
                .eq("unit_name",unitName)
                .eq("status", "1")
                .eq("is_del", 0));

       if (unitDO == null){
          if (UnitConstant.PARAM_MASTERUNIT_ID.equals(type)){
            //主管
              UnitDTO unitDTO = new UnitDTO();
              unitDTO.setId("0");
              unitDTO.setUnitType(UnitConstant.PARAM_MASTERUNIT_ID);
              unitDTO.setUnitName(initInfoVO.getUnit());
              unitDTO.setUnitAddr(initInfoVO.getUnitaddress());
              unitDTO.setPhone(initInfoVO.getUnitchargephone());
              unitDTO.setUnitMaster(initInfoVO.getUnitchargeperson());
              unitDO= saveUnitByHy(unitDTO);
          }else if (UnitConstant.PARAM_HOSTUNIT_ID.equals(type)){
            //主办
              UnitDTO unitDTO = new UnitDTO();
              unitDTO.setId("0");
              unitDTO.setUnitType(UnitConstant.PARAM_HOSTUNIT_ID);
              unitDTO.setUnitName(sponsorInfoVO.getSponsor());
              unitDTO.setUnitAddr(sponsorInfoVO.getSponsoraddress());
              unitDTO.setPhone(sponsorInfoVO.getSponsorpersonphone());
              unitDTO.setUnitMaster(sponsorInfoVO.getSponsorperson());
              unitDO= saveUnitByHy(unitDTO);
          }else if (UnitConstant.PARAM_OPERATION_ID.equals(type)){
            //运维
              UnitDTO unitDTO = new UnitDTO();
              unitDTO.setId("0");
              unitDTO.setUnitType(UnitConstant.PARAM_OPERATION_ID);
              unitDTO.setUnitName(operationUnitInfoVO.getOperationunit());
              unitDTO.setUnitAddr(operationUnitInfoVO.getOperationunitaddress());
              unitDTO.setPhone(operationUnitInfoVO.getOperationphone());
              unitDTO.setUnitMaster(operationUnitInfoVO.getOperationperson());
              unitDO= saveUnitByHy(unitDTO);
          }
       }
        log.warn("同步更新后的unitDO:{}",unitDO.toString());
       return unitDO;
    }


    @Transactional(rollbackFor = Exception.class)
    public UnitDO saveUnitByHy(UnitDTO dto) throws Exception {
        //判断单位名称是否重复
        UnitDO unitDO = unitMapper.selectOne(new QueryWrapper<UnitDO>()
                .eq("unit_name", dto.getUnitName())
                .eq("is_del", "0"));
        UnitDO unitDOAdd = new UnitDO();
        if (unitDO != null) {
            if (dto.getUnitType().equals("1") || dto.getUnitType().equals("2")){
                dto.setUnitType("1,2");
            }else if (dto.getUnitType().equals("3") || dto.getUnitType().equals("4")){
                dto.setUnitType("3,4");
            }
            BaseUtils.copyProperties(dto, unitDO);
            unitDO.setUnitMasterTrueName(dto.getUnitMaster());
            this.setOtherPropertiesByHy(unitDO);
            //向ids添加运维、厂商单位
            addUnitToIdsByHy(dto, unitDO);
            //保存
            int unitDOId = unitMapper.updateById(unitDO);
             unitDOAdd = unitMapper.selectById(unitDO.getId());
            return unitDOAdd;
        }else {
        //设置属性值
        unitDO = new UnitDO();
        BaseUtils.copyProperties(dto, unitDO);
        unitDO.setUnitMasterTrueName(dto.getUnitMaster());
        this.setOtherPropertiesByHy(unitDO);
        //向ids添加运维、厂商单位
        addUnitToIdsByHy(dto, unitDO);
        //保存
        int unitDOId = unitMapper.insert(unitDO);
        log.warn("同步插入unit的id:{}",unitDOId);
         unitDOAdd = unitMapper.selectOne(new QueryWrapper<UnitDO>()
                    .in("unit_type", unitDO.getUnitType())
                    .eq("unit_name",unitDO.getUnitName())
                    .eq("status", "1")
                    .eq("is_del", 0));
        }
        //保存logo信息
        try {
            if (!StringUtils.isEmpty(dto.getLogo()) && !JSONObject.parseArray(dto.getLogo()).isEmpty() && !JSONObject.parseArray(dto.getLogo()).getJSONObject(0).isEmpty()) {
                FileUtils.saveFile(fileService, dto.getLogo(), UnitDO.OBJ_TYPE, Long.toString(unitDO.getId()), "picList");
            }
        } catch (Exception e) {
            Long id = unitDO.getId();
            if (id != null) {
                Try.of(() -> {
                    FileUtils.deleteFile(fileService, UnitDO.OBJ_TYPE, Long.toString(id), "picList");
                    return 0;
                }).onFailure(err -> log.warn("清除文件出错！", err));
            }
            throw new Exception("保存logo失败");
        }
        return unitDOAdd;
    }

    private void addUnitToIdsByHy(UnitDTO dto, UnitDO unitDTO) throws ServiceException{
        if (UnitConstant.PARAM_OPERATION_ID.equals(dto.getUnitType()) || UnitConstant.PARAM_FACTOR_NAME.equals(dto.getUnitType())) {
            GroupDTO groupDTO = new GroupDTO();
            groupDTO.setGroupName(dto.getUnitName());
            groupDTO.setGroupDesc(dto.getUnitDesc());
            groupDTO.setId(Long.parseLong("0"));
            groupDTO.setGroupKey(dto.getUnitCode());
            BaseUtils.setUserInfoToDTO(groupDTO);
            RestfulResults<GroupVO> groupVORestfulResults = iGroupService.editGroupByHy(groupDTO);
            if (groupVORestfulResults.getDatas().getGroupKey() != null) {
                unitDTO.setUnitCode(groupVORestfulResults.getDatas().getGroupKey());
            }
        }

    }

    //设置额外实体属性
    private void setOtherPropertiesByHy(UnitDO unitDO) throws ServiceException {
        unitDO.setUpdateTime(new Date());
    }

    public void syncUnitCode(Parameters parameters) {
        List<UnitDO> updateList = new ArrayList<>();
        //查询数据库中的单位
        List<UnitDO> list = unitMapper.selectList(new QueryWrapper<UnitDO>()
                .isNotNull("unit_code")
                .eq("status", 1)
                .isNotNull("unit_master_true_name")
                .eq("is_del", 0));
        //查询upms中的单位
        Map<String, Object> map = new LinkedHashMap<>();
        map.put(UnitConstant.POST_METHOD, POST_METHOD_VALUE);
        map.put(UnitConstant.PAGE_NUM,0);
        map.put(UnitConstant.PAGE_SIZE,99999);
        map.put(UnitConstant.PARAM_STATUS,1);
        try {
            String result = HttpUtil.newBuilder().setUrl(upmsServiceUrl.concat("/group.do")).setParamter(map).httpSendGet();
            JSONObject resultObj = JSONObject.parseObject(result);
            if (resultObj.containsKey("code")&& resultObj.getString("code").equals("200")){
                JSONArray dataArray = resultObj.getJSONArray("datas");
                List<GroupUnitVO> unitList = dataArray.toJavaList(GroupUnitVO.class);
                Set<GroupUnitVO> unitSet = new HashSet<>(unitList);
                List<GroupUnitVO> listDistinct  = new ArrayList<>();
                //排除已停用的单位
                Map<String ,Optional<GroupUnitVO>> optionalMap = unitSet.stream().collect(Collectors.groupingBy(o->o.getGroupname()+"_"+o.getUsername() ,Collectors.maxBy(Comparator.comparing(GroupUnitVO::getGroupid))));
                optionalMap.forEach((k,v)->{
                    listDistinct.add(v.get());
                });
                for (UnitDO unitDO:list) {
                    for (GroupUnitVO vo: listDistinct) {
                        if(unitDO.getUnitName().equals(vo.getGroupname())
                                && unitDO.getUnitMasterTrueName().equals(vo.getUsername())
                                && !unitDO.getUnitCode().equals(String.valueOf(vo.getGroupid()))){
                            unitDO.setUnitCode(String.valueOf(vo.getGroupid()));
                            updateList.add(unitDO);
                            break;
                        }
                    }
                }
            }
        } catch (RecordableException e) {
            log.error("同步单位unitCode失败:{}",e);
        }
        //更新数据
        //如果数据库中的unitCode和upms中的groupId不一致，修改unit表中的unitCode
        for (UnitDO unitDo: updateList) {
            unitMapper.updateById(unitDo);
        }
        log.info("同步更新单位数量：{}",updateList.size());
    }
}
