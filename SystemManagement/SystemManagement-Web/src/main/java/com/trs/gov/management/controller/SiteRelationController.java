package com.trs.gov.management.controller;

import com.trs.common.base.Report;
import com.trs.common.base.Reports;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ExcelUtil;
import com.trs.gov.management.DTO.SiteNameDTO;
import com.trs.gov.management.DTO.SiteRelationDTO;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.VO.GetAllSitesByTypeVO;
import com.trs.gov.management.VO.HotSiteVO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.management.base.constant.SiteRelationConstant;
import com.trs.gov.management.service.SiteRelationService;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.vavr.control.Try;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/17 14:28
 * @version 1.0
 * @since 1.0
 */
@Api(tags = "站点关系配置API")
@RestController
@RequestMapping("/management/siteRelation")
public class SiteRelationController {

    @Reference(check = false, timeout = 60000)
    private SiteRelationService siteRelationService;

    /**
     * 新建/编辑站点关系<BR>
     *
     * @param dto 请求参数
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:55
     */
    @ApiOperation(value = "保存或者更新站点关系")
    @RequestMapping(value = "saveOrUpdateSiteRelation", method = {RequestMethod.POST})
    public RestfulResults<Report> saveOrUpdateSiteRelation(SiteRelationDTO dto) {
        return siteRelationService.saveOrUpdateSiteRelation(dto);
    }

    /**
     * 查询站点关系列表<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:55
     */
    @ApiOperation(value = "查询站点关系列表")
    @RequestMapping(value = "querySiteRelationList", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults<List<SiteRelationVO>> querySiteRelationList(SiteRelationSearchDTO dto) {
        return siteRelationService.querySiteRelationList(dto);
    }

    /**
     * 删除站点关系<BR>
     *
     * @param id 站点关系id
     * @return 保存结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:57
     */
    @ApiOperation(value = "删除站点关系")
    @RequestMapping(value = "deleteSiteRelation", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults<Reports> deleteSiteRelation(String id) {
        return siteRelationService.deleteSiteRelation(id);
    }

    /**
     * 查询站点名称或监控云名称是否存在<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 11:49
     */
    @ApiOperation(value = "查询站点名称或监控云名称是否存在")
    @RequestMapping(value = "nameIsExist", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults<String> nameIsExist(SiteNameDTO dto) {
        return siteRelationService.nameIsExist(dto);
    }

    /**
     * 获取站点渠道类型关系<BR>
     *
     * @param
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 13:37
     */
    @ApiOperation(value = "获取站点渠道类型关系")
    @RequestMapping(value = "getMediaTypeInfo", method = {RequestMethod.GET})
    public RestfulResults<List<Map>> getMediaTypeInfo() {
        return Try.of(() -> siteRelationService.getMediaTypeInfo()).getOrElseGet(e -> RestfulResults.error(e.getMessage()));
    }

    /**
     * 根据站点分类获取工单系统所有站点信息<BR>
     *
     * @param
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/27 21:27
     */
    @ApiOperation(value = "根据站点分类获取工单系统所有站点信息")
    @RequestMapping(value = "getWorkOrderSysSites", method = {RequestMethod.GET})
    public RestfulResults<List<GetAllSitesByTypeVO>> getWorkOrderSysSites() {
        return siteRelationService.getWorkOrderSysSites();
    }

    /**
     * 导出站点关系表<BR>
     *
     * @param
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/23 13:49
     */
    @ApiOperation(value = "导出站点关系表")
    @RequestMapping(value = "exportSiteRelation", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportSiteRelation(HttpServletResponse response, SiteRelationSearchDTO dto) throws ServiceException {
        ExcelUtil.exportExcel(response, siteRelationService.exportSiteRelation(dto), SiteRelationConstant.SIETRELATION_FILENAME, SiteRelationConstant.SIETRELATION_FILENAME);
    }

    /**
     * @Description  获取最热的站点数据
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.management.VO.SiteRelationVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/28 18:10
     **/
    @PostMapping("/hotSite")
    public RestfulResults<List<HotSiteVO>> listHotSiteRelation(SiteRelationSearchDTO dto) throws ServiceException {
        return siteRelationService.listHotSiteRelation(dto);
    }

    @ApiOperation(value = "获取政府站点占比")
    @RequestMapping(value = "getGovNatureRate", method = {RequestMethod.GET})
    public RestfulResults<Map<String,Double>> getGovNatureRate(SiteRelationSearchDTO dto) {
        return Try.of(()->{
           return RestfulResults.ok(siteRelationService.getGovNatureRate(dto)).addMsg("操作成功");
        }).getOrElseGet(err->RestfulResults.error(err.getMessage()).addMsg("操作失败:"+err.getMessage()));
    }
}
