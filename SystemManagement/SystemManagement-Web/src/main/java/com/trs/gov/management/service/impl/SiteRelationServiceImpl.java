package com.trs.gov.management.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Defaults;
import com.trs.common.base.Report;
import com.trs.common.base.Reports;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.DO.SiteRelationDO;
import com.trs.gov.management.DO.excelModel.SiteRelationModel;
import com.trs.gov.management.DTO.SiteNameDTO;
import com.trs.gov.management.DTO.SiteRelationDTO;
import com.trs.gov.management.DTO.SiteRelationExportDTO;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.VO.GetAllSitesByTypeVO;
import com.trs.gov.management.VO.HotSiteVO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.management.mgr.SiteRelationMgr;
import com.trs.gov.management.service.SiteRelationService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.common.utils.TimeUtils.YYYYMMDD;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 站点配置服务接口实现
 */
@Service
@Slf4j
public class SiteRelationServiceImpl implements SiteRelationService {

    @Autowired
    private SiteRelationMgr siteRelationMgr;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Override
    public RestfulResults<Report> saveOrUpdateSiteRelation(SiteRelationDTO dto) {
        Report report = Defaults.defaultValue(Report.class);
        try {
            //校验参数
            BaseUtils.checkDTO(dto);
            //新建
            if (dto.getId().equals("0")) {
                report = siteRelationMgr.saveSiteRelation(dto);
            }
            //编辑
            else {
                report = siteRelationMgr.updateSiteRelation(dto);
            }
        } catch (Exception e) {
            log.error("新建/编辑站点关系异常！", e);
            return RestfulResults.error("操作异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(report)
                .addMsg("操作成功");
    }

    @Override
    public RestfulResults<List<SiteRelationVO>> querySiteRelationList(SiteRelationSearchDTO dto) {
        //构造返回
        RestfulResults restfulResults;
        try {
            //参数校验
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
            //调用业务方法
            Page<SiteRelationDO> siteRelationDOPage = siteRelationMgr.querySiteRelationList(dto);
            //构造返回VO
            List<SiteRelationVO> siteRelationVOs = new ArrayList<>();
            for (SiteRelationDO record : siteRelationDOPage.getRecords()) {
                SiteRelationVO vo = new SiteRelationVO();
                BaseUtils.copyProperties(record, vo);
                siteRelationVOs.add(vo);
            }
            //构造返回结果
            restfulResults = RestfulResults.ok(siteRelationVOs)
                    .addMsg("查询成功")
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(siteRelationDOPage.getTotal());
        } catch (Exception e) {
            log.error("查询站点关系异常！", e);
            restfulResults = RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return restfulResults;
    }

    @Override
    public RestfulResults<Reports> deleteSiteRelation(String id) {
        Reports reports = null;
        try {
            //参数校验
            Optional.ofNullable(id).orElseThrow(() -> new ParamInvalidException("站点关系id不能为空"));
            //调用业务方法
            reports = siteRelationMgr.deleteSiteRelation(id);
        } catch (Exception e) {
            log.error("删除站点关系异常！", e);
            return RestfulResults.error("删除站点关系异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(reports)
                .addMsg("删除成功");
    }

    @Override
    public RestfulResults<String> nameIsExist(SiteNameDTO dto) {
        try {
            BaseUtils.setUserInfoToDTO(dto);
            String result = siteRelationMgr.nameIsExist(dto);
            return RestfulResults.ok(result)
                    .addMsg("查询成功");
        } catch (Exception e) {
            log.error("查询异常！", e);
            return RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
    }

    @Override
    public RestfulResults<List<Map>> getMediaTypeInfo() throws ServiceException {
        return Try.of(() -> Optional.ofNullable(siteRelationMgr.getMediaTypeInfo()).map(item -> {
            return RestfulResults.ok(item)
                    .addMsg("获取成功")
                    .addTotalCount(Long.parseLong(String.valueOf(item.size())));
        })).onFailure(e -> log.error("查询异常", e)).getOrElseThrow(err -> new ServiceException("查询异常,异常信息:" + err.getMessage())).get();
    }

    @Override
    public RestfulResults<List<GetAllSitesByTypeVO>> getWorkOrderSysSites() {
        List<GetAllSitesByTypeVO> allSitesByTypeVOS;
        try {
            allSitesByTypeVOS = siteRelationMgr.getWorkOrderSysSites();
        } catch (Exception e) {
            log.error("查询异常！", e);
            return RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(allSitesByTypeVOS)
                .addMsg("查询成功")
                .addTotalCount(Long.parseLong(String.valueOf(allSitesByTypeVOS.size())));
    }

    @Override
    public List<SiteRelationModel> exportSiteRelation(SiteRelationSearchDTO dto) throws ServiceException {
        RestfulResults<List<SiteRelationVO>> listRestfulResults = querySiteRelationList(dto);
        List<SiteRelationModel> relationModels = new ArrayList<>();
        //封装数据
        listRestfulResults.getDatas().forEach(siteRelationVO -> {
            SiteRelationModel siteRelationModel = new SiteRelationModel();
            BaseUtils.copyProperties(siteRelationVO, siteRelationModel);
            String startTime = TimeUtils.dateToString(siteRelationVO.getOperationStartTime(), YYYYMMDD);
            String endTime = TimeUtils.dateToString(siteRelationVO.getOperationEndTime(), YYYYMMDD);
            siteRelationModel.setFilingTime(TimeUtils.dateToString(siteRelationVO.getFilingTime(), YYYYMMDD));
            siteRelationModel.setConstructionTime(TimeUtils.dateToString(siteRelationVO.getConstructionTime(), YYYYMMDD));
            siteRelationModel.setOperationTime(startTime + "-" + endTime);
            relationModels.add(siteRelationModel);
        });
        //导出数据
        return relationModels;
    }

    @Override
    public SiteRelationVO exportSiteRelation(SiteRelationExportDTO dto) throws Exception {
        BaseUtils.checkDTO(dto);
        dto.isValid();
        return siteRelationMgr.exportSiteRelation(dto).orElseThrow(() -> new ServiceException("同步站点关系【" + dto.getSiteId() + "】失败！"));
    }

    /**
     * @return com.trs.web.builder.base.RestfulResults<java.util.List < com.trs.gov.management.VO.SiteRelationVO>>
     * @Description 获取最热的站点数据
     * @Param [dto]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/12/28 18:15
     **/
    @Override
    public RestfulResults<List<HotSiteVO>> listHotSiteRelation(SiteRelationSearchDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return Try.of(() -> RestfulResults.ok(siteRelationMgr.listHotSiteRelation(dto)).addMsg("获取最热站点数据成功!")).onFailure(e -> {
            log.error(e.getMessage(), e);
        }).getOrElseGet(e ->
                RestfulResults.error("获取最热数据失败!")
        );
    }

    @Override
    public Map<String, Double> getGovNatureRate(SiteRelationSearchDTO dto) throws ServiceException {
        dto.setIsAll("1");
        //获取所有数据
        RestfulResults<List<SiteRelationVO>> listRestfulResults = querySiteRelationList(dto);
        //获取政府站点，这里通过包含关键词省市区县来界定
        List<SiteRelationVO> govNatureDatas = listRestfulResults.getDatas().stream().filter(siteRelationVO ->
                siteRelationVO.getSiteName().contains("省") ||
                        siteRelationVO.getSiteName().contains("市") ||
                        siteRelationVO.getSiteName().contains("区") ||
                        siteRelationVO.getSiteName().contains("县")).collect(Collectors.toList());
        Map<String, Double> map = new HashMap<>();
        double govNatureRate = listRestfulResults.getDatas().size() == 0 ? 1L : BigDecimal.valueOf(govNatureDatas.size()).divide(BigDecimal.valueOf(listRestfulResults.getDatas().size()), 4, BigDecimal.ROUND_HALF_UP).doubleValue();
        map.put("govNatureRate", govNatureRate);
        map.put("other", 1 - govNatureRate);
        return map;
    }

    @Override
    public void saveOrUpdateSiteRelationByHy(SiteRelationDTO siteRelationDTO) throws Exception {
            //校验参数
            BaseUtils.checkDTO(siteRelationDTO);
            //新建 通过site_id查询站点是否存在，存在，更新，否则，新增
           String exit =  siteRelationMgr.siteIdIsExit(siteRelationDTO);
           if ("false".equals(exit) == false){
               siteRelationDTO.setId(exit);
               siteRelationMgr.updateSiteRelationByHy(siteRelationDTO);
           }else {
            //新增
            siteRelationMgr.saveSiteRelationByHy(siteRelationDTO);
           }

    }

}
