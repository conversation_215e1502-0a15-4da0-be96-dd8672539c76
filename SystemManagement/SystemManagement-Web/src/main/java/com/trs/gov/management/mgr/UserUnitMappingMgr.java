package com.trs.gov.management.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.management.DO.UserUnitMappingDO;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.DTO.UserUnitMappingDTO;
import com.trs.gov.management.DTO.UserUnitMappingSearchDTO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.VO.UserUnitMappingBySearchVO;
import com.trs.gov.management.VO.UserUnitMappingVO;
import com.trs.gov.management.mapper.UserUnitMappingMapper;
import com.trs.gov.management.service.impl.UnitServiceImpl;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户单位映射业务类
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2021-01-05 15:36
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class UserUnitMappingMgr implements IUserUnitMappingMgr {

    @Autowired
    private UnitServiceImpl unitService;

    @Autowired
    private UserUnitMappingMapper mapper;

    /**
     * 获取用户单位映射列表<BR>
     *
     * @param dto 请求参数
     * @return 用户单位映射列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-06 15:04
     */
    @Override
    public RestfulResults<List<UserUnitMappingBySearchVO>> queryUserUnitMappingList(UserUnitMappingSearchDTO dto) throws ServiceException {
        QueryWrapper<UserUnitMappingDO> query = new QueryWrapper<>();

        if (!CMyString.isEmpty(dto.getUnitStatus())) {
            query.eq("status", dto.getUnitStatus());
        }

        if (!CMyString.isEmpty(dto.getUserName())) {
            query.eq("user_name", dto.getUserName());
        }

        if (!CMyString.isEmpty(dto.getUnitId())) {
            query.eq("unit_id", dto.getUnitId());
        }

        if (!CMyString.isEmpty(dto.getUnitIds())) {
            query.in("unit_id", Arrays.asList(CMyString.split(dto.getUnitIds(), ",")));
        }

        if (Optional.ofNullable(dto.getIsMaster()).isPresent()) {
            query.eq("is_master", dto.getIsMaster());
        }

        if (!CMyString.isEmpty(dto.getUnitName())) {
            query.eq("unit_name", dto.getUnitName());
        }

        if (!CMyString.isEmpty(dto.getUnitTypes())) {
            query.and(a -> {
                for (String s : CMyString.split(dto.getUnitTypes(), ",")) {
                    a.or().like("unit_type", s);
                }
            });
        }
        if (!CMyString.isEmpty(dto.getKeywords())) {
            query.and(a -> a.like("true_name", dto.getKeywords())
                    .or().like("unit_name", dto.getKeywords()));
        }


        Long total;
        List<UserUnitMappingBySearchVO> records;

        if (dto.getPageSize() == -1) {
            records = mapper.selectList(query).stream().map(item -> {
                UserUnitMappingBySearchVO vo = new UserUnitMappingBySearchVO();
                BaseUtils.copyProperties(item, vo);
                return vo;
            }).collect(Collectors.toList());
            total = records.stream().count();
        } else {
            Page<UserUnitMappingDO> page = mapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), query);
            records = page.getRecords().stream().map(item -> {
                UserUnitMappingBySearchVO vo = new UserUnitMappingBySearchVO();
                BaseUtils.copyProperties(item, vo);
                return vo;
            }).collect(Collectors.toList());
            total = page.getTotal();
        }
        return RestfulResults.ok(records)
                .addMsg("成功获取数据！")
                .addPageSize(dto.getPageSize())
                .addPageNum(dto.getPageNum())
                .addTotalCount(total);
    }

    /**
     * 同步映射关系<BR>
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 15:37
     */
    @Override
    public void syncUserUnitMapping() {
        // 1、获取单位数据列表
        UnitSearchDTO dto = new UnitSearchDTO();
        dto.setIsAll("1");
        dto.setRpcTag(true);
        unitService.queryUnitList(dto).onSuccess(list -> {
            // 2、循环更新相关映射
            list.forEach(unit -> {
                try {
                    List<UserVO> userVOList = unit.getUserVOS();
                    // 历史数据
                    List<UserUnitMappingVO> oldList = findMappingByUnitId(Long.toString(unit.getId()));
                    // 新数据
                    List<UserUnitMappingDTO> newList = mergeData(oldList, convertUnitVOAndUserListToMappingList(unit, userVOList));
                    // 1、删除原来的数据
                    if (deleteMappingByUnitId(Long.toString(unit.getId()))) {
                        updateMapping(newList);
                    }
                } catch (ServiceException e) {
                    log.error("同步人员单位映射出错!", e);
                }
            });
        }).onFailure(a -> log.error("查询单位列表出错，err={}", a.getMsg()));
    }

    public List<UserUnitMappingDTO> mergeData(List<UserUnitMappingVO> oldList, List<UserUnitMappingDTO> newList) {
        if (oldList.size() == 0) {
            return newList;
        }
        Map<String, Long> map = new HashMap<>(oldList.size());
        oldList.forEach(item -> map.put(String.format("%s_%s", item.getUnitId(), item.getUserName()), item.getId()));
        return newList.stream().map(item -> {
            item.setId(map.get(String.format("%s_%s", item.getUnitId(), item.getUserName())));
            return item;
        }).collect(Collectors.toList());
    }


    /**
     * 更新映射关系<BR>
     *
     * @param list 用户单位映射列表
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 15:47
     */
    @Override
    public void updateMapping(List<UserUnitMappingDTO> list) throws ServiceException {
        if (list == null || list.size() == 0) {
            return;
        }
        list.forEach(item -> {
            UserUnitMappingDO entity = new UserUnitMappingDO();
            BaseUtils.copyProperties(item, entity);
            mapper.insert(entity);
        });
    }

    /**
     * 将单位和人员列表转换成对应的人员单位映射列表<BR>
     *
     * @param unitVO     单位
     * @param userVOList 用户列表
     * @return 映射列表
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 16:28
     */
    public List<UserUnitMappingDTO> convertUnitVOAndUserListToMappingList(UnitVO unitVO, List<UserVO> userVOList) throws ServiceException {
        if (unitVO == null || unitVO.getId() == null || userVOList == null) {
            throw new ServiceException("数据不能为空！");
        }
        List<UserUnitMappingDTO> list = new ArrayList<>(userVOList.size());
        if (userVOList.size() > 0) {
            for (UserVO user : userVOList) {
                if (user == null) {
                    continue;
                }
                list.add(UserUnitMappingDTO.of(unitVO, user));
            }
        }
        return list;
    }

    /**
     * 根据单位ID查询相关映射关系列表<BR>
     *
     * @param unitId 单位ID
     * @return 映射关系列表
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 15:47
     */
    @Override
    public List<UserUnitMappingVO> findMappingByUnitId(String unitId) throws ServiceException {
        List<UserUnitMappingDO> list = mapper.selectList(new QueryWrapper<UserUnitMappingDO>().eq("unit_id", unitId));
        return list.stream().map(item -> {
            UserUnitMappingVO vo = new UserUnitMappingVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 删除相关单位的映射<BR>
     *
     * @param unitId 单位ID
     * @return 清除情况
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-05 15:52
     */
    @Override
    public boolean deleteMappingByUnitId(String unitId) throws ServiceException {
        mapper.delete(new QueryWrapper<UserUnitMappingDO>().eq("unit_id", unitId));
        return true;
    }
}
