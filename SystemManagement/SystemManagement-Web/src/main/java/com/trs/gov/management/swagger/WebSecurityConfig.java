package com.trs.gov.management.swagger;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@EnableWebMvc
public class WebSecurityConfig implements WebMvcConfigurer {

 @Override
 public void addResourceHandlers(ResourceHandlerRegistry registry) {
  registry.addResourceHandler("/swagger-ui.html")
          .addResourceLocations("classpath:/META-INF/resources/");
  registry.addResourceHandler("/doc.html")
          .addResourceLocations("classpath:/META-INF/resources/");

  registry.addResourceHandler("/img/**")
          .addResourceLocations("classpath:/META-INF/img/");

  registry.addResourceHandler("/webjars/**")
          .addResourceLocations("classpath:/META-INF/resources/webjars/");

 }
}