package com.trs.gov.management.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.Report;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.DO.WorkOrderTypeDO;
import com.trs.gov.management.DTO.*;
import com.trs.gov.management.VO.*;
import com.trs.gov.management.base.constant.WorkOrderTypeConstant;
import com.trs.gov.management.mapper.WorkOrderTypeMapper;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.web.builder.base.RestfulResults;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/16 11:39
 * @version 1.0
 * @since 1.0
 */
@Component
public class WorkOrderTypeMgr {

    @Autowired
    private WorkOrderTypeMapper typeMapper;

    @Reference(check = false, timeout = 60000)
    private IWorkOrderService workOrderService;

    @Autowired
    private UnitMgr unitMgr;

    public List<WorkOrderTypeVO> getWorkOrderTypes(WorkOrderTypeSearchDTO dto) throws Exception{
        //构造返回结果
        List<WorkOrderTypeVO> typeVOs = new ArrayList<>();
        //查询
        QueryWrapper<WorkOrderTypeDO> wapper = new QueryWrapper<>();
        if (StringUtils.isEmpty(dto.getId())) {
            //根据父id查询
            wapper.eq(StringUtils.isEmpty(dto.getParentId()) || "null".equals(dto.getParentId()), "parent_id", "0")
                    .eq(!(StringUtils.isEmpty(dto.getParentId()) || "null".equals(dto.getParentId())), "parent_id", dto.getParentId())
                    .eq(!StringUtils.isEmpty(dto.getStatus()), "status", dto.getStatus())
                    .eq("is_del", "0");
        }else {
            //根据id查询
            wapper.eq("id", dto.getId())
                    .eq(!StringUtils.isEmpty(dto.getStatus()), "status", dto.getStatus())
                    .eq("is_del", "0");
        }
        List<WorkOrderTypeDO> workOrderTypeDOS = typeMapper.selectList(wapper);
        //查询二级及以下的工单类型
        List<WorkOrderTypeDO> sonDOS = typeMapper.selectList(new QueryWrapper<WorkOrderTypeDO>().ne("parent_id", "0"));
        //将DO拷贝到VO
        for (WorkOrderTypeDO workOrderTypeDO : workOrderTypeDOS) {
            WorkOrderTypeVO workOrderTypeVO = new WorkOrderTypeVO();
            BaseUtils.copyProperties(workOrderTypeDO, workOrderTypeVO);
            workOrderTypeVO.setHasChild("false");
            if (2L==workOrderTypeDO.getId().longValue()){
                UnitSearchDTO unitSearchDTO = new UnitSearchDTO();
                if (workOrderTypeDO.getAcceptUnitId()!=null){
                    unitSearchDTO.setId(String.valueOf(workOrderTypeDO.getAcceptUnitId()));
                    UnitVO unitDetailById = unitMgr.getUnitDetailById(unitSearchDTO);
                    workOrderTypeVO.setAcceptUnit(unitDetailById.getUnitName());
                    workOrderTypeVO.setAcceptUnitId(String.valueOf(unitDetailById.getId()));
                }
            }
            //判断是否有子级
            for (WorkOrderTypeDO sonDO : sonDOS) {
                if (workOrderTypeDO.getId().longValue() == sonDO.getParentId().longValue())
                    workOrderTypeVO.setHasChild("true");
            }
            typeVOs.add(workOrderTypeVO);
        }
        return typeVOs;
    }

    public Report saveWorkOrderType(WorkOrderTypeDTO dto) throws Exception {

        //判断类型名称是否重复
        WorkOrderTypeDO typeDO = typeMapper.selectOne(new QueryWrapper<WorkOrderTypeDO>()
                .eq("type_name", dto.getTypeName())
                .eq("parent_id",dto.getParentId())
                .eq("is_del", "0"));
        if (typeDO != null) {
            throw new Exception("该层级类型名称已存在");
        }
        //判断父类是否为三级工单类型
        WorkOrderTypeDO typeParentDO = typeMapper.selectOne(new QueryWrapper<WorkOrderTypeDO>()
                .eq("id", dto.getParentId())
                .eq("is_del", "0"));
        if (typeParentDO.getLevel().longValue()==WorkOrderTypeConstant.THIRD_LEVEL){
            throw new Exception("第三级工单类型下不允许新建工单类型");
        }
        typeDO = new WorkOrderTypeDO();
        //添加实体属性值
        BaseUtils.copyProperties(dto, typeDO);
        //设置类型等级
        if (WorkOrderTypeConstant.TOP_ID.longValue() == (dto.getParentId().longValue())) {
            typeDO.setLevel(WorkOrderTypeConstant.FIRST_LEVEL);
        } else if (Arrays.asList(WorkOrderTypeConstant.FIRST_ID).contains(dto.getParentId().longValue())) {
            typeDO.setLevel(WorkOrderTypeConstant.SECOND_LEVEL);
        } else {
            typeDO.setLevel(WorkOrderTypeConstant.THIRD_LEVEL);
        }
        //保存
        typeMapper.insert(typeDO);
        return new Report("添加工单类型", "添加工单类型成功", Report.RESULT.SUCCESS);
    }

    public Report updateWorkOrderType(WorkOrderTypeDTO dto) throws Exception {
        //判断类型是否存在
        WorkOrderTypeDO typeDO = typeMapper.selectOne(new QueryWrapper<WorkOrderTypeDO>()
                .eq("id", dto.getId())
                .eq("is_del", "0"));
        if (typeDO == null) {
            throw new Exception("工单类型不存在");
        }
        //判断类型名称是否重复
        WorkOrderTypeDO typeDO2 = typeMapper.selectOne(new QueryWrapper<WorkOrderTypeDO>()
                .eq("type_name", dto.getTypeName())
                .eq("parent_id",dto.getParentId())
                .ne("id",dto.getId())
                .eq("is_del", "0"));
        if (typeDO2 != null) {
            throw new Exception("该层级类型名称已存在");
        }
        //修改属性值
        BaseUtils.copyProperties(dto, typeDO);
        typeDO.setUpdateTime(new Date());
        //保存
        typeMapper.updateById(typeDO);
        return new Report("编辑工单类型", "编辑工单类型成功", Report.RESULT.SUCCESS);
    }

    public List<Report> operWorkOrderType(OperWorkOrderTypeDTO dto) throws Exception {
        //获取待操作类型数据
        List<WorkOrderTypeDO> workOrderTypeDOS = typeMapper.selectBatchIds(Arrays.asList(dto.getId().split(",")));
        List<Long> collect = workOrderTypeDOS.stream().map(workOrderTypeDO -> workOrderTypeDO.getId()).collect(Collectors.toList());
        for (Long firstId : WorkOrderTypeConstant.FIRST_ID) {
            if (collect.contains(firstId)){
                throw new Exception("顶级工单类型不允许进行操作");
            }
        }
        //如果是二级工单类型，获取二级工单类型未删除数量
        Integer notDelCount = typeMapper.selectCount(new QueryWrapper<WorkOrderTypeDO>().eq("is_del", 0)
                .eq(workOrderTypeDOS.get(0).getLevel()==2,"parent_id", workOrderTypeDOS.get(0).getParentId()));
        //如果是二级工单类型，获取二级工单类型未停用数量
        Integer notStopCount = typeMapper.selectCount(new QueryWrapper<WorkOrderTypeDO>().eq("status", 1)
                .eq(workOrderTypeDOS.get(0).getLevel()==2,"parent_id", workOrderTypeDOS.get(0).getParentId())
                .eq("is_del", 0));
        //获取待操作工单类型下的子级工单
        List<WorkOrderTypeDO> workOrderTypeDOS1 = typeMapper.selectList(new QueryWrapper<WorkOrderTypeDO>().in("parent_id", collect)
                .eq("is_del", 0));
        List<Report> reports = new ArrayList<>();
        //判断是修改状态还是删除
        //启用/停用
        if (dto.getStatus() != null) {
            if (dto.getStatus()==-1&&notStopCount == workOrderTypeDOS.size()) {
                throw new Exception("二级工单类型不能全部停用");
            }
            //获取停用工单类型的子级类型
            workOrderTypeDOS.addAll(workOrderTypeDOS1);
            for (WorkOrderTypeDO workOrderTypeDO : workOrderTypeDOS) {
                workOrderTypeDO.setStatus(dto.getStatus());
                typeMapper.updateById(workOrderTypeDO);
                reports.add(new Report("启用/停用", "启用/停用【" + workOrderTypeDO.getTypeName() + "】工单类型成功", Report.RESULT.SUCCESS));
            }
        }
        //删除
        if (dto.getIsDel() != null) {
            List<Long> datas = workOrderService.searchUsedWorkOrderTypeId().getDatas();
            if (notDelCount == workOrderTypeDOS.size()) {
                throw new Exception("二级工单类型不能全部删除");
            }
            if(workOrderTypeDOS1.size()>0){
                throw new Exception("该工单类型下有子级工单类型，不能删除");
            }
            for (WorkOrderTypeDO workOrderTypeDO : workOrderTypeDOS) {
                if (datas.contains(workOrderTypeDO.getId())){
                    throw new Exception("删除【" + workOrderTypeDO.getTypeName() + "】工单类型失败,该工单类型下有工单");
                }else {
                    workOrderTypeDO.setIsDel(dto.getIsDel());
                    typeMapper.updateById(workOrderTypeDO);
                    reports.add(new Report("删除", "删除【" + workOrderTypeDO.getTypeName() + "】工单类型成功", Report.RESULT.SUCCESS));
                }
            }
        }
        return reports;
    }

    public WorkOrderTypeForNoticeVO getParentInfo(String id) {
        WorkOrderTypeForNoticeVO noticeVO = new WorkOrderTypeForNoticeVO();
        WorkOrderTypeDO workOrderTypeDO = typeMapper.selectOne(new QueryWrapper<WorkOrderTypeDO>()
                .eq("id", id));
        if (workOrderTypeDO != null && workOrderTypeDO.getParentId() != null) {
            BaseUtils.copyProperties(workOrderTypeDO,noticeVO);
        }
        return noticeVO;
    }

    public List<WorkOrderTypeLevelVO> queryWorkOrderTypeByLevel(WorkOrderTypeSearchDTO dto) {
        //构造返回
        List<WorkOrderTypeLevelVO> results = new ArrayList<>();
        //根据类型等级查询
        List<WorkOrderTypeDO> workOrderTypeDOS = typeMapper.selectList(new QueryWrapper<WorkOrderTypeDO>()
                .eq("level", dto.getLevel())
                .eq("is_del",0)
                .eq(!StringUtils.isEmpty(dto.getStatus()),"status",dto.getStatus()));
        for (WorkOrderTypeDO workOrderTypeDO : workOrderTypeDOS) {
            WorkOrderTypeLevelVO typeLevelVO = new WorkOrderTypeLevelVO();
            BaseUtils.copyProperties(workOrderTypeDO, typeLevelVO);
            results.add(typeLevelVO);
        }
        return results;
    }

    /**
     * @Description  获取id列表
     * @Param [workOrderTypeSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/30 10:55
     **/
    public RestfulResults<List<Long>> listAllRelateTypeByRootType(WorkOrderTypeSearchDTO workOrderTypeSearchDTO) throws ServiceException {
        WorkOrderTypeDO workOrderTypeDO = typeMapper.selectOne(new QueryWrapper<WorkOrderTypeDO>().eq("root_key", workOrderTypeSearchDTO.getRootKey()));
        if(workOrderTypeDO==null){
            throw new ServiceException("数据异常,数据库中无key为["+workOrderTypeSearchDTO.getRootKey()+"]的类型");
        }
        Long workOrderTypeId = workOrderTypeDO.getId();
        //层级依次往上，方便扩展
        Integer level = typeMapper.selectMaxLevel();
        List<Long> result = new ArrayList<>();
        result.add(workOrderTypeId);
        List<Long> query = Arrays.asList(workOrderTypeId);
        for (int i = 0; i < level; i++) {
            Optional<List<Long>> list = getList(query,result);
            if(!list.isPresent()){
                break;
            }
            query = list.get();
        }
        return RestfulResults.ok(result);
    }

    /**
     * @Description  根据工单类型id的获取子级id列表，包括本级
     * @Param [workOrderTypeSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/10 11:36
     **/
    public RestfulResults<List<Long>> listAllRelateTypeById(WorkOrderTypeSearchDTO workOrderTypeSearchDTO) throws ServiceException {
        String[] ids = workOrderTypeSearchDTO.getId().split(",");
        //层级依次往上，方便扩展
        Integer level = typeMapper.selectMaxLevel();
        List<Long> result = new ArrayList<>();
        for (String id : ids) {
            result.add(Long.valueOf(id));
        }
        List<Long> query = result;
        for (int i = 0; i < level; i++) {
            Optional<List<Long>> list = getList(query,result);
            if(!list.isPresent()){
                break;
            }
            query = list.get();
        }
        return RestfulResults.ok(result);
    }

    /**
     * @Description 根据父类型list类型ID,获取子集
     * @Param [ids, result]
     * @return java.util.Optional<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/29 18:17
     **/
    public Optional<List<Long>> getList(List<Long> ids,List<Long> result){
        List<Long> temp = null;
        if(ids == null){
            Optional.empty();
        }
        List<WorkOrderTypeDO> workOrderTypeDOList = typeMapper.selectList(new QueryWrapper<WorkOrderTypeDO>()
                .in("parent_id",ids)
                .eq("is_del",0)
                .eq("status","1"));
        if(workOrderTypeDOList != null || !workOrderTypeDOList.isEmpty()){
            temp = workOrderTypeDOList.stream().map(a->a.getId()).collect(Collectors.toList());
            result.addAll(temp);
        }
        if(temp == null || temp.isEmpty()){
            return Optional.empty();
        }
        return Optional.ofNullable(temp);
    }

    public List<ParentWorkOrderTypeVO> getParentWorkOrderTypes(WorkOrderTypeSearchDTO dto) {
        List<ParentWorkOrderTypeVO> workOrderTypeDOS = new ArrayList<>();
        if (!StringUtils.isEmpty(dto.getId())) {
            workOrderTypeDOS = typeMapper.selectParent(dto.getId().split(","));
        }
        return workOrderTypeDOS;
    }

    public void addAcceptUnit(AddAcceptUnitDTO dto) throws Exception{
        WorkOrderTypeDO workOrderTypeDO  = Optional.ofNullable(typeMapper.selectById(dto.getId())).orElseThrow(() -> new Exception(
                String.format("不存在类型id为【%s】的工单类型", dto.getId())));
        workOrderTypeDO.setAcceptUnitId(dto.getAcceptUnitId());
        //更新保存
        typeMapper.updateById(workOrderTypeDO);
    }
}
