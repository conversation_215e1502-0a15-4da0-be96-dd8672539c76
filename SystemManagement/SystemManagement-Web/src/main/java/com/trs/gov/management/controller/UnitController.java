package com.trs.gov.management.controller;

import com.alibaba.fastjson.JSONArray;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ExcelUtil;
import com.trs.gov.management.DTO.UnitDTO;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.DTO.UnitStateDTO;
import com.trs.gov.management.DTO.UnitUserDTO;
import com.trs.gov.management.VO.UnitAndSiteRelationsVO;
import com.trs.gov.management.VO.UnitSearchVO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.gov.management.base.constant.UnitConstant;
import com.trs.gov.management.service.impl.UnitServiceImpl;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/17 17:22
 * @version 1.0
 * @since 1.0
 */
@Api(tags = "单位配置API")
@RestController
@RequestMapping("/management/unit")
public class UnitController {

    @Autowired
    private UnitServiceImpl unitService;

    /**
     * 查询单位列表<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/17 17:26
     */
    @ApiOperation(value = "查询单位列表")
    @RequestMapping(value = "queryUnitList", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults queryUnitList(UnitSearchDTO dto) {
        return unitService.queryUnitList(dto);
    }

    /**
     * 查询单位详情<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/17 17:26
     */
    @ApiOperation(value = "查询单位详情")
    @RequestMapping(value = "queryUnitInfo", method = {RequestMethod.GET})
    public RestfulResults queryUnitInfo(UnitSearchDTO dto) {
        return unitService.queryUnitList(dto);
    }

    /**
     * 新建/编辑单位<BR>
     *
     * @param dto 请求参数
     * @return 保存结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/17 17:27
     */
    @ApiOperation(value = "新建/编辑单位")
    @RequestMapping(value = "saveOrUpdateUnit", method = {RequestMethod.POST})
    public RestfulResults saveOrUpdateUnit(UnitDTO dto) throws Exception {
        return unitService.saveOrUpdateUnit(dto);
    }

    /**
     * 启用/停用单位<BR>
     *
     * @param dto 请求参数
     * @return 保存结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/17 17:28
     */
    @ApiOperation(value = "启用/停用单位")
    @RequestMapping(value = "operUnitState", method = {RequestMethod.GET})
    public RestfulResults operUnitState(UnitStateDTO dto) {
        return unitService.operUnitState(dto);
    }

    /**
     * 根据组织获取用户、根据用户获取所在组织<BR>
     *
     * @param dto 请求参数
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 17:32
     */
    @ApiOperation(value = "根据组织获取用户、根据用户获取所在组织")
    @RequestMapping(value = "queryUnitUserList", method = {RequestMethod.GET}, produces = "application/json; charset=utf-8")
    RestfulResults<JSONArray> queryUnitUserList(UnitUserDTO dto) {
        return unitService.queryUnitUserList(dto);
    }

    /**
     * 查询用户列表<BR>
     *
     * @param dto 请求参数
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 20:27
     */
    @ApiOperation(value = "查询用户列表")
    @RequestMapping(value = "getAllUser", method = {RequestMethod.GET})
    RestfulResults<List<UserVO>> getAllUser(UserSearchDTO dto) {
        return unitService.getAllUser(dto);
    }


    /**
     * 查询单位列表(根据单位类型等值查询)<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/17 17:26
     */
    @ApiOperation(value = "查询单位列表(根据单位类型等值查询)")
    @RequestMapping(value = "queryUnitByType", method = {RequestMethod.GET})
    public RestfulResults queryUnitByType(UnitSearchDTO dto) {
        return unitService.queryUnitByType(dto);
    }

    /**
     * 通过用户名查询单位列表<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/17 17:26
     */
    @ApiOperation(value = "通过用户名查询单位列表")
    @RequestMapping(value = "queryUnitByUser", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults queryUnitByUser(UserSearchDTO dto) {
        return unitService.queryUnitByUser(dto);
    }

    /**
     * 获取单位绑定站点关系信息<BR>
     *
     * @param
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/16 16:32
     */
    @ApiOperation(value = "获取单位绑定站点关系信息")
    @RequestMapping(value = "getUnitAndSiteRelations", method = {RequestMethod.GET})
    public RestfulResults<List<UnitAndSiteRelationsVO>> getUnitAndSiteRelations(UnitSearchDTO dto) {
        return unitService.getUnitAndSiteRelations(dto);
    }

    /**
     * 导出单位列表<BR>
     *
     * @param
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/23 17:56
     */
    @ApiOperation(value = "导出单位列表")
    @RequestMapping(value = "exportUnit", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportUnit(HttpServletResponse response, UnitSearchDTO dto) throws ServiceException {
        ExcelUtil.exportExcel(response, unitService.exportUnit(dto), UnitConstant.getFileName(dto.getUnitType()).get(), UnitConstant.getFileName(dto.getUnitType()).get());
    }

    /**
     * 获取单位详情<BR>
     *
     * @param dto 传递参数
     * @return
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/23 16:49
     */
    @ApiOperation(value = "获取单位详情")
    @RequestMapping(value = "getUnitDetailById", method = {RequestMethod.GET})
    public RestfulResults<UnitVO> getUnitDetailById(UnitSearchDTO dto) throws Exception {
        return unitService.getUnitDetailById(dto);
    }

    // TODO 调试用的，后期记得删除或加上权限校验
    @RequestMapping(value = "queryBaseChildrenUnitList", method = {RequestMethod.GET})
    public RestfulResults<List<UnitVO>> queryBaseChildrenUnitList(UnitSearchDTO dto) {
        return unitService.queryBaseChildrenUnitList(dto);
    }

    /**
     * 获取检索所需单位信息接口<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/2 15:54
     * @param  dto 请求参数
     * @throws
     * @return
     */
    @ApiOperation(value = "获取检索所需单位信息接口")
    @RequestMapping(value ="getAllUnitForSearch",method = RequestMethod.GET)
    public RestfulResults<List<UnitSearchVO>> getAllUnitForSearch(UnitSearchDTO dto){
        return unitService.getAllUnitForSearch(dto);
    }

    /**
     * 根据单位id获取用户数量<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/16 21:07
     * @param
     * @throws
     * @return
     */
    @RequestMapping(value ="getUserCountByUnitIds",method = RequestMethod.GET)
    public RestfulResults<List<UserCountVO>> getUserCountByUnitIds(String ids){
        return unitService.getUserCountByUnitIds(ids);
    }

    /**
     * 查询有权限的单位列表(根据单位类型等值查询)<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * @throws
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/17 17:26
     */
    @ApiOperation(value = "查询有权限的单位列表(根据单位类型等值查询)")
    @RequestMapping(value = "queryHasRightUnitByType", method = {RequestMethod.GET})
    public RestfulResults queryHasRightUnitByType(UnitSearchDTO dto) {
        return unitService.queryHasRightUnitByType(dto);
    }

    /**
     * 获取检索所需单位信息接口(通知组件使用)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/2 15:54
     * @param  dto 请求参数
     * @throws
     * @return
     */
    @ApiOperation(value = "获取检索所需单位信息接口(通知组件使用)")
    @RequestMapping(value = "getAllUnitForTongZhiSearch", method = {RequestMethod.GET})
    public RestfulResults<List<UnitSearchVO>> getAllUnitForTongZhiSearch(UnitSearchDTO dto){
        return unitService.getAllUnitForTongZhiSearch(dto);
    }
}
