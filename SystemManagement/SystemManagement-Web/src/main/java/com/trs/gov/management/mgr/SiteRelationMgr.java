package com.trs.gov.management.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Report;
import com.trs.common.base.Reports;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.externalSystem.DTO.RightSearchDTO;
import com.trs.gov.externalSystem.service.RightService;
import com.trs.gov.management.DO.SiteRelationDO;
import com.trs.gov.management.DTO.SiteNameDTO;
import com.trs.gov.management.DTO.SiteRelationDTO;
import com.trs.gov.management.DTO.SiteRelationExportDTO;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.VO.GetAllSitesByTypeVO;
import com.trs.gov.management.VO.HotSiteVO;
import com.trs.gov.management.VO.ProductsVO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.management.base.constant.BaseConstant;
import com.trs.gov.management.base.enums.MediaType;
import com.trs.gov.management.mapper.SiteRelationMapper;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.upms.client.mgr.UpmsRightMgr;
import com.trs.user.DTO.UserDTO;
import com.trs.user.service.IUserService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/16 11:37
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class SiteRelationMgr {

    @Autowired
    private SiteRelationMapper siteMapper;

    @Autowired
    private UpmsRightMgr upmsRightMgr;

    @Reference(check = false, timeout = 60000)
    private IUserService service;

    @Reference(check = false, timeout = 60000)
    private IWorkOrderService workOrderService;

    @Reference(check = false, timeout = 60000)
    private RightService rightService;

    public Report saveSiteRelation(SiteRelationDTO dto) throws Exception{
        //判断站点信息是否存在
        siteRelationInfoIsExist(dto);
        //设置实体属性值
        SiteRelationDO siteRelationDO = new SiteRelationDO();
        BaseUtils.copyProperties(dto,siteRelationDO);
        this.setOtherProperties(siteRelationDO, dto);
        //保存
        siteMapper.insert(siteRelationDO);
        return new Report("新建站点","新建站点成功");
    }

    public Report updateSiteRelation(SiteRelationDTO dto) throws Exception{
        //查询该站点关系是否存在
        SiteRelationDO siteRelationDO = siteMapper.selectOne(new QueryWrapper<SiteRelationDO>()
                .eq("id", dto.getId())
                .eq("is_del", 0));
        if (siteRelationDO == null) {
            throw new Exception("站点关系不存在");
        }
        //判断站点信息是否存在
        siteRelationInfoIsExist(dto);
        BaseUtils.copyProperties(dto,siteRelationDO);
        //修改属性值
        this.setOtherProperties(siteRelationDO, dto);
        //保存
        siteMapper.updateById(siteRelationDO);
        return new Report("编辑站点关系","编辑站点关系成功");
    }

    public Page<SiteRelationDO> querySiteRelationList(SiteRelationSearchDTO dto) throws Exception{
        //过滤站点关系数据
        if (!siteRightFilter(dto)){
            return new Page<>();
        }
        QueryWrapper<SiteRelationDO> wrapper = new QueryWrapper<>();
        Optional.ofNullable(dto.getSiteId()).ifPresent(siteId->wrapper.in("site_id",Arrays.asList(siteId.split(","))));
        //构造查询
        wrapper.and(wrapper_->{
            wrapper_.like(!StringUtils.isEmpty(dto.getSiteName()), "site_name", dto.getSiteName())
                    .eq(!StringUtils.isEmpty(dto.getMediaType()), "media_type", dto.getMediaType())
                    .eq(!StringUtils.isEmpty(dto.getHostUnitId()), "host_unit_id", dto.getHostUnitId())
                    .eq(!StringUtils.isEmpty(dto.getMasterUnitId()), "master_unit_id", dto.getMasterUnitId())
                    .eq(!StringUtils.isEmpty(dto.getOperationUnitId()), "operation_unit_id", dto.getOperationUnitId())
                    .between(!(StringUtils.isEmpty(dto.getFilingStartTime()) && StringUtils.isEmpty(dto.getFilingEndTime())), "filing_time", dto.getFilingStartTime(), dto.getFilingEndTime())
                    .between(!(StringUtils.isEmpty(dto.getConstructionStartTime()) && StringUtils.isEmpty(dto.getConstructionEndTime())), "construction_time", dto.getConstructionStartTime(), dto.getConstructionEndTime())
                    .ge(!StringUtils.isEmpty(dto.getOperationStartTime()), "operation_start_time", dto.getOperationStartTime())
                    .le(!StringUtils.isEmpty(dto.getOperationEndTime()),"operation_end_time", dto.getOperationEndTime())
                    .eq("is_del",0);
        });
        wrapper.and(!StringUtils.isEmpty(dto.getUnitIdAsHostUnitId())
                            ||!StringUtils.isEmpty(dto.getUnitIdAsOperUnitId()),wrapper_->{
            wrapper_.eq(!StringUtils.isEmpty(dto.getUnitIdAsHostUnitId()), "host_unit_id", dto.getUnitIdAsHostUnitId())
                    .or().eq(!StringUtils.isEmpty(dto.getUnitIdAsOperUnitId()), "operation_unit_id", dto.getUnitIdAsOperUnitId());
            wrapper_.and(wrapper_1->wrapper_1.eq("is_del",0));
        });
        wrapper.orderByDesc("cr_time");
        //设置分页
        Page<SiteRelationDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        if ("1".equals(dto.getIsAll())){
            List<SiteRelationDO> siteRelationDOS = siteMapper.selectList(wrapper);
            page.setTotal(siteRelationDOS.size());
            page.setRecords(siteRelationDOS);
            return page;
        }
        //执行查询
        Page<SiteRelationDO> siteRelationDOPage = siteMapper.selectPage(page, wrapper);
        return siteRelationDOPage;
    }

    //过滤站点关系数据
    private Boolean siteRightFilter(SiteRelationSearchDTO dto)throws Exception{
        if (dto.getRpcTag()){
            return true;
        }
        //当前用户所在当前单位是否有关于站点关系的任何数据范围权限
        Boolean hasAnyRight = false;
        String userName = ContextHelper.getLoginUser().orElseThrow(() -> new Exception("当前用户不能为空"));
        String unitId = ContextHelper.getLoginUnitId().orElseThrow(() -> new Exception("当前用户单位id不能为空"));
        RightSearchDTO rightSearchDTO = new RightSearchDTO();
        rightSearchDTO.setUserName(userName);
        rightSearchDTO.setOperId(unitId);
        //获取权限位
        List<String> datas = rightService.getMyRightOper(rightSearchDTO).getDatas();
        //权限过滤
        if (datas.size()==0
                ||(!datas.contains(BaseConstant.OWNOPERUNITSITE)
                &&!datas.contains(BaseConstant.OWNHOSTUNITSITE))){
            hasAnyRight = false;
        }
        if (upmsRightMgr.isAdmin(userName)||datas.contains(BaseConstant.ALLSITE)){
            hasAnyRight = true;
        }
        if (!upmsRightMgr.isAdmin(userName)
                &&!datas.contains(BaseConstant.ALLSITE)
                &&datas.contains(BaseConstant.OWNOPERUNITSITE)){
            hasAnyRight = true;
            dto.setUnitIdAsOperUnitId(unitId);
        }
        if (!upmsRightMgr.isAdmin(userName)
                &&!datas.contains(BaseConstant.ALLSITE)
                &&datas.contains(BaseConstant.OWNHOSTUNITSITE)){
            hasAnyRight = true;
            dto.setUnitIdAsHostUnitId(unitId);
        }
        return hasAnyRight;

    }

    public Reports deleteSiteRelation(String id) throws Exception{
        //构造返回结果
        Reports reports = Reports.createNewReports("删除站点");
        //获取对象站点关系实体
        List<SiteRelationDO> siteRelationDOS = siteMapper.selectBatchIds(Arrays.asList(id.split(",")));
        RestfulResults<List<Long>> listRestfulResults = workOrderService.searchUsedSiteId();
        //伪删除
        for (SiteRelationDO siteRelationDO : siteRelationDOS) {
            if (listRestfulResults.getDatas().size()>0&&listRestfulResults.getDatas().contains(siteRelationDO.getSiteId())){
                reports.error(String.format("站点【%s】有正在被使用的工单，不能删除",siteRelationDO.getSiteName()));
            }else {
                siteRelationDO.setIsDel(1);
                siteMapper.updateById(siteRelationDO);
                reports.success(String.format("删除站点【%s】成功",siteRelationDO.getSiteName()));
            }
        }
        return reports;
    }

    //设置实体属性
    private void setOtherProperties(SiteRelationDO siteRelationDO, SiteRelationDTO dto) throws ServiceException {
        siteRelationDO.setFilingTime(TimeUtils.stringToDate(dto.getFilingTime()));
        siteRelationDO.setConstructionTime(TimeUtils.stringToDate(dto.getConstructionTime()));
        siteRelationDO.setOperationStartTime(TimeUtils.stringToDate(dto.getOperationStartTime()));
        siteRelationDO.setOperationEndTime(TimeUtils.stringToDate(dto.getOperationEndTime()));
        siteRelationDO.setUpdateTime(new Date());
        UserDTO userDTO = new UserDTO();
        userDTO.setUserName(siteRelationDO.getCrUser());
        siteRelationDO.setTrueName(service.getBaseUserInfo(userDTO).getTrueName());
    }

    public String nameIsExist(SiteNameDTO dto) {
        //判断站点名称是否存在
        List<SiteRelationDO> siteRelationDOs = siteMapper.selectList(new QueryWrapper<SiteRelationDO>()
                .eq(dto.getSiteId()!=null,"site_id", dto.getSiteId())
                .eq(dto.getMediaType()!=null,"media_type",dto.getMediaType())
                .eq(dto.getMonitorSiteId()!=null,"monitor_site_id",dto.getMonitorSiteId())
                .eq(!StringUtils.isEmpty(dto.getSiteName()),"site_name", dto.getSiteName())
                .eq(!StringUtils.isEmpty(dto.getMonitorSite()),"monitor_site",dto.getMonitorSite())
                .eq("is_del", "0"));
        if (siteRelationDOs!=null&&siteRelationDOs.size()>0){
            return "true";
        }
        return "false";
    }

    public List<Map> getMediaTypeInfo() {
        List<Map> results = new ArrayList<>();
        //获取渠道类型相关数据
        Map<Integer, String> mediaType = MediaType.getMediaType();
        //构造返回数据
        for (Map.Entry<Integer, String> type : mediaType.entrySet()) {
           Map<String,Object> resultMap = new HashMap();
           resultMap.put("mediaType",type.getKey());
           resultMap.put("mediaName",type.getValue());
           results.add(resultMap);
        }
        return results;
    }

    public List<GetAllSitesByTypeVO> getWorkOrderSysSites() {
        String currentDate = TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD);
        //查询
        List<SiteRelationDO> siteRelationDOS = siteMapper.selectList(new QueryWrapper<SiteRelationDO>()
                .eq("is_del", 0)
                .le("operation_start_time",currentDate)
                .ge("operation_end_time", currentDate));
        //将站点信息根据mediaType分类
        List<GetAllSitesByTypeVO> sitesByTypeVOS = new ArrayList<>();
        Map<Integer, String> mediaTypes = MediaType.getMediaType();
        //遍历分类
        for (Map.Entry<Integer, String> mediaType : mediaTypes.entrySet()) {
            if (mediaType.getKey()!=0){
                GetAllSitesByTypeVO sitesByTypeVO = new GetAllSitesByTypeVO();
                sitesByTypeVO.setMediaType(StringUtils.toStringValue(mediaType.getKey()));
                sitesByTypeVO.setMediaName(mediaType.getValue());
                List<ProductsVO> productsVOS = new ArrayList<>();
                //遍历站点列表
                for (SiteRelationDO siteRelationDO : siteRelationDOS) {
                    ProductsVO productsVO = new ProductsVO();
                    if (siteRelationDO.getMediaType()==mediaType.getKey()){
                        productsVO.setSiteName(siteRelationDO.getSiteName());
                        productsVO.setHostUnitId(StringUtils.toStringValue(siteRelationDO.getHostUnitId()));
                        productsVO.setOperationUnitId(StringUtils.toStringValue(siteRelationDO.getOperationUnitId()));
                        productsVO.setSiteId(Long.toString(siteRelationDO.getSiteId()));
                        productsVO.setHostUnit(siteRelationDO.getHostUnit());
                        productsVO.setOperationUnit(siteRelationDO.getOperationUnit());
                        productsVOS.add(productsVO);
                    }
                }
                sitesByTypeVO.setProducts(productsVOS);
                sitesByTypeVOS.add(sitesByTypeVO);
            }
        }
        return sitesByTypeVOS;
    }

    public void siteRelationInfoIsExist(SiteRelationDTO dto) throws Exception{
        //判断站点信息是否存在
        if (dto.getId().equals("0")) {
            QueryWrapper<SiteRelationDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(wrapper ->wrapper
                    .eq("site_id", dto.getSiteId())
                    .eq("media_type",dto.getMediaType())
                    .or().eq("unique_id", dto.getUniqueId())
                    .or().eq(!StringUtils.isEmpty(dto.getMonitorSite()), "monitor_site", dto.getMonitorSite()));
            queryWrapper.eq("is_del", "0");
            List<SiteRelationDO> siteRelationDOS = siteMapper.selectList(queryWrapper);
            judgeInfos(siteRelationDOS,dto);
        }else {
            QueryWrapper<SiteRelationDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(wrapper ->wrapper
                    .eq("site_id", dto.getSiteId())
                    .eq("media_type",dto.getMediaType())
                    .or().eq("unique_id", dto.getUniqueId())
                    .or().eq(!StringUtils.isEmpty(dto.getMonitorSite()), "monitor_site", dto.getMonitorSite()));
            queryWrapper.ne("id",dto.getId())
                    .eq("is_del", "0");
            List<SiteRelationDO> siteRelationDOS = siteMapper.selectList(queryWrapper);
            judgeInfos(siteRelationDOS,dto);
        }

    }
    public void judgeInfos(List<SiteRelationDO> siteRelationDOS,SiteRelationDTO dto) throws Exception{
        List<Long> siteIds = siteRelationDOS.stream().map(siteRelationDO -> siteRelationDO.getSiteId()).collect(Collectors.toList());
        List<Integer> mediaTypes = siteRelationDOS.stream().map(siteRelationDO -> siteRelationDO.getMediaType()).collect(Collectors.toList());
        List<String> uniqueIds = siteRelationDOS.stream().map(siteRelationDO -> siteRelationDO.getUniqueId()).collect(Collectors.toList());
        List<String> monitorSites = siteRelationDOS.stream().map(siteRelationDO -> siteRelationDO.getMonitorSite()).collect(Collectors.toList());
        if (siteRelationDOS.size()>0){
            if (siteIds.contains(dto.getSiteId())&&mediaTypes.contains(dto.getMediaType())){
                throw new Exception("改类型下站点id已存在");
            }
            if (uniqueIds.contains(dto.getUniqueId())){
                throw new Exception("唯一标识已存在");
            }
            if (monitorSites.contains(dto.getMonitorSite())){
                throw new Exception("监测云站点已存在");
            }
        }
    }

    public Optional<SiteRelationVO> exportSiteRelation(SiteRelationExportDTO dto) throws Exception {
        SiteRelationDTO siteRelationDTO = new SiteRelationDTO();
        BaseUtils.copyProperties(dto,siteRelationDTO);
        //判断站点信息是否存在
        siteRelationDTO.setId("0");
        siteRelationInfoIsExist(siteRelationDTO);
        //设置实体属性值
        SiteRelationDO siteRelationDO = new SiteRelationDO();
        BaseUtils.copyProperties(dto,siteRelationDO);
        //保存
        siteRelationDO.setExportFromOther(1);
        int result = siteMapper.insert(siteRelationDO);
        if(result <= 0){
            throw new ServiceException("同步数据id["+dto.getSiteId()+"]失败!");
        }
        SiteRelationVO siteRelationVO = new SiteRelationVO();
        BaseUtils.copyProperties(siteRelationDO,siteRelationVO);
        return Optional.of(siteRelationVO);
    }



    public List<HotSiteVO> listHotSiteRelation(SiteRelationSearchDTO dto){
        //构造返回结果
        List<HotSiteVO> resultVOS = new ArrayList<>();
        String currentDate = TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD);
        dto.setOperationTime(currentDate);
        List<HotSiteVO> hotSiteVOS = siteMapper.selectHotSite(dto);
        resultVOS.addAll(hotSiteVOS);
        //数量没有三个根据时间倒排
        if (hotSiteVOS.size() < dto.getHotSiteCount()){
            dto.setHotSiteCount(dto.getHotSiteCount() - hotSiteVOS.size());
            dto.setDistinctField(hotSiteVOS.stream().map(HotSiteVO::getDistinctField).collect(Collectors.toList()));
            List<HotSiteVO> hotSiteByCrTime = siteMapper.selectHotSiteByCrTime(dto);
            resultVOS.addAll(hotSiteByCrTime);
        }
        return resultVOS;
    }

    public String siteIdIsExit(SiteRelationDTO siteRelationDTO) {
        //判断站点名称是否存在
        List<SiteRelationDO> siteRelationDOs = siteMapper.selectList(new QueryWrapper<SiteRelationDO>()
                .eq(siteRelationDTO.getSiteName()!=null,"site_name", siteRelationDTO.getSiteName())
                .eq(siteRelationDTO.getMediaType()!=null,"media_type",siteRelationDTO.getMediaType())
                .eq(siteRelationDTO.getMonitorSiteId()!=null,"monitor_site_id",siteRelationDTO.getMonitorSiteId())
                //.eq(!StringUtils.isEmpty(siteRelationDTO.getSiteName()),"site_name", siteRelationDTO.getSiteName())
                .eq(!StringUtils.isEmpty(siteRelationDTO.getMonitorSite()),"monitor_site",siteRelationDTO.getMonitorSite())
                .eq("is_del", "0"));
        if (siteRelationDOs!=null&&siteRelationDOs.size()>0){
            return String.valueOf(siteRelationDOs.get(0).getId());
        }
        return "false";
    }

    public void updateSiteRelationByHy(SiteRelationDTO dto) throws Exception {
        //查询该站点关系是否存在
        SiteRelationDO siteRelationDO = siteMapper.selectOne(new QueryWrapper<SiteRelationDO>()
                .eq("id", dto.getId())
                .eq("is_del", 0));
        if (siteRelationDO == null) {
            throw new Exception("站点关系不存在");
        }
        //判断站点信息是否存在
        siteRelationInfoIsExist(dto);
        BaseUtils.copyProperties(dto,siteRelationDO);
        //修改属性值
        this.setOtherPropertiesByHy(siteRelationDO, dto);
        //保存
        siteMapper.updateById(siteRelationDO);
        //return new Report("编辑站点关系","编辑站点关系成功");
    }

    private void setOtherPropertiesByHy(SiteRelationDO siteRelationDO, SiteRelationDTO dto) throws ServiceException {
        siteRelationDO.setUpdateTime(new Date());
        siteRelationDO.setSiteName(dto.getSiteName());
        siteRelationDO.setUniqueId(dto.getUniqueId());
        siteRelationDO.setHostUnitId(dto.getHostUnitId());
        siteRelationDO.setHostUnit(dto.getHostUnit());
        siteRelationDO.setMasterUnitId(dto.getMasterUnitId());
        siteRelationDO.setMasterUnit(dto.getMasterUnit());
        siteRelationDO.setOperationUnitId(dto.getOperationUnitId());
        siteRelationDO.setOperationUnit(dto.getOperationUnit());
        siteRelationDO.setOperationHost(dto.getOperationHost());
        siteRelationDO.setPhone(dto.getPhone());
    }

    public void saveSiteRelationByHy(SiteRelationDTO dto) throws Exception {
        //判断站点信息是否存在
        siteRelationInfoIsExist(dto);
        //设置实体属性值
        SiteRelationDO siteRelationDO = new SiteRelationDO();
        BaseUtils.copyProperties(dto,siteRelationDO);
        this.setOtherProperties(siteRelationDO, dto);
        //保存
       if (siteRelationDO.getSiteId().equals("0")){
           int id = siteMapper.insert(siteRelationDO);
           String exit =  siteIdIsExit(dto);
           log.warn("新增站点关系后id：{}",id);
           if (exit.equals("false") == false){
               SiteRelationDO siteRelationDOAdd= siteMapper.selectById(Long.valueOf(exit));
               siteRelationDOAdd.setSiteId(Long.valueOf(exit));
               siteMapper.updateById(siteRelationDOAdd);
           }

       }else {
           siteMapper.insert(siteRelationDO);
       }
    }
}
