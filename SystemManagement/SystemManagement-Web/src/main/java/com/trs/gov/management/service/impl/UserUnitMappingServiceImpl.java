package com.trs.gov.management.service.impl;

import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.DTO.UserUnitMappingSearchDTO;
import com.trs.gov.management.VO.UserUnitMappingBySearchVO;
import com.trs.gov.management.VO.UserUnitMappingVO;
import com.trs.gov.management.mgr.IUserUnitMappingMgr;
import com.trs.gov.management.service.IUserUnitMappingService;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class UserUnitMappingServiceImpl implements IUserUnitMappingService {

    @Resource(name = "userUnitMappingMgr")
    private IUserUnitMappingMgr mgr;

    @Override
    public RestfulResults<List<UserUnitMappingBySearchVO>> queryUserUnitMappingList(UserUnitMappingSearchDTO dto) {
        return Try.of(() -> {
            BaseUtils.checkDTO(dto);
            return mgr.queryUserUnitMappingList(dto);
        }).getOrElseGet(err -> {
            log.error("获取数据出错", err);
            return RestfulResults.error(err.getMessage());
        });
    }
}
