package com.trs.gov;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Slf4j
@MapperScan("com.trs.gov.management")
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.trs")
@EnableSwagger2
@EnableScheduling
public class SystemManagementApplication {
    public static void main(String[] args) {
        SpringApplication.run(SystemManagementApplication.class, args);
        log.info("SystemManagement-Service模块启动成功!");
    }
}
