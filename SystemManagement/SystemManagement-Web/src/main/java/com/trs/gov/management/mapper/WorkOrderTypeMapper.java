package com.trs.gov.management.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.management.DO.WorkOrderTypeDO;
import com.trs.gov.management.VO.ParentWorkOrderTypeVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WorkOrderTypeMapper extends BaseMapper<WorkOrderTypeDO> {

    @Select("select max(level) from work_order_type")
    Integer selectMaxLevel();

    List<ParentWorkOrderTypeVO> selectParent(@Param("ids") String[] ids);


}
