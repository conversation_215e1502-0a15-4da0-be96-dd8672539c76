package com.trs.gov.management.scheduler;

import com.trs.gov.management.mgr.UnitMgr;
import com.trs.scheduler.Parameters;
import com.trs.scheduler.SchedulerRunnable;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Description:  <BR>
 * Copyright: Copyright (c) 2004-2016 拓尔思信息技术股份有限公司 <BR>
 * Company: www.trs.com.cn <BR>
 *
 * <AUTHOR>
 * @version 1.0
 * @createTime 2022/11/28 10:15
 */
@Component
@Slf4j
public class SyncUnitCodeFromUpmsScheduler implements SchedulerRunnable {

    @Autowired
    private UnitMgr unitMgr;

    @Scheduled(cron="${systemManagementService.syncUnitCode.cron:0 20 5 * * ?}")
    public void run(){
        this.run(new Parameters());
    }

    @Override
    public void run(Parameters parameters) {
        log.info("开始执行单位组织编码同步");
        Try.of(()->{
                    unitMgr.syncUnitCode(parameters);
                    return 0;
                }
        ).onFailure( err-> log.error("同步单位组织编码失败：{}",err));
    }
}
