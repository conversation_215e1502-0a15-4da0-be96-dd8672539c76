package com.trs.gov.management.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Report;
import com.trs.common.base.Reports;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.DO.UnitGroupDO;
import com.trs.gov.management.DO.UnitGroupRelationDO;
import com.trs.gov.management.DO.excelModel.UnitGroupModel;
import com.trs.gov.management.DTO.*;
import com.trs.gov.management.VO.GroupUserCountVO;
import com.trs.gov.management.VO.UnitGroupVO;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.gov.management.mapper.UnitGroupRelationMapper;
import com.trs.gov.management.mgr.UnitGroupMgr;
import com.trs.gov.management.service.UnitGroupService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 11:12
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class UnitGroupServiceImpl implements UnitGroupService {

    @Autowired
    private UnitGroupMgr groupMgr;

    @Autowired
    private UnitGroupRelationMapper relationMapper;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Override
    public RestfulResults<Report> saveOrUpdateUnitGroup(UnitGroupDTO dto) throws ServiceException {
        //构造返回结果
        Report report;
        try {
            //校验参数
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
            //调用业务方法
            if (dto.getId() == 0) {
                report = groupMgr.saveUnitGroup(dto);
            } else {
                report = groupMgr.updateUnitGroup(dto);
            }
        } catch (Exception e) {
            log.error("查询异常！", e);
            return RestfulResults.error("查询单位异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(report).addMsg("操作成功");
    }

    @Override
    public RestfulResults<Reports> deleteUnitGroup(String groupIds) {
        //构造返回结果
        Reports reports;
        try {
            //调用业务方法
            reports = groupMgr.deleteUnitGroup(groupIds);
        } catch (Exception e) {
            log.error("删除异常！", e);
            return RestfulResults.error("删除单位分组异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(reports).addMsg("操作成功");
    }

    @Override
    public RestfulResults<List<Report>> operUnitGroupStatus(OperUnitGroupStatusDTO dto) {
        //构造返回结果
        List<Report> reports;
        try {
            //校验参数
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
            //调用业务方法
            reports = groupMgr.operUnitGroupStatus(dto);
        } catch (Exception e) {
            log.error("操作异常！", e);
            return RestfulResults.error("操作单位分组异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(reports).addMsg("操作成功");
    }

    @Override
    public RestfulResults<Report> addUnitsToGroup(AddUnitsToGroupDTO dto) {
        //构造返回结果
        Report report;
        try {
            //校验参数
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
            report = groupMgr.addUnitsToGroup(dto);
        } catch (Exception e) {
            log.error("操作异常！", e);
            return RestfulResults.error("操作单位分组异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(report).addMsg("操作成功");
    }

    @Override
    public RestfulResults<List<UnitGroupVO>> queryUnitGroupList(UnitGroupSearchDTO dto) {
        List<UnitGroupVO> list = new ArrayList<>();
        Page<UnitGroupDO> page;
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
            //调用业务方法
            page = groupMgr.queryUnitGroupList(dto);
            //获取分组id和单位数量关系
            Map<Long, Long> groupUnitCountsMap = groupMgr.getGroupUnitCountsMap();
            //构造返回vo
            page.getRecords().forEach(unitGroupDO -> {
                UnitGroupVO unitGroupVO = new UnitGroupVO();
                BaseUtils.copyProperties(unitGroupDO, unitGroupVO);
                unitGroupVO.setUnitCounts(groupUnitCountsMap.getOrDefault(unitGroupDO.getId(), 0L));
                list.add(unitGroupVO);
            });
        } catch (Exception e) {
            log.error("操作异常！", e);
            return RestfulResults.error("查询单位分组异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(list)
                .addMsg("获取数据成功")
                .addTotalCount(page.getTotal())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize());
    }

    @Override
    public RestfulResults<List<UserCountVO>> getGroupUnitInfoListByGroupId(GroupUnitListSearchDTO dto) {
        List<UserCountVO> list = new ArrayList<>();
        Page<UnitGroupRelationDO> relationDOPage;
        try {
            //获取用户有权限单位id
            List<Long> unitIds = groupMgr.getMyRightUnitIds();
            //获取分组单位列表
            relationDOPage = groupMgr.getGroupUnitListForRight(dto, unitIds);
            //获取单位列表信息
            list = groupMgr.getUnitListCountInfo(relationDOPage.getRecords(), dto);
        } catch (Exception e) {
            log.error("操作异常！", e);
            return RestfulResults.error("操作单位分组异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(list)
                .addTotalCount(relationDOPage.getTotal())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addMsg("操作成功");
    }

    @Override
    public List<UnitGroupModel> exportUnitGroup(UnitGroupSearchDTO dto) {
        List<UnitGroupVO> unitGroupList = queryUnitGroupList(dto).getDatas();
        List<UnitGroupModel> list = new ArrayList<>();
        int index = 1;
        for (UnitGroupVO unitGroupVO : unitGroupList) {
            UnitGroupModel groupModel = new UnitGroupModel();
            BaseUtils.copyProperties(unitGroupVO, groupModel);
            groupModel.setId(index);
            list.add(groupModel);
            index++;
        }
        return list;
    }

    @Override
    public RestfulResults<List<UserCountVO>> getGroupUnitInfoListByGroupIdNotRight(GroupUnitListSearchDTO dto) {
        List<UserCountVO> list = new ArrayList<>();
        Page<UnitGroupRelationDO> relationDOPage;
        try {
            //获取未做权限过滤的数据
            relationDOPage = groupMgr.getGroupUnitListNotRight(dto);
            //获取单位列表信息
            list = groupMgr.getUnitListCountInfo(relationDOPage.getRecords(), dto);
        } catch (Exception e) {
            log.error("操作异常！", e);
            return RestfulResults.error("操作单位分组异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(list)
                .addTotalCount(relationDOPage.getTotal())
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addMsg("操作成功");
    }

    @Override
    public RestfulResults<List<UnitGroupVO>> queryUnitGroupNotRight(UnitGroupSearchDTO dto)throws ServiceException {
        List<UnitGroupVO> list = new ArrayList<>();
        groupMgr.queryUnitGroupNotRight(dto).forEach(unitGroupDO -> {
            UnitGroupVO unitGroupVO = new UnitGroupVO();
            BaseUtils.copyProperties(unitGroupDO,unitGroupVO);
            list.add(unitGroupVO);
        });
        return RestfulResults.ok(list).addMsg("获取数据成功");
    }

    @Override
    public RestfulResults<List<UserCountVO>> getGroupUnitInfoListByGroupIds(GroupUnitListSearchDTO dto) throws Exception{
        List<UserCountVO> list = new ArrayList<>();
        List<UnitGroupRelationDO> relationDOS = new ArrayList<>();
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService,dto);
            //获取用户有权限单位id
            List<Long> unitIds = groupMgr.getMyRightUnitIds();
            //获取分组单位列表
            relationDOS = groupMgr.getGroupsUnitListForRight(dto, unitIds);
            //获取单位列表信息
            list = groupMgr.getUnitListCountInfo(relationDOS, dto);
        return RestfulResults.ok(list)
                .addMsg("获取数据成功");
    }

    @Override
    public RestfulResults<List<GroupUserCountVO>> getGroupUserCount(GroupUnitListSearchDTO dto) throws Exception {
        List<GroupUserCountVO> list = new ArrayList<>();
        List<GroupUserCountVO> groupUserCount = groupMgr.getGroupUserCount(dto);
        return RestfulResults.ok(groupUserCount).addMsg("获取数据成功");
    }
}
