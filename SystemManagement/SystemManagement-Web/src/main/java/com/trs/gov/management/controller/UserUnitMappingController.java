package com.trs.gov.management.controller;

import com.trs.gov.management.DTO.UserUnitMappingSearchDTO;
import com.trs.gov.management.VO.UserUnitMappingBySearchVO;
import com.trs.gov.management.VO.UserUnitMappingVO;
import com.trs.gov.management.service.impl.UserUnitMappingServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "人员单位映射API")
@RestController
@RequestMapping("/management/user_unit_mapping")
public class UserUnitMappingController {

    @Autowired
    private UserUnitMappingServiceImpl service;

    @ApiOperation(value = "查询人员单位列表")
    @RequestMapping(value = "queryUserUnitMappingList")
    @ResponseBody
    public RestfulResults<List<UserUnitMappingBySearchVO>> queryUserUnitMappingList(UserUnitMappingSearchDTO dto) {
        return service.queryUserUnitMappingList(dto);
    }
}
