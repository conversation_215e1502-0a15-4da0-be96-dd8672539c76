package com.trs.gov.management.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ExcelUtil;
import com.trs.gov.management.DTO.*;
import com.trs.gov.management.base.constant.UnitGroupConstant;
import com.trs.gov.management.service.impl.UnitGroupServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/30 9:36
 * @version 1.0
 * @since  1.0
 */
@Api(tags = "单位分组配置API")
@RestController
@RequestMapping("/management/unitGroup")
public class UnitGroupController {
    @Autowired
    private UnitGroupServiceImpl groupService;

    /**
     * 新建/编辑单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 9:54
     * @param  dto 传递参数
     * @throws
     * @return 保存结果
     */
    @ApiOperation(value = "新建或编辑单位分组")
    @RequestMapping(value ="saveOrUpdateUnitGroup",method = RequestMethod.POST)
    public RestfulResults saveOrUpdateUnitGroup(UnitGroupDTO dto) throws ServiceException {
        return groupService.saveOrUpdateUnitGroup(dto);
    }

    /**
     * 删除分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 9:57
     * @param  groupIds  分组id串
     * @throws
     * @return 删除结果
     */
    @ApiOperation(value = "删除分组")
    @RequestMapping(value ="deleteUnitGroup",method = RequestMethod.POST)
    public RestfulResults deleteUnitGroup(String groupIds) {
        return groupService.deleteUnitGroup(groupIds);
    }

    /**
     * 启用/停用单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 10:03
     * @param  dto  传递参数
     * @throws
     * @return 操作结果
     */
    @ApiOperation(value = "启用/停用单位分组")
    @RequestMapping(value ="operUnitGroupStatus",method = RequestMethod.POST)
    public RestfulResults operUnitGroupStatus(OperUnitGroupStatusDTO dto) {
        return groupService.operUnitGroupStatus(dto);
    }

    /**
     * 配置单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 10:30
     * @param  dto  传递参数
     * @throws
     * @return 配置结果
     */
    @ApiOperation(value = "配置单位分组")
    @RequestMapping(value ="addUnitsToGroup",method = RequestMethod.POST)
    public RestfulResults addUnitsToGroup(AddUnitsToGroupDTO dto) {
        return groupService.addUnitsToGroup(dto);
    }

    /**
     * 查询单位分组<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 10:52
     * @param  dto 传递参数
     * @throws
     * @return 查询结果
     */
    @ApiOperation(value = "查询单位分组")
    @RequestMapping(value ="queryUnitGroupList",method = RequestMethod.GET)
    public RestfulResults queryUnitGroupList(UnitGroupSearchDTO dto) {
        return groupService.queryUnitGroupList(dto);
    }

    /**
     * 获取单位分组下单位信息<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 17:52
     * @param  dto 分组id
     * @throws
     * @return 查询结果
     */
    @ApiOperation(value = "获取单位分组下单位信息")
    @RequestMapping(value ="getGroupUnitInfoListByGroupId",method = RequestMethod.GET)
    public RestfulResults getGroupUnitInfoListByGroupId(GroupUnitListSearchDTO dto) {
        return groupService.getGroupUnitInfoListByGroupId(dto);
    }

    /**
     * 导出单位分组数据<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/30 17:48
     * @param  dto 传递参数
     * @throws
     * @return
     */
    @ApiOperation(value = "导出单位分组数据")
    @RequestMapping(value ="exportUnitGroup",method = RequestMethod.GET)
    public void exportUnitGroup(HttpServletResponse response,UnitGroupSearchDTO dto) throws Exception{
        ExcelUtil.exportExcel(response,groupService.exportUnitGroup(dto), UnitGroupConstant.GROUP_FIELNAME,UnitGroupConstant.GROUP_SHEETNAME);
    }

    /**
     * 获取单位分组下单位信息(不做权限过滤)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/12/29 17:52
     * @param  dto 分组id
     * @throws
     * @return 查询结果
     */
    @ApiOperation(value = "获取单位分组下单位信息")
    @RequestMapping(value ="getGroupUnitInfoListByGroupIdNotRight",method = RequestMethod.GET)
    public RestfulResults getGroupUnitInfoListByGroupIdNotRight(GroupUnitListSearchDTO dto) {
        return groupService.getGroupUnitInfoListByGroupIdNotRight(dto);
    }
}
