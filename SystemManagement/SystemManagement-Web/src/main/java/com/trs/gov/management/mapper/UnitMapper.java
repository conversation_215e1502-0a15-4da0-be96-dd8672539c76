package com.trs.gov.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.management.VO.UnitSearchVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @auther
 * @date
 **/
@Component
public interface UnitMapper extends BaseMapper<UnitDO> {

    @Select("select id,unit_name,unit_type from unit where status=#{status} and if(#{unitType} is NULL,0=0,unit_type like CONCAT('%',#{unitType},'%')) and if(#{unitName} is NULL,0=0,unit_name like CONCAT('%',#{unitName},'%')) and is_del = 0")
    List<UnitSearchVO> getAllUnitForSearch(@Param("status") String status,@Param("unitType") String unitType,@Param("unitName") String unitName);

    @Select("select id from unit")
    List<Long> getAllHasRightUnits();
}
