package com.trs.gov.management.swagger;


import io.swagger.annotations.Api;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
public class SwaggerConfig {

	@Bean
	public Docket createRestApi() {
		return new Docket(DocumentationType.SWAGGER_2).apiInfo(apiInfo()).select()//
				// swagger要扫描的包路径
				.apis(RequestHandlerSelectors.withClassAnnotation(Api.class))//
				.paths(PathSelectors.any()).build();//
	}
	private ApiInfo apiInfo() {
		return new ApiInfoBuilder().title("situationAware 1.0.1 API")//
				.description("态势感知项目接口")//
				.version("1.0").termsOfServiceUrl("http://www.trs.com.cn/")//
				.license("北京拓尔思信息技术股份有限公司")//
				.licenseUrl("http://www.trs.com.cn/").build();//
	}
}
