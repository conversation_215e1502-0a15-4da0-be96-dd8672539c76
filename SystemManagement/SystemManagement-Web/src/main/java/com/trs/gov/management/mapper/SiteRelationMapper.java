package com.trs.gov.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.management.DO.SiteRelationDO;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.VO.HotSiteVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface SiteRelationMapper extends BaseMapper<SiteRelationDO> {

    List<HotSiteVO> selectHotSite(@Param("dto") SiteRelationSearchDTO dto);

    List<HotSiteVO> selectHotSiteByCrTime(@Param("dto") SiteRelationSearchDTO dto);

}
