package com.trs.gov.management.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.management.DO.UnitGroupRelationDO;
import com.trs.gov.management.DTO.GroupUnitListSearchDTO;
import com.trs.gov.management.VO.UnitGroupRelationVO;
import com.trs.gov.management.mapper.UnitGroupRelationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class UnitGroupRelationMgr {

    @Autowired
    private UnitGroupRelationMapper relationMapper;

    public List<Long> getGroupByUnitId(String unitIds) {
        return relationMapper.selectList(new QueryWrapper<UnitGroupRelationDO>()
                .in("unit_id", Arrays.asList(unitIds.split(",")))
                .inSql("group_id","select id from unit_group where status = 1"))
                .stream()
                .map(UnitGroupRelationDO::getGroupId)
                .distinct()
                .collect(Collectors.toList());
    }

    public List<UnitGroupRelationVO> getUnitGroupRelationList(GroupUnitListSearchDTO searchDTO) {
        List<UnitGroupRelationVO> list = new ArrayList<>();
        List<String> groupIds = Arrays.asList(searchDTO.getGroupId().split(","));
        //获取分组和单位关系
        List<UnitGroupRelationDO> relationDOS = relationMapper.selectList(new QueryWrapper<UnitGroupRelationDO>()
                .in("group_id", groupIds)
                .inSql(searchDTO.getStatus() != null, "unit_id", "select id from unit where status = " + searchDTO.getStatus()));
        //构造返回
        for (String groupId : groupIds) {
            List<Long> unitIds = new ArrayList<>();
            for (UnitGroupRelationDO relationDO : relationDOS) {
                if (groupId.equals(relationDO.getGroupId().toString())){
                    unitIds.add(relationDO.getUnitId());
                }
            }
            UnitGroupRelationVO relationVO = new UnitGroupRelationVO();
            relationVO.setGroupId(Long.valueOf(groupId));
            relationVO.setUnitIds(unitIds);
            list.add(relationVO);
        }
        return list;
    }
}
