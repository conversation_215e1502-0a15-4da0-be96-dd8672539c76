package com.trs.gov.management.scheduler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.management.DTO.SiteRelationDTO;
import com.trs.gov.management.VO.BaseInfoVO;
import com.trs.gov.management.VO.InitInfoVO;
import com.trs.gov.management.VO.OperationUnitInfoVO;
import com.trs.gov.management.VO.SponsorInfoVO;
import com.trs.gov.management.base.constant.UnitConstant;
import com.trs.gov.management.service.SiteRelationService;
import com.trs.gov.management.service.impl.UnitServiceImpl;
import com.trs.scheduler.Parameters;
import com.trs.scheduler.SchedulerRunnable;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Description:  <BR>
 * Copyright: Copyright (c) 2004-2016 拓尔思信息技术股份有限公司 <BR>
 * Company: www.trs.com.cn <BR>
 *
 * <AUTHOR>
 * @version 1.0
 * @createTime 2022/8/1 17:16
 */
@Component
@Slf4j
public class SyncSiteRelationFromHyScheduler implements SchedulerRunnable {
    //注释，暂时站点关系有项目组维护
    //@Scheduled(cron = "${systemManagementService.syncSiteRelation.cron:0 */30 * * * ?}")
    public void run() {
        this.run(new Parameters());
    }

    @Reference
    private SiteRelationService siteRelationService;

    public static HttpRequest httpRequest = new HttpRequest();

    @Value("${hy.site.info.url:http://***********/pub/sjwtzsk/qt/wzywtj/index.json}")
    private String getSiteInfoFromHyOfURL;

    @Autowired
    private UnitServiceImpl unitService;

    @Override
    public void run(Parameters parameters)  {
        Try.of(() ->{
            //获取用户信息
            String response =  httpRequest.doGet(getSiteInfoFromHyOfURL);
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson.containsKey("data")){
                JSONArray dataJson = responseJson.getJSONArray("data");
                for (int i = 0; i < dataJson.size(); i++) {
                    updateUnit(dataJson.getJSONObject(i));
                }
            }
            return 0;
        }).onFailure(err ->log.error("从海云同步站点信报错：{}",err));
    }

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    public void updateUnit(JSONObject siteRelationJson) throws Exception {
        //首先更新单位
        BaseInfoVO baseInfoVO = siteRelationJson.getObject("baseinfo",BaseInfoVO.class);
        //更新以下三个单位
        SponsorInfoVO sponsorInfoVO = siteRelationJson.getObject("sponsorinfo",SponsorInfoVO.class);
        InitInfoVO initInfoVO = siteRelationJson.getObject("initinfo",InitInfoVO.class);
        OperationUnitInfoVO operationUnitInfoVO = siteRelationJson.getObject("operationunitinfo",OperationUnitInfoVO.class);
        //主管单位 转换为unit 通过类型和名称去查询unit 如果有->更新，没有->新增

        //主办
        UnitDO sponsorUnit = unitService.getUnitByTypeAndUnitName(sponsorInfoVO.getSponsor(), UnitConstant.PARAM_HOSTUNIT_ID,sponsorInfoVO,initInfoVO,operationUnitInfoVO);
        //主管
        UnitDO initUnit = unitService.getUnitByTypeAndUnitName(initInfoVO.getUnit(), UnitConstant.PARAM_MASTERUNIT_ID,sponsorInfoVO,initInfoVO,operationUnitInfoVO);
        UnitDO operationUnit = unitService.getUnitByTypeAndUnitName(operationUnitInfoVO.getOperationunit(), UnitConstant.PARAM_OPERATION_ID,sponsorInfoVO,initInfoVO,operationUnitInfoVO);

        //查询站点是否存在
        //1.1 站点编码为0 直接新增
        //1.2 否则，通过站点编码查询站点，如果存在，则，更新，否则新增
        SiteRelationDTO siteRelationDTO = new SiteRelationDTO();
        siteRelationDTO.setId("0");
        String siteId = baseInfoVO.getSitecode();
        if (NumberUtils.isParsable(siteId) == false || siteId.equals("0") ){
            siteId = String.valueOf(System.currentTimeMillis());
        }
        String uniqueId = baseInfoVO.getIcpcode();
        if (siteId.equals("0") == false && siteId.length() >3){
            uniqueId = (uniqueId+"-"+siteId.substring(siteId.length() -2));
        }
        siteRelationDTO.setSiteId(Long.valueOf(siteId));
        siteRelationDTO.setSiteName(baseInfoVO.getSitename());
        siteRelationDTO.setUniqueId(uniqueId);
        siteRelationDTO.setMediaType(1);
        siteRelationDTO.setMediaName("网站");
        siteRelationDTO.setHostUnitId(sponsorUnit.getId());
        siteRelationDTO.setHostUnit(sponsorUnit.getUnitName());
        siteRelationDTO.setMasterUnitId(initUnit.getId());
        siteRelationDTO.setMasterUnit(initUnit.getUnitName());
        siteRelationDTO.setOperationUnitId(operationUnit.getId());
        siteRelationDTO.setOperationUnit(operationUnit.getUnitName());
        siteRelationDTO.setOperationHost(operationUnit.getUnitMaster());
        siteRelationDTO.setPhone(operationUnit.getPhone());
        //4个时间 备案，建设，运维开始，结束
        siteRelationDTO.setFilingTime(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD));
        siteRelationDTO.setConstructionTime(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD));
        siteRelationDTO.setOperationStartTime(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD));
        siteRelationDTO.setOperationEndTime(TimeUtils.dateBefOrAft(new Date(),730,TimeUtils.YYYYMMDD));
        siteRelationService.saveOrUpdateSiteRelationByHy(siteRelationDTO);

    }
}
