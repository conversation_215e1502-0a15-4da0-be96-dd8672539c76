package com.trs.gov.management.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.DTO.UserUnitMappingSearchDTO;
import com.trs.gov.management.DTO.WorkOrderTypeSearchDTO;
import com.trs.gov.management.VO.GetAllSitesByTypeVO;
import com.trs.gov.management.VO.UnitSearchVO;
import com.trs.gov.management.VO.UserUnitMappingBySearchVO;
import com.trs.gov.management.VO.WorkOrderTypeVO;
import com.trs.gov.management.mgr.OpenApiMgr;
import com.trs.gov.management.service.SiteRelationService;
import com.trs.gov.management.service.impl.UnitServiceImpl;
import com.trs.gov.management.service.impl.UserUnitMappingServiceImpl;
import com.trs.gov.management.service.impl.WorkOrderTypeServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2025-08-11 15:50
 */
@RestController
@RequestMapping("/management/openapi")
@Slf4j
public class OpenApiController {

    @Autowired
    private UserUnitMappingServiceImpl service;

    @Autowired
    private WorkOrderTypeServiceImpl workOrderTypeService;

    @Autowired
    private UnitServiceImpl unitService;

    @Reference(check = false, timeout = 60000)
    private SiteRelationService siteRelationService;

    @Autowired
    private OpenApiMgr openApiMgr;


    @ApiOperation(value = "查询人员单位列表")
    @RequestMapping(value = "queryUserUnitMappingList")
    @ResponseBody
    public RestfulResults<List<UserUnitMappingBySearchVO>> queryUserUnitMappingList(HttpServletRequest request, UserUnitMappingSearchDTO dto) {
        try {
            if (!openApiMgr.checkAuth(request)) {
                throw new ServiceException("接口签名校验失败，请重试！");
            }
            return service.queryUserUnitMappingList(dto);
        } catch (Exception e){
            log.error("查询人员单位列表失败", e);
            return RestfulResults.error("查询人员单位列表失败:"+e.getMessage());
        }
    }

    @ApiOperation(value = "获取检索所需单位信息接口(通知组件使用)")
    @RequestMapping(value = "getAllUnitForTongZhiSearch", method = {RequestMethod.GET})
    public RestfulResults<List<UnitSearchVO>> getAllUnitForTongZhiSearch(HttpServletRequest request, UnitSearchDTO dto){
        try {
            if (!openApiMgr.checkAuth(request)) {
                throw new ServiceException("接口签名校验失败，请重试！");
            }
            return unitService.getAllUnitForTongZhiSearch(dto);
        } catch (Exception e){
            log.error("获取检索所需单位信息失败", e);
            return RestfulResults.error("获取检索所需单位信息失败:"+e.getMessage());
        }
    }

    @ApiOperation(value = "获取工单类型")
    @RequestMapping(value = "getWorkOrderTypes",method = {RequestMethod.GET})
    public RestfulResults<List<WorkOrderTypeVO>> getWorkOrderTypes(HttpServletRequest request, WorkOrderTypeSearchDTO dto){
        try {
            if (!openApiMgr.checkAuth(request)) {
                throw new ServiceException("接口签名校验失败，请重试！");
            }
            dto.setOpenApi(true);
            return workOrderTypeService.getWorkOrderTypes(dto);

        } catch (Exception e){
            log.error("获取工单类型失败", e);
            return RestfulResults.error("获取工单类型失败:"+e.getMessage());
        }
    }

    @ApiOperation(value = "根据站点分类获取工单系统所有站点信息")
    @RequestMapping(value = "getWorkOrderSysSites", method = {RequestMethod.GET})
    public RestfulResults<List<GetAllSitesByTypeVO>> getWorkOrderSysSites(HttpServletRequest request) {
        try {
            if (!openApiMgr.checkAuth(request)) {
                throw new ServiceException("接口签名校验失败，请重试！");
            }
            return siteRelationService.getWorkOrderSysSites();

        } catch (Exception e){
            log.error("根据站点分类获取工单系统所有站点信息失败", e);
            return RestfulResults.error("根据站点分类获取工单系统所有站点信息失败:"+e.getMessage());
        }
    }
}
