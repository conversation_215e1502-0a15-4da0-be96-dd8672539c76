package com.trs.gov.management.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Defaults;
import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.management.DO.excelModel.UnitModel;
import com.trs.gov.management.DTO.*;
import com.trs.gov.management.VO.*;
import com.trs.gov.management.base.constant.UnitConstant;
import com.trs.gov.management.mgr.UnitMgr;
import com.trs.gov.management.service.UnitService;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 单位配置服务接口实现
 */
@Service
@Slf4j
public class UnitServiceImpl implements UnitService {

    @Autowired
    private UnitMgr unitMgr;

    @Reference(check = false, timeout = 60000)
    private IUserService service;

    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileService;

    @Override
    public RestfulResults<List<UnitVO>> queryUnitList(UnitSearchDTO dto) {
        RestfulResults restfulResults;
        List<UnitVO> results = null;
        try {
            //参数校验
            UserUtils.checkDTOAndLoadUserInfoByDTO(service, dto);
            //调用业务方法
            Page<UnitDO> unitPage = unitMgr.queryUnitList(dto);
            //构造返回VO
            UnitUserDTO userDTO = new UnitUserDTO();
            List<UnitVO> unitVOS = new ArrayList<>();
            for (UnitDO record : unitPage.getRecords()) {
                UnitVO unitVO = new UnitVO();
                BaseUtils.copyProperties(record, unitVO);
                //添加单位logo
                unitMgr.addUnitLogo(unitVO);
                //添加单位用户
                unitMgr.addUnitUser(record.getUnitCode(), userDTO, unitVO);
                unitVOS.add(unitVO);
            }
            //构造返回结果
            restfulResults = RestfulResults.ok(unitVOS)
                    .addMsg("查询成功")
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(unitPage.getTotal());
        } catch (Exception e) {
            log.error("查询异常！", e);
            restfulResults = RestfulResults.error("查询单位异常,异常信息:" + e.getMessage());
        }
        return restfulResults;
    }

    @Override
    public RestfulResults<List<UnitVO>> queryBaseUnitList(UnitSearchDTO dto) {
        RestfulResults restfulResults;
        List<UnitVO> results = null;
        try {
            //参数校验
            UserUtils.checkDTOAndLoadUserInfoByDTO(service, dto);
            //调用业务方法
            Page<UnitDO> unitPage = unitMgr.queryUnitList(dto);
            //构造返回VO
            UnitUserDTO userDTO = new UnitUserDTO();
            List<UnitVO> unitVOS = new ArrayList<>();
            for (UnitDO record : unitPage.getRecords()) {
                UnitVO unitVO = new UnitVO();
                BaseUtils.copyProperties(record, unitVO);
                unitVOS.add(unitVO);
            }
            //构造返回结果
            restfulResults = RestfulResults.ok(unitVOS)
                    .addMsg("查询成功")
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(unitPage.getTotal());
        } catch (Exception e) {
            log.error("查询异常！", e);
            restfulResults = RestfulResults.error("查询基础单位异常,异常信息:" + e.getMessage());
        }
        return restfulResults;
    }

    /**
     * 查询下属单位的基础信息，不包含单位用户和logo<BR>
     *
     * @param dto 请求参数
     * @return 查询结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-05 18:11
     */
    @Override
    public RestfulResults<List<UnitVO>> queryBaseChildrenUnitList(UnitSearchDTO dto) {
        RestfulResults restfulResults;
        try {
            //参数校验
//            UserUtils.checkDTOAndLoadUserInfoByDTO(service, dto);
            //调用业务方法
            Page<UnitDO> unitPage = unitMgr.queryChildrenUnitList(dto);
            //构造返回VO
            List<UnitVO> unitVOS = new ArrayList<>();
            for (UnitDO record : unitPage.getRecords()) {
                UnitVO unitVO = new UnitVO();
                BaseUtils.copyProperties(record, unitVO);
                unitVOS.add(unitVO);
            }
            //构造返回结果
            restfulResults = RestfulResults.ok(unitVOS)
                    .addMsg("查询成功")
                    .addPageNum(dto.getPageNum())
                    .addPageSize(dto.getPageSize())
                    .addTotalCount(unitPage.getTotal());
        } catch (Exception e) {
            log.error("查询异常！", e);
            restfulResults = RestfulResults.error("查询基础单位异常,异常信息:" + e.getMessage());
        }
        return restfulResults;
    }

    @Override
    public RestfulResults<Report> saveOrUpdateUnit(UnitDTO dto) {
        Report report = Defaults.defaultValue(Report.class);
        try {
            //校验参数
            UserUtils.checkDTOAndLoadUserInfoByDTO(service, dto);
            //新建
            if (dto.getId().equals("0")) {
                report = unitMgr.saveUnit(dto);
            }
            //编辑
            else {
                report = unitMgr.updateUnit(dto);
            }
        } catch (Exception e) {
            log.error("操作异常", e);
            return RestfulResults.error("操作异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(report)
                .addMsg("操作成功");
    }

    @Override
    public RestfulResults<Report> operUnitState(UnitStateDTO dto) {
        List<Report> reports;
        try {
            //参数校验
            BaseUtils.checkDTO(dto);
            //调用业务方法
            reports = unitMgr.operUnitState(dto);
        } catch (Exception e) {
            log.error("操作异常", e);
            return RestfulResults.error("操作异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(reports)
                .addMsg("操作成功");
    }

    @Override
    public RestfulResults<JSONArray> queryUnitUserList(UnitUserDTO dto) {
        JSONArray jsonArray;
        try {
            //参数校验
            BaseUtils.checkDTO(dto);
            jsonArray = unitMgr.queryUnitUserList(dto);
        } catch (Exception e) {
            log.error("查询数据异常", e);
            return RestfulResults.error("查询数据异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(jsonArray)
                .addTotalCount(Long.parseLong(String.valueOf(jsonArray.size())))
                .addMsg("获取数据成功");
    }

    @Override
    public RestfulResults<List<UserVO>> getAllUser(UserSearchDTO dto) {
        RestfulResults<List<UserVO>> allUser = null;
        try {
            dto.setStatus(1);
            allUser = service.getAllUser(dto);
        } catch (Exception e) {
            log.error("获取数据异常", e);
            return RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return allUser;
    }

    @Override
    public RestfulResults<List<UnitVO>> queryUnitByType(UnitSearchDTO dto) {
        List<UnitVO> results = null;
        try {
            //参数校验
            BaseUtils.checkDTO(dto);
            //调用业务方法
            results = unitMgr.queryUnitByType(dto);
        } catch (Exception e) {
            log.error("查询异常", e);
            return RestfulResults.error("查询单位异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(results)
                .addMsg("查询成功")
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(Long.parseLong(String.valueOf(results.size())));
    }

    @Override
    public RestfulResults<List<SimpleUnitVO>> queryUnitByUser(UserSearchDTO dto) {
        List<SimpleUnitVO> results = null;
        try {
            //参数校验
            BaseUtils.checkDTO(dto);
            //调用业务方法
            results = unitMgr.queryUnitByUser(dto);
        } catch (Exception e) {
            log.error("查询异常", e);
            return RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(results)
                .addMsg("查询成功")
                .addPageNum(dto.getPageNum())
                .addTotalCount(Long.parseLong(String.valueOf(results.size())));
    }

    @Override
    public RestfulResults<List<UnitAndSiteRelationsVO>> getUnitAndSiteRelations(UnitSearchDTO dto) {

        List<UnitAndSiteRelationsVO> relationsVOS = null;
        try {
            relationsVOS = unitMgr.getUnitAndSiteRelations(dto);

        } catch (Exception e) {
            log.error("查询异常", e);
            return RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(relationsVOS)
                .addTotalCount(Long.parseLong(String.valueOf(relationsVOS.size())));
    }

    @Override
    public RestfulResults<UnitVO> getUnitDetailById(UnitSearchDTO dto) {
      UnitVO unitVO ;
        try {
            unitVO = unitMgr.getUnitDetailById(dto);
        } catch (Exception e) {
            log.error("查询异常", e);
            return RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(unitVO);
    }

    @Override
    public List<UnitModel> exportUnit(UnitSearchDTO dto) {
        RestfulResults<List<UnitVO>> listRestfulResults = queryUnitList(dto);
        List<UnitModel> unitModels = new ArrayList<>();
        List<UnitVO> datas = listRestfulResults.getDatas();
        int index = 1;
        for (UnitVO data : datas) {
            UnitModel unitModel = new UnitModel();
            BaseUtils.copyProperties(data, unitModel);
            unitModel.setId(index);
            unitModel.setUnitTypeName(UnitConstant.getUnitTypeDesc(data.getUnitType()).get());
            unitModel.setUnitUsers(String.valueOf(data.getUserVOS().size()));
            unitModel.setStatus(data.getStatus());
            unitModels.add(unitModel);
            index++;
        }
        return unitModels;
    }

    @Override
    public UnitVO exportUnit(UnitExportDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(service, dto);
        dto.isValid();
        return unitMgr.exportUnit(dto).orElseThrow(() -> new ServiceException("同步单位【"+dto.getOldUnitId()+"】失败！"));
    }

    @Override
    public RestfulResults<List<UnitSearchVO>> getAllUnitForSearch(UnitSearchDTO dto) {
        List<UnitSearchVO> resultList ;
        try {
            resultList = unitMgr.getAllUnitForSearch(dto);
        }catch (Exception e){
            log.error("查询异常", e);
            return RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(resultList)
                .addMsg("获取成功")
                .addTotalCount(Long.parseLong(String.valueOf(resultList.size())));
    }

    @Override
    public RestfulResults<List<UserCountVO>> getUserCountByUnitIds(String ids) {
        List<UserCountVO> list;
        try {
            Integer status = 1;
            list = unitMgr.getUserCountByIds(ids,status);
        }catch (Exception e){
            log.error("查询异常", e);
            return RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(list)
                .addMsg("获取成功")
                .addTotalCount(Long.parseLong(String.valueOf(list.size())));
    }

    @Override
    public RestfulResults<List<BaseUnitVO>> queryHasRightUnitByType(UnitSearchDTO dto) {
        List<BaseUnitVO> results = null;
        try {
            //参数校验
            BaseUtils.checkDTO(dto);
            //调用业务方法
            results = unitMgr.queryHasRightUnitByType(dto);
        } catch (Exception e) {
            log.error("查询异常", e);
            return RestfulResults.error("查询单位异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(results)
                .addMsg("查询成功")
                .addTotalCount(Long.parseLong(String.valueOf(results.size())));
    }

    @Override
    public RestfulResults<List<UnitSearchVO>> getAllUnitForTongZhiSearch(UnitSearchDTO dto) {
        List<UnitSearchVO> list = new ArrayList<>();
        Page<UnitDO> unitDOPage;
        try {
             unitDOPage = unitMgr.getAllUnitForTongZhiSearch(dto);
             unitDOPage.getRecords().forEach(unitDO -> {
                 UnitSearchVO unitSearchVO = new UnitSearchVO();
                 BaseUtils.copyProperties(unitDO,unitSearchVO);
                 list.add(unitSearchVO);
             });
        }catch (Exception e){
            log.error("查询异常", e);
            return RestfulResults.error("查询异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(list)
                .addMsg("获取成功")
                .addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(unitDOPage.getTotal());
    }

    @Override
    public UnitDO getUnitByTypeAndUnitName(String unitName, String type, SponsorInfoVO sponsorInfoVO, InitInfoVO initInfoVO, OperationUnitInfoVO operationUnitInfoVO) throws Exception {
        //查询该单位是否存在 ，若存在，则更新，否则新增
        UnitDO unitDO = new UnitDO();
        try {
            unitDO = unitMgr.getUnitByTypeAndUnitName(unitName, type, sponsorInfoVO, initInfoVO, operationUnitInfoVO);
            log.info("单位同步完成，单位信息：{}",unitDO.toString());
        }catch (Exception e){
            log.error("同步单位信息报错：{}",e);
        }
        return unitDO;
    }

}
