package com.trs.gov.management.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DO.UnitGroupRelationDO;
import com.trs.gov.management.DTO.GroupUnitListSearchDTO;
import com.trs.gov.management.VO.UnitGroupRelationVO;
import com.trs.gov.management.mapper.UnitGroupRelationMapper;
import com.trs.gov.management.mgr.UnitGroupRelationMgr;
import com.trs.gov.management.service.UnitGroupRelationService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;


/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 15:43
 * @version 1.0
 * @since  1.0
 */
@Service
public class UnitGroupRelationServiceImpl extends ServiceImpl<UnitGroupRelationMapper,UnitGroupRelationDO> implements UnitGroupRelationService {

    @Autowired
    private UnitGroupRelationMgr relationMgr;


    @Override
    public List<Long> getGroupByUnitId(String unitIds) throws ServiceException {
        if (StringUtils.isEmpty(unitIds)||"null".equalsIgnoreCase(unitIds)){
            return Collections.emptyList();
        }
        return relationMgr.getGroupByUnitId(unitIds);
    }

    @Override
    public List<UnitGroupRelationVO> getUnitGroupRelationList(GroupUnitListSearchDTO searchDTO) throws ServiceException {
        return relationMgr.getUnitGroupRelationList(searchDTO);
    }
}
