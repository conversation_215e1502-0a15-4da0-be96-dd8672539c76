package com.trs.gov.management.service.impl;

import com.trs.common.base.Defaults;
import com.trs.common.base.Report;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.DTO.AddAcceptUnitDTO;
import com.trs.gov.management.DTO.OperWorkOrderTypeDTO;
import com.trs.gov.management.DTO.WorkOrderTypeDTO;
import com.trs.gov.management.DTO.WorkOrderTypeSearchDTO;
import com.trs.gov.management.VO.ParentWorkOrderTypeVO;
import com.trs.gov.management.VO.WorkOrderTypeForNoticeVO;
import com.trs.gov.management.VO.WorkOrderTypeLevelVO;
import com.trs.gov.management.VO.WorkOrderTypeVO;
import com.trs.gov.management.mgr.WorkOrderTypeMgr;
import com.trs.gov.management.service.WorkOrderTypeService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 工单类型配置服务接口实现
 */
@Service
@Slf4j
public class WorkOrderTypeServiceImpl implements WorkOrderTypeService {

    @Autowired
    private WorkOrderTypeMgr typeMgr;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Override
    public RestfulResults<List<WorkOrderTypeVO>> getWorkOrderTypes(WorkOrderTypeSearchDTO dto) {
        RestfulResults restfulResults;
        try {
            //调用业务方法
            List<WorkOrderTypeVO> workOrderTypeVOs = typeMgr.getWorkOrderTypes(dto);
            //构造返回结果
            restfulResults = RestfulResults.ok(workOrderTypeVOs)
                    .addMsg("查询成功")
                    .addTotalCount(Long.parseLong(String.valueOf(workOrderTypeVOs.size())));
        } catch (Exception e) {
            log.error("新建编辑工单类型异常",e.getMessage(),e);
            restfulResults = RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return restfulResults;
    }

    @Override
    public RestfulResults<Report> saveOrUpdateWorkOrderType(WorkOrderTypeDTO dto) {
        Report report = Defaults.defaultValue(Report.class);
        try {
            //校验参数
            BaseUtils.checkDTO(dto);
            //调用业务方法
            if (dto.getId().equals("0")) {
                report = typeMgr.saveWorkOrderType(dto);
            } else {
                report = typeMgr.updateWorkOrderType(dto);
            }
        } catch (Exception e) {
            log.error("新建编辑工单类型异常",e.getMessage(),e);
            return RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(report)
                .addMsg("操作成功");
    }

    @Override
    public RestfulResults<Report> operWorkOrderType(OperWorkOrderTypeDTO dto) {
        List<Report> reports;
        try {
            //参数校验
            BaseUtils.checkDTO(dto);
            //调用业务方法
            reports = typeMgr.operWorkOrderType(dto);
        } catch (Exception e) {
            log.error("操作工单类型异常",e.getMessage(),e);
            return RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(reports)
                .addMsg("操作成功");
    }

    @Override
    public RestfulResults<WorkOrderTypeForNoticeVO> getTypeInfoForOtherPart(String id) {
        WorkOrderTypeForNoticeVO noticeVO ;
        try {
            Optional.ofNullable(id).orElseThrow(()-> new ParamInvalidException("类型id不能为空"));
            noticeVO = typeMgr.getParentInfo(id);
        }catch (Exception e){
            log.error("获取父节点id异常",e.getMessage(),e);
            return RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(noticeVO).addMsg("查询数据成功");
    }

    @Override
    public RestfulResults<List<WorkOrderTypeLevelVO>> queryWorkOrderTypeByLevel(WorkOrderTypeSearchDTO dto) {
        List<WorkOrderTypeLevelVO> workOrderTypeLevelVOS;
        try {
            workOrderTypeLevelVOS = typeMgr.queryWorkOrderTypeByLevel(dto);
        }catch (Exception e){
            log.error("获取工单类型异常",e.getMessage(),e);
            return RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(workOrderTypeLevelVOS)
                .addMsg("查询成功")
                .addTotalCount(Long.parseLong(String.valueOf(workOrderTypeLevelVOS.size())));
    }

    /**
     * @Description  根据key获取该类型的TypeId
     * @Param [workOrderTypeSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/29 18:18
     **/
    @Override
    public RestfulResults<List<Long>> listAllRelateTypeByRootType(WorkOrderTypeSearchDTO workOrderTypeSearchDTO){
        try{
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderTypeSearchDTO);
            return typeMgr.listAllRelateTypeByRootType(workOrderTypeSearchDTO);
        }catch (Exception e){
            return RestfulResults.error(e.getMessage());
        }
    }

    /**
     * @Description  根据工单类型id的获取子级id列表，包括本级
     * @Param [workOrderTypeSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/10 11:36
     **/
    @Override
    public RestfulResults<List<Long>> listAllRelateTypeById(WorkOrderTypeSearchDTO workOrderTypeSearchDTO){
        try{
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderTypeSearchDTO);
            return typeMgr.listAllRelateTypeById(workOrderTypeSearchDTO);
        }catch (Exception e){
            e.printStackTrace();
            log.error("查询工单子类型异常",e.getMessage(),e);
            return RestfulResults.error(e.getMessage());
        }
    }

    @Override
    public RestfulResults<List<ParentWorkOrderTypeVO>> getParentWorkOrderTypes(WorkOrderTypeSearchDTO dto) {
        RestfulResults restfulResults;
        try {
            //调用业务方法
            List<ParentWorkOrderTypeVO> workOrderTypeVOs = typeMgr.getParentWorkOrderTypes(dto);
            //构造返回结果
            restfulResults = RestfulResults.ok(workOrderTypeVOs)
                    .addMsg("查询成功")
                    .addTotalCount(Long.parseLong(String.valueOf(workOrderTypeVOs.size())));
        } catch (Exception e) {
            log.error("查询工单类型异常",e.getMessage(),e);
            restfulResults = RestfulResults.error("获取数据异常,异常信息:" + e.getMessage());
        }
        return restfulResults;
    }

    @Override
    public RestfulResults<Report> addAcceptUnit(AddAcceptUnitDTO dto) {
        RestfulResults restfulResults;
        try {
            BaseUtils.checkDTO(dto);
            //调用业务方法
           typeMgr.addAcceptUnit(dto);
            //构造返回结果
            restfulResults = RestfulResults.ok(new Report("保存投诉受单位","保存成功"))
                    .addMsg("查询成功");
        } catch (Exception e) {
            log.error("查询工单类型异常",e.getMessage(),e);
            restfulResults = RestfulResults.error("保存数据异常,异常信息:" + e.getMessage());
        }
        return restfulResults;
    }
}
