package com.trs.gov.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.management.DO.UnitGroupDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 11:28
 * @version 1.0
 * @since  1.0
 */
@Component
public interface UnitGroupMapper extends BaseMapper<UnitGroupDO> {

    @Update({"<script> " +
            "update unit_group set status =#{status} " +
            "where id in "+
            " <foreach item = 'item' collection = 'ids' open = '(' separator = ',' close = ')' > " +
            " #{item} " +
            " </foreach> " +
            "and is_del = 0"+
            "</script>"})
    void updateStatusByIds(@Param("status") Integer status, @Param("ids")List<Long> ids);
}
