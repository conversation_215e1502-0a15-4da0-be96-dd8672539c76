package com.trs.gov.management.service.impl;

import com.trs.upms.client.po.Resource;
import com.trs.upms.client.service.impl.UpmsRightServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/25 9:57
 * @version 1.0
 * @since  1.0
 */
@Slf4j
@Service
public class UserRightServiceImpl extends UpmsRightServiceImpl {
    @Override
    public List<Resource> queryRightResources(String resourceId, String userName) throws Exception {
        return Collections.EMPTY_LIST;
    }
}
