package com.trs.gov.management.controller;

import com.trs.common.base.Report;
import com.trs.gov.management.DTO.AddAcceptUnitDTO;
import com.trs.gov.management.DTO.OperWorkOrderTypeDTO;
import com.trs.gov.management.DTO.WorkOrderTypeDTO;
import com.trs.gov.management.DTO.WorkOrderTypeSearchDTO;
import com.trs.gov.management.VO.WorkOrderTypeLevelVO;
import com.trs.gov.management.VO.WorkOrderTypeVO;
import com.trs.gov.management.service.impl.WorkOrderTypeServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/18 13:14
 * @version 1.0
 * @since  1.0
 */
@Api(tags = "工单类型配置API")
@RestController
@RequestMapping("/management/workOrderType")
public class WorkOrderTypeController {

    @Autowired
    private WorkOrderTypeServiceImpl workOrderTypeService;

    /**
     * 获取工单类型<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/18 13:19
     * @param  dto 工单类型父级id
     * @throws
     * @return 查询结果
     */
    @ApiOperation(value = "获取工单类型")
    @RequestMapping(value = "getWorkOrderTypes",method = {RequestMethod.GET})
    public RestfulResults<List<WorkOrderTypeVO>> getWorkOrderTypes(WorkOrderTypeSearchDTO dto){
        return workOrderTypeService.getWorkOrderTypes(dto);
    }

    /**
     * 新建/编辑工单类型<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/18 13:21
     * @param  dto  请求参数
     * @throws
     * @return 保存结果z
     */
    @ApiOperation(value = "新建/编辑工单类型")
    @RequestMapping(value = "saveOrUpdateWorkOrderType",method = {RequestMethod.POST})
    public RestfulResults<Report> saveOrUpdateWorkOrderType(WorkOrderTypeDTO dto){
        return workOrderTypeService.saveOrUpdateWorkOrderType(dto);
    }

    /**
     * 删除，启用/停用工单类型<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/18 13:23
     * @param  dto 请求参数
     * @throws
     * @return 保存结果
     */
    @ApiOperation(value = "删除，启用/停用工单类型")
    @RequestMapping(value = "operWorkOrderType",method = {RequestMethod.GET})
    public RestfulResults<Report> operWorkOrderType(OperWorkOrderTypeDTO dto){
       return workOrderTypeService.operWorkOrderType(dto);
    }

    /**
     * 根据类型等级获取工单类型<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/25 12:50
     * @param  dto  请求参数
     * @throws
     * @return 查询结果
     */
    @ApiOperation(value = "根据类型等级获取工单类型")
    @RequestMapping(value = "queryWorkOrderTypeByLevel",method = {RequestMethod.GET})
    public RestfulResults<List<WorkOrderTypeLevelVO>> queryWorkOrderTypeByLevel(WorkOrderTypeSearchDTO dto){
        return workOrderTypeService.queryWorkOrderTypeByLevel(dto);
    }

    /**
     * @Description  根据顶级类型key获取该类型以及该类型所有下的工单类型id
     * @Param [rootType]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/29 17:34
     **/
    @ApiOperation(value = "根据顶级类型key获取该类型以及该类型所有下的工单类型id")
    @PostMapping("listTypeIds")
    public RestfulResults<List<Long>> listAllRelateTypeByRootType(WorkOrderTypeSearchDTO WorkOrderTypeSearchDTO){
        return workOrderTypeService.listAllRelateTypeByRootType(WorkOrderTypeSearchDTO);
    }

    /**
     * 添加投诉的受理单位<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/27 9:51
     * @param   dto   请求参数
     * @throws
     * @return
     */
    @ApiOperation(value = "添加投诉的受理单位")
    @RequestMapping(value ="addAcceptUnit",method = {RequestMethod.GET})
    public RestfulResults<Report> addAcceptUnit(AddAcceptUnitDTO dto){
        return workOrderTypeService.addAcceptUnit(dto);
    }
}
