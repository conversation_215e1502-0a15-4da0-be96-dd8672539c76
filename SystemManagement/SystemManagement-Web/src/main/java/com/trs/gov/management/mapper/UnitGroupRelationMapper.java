package com.trs.gov.management.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.management.DO.UnitGroupRelationDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/12/29 15:06
 * @version 1.0
 * @since  1.0
 */
@Component
public interface UnitGroupRelationMapper extends BaseMapper<UnitGroupRelationDO> {

    @Select({"<script> " +
            " select group_id AS groupId,COUNT(unit_id in "+
            " <foreach item = 'item' collection = 'unitIds' open = '(' separator = ',' close = ')' > " +
            " #{item} " +
            " </foreach> " +
            " OR NULL)AS unitCounts"+
            " FROM unit_group_relation   GROUP BY group_id"+
            "</script>"
    })
    List<Map<String,Long>> getGroupUnitCounts(@Param("unitIds") List<Long> unitIds);
}
