package com.trs.gov.management.config;


import com.trs.user.filter.LoginFilter;
import com.trs.user.service.IUserService;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfig {


    @Reference(check = false, timeout = 60000)
    private IUserService service;

    @Bean
    public FilterRegistrationBean registFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        LoginFilter filter = new LoginFilter(service);
        filter.setSkipFilter((request,response)->{
            String str =  request.getRequestURI();
            if(str.contains("/doc.html")||str.contains("/swagger-resources")
                || str.contains("/openapi")){
                return true;
            }
            return false;
        });
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("LoginFilter");
        registration.setOrder(1);
        return registration;
    }

}
