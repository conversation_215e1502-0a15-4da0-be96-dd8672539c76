
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.metadata.Sheet;
import model.WriteModel;
import org.junit.Test;

import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @auther
 * @description
 * @date
 **/
public class ExcelTest {

    @Test
    public void easyExcelTest() throws Exception{
        List<WriteModel> writeModels = new ArrayList<>();
        for (int i =0;i<10;i++){
            WriteModel build = WriteModel.builder().name("学生" + i).age(i + 1).build();
            writeModels.add(build);
        }
        FileOutputStream fileOutputStream = new FileOutputStream("D:\\项目\\workordersystem\\SystemManagement\\SystemManagement-Web\\tt.xlsx");
        com.alibaba.excel.ExcelWriter writer = EasyExcelFactory.getWriter(fileOutputStream);

        Sheet sheet = new Sheet(1, 0, WriteModel.class);

        sheet.setSheetName("第一个sheet名字");
        writer.write(writeModels,sheet);
        writer.finish();
        writer.hashCode();
    }

}
