
import com.trs.gov.SystemManagementApplication;
import com.trs.gov.management.VO.WorkOrderTypeForNoticeVO;
import com.trs.gov.management.service.impl.WorkOrderTypeServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @auther
 * @description
 * @date
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SystemManagementApplication.class)
public class WorkOrderTypeTest {

    @Autowired
    private WorkOrderTypeServiceImpl workOrderTypeService;
    @Test
    public void getParentInfoTest(){
        RestfulResults<WorkOrderTypeForNoticeVO> parentInfo = workOrderTypeService.getTypeInfoForOtherPart("3");
        System.out.println(parentInfo.getDatas());
    }

}
