import com.alibaba.fastjson.JSONObject;
import com.trs.gov.SystemManagementApplication;
import com.trs.gov.management.DTO.SiteRelationExportDTO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.management.mapper.SiteRelationMapper;
import com.trs.gov.management.service.SiteRelationService;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @auther
 * @description
 * @date
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SystemManagementApplication.class)
public class SiteRelationTest {

    @Reference(check = false, timeout = 60000)
    private SiteRelationService siteRelationService;
    @Autowired
    private SiteRelationMapper siteRelationMapper;
    @Test
    public void saveSiteRelate() throws Exception {
        SiteRelationExportDTO dto = new SiteRelationExportDTO();
        dto.setSiteId(503325L);
        dto.setOldSiteRelationId("fsdsfedfusdfsf");
        dto.setSiteName("我uufe是s单位");
        dto.setUniqueId("dfgdgfdffegsgugdfgd");
//        dto.setFilingTime("2020-11-04");
//        dto.setConstructionTime("2020-11-07");
        dto.setHostUnitId(179L);
        dto.setHostUnit("铜仁市教育局");
        dto.setMasterUnitId(184L);
        dto.setMasterUnit("六盘水市教育局");
        dto.setOperationUnitId(180L);
        dto.setOperationUnit("中国移动");

        dto.setOperationHost("铜仁市教育局002");
        dto.setMediaType(3);
        dto.setMediaName("微信");
//        dto.setOperationStartTime("2020-11-19");
//        dto.setOperationEndTime("2020-12-24");
//        dto.setPhone("13454534533");
        SiteRelationVO siteRelationVO = siteRelationService.exportSiteRelation(dto);
        System.out.println(JSONObject.toJSONString(siteRelationVO));
        siteRelationMapper.deleteById(siteRelationVO.getId());
    }
}
