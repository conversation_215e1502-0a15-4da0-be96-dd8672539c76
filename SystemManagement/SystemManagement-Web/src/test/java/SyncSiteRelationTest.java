import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.management.DTO.SiteRelationDTO;
import com.trs.gov.management.VO.BaseInfoVO;
import com.trs.gov.management.VO.InitInfoVO;
import com.trs.gov.management.VO.OperationUnitInfoVO;
import com.trs.gov.management.VO.SponsorInfoVO;
import com.trs.gov.management.base.constant.UnitConstant;
import com.trs.gov.management.service.SiteRelationService;
import com.trs.gov.management.service.impl.UnitServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * Description:  <BR>
 * Copyright: Copyright (c) 2004-2016 拓尔思信息技术股份有限公司 <BR>
 * Company: www.trs.com.cn <BR>
 *
 * <AUTHOR>
 * @version 1.0
 * @createTime 2022/7/29 10:42
 */
@Slf4j
public class SyncSiteRelationTest {

    public static HttpRequest httpRequest = new HttpRequest();

    @Autowired
    private UnitServiceImpl unitService;

    public static int j =0;

    @Reference(check = false, timeout = 60000)
    private SiteRelationService siteRelationService;
    @Test
    public void getSiteInfo(){
        String requestUrl = "http://***********/pub/sjwtzsk/qt/wzywtj/index.json";
        try{
          String response =  httpRequest.doGet(requestUrl);
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson.containsKey("data")){

                JSONArray dataJson = responseJson.getJSONArray("data");
                for (int i = 0; i < dataJson.size(); i++) {
                    updateUnit(dataJson.getJSONObject(i));
                }

            }
            //System.out.println(responseJson);
            log.info("返回信息：{}",responseJson);
        }catch (Exception exception){

        }

    }

    /**
     * 单位类型( 1：主管单位，2：主办单位，3：运维单位，4：厂商单位)
     */
    public void updateUnit(JSONObject siteRelationJson) throws Exception {

        //首先更新单位
        BaseInfoVO baseInfoVO = siteRelationJson.getObject("baseinfo",BaseInfoVO.class);
        System.out.println(baseInfoVO);
        if (baseInfoVO.getSitecode().equals("0")){
            System.out.println(j++);
        }
        //更新以下三个单位
        SponsorInfoVO sponsorInfoVO = siteRelationJson.getObject("sponsorinfo",SponsorInfoVO.class);

        System.out.println(sponsorInfoVO);
        InitInfoVO initInfoVO = siteRelationJson.getObject("initinfo",InitInfoVO.class);
        System.out.println(initInfoVO);
        OperationUnitInfoVO operationUnitInfoVO = siteRelationJson.getObject("operationunitinfo",OperationUnitInfoVO.class);
        System.out.println(operationUnitInfoVO);
        //主管单位 转换为unit 通过类型和名称去查询unit 如果有->更新，没有->新增

        UnitDO sponsorUnit = unitService.getUnitByTypeAndUnitName(sponsorInfoVO.getSponsor(), UnitConstant.PARAM_HOSTUNIT_ID,sponsorInfoVO,initInfoVO,operationUnitInfoVO);
        UnitDO initUnit = unitService.getUnitByTypeAndUnitName(initInfoVO.getUnit(), UnitConstant.PARAM_MASTERUNIT_ID,sponsorInfoVO,initInfoVO,operationUnitInfoVO);
        UnitDO operationUnit = unitService.getUnitByTypeAndUnitName(operationUnitInfoVO.getOperationunit(), UnitConstant.PARAM_OPERATION_ID,sponsorInfoVO,initInfoVO,operationUnitInfoVO);

        //查询站点是否存在
        //1.1 站点编码为0 直接新增
        //1.2 否则，通过站点编码查询站点，如果存在，则，更新，否则新增
        SiteRelationDTO siteRelationDTO = new SiteRelationDTO();
        siteRelationDTO.setId("0");
        siteRelationDTO.setSiteId(Long.parseLong(baseInfoVO.getSitecode()));
        siteRelationDTO.setSiteName(baseInfoVO.getSitename());
        siteRelationDTO.setUniqueId(baseInfoVO.getIcpcode());
        siteRelationDTO.setMediaType(1);
        siteRelationDTO.setHostUnitId(sponsorUnit.getId());
        siteRelationDTO.setHostUnit(sponsorUnit.getUnitName());
        siteRelationDTO.setMasterUnitId(initUnit.getId());
        siteRelationDTO.setMasterUnit(initUnit.getUnitName());
        siteRelationDTO.setOperationUnitId(operationUnit.getId());
        siteRelationDTO.setOperationUnit(operationUnit.getUnitName());
        siteRelationDTO.setOperationHost(operationUnit.getUnitMasterTrueName());
        siteRelationDTO.setPhone(operationUnit.getPhone());
        //4个时间 备案，建设，运维开始，结束
        siteRelationDTO.setFilingTime(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD));
        siteRelationDTO.setConstructionTime(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD));
        siteRelationDTO.setOperationStartTime(TimeUtils.getCurrentDate(TimeUtils.YYYYMMDD));
        siteRelationDTO.setOperationEndTime(TimeUtils.dateBefOrAft(new Date(),730,TimeUtils.YYYYMMDD));
        siteRelationService.saveOrUpdateSiteRelationByHy(siteRelationDTO);

    }


    @Test
    public void testLongEquals(){
        Long a = new Long(3);
        System.out.println(a.equals(new Long(3)));
    }


}
