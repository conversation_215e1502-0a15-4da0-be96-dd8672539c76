import com.alibaba.fastjson.JSONObject;
import com.trs.gov.SystemManagementApplication;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.management.DTO.GroupUnitListSearchDTO;
import com.trs.gov.management.DTO.UnitExportDTO;
import com.trs.gov.management.DTO.UnitGroupSearchDTO;
import com.trs.gov.management.VO.*;
import com.trs.gov.management.mapper.UnitMapper;
import com.trs.gov.management.service.UnitService;
import com.trs.gov.management.service.impl.UnitGroupRelationServiceImpl;
import com.trs.gov.management.service.impl.UnitGroupServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.List;

/**
 * @auther
 * @description
 * @date
 **/
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SystemManagementApplication.class)
public class UnitTest {

    @Autowired
    private UnitMapper unitMapper;
    @Reference(check = false, timeout = 60000)
    private UnitService unitService;

    @Autowired
    private UnitGroupRelationServiceImpl relationService;

    @Autowired
    private UnitGroupServiceImpl groupService;
    @Test
    public void lockTest(){
        UnitDO unitDO = unitMapper.selectById(10);
        UnitDO unitDO2 = unitMapper.selectById(10);
        unitDO2.setUnitName("hahahahhaPPP");
        int i = unitMapper.updateById(unitDO2);
        System.out.println(i);
        unitDO.setUnitName("乐观锁测试22");
        int i1 = unitMapper.updateById(unitDO);
        System.out.println(i1);


    }

    @Test
    public void exportUnit() throws ServiceException {
        UnitExportDTO unitExportDTO = new UnitExportDTO();
        unitExportDTO.setOldUnitId("DFSDFFERGDSfgsdgdfge");
        unitExportDTO.setUnitType("1");
        unitExportDTO.setUnitName("成都托尔思");
        unitExportDTO.setEmail("<EMAIL>");
        unitExportDTO.setPhone("13281837003");
        unitExportDTO.setUnitMaster("trs杨鑫");
        unitExportDTO.setCrTime(new Date());
        UnitVO unitVO = unitService.exportUnit(unitExportDTO);
        System.out.println(JSONObject.toJSONString(unitVO));
    }

    @Test
    public void getGroupIdTest()throws ServiceException{
        List<Long> groupByUnitId = relationService.getGroupByUnitId("1");
        System.out.println(groupByUnitId);
    }

    @Test
    public void queryUnitGroupNotRightTest() throws ServiceException {
        UnitGroupSearchDTO dto = new UnitGroupSearchDTO();
        dto.setId("7");
        List<UnitGroupVO> datas = groupService.queryUnitGroupNotRight(dto).getDatas();
        System.out.println(datas);
    }

    @Test
    public void getGroupUnitInfoListByGroupIdsTest() throws Exception {
        GroupUnitListSearchDTO dto = new GroupUnitListSearchDTO();
        dto.setToken("eae9b38b-a440-4a1c-bd12-7ac209eadcbb");
        dto.setUnitId("0");
        dto.setGroupId("1,5,7");
        RestfulResults<List<UserCountVO>> groupUnitInfoListByGroupIds = groupService.getGroupUnitInfoListByGroupIds(dto);
        System.out.println(groupUnitInfoListByGroupIds.getDatas());
    }
    @Test
    public void getGroupUserCount() throws Exception{
        GroupUnitListSearchDTO dto = new GroupUnitListSearchDTO();
        dto.setGroupId("1,5,6");
        dto.setStatus(1);
        RestfulResults<List<GroupUserCountVO>> groupUserCount = groupService.getGroupUserCount(dto);
        System.out.println(groupUserCount.getDatas());
    }

    @Test
    public void getUnitGroupRelationListTest() throws ServiceException{
        GroupUnitListSearchDTO searchDTO = new GroupUnitListSearchDTO();
        searchDTO.setGroupId("1,5");
        List<UnitGroupRelationVO> unitGroupRelationList = relationService.getUnitGroupRelationList(searchDTO);
        System.out.println(unitGroupRelationList);
    }
}
