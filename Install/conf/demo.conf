# 工单系统的网关
upstream Gateway {
    server 网关模块IP:网关模块端口;
}

server {
    listen       80;
    server_name  localhost;
######################################以下是工单的配置######################################################

    location /workorder_gw/ {
        proxy_pass http://Gateway/;
        proxy_buffering off;
        proxy_redirect off;
        proxy_read_timeout 86400;
        proxy_connect_timeout 120;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        index  index.html index.htm;
        client_max_body_size 2048m;
    }
    location /user/ {
        proxy_pass http://Gateway/user/;
        proxy_buffering off;
        proxy_redirect off;
        proxy_read_timeout 86400;
        proxy_connect_timeout 120;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $host:$server_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        index  index.html index.htm;
    }
######################################以上是工单的配置######################################################
}

