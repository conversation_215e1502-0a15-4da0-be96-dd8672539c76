# 网关配置
spring:
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
      routes:
        - id: User-Service-IDS
          uri: lb://User-Service-IDS
          predicates:
            - Path= /user/**
          filters:
            - PreserveHostHeader
        - id: ExternalSystem-Service
          uri: lb://ExternalSystem-Service
          predicates:
            - Path= /externalSystem/**
        - id: FileManager-Service
          uri: lb://FileManager-Service
          predicates:
            - Path= /file/**
        - id: Interaction-Service
          uri: lb://Interaction-Service
          predicates:
            - Path= /interaction/**
        - id: Message-Service
          uri: lb://Message-Service
          predicates:
            - Path= /message/**
        - id: Message-webSocket
          uri: lb://Message-Service
          predicates:
            - Path= /webSocket/**
        - id: Search-Service
          uri: lb://Search-Service
          predicates:
            - Path= /search/**
        - id: SystemManagement-Service
          uri: lb://SystemManagement-Service
          predicates:
            - Path= /management/**
        - id: WorkOrder-Service
          uri: lb://WorkOrder-Service
          predicates:
            - Path= /workorder/**
        - id: StatisticsCenter-Service
          uri: lb://StatisticsCenter-Service
          predicates:
            - Path= /statistics/**
        - id: KnowledgeBase-Service
          uri: lb://KnowledgeBase-Service
          predicates:
            - Path= /know/**

