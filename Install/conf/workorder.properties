# \u7528\u6237\u670D\u52A1\u76F8\u5173Redis\u7684DB
UserService.Redis.Index=1
# \u7528\u6237\u670D\u52A1\u76F8\u5173redis Key\u7684\u6709\u6548\u671F\uFF08\u5355\u4F4D\uFF1A\u79D2\uFF09
UserService.Redis.KeyExpireSecond=3600
# \u5355\u70B9\u6A21\u5F0F\u65F6redis\u7684IP
spring.redis.host=***************
# \u5355\u70B9\u6A21\u5F0F\u65F6redis\u7684\u7AEF\u53E3
spring.redis.port=6379
# \u4E0A\u4F20\u7684\u6587\u4EF6\u5927\u5C0F
spring.servlet.multipart.max-file-size=10000MB
# \u4E0A\u4F20\u8BF7\u6C42\u5927\u5C0F
spring.servlet.multipart.max-request-size=10000MB
# \u6587\u4EF6\u4E0A\u4F20\u7684\u6839\u8DEF\u5F84
FileManagerService.UploadDir=/TRS/DATA/WORKORDER/
# \u5141\u8BB8\u4E0A\u4F20\u7684\u6587\u4EF6\u540E\u7F00
FileManagerService.AllowFileExts=jpg;jpeg;png;gif;xls;xlsx;doc;docx;ppt;pdf;pptx;zip;rar;mp4
# \u6587\u4EF6\u7684Url\u524D\u7F00
FileManagerService.UrlPrefix=http://***************:81
# \u6587\u4EF6\u4E0B\u8F7D\u7684Url\u524D\u7F00 [\u524D\u7AEF\u8BBF\u95EE\u5730\u5740 + \u7AEF\u53E3 + file/downloadFile]
FileManagerService.downloadUrlPrefix=http://***************:81/file/downloadFile
# \u6743\u9650\u7CFB\u7EDF
upmsApiUrl.vaule=http://***************:8891/upms/group.do
# \u6295\u8BC9\u5355\u4F4D
complaint.unitId=2
# \u6743\u9650\u7CFB\u7EDF\u5B50\u7CFB\u7EDF\u6807\u8BC6
upms.client.tenant=chqdnh
# \u6743\u9650\u7CFB\u7EDF\u5B50\u7CFB\u7EDF\u8D44\u6E90\u6807\u8BC6
upms.client.resourcetype=chqdnh
# \u6743\u9650\u7CFB\u7EDF\u670D\u52A1\u5668\u5730\u5740
upms.service.url=http://***************:8891/upms

#\u6D77\u4E91\u5DF2\u6DFB\u52A0\u5230\u767D\u540D\u5355\u7684\u7528\u6237(\u7528\u6237\u8BBF\u95EE\u6D77\u4E91\u63A5\u53E3\u6743\u9650)
hycloud.currUserName = dev
#\u8BBF\u95EE\u6D77\u4E91url
hycloud.url = http://***************/gov/opendata.do
#\u6D77\u8FD0\u63A5\u53E3\u670D\u52A1\u540D\u79F0
hycloud.serviceid = gov_site
#\u6D77\u8FD0\u63A5\u53E3\u65B9\u6CD5\u540D\u79F0
hycloud.methodname = querySitesOnEditorCenter

# \u670D\u52A1\u5355\u4F4D\u5728IDS\u4E2D\u9ED8\u8BA4\u7684\u526F\u7EC4\u7EC7
IDS.Group.parentGroupId=64

#\u83B7\u53D6\u76D1\u6D4B\u4E91\u7AD9\u70B9\u6570\u636E\u63A5\u53E3\u5730\u5740
monitorSite.url = http://isp.devdemo.trs.net.cn/isp/http/exposion
#\u83B7\u53D6\u76D1\u6D4B\u4E91\u7AD9\u70B9\u6570\u636E\u8BF7\u6C42\u53C2\u6570
#\u56FA\u5B9A\u503C
monitorSite.param.isp_h = /wmcs/opendata/sites
#\u56FA\u5B9A\u503C
monitorSite.param.isp_a = 21
#\u56FA\u5B9A\u503C
monitorSite.param.isp_s = 144
#\u56FA\u5B9A\u503C
monitorSite.param.isp_v = 1
#\u76D1\u6D4B\u4E91\u79DF\u6237Id(\u975E\u56FA\u5B9A\u503C)
monitorSite.param.tenantId = 52
#\u76D1\u6D4B\u4E91\u4E2D\u7684\u7528\u6237\u540D(\u975E\u56FA\u5B9A\u503C)
monitorSite.param.userName = manager
#\u624B\u673A\u53F7\u7801(\u975E\u56FA\u5B9A\u503C)
monitorSite.param.phone = 17721866595
#token\u83B7\u53D6\u5730\u5740
monitorSite.access_token.url = http://isp.devdemo.trs.net.cn:80/isp/oauth/token?username=service_144,application_21,version_1&password=service_144,application_21,version_1&grant_type=password&scope=select&client_id=client_1&client_secret=123456

#es\u76F8\u5173\u914D\u7F6E
es.dataSource.url=http://10.11.2.114:9200
es.date.format = yyyy-MM-dd'T'HH:mm:ss.SSS'Z'

#\u5DE5\u5355\u6D88\u606F\u6A21\u5757
#\u901A\u77E5\u6D88\u606F\u5C06\u8981\u63A8\u9001\u81F3\u7684\u5E73\u53F0,\u901A\u8FC7\u4ECE\u5DE6\u5230\u53F3\u7684\u987A\u5E8F\u8FDB\u884C\u6267\u884C\u63A8\u9001,\u5EFA\u8BAE\u5C06\u63A8\u9001\u81F3\u5DE5\u5355\u5199\u5728\u6700\u524D\u9762,\u9632\u6B62\u63A8\u9001\u81F3\u7B2C\u4E09\u65B9\u5E73\u53F0\u65F6\u7F51\u7EDC\u65F6\u95F4\u7684\u54CD\u5E94\u95EE\u9898
# 1.\u5DE5\u5355,2.\u7B2C\u4E09\u65B9\u63A5\u53E3
#different.channel.list=gz_work_order,third_party_interface
#\u63A8\u9001\u81F3\u7B2C\u4E09\u65B9\u63A5\u53E3\u4E2D\u90A3\u4E9B\u6E20\u9053,\u591A\u4E2A\u7528\u9017\u53F7\u9694\u5F00 1.\u5FAE\u4FE1\u516C\u4F17\u53F7
thirdPartyInterface.of.channels=wxgzh
#\u63A8\u9001\u7684\u7528\u6237\u540D
push.user-name=szh_tes_user
#\u63A8\u9001\u7684\u6388\u6743\u7801
push.auth-code=WOi3PjVsb8JYFSz7
#token \u7684\u8FC7\u671F\u65F6\u95F4 10\u5206\u949F
token.expiration.time=600
#token\u5B58\u5728\u7684\u7D22\u5F15\u5E93
token.expiration.index=10
#\u83B7\u53D6tokend\u7684Url\u5730\u5740
token.url=https://117.187.141.80/index.php/token
# \u63A8\u9001\u6570\u636E\u5730\u5740
push.url=https://117.187.141.80/index.php/notice
# \u7B2C\u4E00\u6B21\u53D1\u9001\u5931\u8D25\u540E,\u9700\u8981\u91CD\u8BD5\u7684\u6B21\u6570
fail.retry.send.count=2
##\u6CE8\u610F:\u8BE5\u914D\u7F6E\u4E0D\u662F\u65B0\u589E\u914D\u7F6E,\u800C\u662F\u4FEE\u6539\u5DF2\u6709\u7684\u914D\u7F6E\u503C ## \u65B0\u589E\u4E00\u4E2A\u77ED\u4FE1\u6E20\u9053  1.\u5DE5\u5355\u81EA\u8EAB\u7684webstocket 2.\u77ED\u4FE1\u6E20\u9053  3.\u7B2C\u4E09\u5E73\u53F0  \u5FAE\u4FE1\u516C\u4F17\u53F7
different.channel.list=gz_work_order,sms_platform,third_party_interface
# sms \u6279\u91CF\u53D1\u9001\u77ED\u4FE1\u7684\u5730\u5740
sms.batch.url=http://1.207.107.56/smsapi/smsapi/batchSend.json
# sms \u53D1\u9001\u77ED\u4FE1\u5E73\u53F0\u7684\u8D26\u53F7
sms.account=ys20100026
# sms \u53D1\u9001\u77ED\u4FE1\u5E73\u53F0\u7684\u5BC6\u7801
sms.password=Gzszfwzjyh01@
# \u77ED\u4FE1\u6A21\u677F\u76F8\u5173\u914D\u7F6E,\u5982\u679C\u4E0D\u914D\u7F6E\u9ED8\u8BA4\u662F\u4EE5\u4E0B\u4FE1\u606F
sms.template.hasDealEd=[TRUENAME]\uFF1A\u60A8\u597D\uFF0C\u60A8\u5728\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u7CFB\u7EDF\u63D0\u4EA4\u7684\u95EE\u9898\u5DF2\u8FDB\u884C\u53D7\u7406\uFF0C\u8BF7\u7B49\u5F85\u5904\u7406\u3002\u540E\u7EED\u6709\u95EE\u9898\u53EF\u8054\u7CFB0851-********\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u670D\u52A1\u70ED\u7EBF\u3002
sms.template.hasFinishEd=[TRUENAME]\uFF1A\u60A8\u597D\uFF0C\u60A8\u5728\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u7CFB\u7EDF\u63D0\u4EA4\u7684\u95EE\u9898\u5DF2\u8FDB\u884C\u5B8C\u6210\u5904\u7406\uFF0C\u8BF7\u767B\u5F55\u7CFB\u7EDF\u8FDB\u884C\u67E5\u770B\u3002\u540E\u7EED\u6709\u95EE\u9898\u53EF\u8054\u7CFB0851-********\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u670D\u52A1\u70ED\u7EBF\u3002
sms.template.hasAppraise=[TRUENAME]\uFF1A\u60A8\u597D\uFF0C\u60A8\u5728\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u7CFB\u7EDF\u5904\u7406\u7684\u95EE\u9898\u5DF2\u5B8C\u6210\u8BC4\u4EF7\uFF0C\u8BF7\u767B\u5F55\u7CFB\u7EDF\u8FDB\u884C\u67E5\u770B\u3002\u540E\u7EED\u6709\u95EE\u9898\u53EF\u8054\u7CFB0851-********\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u670D\u52A1\u70ED\u7EBF\u3002
sms.template.waitDeal=[TRUENAME]\uFF1A\u60A8\u597D\uFF0C[UNITNAME]\u5728\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u7CFB\u7EDF\u5C06\u95EE\u9898\u63D0\u4EA4\u5230[DEALUNITNAME]\u8FDB\u884C\u5904\u7406\uFF0C\u8BF7\u767B\u5F55\u7CFB\u7EDF\u8FDB\u884C\u67E5\u770B\u5904\u7406\u3002\u540E\u7EED\u6709\u95EE\u9898\u53EF\u8054\u7CFB0851-********\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u670D\u52A1\u70ED\u7EBF\u3002
sms.template.assignOrCopy=[TRUENAME]\uFF1A\u60A8\u597D\uFF0C[UNITNAME]\u5728\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u7CFB\u7EDF\u5C06\u95EE\u9898\u8F6C\u529E/\u6284\u9001\u5230[DEALUNITNAME]\u8FDB\u884C\u5904\u7406\uFF0C\u8BF7\u767B\u5F55\u7CFB\u7EDF\u8FDB\u884C\u67E5\u770B\u5904\u7406\u3002\u540E\u7EED\u6709\u95EE\u9898\u53EF\u8054\u7CFB0851-********\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u670D\u52A1\u70ED\u7EBF\u3002
sms.template.hasRollback=\u3010\u96C6\u7EA6\u5316\u5E73\u53F0\u3011[TRUENAME]\uFF1A\u60A8\u597D\uFF0C\u60A8\u5728\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u7CFB\u7EDF\u5904\u7406\u7684\u5DE5\u5355\uFF08[WORKORDERID]\uFF09\u56E0\u7F3A\u4E4F\u5173\u952E\u4FE1\u606F\u6216\u9700\u6C42\u4E0D\u660E\u5DF2\u88AB\u56DE\u9000\uFF0C\u8BF7\u767B\u5F55\u7CFB\u7EDF\u8FDB\u884C\u67E5\u770B\u5E76\u8FDB\u884C\u5B8C\u5584\u3002\u540E\u7EED\u6709\u95EE\u9898\u53EF\u8054\u7CFB0851-********\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u670D\u52A1\u70ED\u7EBF\u3002
sms.template.hasMasterUser=[TRUENAME]\uFF1A\u60A8\u597D\uFF0C[UNITNAME]\u5728\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u7CFB\u7EDF\u521B\u5EFA\u5DE5\u5355\uFF08[WORKORDERID]\uFF09\uFF0C\u8BF7\u767B\u5F55\u7CFB\u7EDF\u8FDB\u884C\u67E5\u770B\u5904\u7406\u3002\u540E\u7EED\u6709\u95EE\u9898\u53EF\u8054\u7CFB0851-********\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u670D\u52A1\u70ED\u7EBF\u3002
sms.template.hasNoticeMessage=[TRUENAME]\uFF1A\u60A8\u597D\uFF0C\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u5DE5\u4F5C\u7EC4\u5728\u96C6\u7EA6\u5316\u5E73\u53F0\u5DE5\u5355\u7CFB\u7EDF\u53D1\u5E03\u4E86\u4E00\u5219\u901A\u77E5\uFF0C\u5DE5\u5355\u7F16\u53F7\uFF1A[WORKORDERID]\uFF0C\u8BF7\u60A8\u53CA\u65F6\u767B\u5F55\u7CFB\u7EDF\u8FDB\u884C\u67E5\u770B\u5904\u7406\u3002\u540E\u7EED\u6709\u95EE\u9898\u53EF\u8054\u7CFB\u96C6\u7EA6\u5316\u5E73\u53F0\u8FD0\u7EF4\u5DE5\u4F5C\u7EC4\uFF08\u8054\u7CFB\u7535\u8BDD\uFF1A0851-********\uFF09\u3002


#\u589E\u52A0Es\u7D22\u5F15\u540D\u5B57\u548C\u7D22\u5F15\u7C7B\u578B\u914D\u7F6E
workorder.indeces-name=work_order
#notice.indices-type=notice_detail
notice.indices-type=noticedetail
#siteRelation.indices-type=site_relation
siteRelation.indices-type=siterelation
#unit.indices-type=unit
unit.indices-type=unit
#user.indices-type=user
user.indices-type=user
#workorder.indices-type=work_order_detail
workorder.indices-type=workorderdetail
#\u975E\u5DE5\u4F5C\u65F6\u95F4\u521B\u5EFA\u5DE5\u5355\u81EA\u52A8\u56DE\u590D\u5185\u5BB9
workOrder.autoReply.content=\u3010\u81EA\u52A8\u56DE\u590D\u3011\u4F9D\u636E\u300A\u8D35\u5DDE\u7701\u653F\u5E9C\u7F51\u7AD9\u96C6\u7EA6\u5316\u5E73\u53F0\u5DE5\u5355\u529E\u7406\u529E\u6CD5\uFF08\u8BD5\u884C\uFF09\u300B\uFF0C\u975E\u5DE5\u4F5C\u65F6\u95F4\u63D0\u4EA4\u5DE5\u5355\uFF0C\u539F\u5219\u4E0A\u987A\u5EF6\u81F3\u5DE5\u4F5C\u65F6\u95F4\u5904\u7406\uFF0C\u5982\u6025\u9700\u5904\u7406\uFF0C\u8BF7\u62E8\u625313027880860\u30010851-********\u300117152144142\uFF0C\u6211\u4EEC\u5C06\u5728\u7B2C\u4E00\u65F6\u95F4\u5904\u7406\u3002\u8C22\u8C22\uFF01
#\u5F00\u542F\u975E\u5DE5\u4F5C\u65F6\u95F4\u521B\u5EFA\u5DE5\u5355\u81EA\u52A8\u56DE\u590D\u5185\u5BB9-\u9ED8\u8BA4\u5173\u95ED
workOrder.autoReply.content.open=false

#\u5DE5\u5355\u8003\u6838\uFF0C\u5206\u914D\u5DE5\u53555\u5206\u949F\u5185\u9700\u8981\u54CD\u5E94
#\u9700\u8981\u81EA\u52A8\u56DE\u590D\u7684\u53D7\u7406\u65B9\u4EBA\u5458\u540D\u5355\uFF0C\u201C\uFF0C\u201D\u9694\u5F00
workOrder.autoResponse.person.name=\u8D1F\u8D23\u4EBA
#\u81EA\u52A8\u56DE\u590D\u5185\u5BB9
workOrder.autoReply.person.content=\u60A8\u597D\uFF0C\u60A8\u63D0\u7684\u95EE\u9898\uFF0F\u9700\u6C42\u5DF2\u6536\u5230\uFF0C\u6B63\u5728\u5904\u7406\u4E2D\uFF0C\u8BF7\u8010\u5FC3\u7B49\u5F85\uFF0C\u7A0D\u540E\u7ED9\u60A8\u56DE\u590D\u3002

#\u5DE5\u5355\u6D41\u8F6C\u77ED\u4FE1\u63D0\u9192\uFF0C\u540C\u6B65\u77ED\u4FE1\u6D88\u606F\u5230\u5FAE\u4FE1\u516C\u4F17\u53F7
#\u662F\u5426\u5F00\u542F\u77ED\u4FE1\u6D88\u606F\u540C\u6B65\u5230\u5FAE\u4FE1\u516C\u4F17\u53F7\uFF0C\u9ED8\u8BA4false
gzh.push.open=false
#\u516C\u4F17\u53F7\u63A5\u6536\u7AEF\u7528\u6237\u540D
gzh.user-name=jhyptgdxt
#\u516C\u4F17\u53F7\u63A5\u6536\u7AEF\u7528\u6237\u5BC6\u7801
gzh.auth-code=+HGR8k$v
#\u516C\u4F17\u53F7\u63A5\u6536\u7AEFtoken\u6709\u6548\u671F
gzh.token.expiration.time=119
#redis\u5B58\u653Etoken\u7684index
gzh.token.expiration.index=10
#\u4ECE\u516C\u4F17\u53F7\u83B7\u53D6token\u7684url
gzh.token.url=http://message.guizhou.gov.cn/api/receive/public/xml/getToken
#\u4ECE\u516C\u4F17\u53F7\u63A8\u9001\u6D88\u606F\u7684url
gzh.push.url=http://message.guizhou.gov.cn/api/receive/public/message

#\u6D77\u4E91\u7AD9\u70B9\u4FE1\u606F\u83B7\u53D6\u7684URL
hy.site.info.url=http://10.11.2.144/pub/sjwtzsk/qt/wzywtj/index.json
#\u4ECE\u6D77\u4E91\u540C\u6B65\u7AD9\u70B9\u5173\u7CFB
systemManagementService.syncSiteRelation.cron=0 0 23 * * ?
#\u540C\u6B65\u66F4\u65B0\u5355\u4F4D\u7F16\u7801
systemManagementService.syncUnitCode.cron=0 40 15 * * ?
