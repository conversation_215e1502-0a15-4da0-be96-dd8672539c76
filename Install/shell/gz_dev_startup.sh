#!/bin/bash
APP_HOME="/TRS/WORKORDER"
echo -e  "#############1：User-Service-IDS-1.0.jar#############\n#############2：WorkOrder-Web-1.0.jar#############\n#############3：FileManager-Web-1.0.jar#############\n#############4：Message-Web-1.0.jar#############\n#############5：SystemManagement-Web-1.0.jar#############\n#############6：Search-Web-1.0.jar#############\n#############7：StatisticsCenter-Web-1.0.jar#############\n#############8：Interaction-Web-1.0.jar#############\n#############9：ExternalSystem-Web-1.0.jar#############\n#############10:Gateway-1.0.jar#############"
read -n 2 -p "请选择要启动的应用(应用的编号)：" num
if [ "$num" -eq "1" ]; then
APP_PORT=18080
PROJECT="User-Service-IDS-1.0"
elif [ "$num" -eq "2" ];then
APP_PORT=18081
PROJECT="WorkOrder-Web-1.0"
elif [ "$num" -eq "3" ];then
APP_PORT=18082
PROJECT="FileManager-Web-1.0"
elif [ "$num" -eq "4" ];then
APP_PORT=18083
PROJECT="Message-Web-1.0"
elif [ "$num" -eq "5" ];then
APP_PORT=18084
PROJECT="SystemManagement-Web-1.0"
elif [ "$num" -eq "6" ];then
APP_PORT=18085
PROJECT="Search-Web-1.0"
elif [ "$num" -eq "7" ];then
APP_PORT=18086
PROJECT="StatisticsCenter-Web-1.0"
elif [ "$num" -eq "8" ];then
APP_PORT=18087
PROJECT="Interaction-Web-1.0"
elif [ "$num" -eq "9" ];then
APP_PORT=18088
PROJECT="ExternalSystem-Web-1.0"
elif [ "$num" -eq "10" ];then
APP_PORT=18089
PROJECT="Gateway-1.0"
else echo "输入类型错误";
exit
fi
pid=`/usr/sbin/lsof -n -P -t -i :$APP_PORT`
echo "current :" $pid
while [ -n "$pid" ]
do
 sleep 5
 pid=`/usr/sbin/lsof -n -P -t -i :$APP_PORT`
 echo "scan pid :" $pid
 if [ -n "$pid" ]; then
   echo "kill msg :" $pid
   kill -9 $pid
 fi
done
echo "start $PROJECT"
nohup java -javaagent:/TRS/skywalking/skywalking/agent/skywalking-agent.jar -Dskywalking.agent.service_name=${PROJECT} -Dskywalking.collector.backend_service=10.11.2.148:11800 -Dserver.port=${APP_PORT} -Djava.security.egd=file:/dev/./urandom -Xms512m -Xmx512m -Xmn256m -jar ${APP_HOME}/$PROJECT.jar>${APP_HOME}/$PROJECT.log 2>&1 & 
echo "$PROJECT is starting"
