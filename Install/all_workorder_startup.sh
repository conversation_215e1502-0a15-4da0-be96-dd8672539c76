APP_HOME="/TRS/worksheet"
echo "start User-Service-IDS jar"
nohup java -Dserver.port=18080 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/User-Service-IDS-1.0.jar >${APP_HOME}/User-Service-IDS-1.0.log & echo "User-Service-IDS is starting"
sleep 1
echo "start WorkOrder-Web-1.0.jar"
nohup java -Dserver.port=18081 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/WorkOrder-Web-1.0.jar >${APP_HOME}/WorkOrder-Web-1.0.log & echo "WorkOrder-Web is starting"
sleep 1
echo "start FileManager-Web-1.0.jar"
nohup java -Dserver.port=18082 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/FileManager-Web-1.0.jar >${APP_HOME}/FileManager-Web-1.0.log & echo "FileManager is starting"
sleep 1
echo "start Message-Web-1.0.jar"
nohup java -Dserver.port=18083 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/Message-Web-1.0.jar >${APP_HOME}/Message-Web-1.0.log & echo "Message-Web is starting"
sleep 1
echo "start SystemManagement-Web-1.0.jar"
nohup java -Dserver.port=18084 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/SystemManagement-Web-1.0.jar >${APP_HOME}/SystemManagement-Web-1.0.log & echo "SystemManagement-Web is starting"
sleep 1
echo "start Search-Web-1.0.jar"
nohup java -Dserver.port=18085 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/Search-Web-1.0.jar >${APP_HOME}/Search-Web-1.0.log & echo "Search-Web is starting"
sleep 1
echo "start StatisticsCenter-Web-1.0.jar"
nohup java -Dserver.port=18086 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/StatisticsCenter-Web-1.0.jar >${APP_HOME}/StatisticsCenter-Web-1.0.log & echo "StatisticsCenter-Web is starting"
sleep 1
echo "start Interaction-Web-1.0.jar"
nohup java -Dserver.port=18087 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/Interaction-Web-1.0.jar >${APP_HOME}/Interaction-Web-1.0.log & echo "Interaction-Web is starting"
sleep 1
echo "start ExternalSystem-Web-1.0.jar"
nohup java -Dserver.port=18088 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/ExternalSystem-Web-1.0.jar >${APP_HOME}/ExternalSystem-Web-1.0.log & echo "ExternalSystem-Web is starting"
sleep 1
echo "start Gateway-1.0.jar"
nohup java -Dserver.port=18089 -Djava.security.egd=file:/dev/./urandom -Xms256m -Xmx256m -Xmn256m -jar ${APP_HOME}/Gateway-1.0.jar >${APP_HOME}/Gateway-1.0.log & echo "Gateway is starting"
sleep 1
