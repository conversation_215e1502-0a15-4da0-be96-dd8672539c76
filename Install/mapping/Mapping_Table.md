## 贵州使用的2.3.3版本的Es,即一个索引类型下多个索引类型,索引名字都是:work_order

### 工单表索引（索引名字:work_order;索引类型:work_order_detail）

```
{
    "properties":{
        "id":{
            "type":"long"
        },
        "cr_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "cr_user":{
            "type":"string",
            "index":"not_analyzed"
        },
        "work_order_top_type_id":{
            "type":"long"
        },
        "work_order_top_type_name":{
            "type":"string",
            "index":"not_analyzed"
        },
        "work_order_type_id":{
            "type":"long"
        },
        "work_order_type_name":{
            "type":"string",
            "index":"not_analyzed"
        },
        "cr_unit_id":{
            "type":"long"
        },
        "cr_unit":{
            "type":"string",
            "index":"not_analyzed"
        },
        "cr_username":{
            "type":"string",
            "index":"not_analyzed"
        },
        "cr_truename":{
            "type":"string",
            "index":"not_analyzed"
        },
        "site_id":{
            "type":"long"
        },
        "sitename":{
            "type":"string",
            "index":"not_analyzed"
        },
        "content":{
            "type":"string"
        },
        "priority":{
            "type":"string",
            "index":"not_analyzed"
        },
        "host_unit_id":{
            "type":"long"
        },
        "host_unit":{
            "type":"string",
            "index":"not_analyzed"
        },
        "host_assign_type":{
            "type":"integer"
        },
        "host_username":{
            "type":"string",
            "index":"not_analyzed"
        },
        "host_truename":{
            "type":"string",
            "index":"not_analyzed"
        },
        "deal_unit_id":{
            "type":"long"
        },
        "deal_unit":{
            "type":"string",
            "index":"not_analyzed"
        },
        "deal_assign_type":{
            "type":"integer"
        },
        "deal_username":{
            "type":"string",
            "index":"not_analyzed"
        },
        "deal_truename":{
            "type":"string",
            "index":"not_analyzed"
        },
        "media_type":{
            "type":"integer"
        },
        "expected_end_date":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "update_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "receive_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "is_delete":{
            "type":"integer"
        },
        "status":{
            "type":"integer"
        },
        "action_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "is_return":{
            "type":"integer"
        },
        "work_order_parent_type_id":{
            "type":"long"
        },
        "work_order_parent_type_name":{
            "type":"string",
            "index":"not_analyzed"
        },
        "source":{
            "type":"string",
            "index":"not_analyzed"
        }
    },
    "_meta":{
        "properties":[
            {
                "cnName":"工单ID",
                "name":"id",
                "selected":true
            },
            {
                "cnName":"创建时间",
                "name":"cr_time",
                "selected":true
            },
            {
                "cnName":"创建人",
                "name":"cr_user",
                "selected":true
            },
            {
                "cnName":"顶级类型id",
                "name":"work_order_top_type_id",
                "selected":true
            },
            {
                "cnName":"顶级类型名字",
                "name":"work_order_top_type_name",
                "selected":true
            },
            {
                "cnName":"work_order_type_id",
                "name":"工单类型ID",
                "selected":true
            },
            {
                "cnName":"工单类型名字",
                "name":"work_order_type_name",
                "selected":true
            },
            {
                "cnName":"创建单位ID",
                "name":"cr_unit_id",
                "selected":false
            },
            {
                "cnName":"创建单位名字",
                "name":"cr_unit",
                "selected":true
            },
            {
                "cnName":"创建单位用户名",
                "name":"cr_username",
                "selected":true
            },
            {
                "cnName":"创建人真实姓名",
                "name":"cr_truename",
                "selected":true
            },
            {
                "cnName":"站点ID",
                "name":"site_id",
                "selected":true
            },
            {
                "cnName":"站点名字",
                "name":"sitename",
                "selected":true
            },
            {
                "cnName":"工单内容",
                "name":"content",
                "selected":true
            },
            {
                "cnName":"紧急情况",
                "name":"priority",
                "selected":true
            },
            {
                "cnName":"主办单位ID",
                "name":"host_unit_id",
                "selected":true
            },
            {
                "cnName":"主办单位名字",
                "name":"host_unit",
                "selected":true
            },
            {
                "cnName":"指定类型",
                "name":"host_assign_type",
                "selected":true
            },
            {
                "cnName":"主办人",
                "name":"host_username",
                "selected":true
            },
            {
                "cnName":"主办人真实姓名",
                "name":"host_truename",
                "selected":true
            },
            {
                "cnName":"受理单位ID",
                "name":"deal_unit_id",
                "selected":true
            },
            {
                "cnName":"受理单位名字",
                "name":"deal_unit",
                "selected":true
            },
            {
                "cnName":"受理指令类型",
                "name":"deal_assign_type",
                "selected":true
            },
            {
                "cnName":"受理人",
                "name":"deal_username",
                "selected":true
            },
            {
                "cnName":"受理人真实姓名",
                "name":"deal_truename",
                "selected":true
            },
            {
                "cnName":"渠道类型",
                "name":"media_type",
                "selected":true
            },
            {
                "cnName":"期望完成时间",
                "name":"expected_end_date",
                "selected":true
            },
            {
                "cnName":"更新时间",
                "name":"update_time",
                "selected":true
            },
            {
                "cnName":"收到工单时间",
                "name":"receive_time",
                "selected":true
            },
            {
                "cnName":"是否删除",
                "name":"is_delete",
                "selected":true
            },
            {
                "cnName":"工单状态",
                "name":"status",
                "selected":true
            },
            {
                "cnName":"相应时间",
                "name":"action_time",
                "selected":true
            },
            {
                "cnName":"是否回退",
                "name":"is_return",
                "selected":true
            },
            {
                "cnName":"工单父类型id",
                "name":"work_order_parent_type_id",
                "selected":true
            },
            {
                "cnName":"工单父类型名字",
                "name":"work_order_parent_type_name",
                "selected":true
            },
            {
                "cnName":"工单来源",
                "name":"source",
                "selected":true
            }
        ]
    }
}
```

### 通知表索引(索引名字:work_order;索引类型:notice_detail)

```json
{
    "properties":{
        "id":{
            "type":"long"
        },
        "cr_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "cr_user":{
            "type":"string",
            "index":"not_analyzed"
        },
        "cr_truename":{
            "type":"string",
            "index":"not_analyzed"
        },
        "cr_unit_id":{
            "type":"long"
        },
        "cr_unit_name":{
            "type":"string",
            "index":"not_analyzed"
        },
        "content":{
            "type":"string"
        },
        "work_order_id":{
            "type":"long"
        },
        "group_id":{
            "type":"integer"
        },
        "type":{
            "type":"integer"
        },
        "target_username":{
            "type":"string",
            "index":"not_analyzed"
        },
        "target_unit_id":{
            "type":"long"
        }
    },
    "_meta":{
        "properties":[
            {
                "cnName":"通知ID",
                "name":"id",
                "selected":true
            },
            {
                "cnName":"创建时间",
                "name":"cr_time",
                "selected":true
            },
            {
                "cnName":"通知创建人用户名",
                "name":"cr_user",
                "selected":true
            },
            {
                "cnName":"创建人用户名",
                "name":"cr_truename",
                "selected":true
            },
           
            {
                "cnName":"创建人单位",
                "name":"cr_unit_id",
                "selected":true
            },
            {
                "cnName":"创建单位名称",
                "name":"cr_unit_name",
                "selected":true
            },
            {
                "cnName":"内容",
                "name":"content",
                "selected":true
            },
            {
                "cnName":"工单ID",
                "name":"work_order_id",
                "selected":true
            },
            {
                "cnName":"组织ID",
                "name":"group_id",
                "selected":true
            },
            {
                "cnName":"通知类型",
                "name":"type",
                "selected":true
            },
            {
                "cnName":"目标人",
                "name":"target_username",
                "selected":true
            },
            {
                "cnName":"目标单位ID",
                "name":"target_unit_id",
                "selected":true
            }
        ]
    }
}
```

### 用户表索引（索引名字:work_order;索引类型:user）

```json
{
    "properties":{
        "id":{
          "type":"long"
        },
        "cr_time":{
          "type":"date",
          "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "cr_user":{
          "type":"string",
          "index":"not_analyzed"
        },
        "status":{
          "type":"integer"
        },
        "user_name":{
          "type": "string"
        },
        "password":{
          "type": "string",
          "index":"not_analyzed"
        },
        "true_name":{
          "type": "string"
        },
        "email":{
          "type":"string",
          "index":"not_analyzed"
        },
        "phone":{
          "type":"string",
          "index":"not_analyzed"
        },
        "source_key":{
          "type":"string",
          "index":"not_analyzed"
        },
        "source_user_id":{
          "type":"string",
          "index":"not_analyzed"
        },
        "avatar":{
          "type":"string",
          "index":"not_analyzed"
        }
    },
    "_meta":{
        "properties":[
            {
                "cnName":"用户ID",
                "name":"id",
                "selected":true
            },
            {
                "cnName":"创建时间",
                "name":"cr_time",
                "selected":true
            },
            {
                "cnName":"创建人用户名",
                "name":"cr_user",
                "selected":true
            },
            {
                "cnName":"用户状态",
                "name":"status",
                "selected":true
            },
            {
                "cnName":"用户名",
                "name":"user_name",
                "selected":true
            },
            {
                "cnName":"密码",
                "name":"password",
                "selected":true
            },
            {
                "cnName":"真实姓名",
                "name":"true_name",
                "selected":true
            },
            {
                "cnName":"邮箱",
                "name":"email",
                "selected":true
            },
            {
                "cnName":"电话号码",
                "name":"phone",
                "selected":true
            },
            {
                "cnName":"来源key",
                "name":"source_key",
                "selected":false
            },
            {
                "type":"来源Id",
                "name":"source_user_id",
                "selected":false
            },
            {
                "type":"头像",
                "name":"avatar",
                "selected":false
            }
        ]
    }
}
```



### 站点关系索引(索引名字:work_order;索引类型:site_relation)

```json
{
    "properties":{
        "id":{
            "type":"long"
        },
        "cr_user":{
            "type":"string",
            "index":"not_analyzed"
        },
        "site_id":{
            "type":"long"
        },
        "site_name":{
            "type":"string"
        },
        "media_type":{
            "type":"integer"
        },
        "unique_id":{
            "type":"string",
            "index":"not_analyzed"
        },
        "filing_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "construction_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "host_unit_id":{
            "type":"long"
        },
        "host_unit":{
            "type":"string",
            "index":"not_analyzed"
        },
        "master_unit_id":{
            "type":"long"
        },
        "master_unit":{
            "type":"string",
            "index":"not_analyzed"
        },
        "operation_unit_id":{
            "type":"long"
        },
        "operation_unit":{
            "type":"string",
            "index":"not_analyzed"
        },
        "operation_host":{
            "type":"string",
            "index":"not_analyzed"
        },
        "operation_start_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "operation_end_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "monitor_site":{
            "type":"string"
        },
        "update_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "is_del":{
            "type":"integer"
        },
        "cr_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "status":{
            "type":"integer"
        },
        "media_name":{
            "type":"string"
        },
        "phone":{
            "type":"string",
            "index":"not_analyzed"
        },
        "true_name":{
            "type":"string"
        }
    },
    "_meta":{
        "properties":[
            {
                "cnName":"站点ID",
                "name":"id",
                "selected":true
            },
            {
                "cnName":"创建人",
                "name":"cr_user",
                "selected":true
            },
            {
                "cnName":"站点ID",
                "name":"site_id",
                "selected":true
            },
            {
                "cnName":"站点名称",
                "name":"site_name",
                "selected":true
            },
            {
                "cnName":"媒体类型",
                "name":"media_type",
                "selected":true
            },
            {
                "cnName":"唯一标识",
                "name":"unique_id",
                "selected":true
            },
            {
                "cnName":"备案时间",
                "name":"filing_time",
                "selected":true
            },
            {
                "cnName":"建设时间",
                "name":"construction_time",
                "selected":true
            },
            {
                "cnName":"主办单位ID",
                "name":"host_unit_id",
                "selected":true
            },
            {
                "cnName":"主办单位",
                "name":"host_unit",
                "selected":true
            },
            {
                "cnName":"主管单位ID",
                "name":"master_unit_id",
                "selected":true
            },
            {
                "cnName":"主管单位名字",
                "name":"master_unit",
                "selected":true
            },
            {
                "cnName":"运维单位ID",
                "name":"operation_unit_id",
                "selected":true
            },
            {
                "cnName":"运维单位名称",
                "name":"operation_unit",
                "selected":true
            },
            {
                "cnName":"运维单位负责人",
                "name":"operation_host",
                "selected":true
            },
            {
                "cnName":"运维开始时间",
                "name":"operation_start_time",
                "selected":true
            },
            {
                "cnName":"运维结束时间",
                "name":"operation_end_time",
                "selected":true
            },
            {
                "cnName":"监控站点云",
                "name":"monitor_site",
                "selected":true
            },
            {
                "cnName":"更新时间",
                "name":"update_time",
                "selected":true
            },
            {
                "cnName":"是否删除",
                "name":"is_del",
                "selected":true
            },
            {
                "cnName":"创建时间",
                "name":"cr_time",
                "selected":true
            },
            {
                "cnName":"状态",
                "name":"status",
                "selected":true
            },
            {
                "cnName":"媒体类型名称",
                "name":"media_name",
                "selected":true
            },
            {
                "cnName":"电话号码",
                "name":"phone",
                "selected":true
            },
            {
                "cnName":"渠道类型",
                "name":"media_type",
                "selected":true
            },
            {
                "cnName":"真实姓名",
                "name":"true_name",
                "selected":true
            }
        ]
    }
}
```



### 单位表索引(索引名字:work_order;索引类型:unit)

```json
{
    "properties":{
        "id":{
            "type":"long"
        },
        "cr_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "cr_user":{
            "type":"string",
            "index":"not_analyzed"
        },
        "unit_type":{
            "type":"string"
        },
        "update_time":{
            "type":"date",
            "format":"strict_date_optional_time||epoch_millis||yyyy-MM-ddHH:mm:ss"
        },
        "unit_name":{
            "type":"string"
        },
        "unit_master":{
            "type":"string"
        },
        "email":{
            "type":"string",
            "index":"not_analyzed"
        },
        "unit_addr":{
            "type":"string",
            "index":"not_analyzed"
        },
        "unit_desc":{
            "type":"string"
        },
        "unit_code":{
            "type":"string",
            "index":"not_analyzed"
        },
        "business_area":{
            "type":"string"
        },
        "person_count":{
            "type":"integer"
        },
        "logo":{
            "type":"string",
            "index":"not_analyzed"
        },
        "status":{
            "type":"integer"
        },
        "is_del":{
            "type":"integer"
        },
        "phone":{
            "type":"string",
            "index":"not_analyzed"
        },
        "true_name":{
            "type":"string"
        },
        "unit_master_true_name":{
            "type":"string",
            "index":"not_analyzed"
        }
    },
    "_meta":{
        "properties":[
            {
                "cnName":"单位ID",
                "name":"id",
                "selected":true
            },
            {
                "cnName":"创建时间",
                "name":"cr_time",
                "selected":true
            },
            {
                "cnName":"创建人",
                "name":"cr_user",
                "selected":true
            },
            {
                "cnName":"单位类型",
                "name":"unit_type",
                "selected":true
            },
            {
                "cnName":"更新时间",
                "name":"update_time",
                "selected":true
            },
            {
                "cnName":"单位名称",
                "name":"unit_name",
                "selected":true
            },
            {
                "cnName":"单位负责人",
                "name":"unit_master",
                "selected":false
            },
            {
                "cnName":"邮箱",
                "name":"email",
                "selected":true
            },
            {
                "cnName":"单位地址",
                "name":"unit_addr",
                "selected":true
            },
            {
                "cnName":"单位描述",
                "name":"unit_desc",
                "selected":true
            },
            {
                "cnName":"单位编码",
                "name":"unit_code",
                "selected":true
            },
            {
                "cnName":"业务领域",
                "name":"business_area",
                "selected":true
            },
            {
                "cnName":"单位人数",
                "name":"person_count",
                "selected":true
            },
            {
                "cnName":"图标",
                "name":"logo",
                "selected":true
            },
            {
                "cnName":"状态",
                "name":"status",
                "selected":true
            },
            {
                "cnName":"是否删除",
                "name":"is_del",
                "selected":true
            },
            {
                "cnName":"电话号码",
                "name":"phone",
                "selected":true
            },
            {
                "cnName":"真实姓名",
                "name":"true_name",
                "selected":true
            },
            {
                "cnName":"负责人用于显示的名称",
                "name":"unit_master_true_name",
                "selected":true
            }
        ]
    }
}
```







