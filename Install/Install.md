# 后端安装手册

### 一、外部应用
>*外部应用需要在部署前安装好*  
1. IDS
2. Redis
3. nacos
4. upms (1.4或以上版本)
5. nginx
6. 海云
7. ES (可以复用海云的ES)
8. MySQL
9. 监控云（网站监测云）
10. 拓尔思融合服务平台(用户申请获取监控云相关数据的接口信息)
11. Skywalking([下载地址](https://wiki.trscd.com.cn/download/attachments/77627649/apache-skywalking-apm-es7-8.2.0.tar.gz?version=1&modificationDate=1613813744000&api=v2))

### 二、安装步骤
1. 正确安装上述外部应用应用
2. 导入初始化的SQL语句[[db/init.sql]](db/init.sql)
3. 下载相关[配置表格](https://docs.qq.com/sheet/DZmRkUHhWRGdzRG5K) 填写配置信息
4. 按照情况修改[conf/workorder.properties](conf/workorder.properties)并导入到nacos中
5. 按照情况填写[conf/bootstrap.properties](conf/bootstrap.properties)提供给开发人员，从而获取相关部署介质，使用**java -jar xxx.jar**或**startup.sh xxx.jar**的方式运行即可
6. 现在是通过网关模块集成各个子模块，各个微服务模块的访问前缀，请参考[conf/demo.conf](conf/demo.conf)

### 三、相关配置
#### nacos配置
1. 访问nacos的web管理界面，并登陆相关nacos账户。地址：http://ip:8848/nacos  
2. 切换到“**命名空间**”，新建命名空间。命名空间名：workorder，描述：workorder  
3. 切换到“**配置管理-配置列表-workorder**”，新建配置:  

| Data ID | Group | 配置格式 | 配置内容 |
|---------|-------|---------|----------|
| workorder.properties | DEFAULT_GROUP | Properties | 参考[conf/workorder.properties](conf/workorder.properties)修改填充 |
| gateway.yml | DEFAULT_GROUP | YAML | 参考[conf/gateway.yml](conf/gateway.yml)修改填充 |

4. 将nacos的信息填充到[conf/bootstrap.properties](conf/bootstrap.properties)中

#### Es 配置
1. 查看[conf/bootstrap.properties](conf/bootstrap.properties)配置文件中数据库的地址  
2. 搭建es环境并通过Adpter工具创建索引  [mapping/Mapping_Table.md](mapping/Mapping_Table.md)
3. Es配置如果是集群用逗号隔开【es.dataSource.url=http://***********:9200,http://***********:9200】
4. 将数据库中数据导入到Es【mq】

### Skywalking 配置（非必须）
1. 查看wiki进行相关学习[skywalking](https://wiki.trscd.com.cn/pages/viewpage.action?pageId=77627644)  
2. Skywalking的UI界面默认端口为8080,如有端口占用情况需要进行修改:vim skywalking/webapp/webapp.yml  
3. 根据wiki修改存储工具[不修改默认是h2数据库]  
3. Linux环境下启动:./skywalking/bin/start.sh,启动成功后在浏览器查看访问地址:http://ip:port  
4. 在项目启动过程中添加探针相关配置,可以参照[贵州开发环境启动脚本第51行](./shell/gz_dev_startup.sh)  
   ```jvm
      -javaagent:/TRS/skywalking/skywalking/agent/skywalking-agent.jar   # 指定agent的jar包
      -Dskywalking.agent.service_name=swdemo    # 项目名称
      -Dskywalking.collector.backend_service=127.0.0.1:11800    #collector收集器默认端口11800【grpc】,12800为http请求端口
   ```
5. 项目启动后,点击部分接口,并等待少许时间后，查看ui界面上面的相关数据[贵州工单开发环境地址](http://***********:8089/trace)  

#### ids配置
1. 新建应用。应用根地址和主页：填写前端包地址，IDS通知应用的URL：网关地址+/user/checkLogin  
2. 点击生成的应用后面的“**更多操作-配置用户属性读写权限**”，将用户的基本属性均配置为可读  
3. 配置身份同步和机构同步。  
4. 将ids的信息填充到[conf/bootstrap.properties](conf/bootstrap.properties)中  

#### 权限系统配置
1. 新建子系统。  

|系统名称|资源接口|系统页面|
|----|----|----|
|工单系统|ExternalSystem模块的访问地址（如：http://ip:18088/）|工单系统前端包访问地址|

2. 将权限系统信息和得到的**唯一标识**填充到nacos的workorder.properties配置中  

### 四、各个模块对应的jar包
| 模块名称 | 介质命名 | 模块说明 |
| ------ | ------ | ------ |
| [WorkOrder - 工单系统](../WorkOrder) | WorkOrder-Web-x.x.jar|工单的业务模块|
| [SystemManagement - 系统管理](../SystemManagement) | SystemManagement-Web-x.x.jar|站点关系，单位管理等配置模块|
| [Search - 全文搜索](../Search) | Search-Web-x.x.jar|统一的检索操作|
| [Message - 消息中心](../Message) | Message-Web-x.x.jar|相关消息提醒|
| [StatisticsCenter - 统计分析](../StatisticsCenter) | StatisticsCenter-Web-x.x.jar|提供相关统计数据|
| [Interaction - 互动模块](../Interaction) | Interaction-Web-x.x.jar|回复评价等互动操作|
| [ExternalSystem - 外部系统对接](../ExternalSystem) | ExternalSystem-Web-x.x.jar|与第三方系统的集成|
| [UserCenter - 用户中心](../UserCenter) | User-Service-IDS-x.x.jar|提供用户登录，token生成和校验等操作|
| [FileManager - 文件上传服务](../UserCenter) | FileManager-Web-x.x.jar|提供文件上传服务|
| [Gateway - 网关服务](../Gateway) | Gateway-x.x.jar|网关模块用于统一代理各个子模块|


### 其他说明
1. **如果部署的前端包和IDS不在同一个域中，需要将IDS配置成允许跨域访问**
2. **IDS应用中需要配置用户（用户的所有属性需要赋予读权限）和组织的同步**
3. **工单系统初次运行的时候，需要在IDS中手动同步组织和用户到工单系统中**
4. **系统所需监控云站点数据的接口信息，需要通过拓尔思融合服务平台去申请，服务名称为监测云站点列表，申请成功后填写相关信息到配置文件**
