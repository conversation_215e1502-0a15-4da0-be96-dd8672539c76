/*
 Navicat Premium Data Transfer

 Source Server         : MariaDB1=6
 Source Server Type    : MariaDB
 Source Server Version : 100313
 Source Host           : 127.0.0.1:3307
 Source Schema         : workorder_init

 Target Server Type    : MariaDB
 Target Server Version : 100313
 File Encoding         : 65001

 Date: 08/02/2021 15:47:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for appendix_table
-- ----------------------------
DROP TABLE IF EXISTS `appendix_table`;
CREATE TABLE `appendix_table`  (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                   `cr_time` datetime(0) NULL DEFAULT NULL,
                                   `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `status` int(11) NULL DEFAULT NULL,
                                   `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `file_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `file_ext` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `obj_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `obj_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `field_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `file_size` bigint(20) NULL DEFAULT NULL,
                                   `export_from_other` int(11) NULL DEFAULT 0,
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1015 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cc
-- ----------------------------
DROP TABLE IF EXISTS `cc`;
CREATE TABLE `cc`  (
                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                       `cr_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                       `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                       `work_order_id` bigint(20) NULL DEFAULT NULL COMMENT '工单id',
                       `cr_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '抄送单位id',
                       `cr_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抄送人',
                       `target_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '抄送目标单位id',
                       `target_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抄送目标人',
                       `type` int(11) NULL DEFAULT NULL COMMENT '抄送类型 （1: 抄送给个人， 2：抄送给单位）',
                       `cr_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抄送人单位',
                       `cr_truename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抄送人真实姓名',
                       `group_id` bigint(20) NULL DEFAULT NULL COMMENT '分组id',
                       `target_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抄送目标人单位',
                       `target_truename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抄送目标人真实姓名',
                       `status` int(11) NULL DEFAULT NULL,
                       `export_from_other` int(11) NULL DEFAULT 0,
                       PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 802 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抄送表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for grade
-- ----------------------------
DROP TABLE IF EXISTS `grade`;
CREATE TABLE `grade`  (
                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                          `cr_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                          `cr_time` datetime(0) NULL DEFAULT NULL,
                          `grade_unit` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                          `content` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                          `attitude` double NULL DEFAULT NULL,
                          `complete_time` double NULL DEFAULT NULL,
                          `complete_content` double NULL DEFAULT NULL,
                          `work_order_id` bigint(20) NULL DEFAULT NULL,
                          `work_order_grade` decimal(10, 1) NULL DEFAULT NULL,
                          `status` int(4) NULL DEFAULT 1,
                          `grade_unit_id` bigint(20) NULL DEFAULT NULL,
                          `export_from_other` int(11) NULL DEFAULT 0,
                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 343 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for group_key_mapping
-- ----------------------------
DROP TABLE IF EXISTS `group_key_mapping`;
CREATE TABLE `group_key_mapping`  (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                      `cr_time` datetime(0) NULL DEFAULT NULL,
                                      `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `status` int(11) NULL DEFAULT NULL,
                                      `parent_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                      `children_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                      `group_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `export_from_other` int(11) NULL DEFAULT 0,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9929 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for group_table
-- ----------------------------
DROP TABLE IF EXISTS `group_table`;
CREATE TABLE `group_table`  (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                `cr_time` datetime(0) NULL DEFAULT NULL,
                                `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `status` int(2) NULL DEFAULT NULL,
                                `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `group_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `group_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `group_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `group_order` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                `parent_id` bigint(20) NULL DEFAULT NULL,
                                `export_from_other` int(11) NULL DEFAULT 0,
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6937 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for group_user_table
-- ----------------------------
DROP TABLE IF EXISTS `group_user_table`;
CREATE TABLE `group_user_table`  (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                     `cr_time` datetime(0) NULL DEFAULT NULL,
                                     `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `status` int(2) NULL DEFAULT NULL,
                                     `group_id` bigint(20) NULL DEFAULT NULL,
                                     `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                     `export_from_other` int(11) NULL DEFAULT 0,
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14422 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for message_config
-- ----------------------------
DROP TABLE IF EXISTS `message_config`;
CREATE TABLE `message_config`  (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                   `cr_time` datetime(0) NULL DEFAULT NULL,
                                   `cr_user` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `status` int(1) NULL DEFAULT 1,
                                   `work_order_type_id` bigint(20) NULL DEFAULT NULL,
                                   `is_follow_parent_config` int(2) NULL DEFAULT NULL,
                                   `message_config` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `update_time` datetime(0) NULL DEFAULT NULL,
                                   `update_user_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `export_from_other` int(11) NULL DEFAULT 0,
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 66 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notice
-- ----------------------------
DROP TABLE IF EXISTS `notice`;
CREATE TABLE `notice`  (
                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                           `cr_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                           `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                           `work_order_id` bigint(20) NULL DEFAULT NULL COMMENT '工单id',
                           `group_id` bigint(20) NULL DEFAULT NULL COMMENT '分组id',
                           `target_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '通知目标单位id',
                           `target_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知目标人单位',
                           `target_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知目标人',
                           `target_truename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知目标人真实姓名',
                           `status` int(11) NULL DEFAULT NULL,
                           `type` int(11) NULL DEFAULT NULL COMMENT '通知类型（1：个人，2：单位，3：全部）',
                           `export_from_other` int(11) NULL DEFAULT 0,
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1205 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notice_message
-- ----------------------------
DROP TABLE IF EXISTS `notice_message`;
CREATE TABLE `notice_message`  (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                   `cr_time` datetime(0) NULL DEFAULT NULL,
                                   `cr_user` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `status` int(255) NULL DEFAULT 1,
                                   `work_order_id` bigint(20) NULL DEFAULT NULL,
                                   `message_config_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `reveive_user_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `is_read` int(255) NULL DEFAULT 0,
                                   `update_time` datetime(0) NULL DEFAULT NULL,
                                   `update_user_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `title` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                   `reveive_unit_id` bigint(20) NULL DEFAULT NULL,
                                   `update_unit_id` bigint(20) NULL DEFAULT NULL,
                                   `cr_unit_id` bigint(20) NULL DEFAULT NULL,
                                   `export_from_other` int(11) NULL DEFAULT 0,
                                   PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8294 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notice_read
-- ----------------------------
DROP TABLE IF EXISTS `notice_read`;
CREATE TABLE `notice_read`  (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                `cr_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                                `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                                `work_order_id` bigint(20) NULL DEFAULT NULL COMMENT '工单id',
                                `target_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '通知目标单位id',
                                `target_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知目标人',
                                `status` int(11) NULL DEFAULT 0 COMMENT '阅读状态(0：未读，1：已读)',
                                `export_from_other` int(11) NULL DEFAULT 0 COMMENT '是否来自老工单（0：否，1：是）',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 84 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for opr_record
-- ----------------------------
DROP TABLE IF EXISTS `opr_record`;
CREATE TABLE `opr_record`  (
                               `id` bigint(11) NOT NULL AUTO_INCREMENT,
                               `work_order_id` bigint(11) NOT NULL,
                               `cr_unit_id` bigint(11) NOT NULL,
                               `ope_record_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                               `option` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
                               `target_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `target_unit_id` bigint(11) NULL DEFAULT NULL,
                               `cr_time` datetime(0) NULL DEFAULT NULL,
                               `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `status` int(11) NULL DEFAULT NULL,
                               `assign_type` int(11) NULL DEFAULT 0 COMMENT '是否指定到单位： 0都不指定，1指定到人，2指定到单位',
                               `export_from_other` int(11) NULL DEFAULT 0,
                               `group_id` bigint(20) NULL DEFAULT NULL,
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3716 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for site_relation
-- ----------------------------
DROP TABLE IF EXISTS `site_relation`;
CREATE TABLE `site_relation`  (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                  `cr_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `site_id` bigint(20) NULL DEFAULT NULL,
                                  `site_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `media_type` int(8) NULL DEFAULT NULL,
                                  `unique_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `filing_time` datetime(0) NULL DEFAULT NULL,
                                  `construction_time` datetime(0) NULL DEFAULT NULL,
                                  `host_unit_id` bigint(20) NULL DEFAULT NULL,
                                  `host_unit` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `master_unit_id` bigint(20) NULL DEFAULT NULL,
                                  `master_unit` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `operation_unit_id` bigint(20) NULL DEFAULT NULL,
                                  `operation_unit` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `operation_host` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `operation_start_time` datetime(0) NULL DEFAULT NULL,
                                  `operation_end_time` datetime(0) NULL DEFAULT NULL,
                                  `monitor_site` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `update_time` datetime(0) NULL DEFAULT NULL,
                                  `is_del` int(2) NULL DEFAULT 0,
                                  `cr_time` datetime(0) NULL DEFAULT NULL,
                                  `status` int(2) NULL DEFAULT NULL,
                                  `media_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `phone` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `true_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                  `monitor_site_id` bigint(20) NULL DEFAULT NULL,
                                  `export_from_other` int(11) NULL DEFAULT 0,
                                  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 96 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for unit
-- ----------------------------
DROP TABLE IF EXISTS `unit`;
CREATE TABLE `unit`  (
                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                         `cr_time` datetime(0) NULL DEFAULT NULL,
                         `cr_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `unit_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `update_time` datetime(0) NULL DEFAULT NULL,
                         `unit_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `unit_master` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `email` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `unit_addr` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
                         `unit_desc` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
                         `unit_code` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `business_area` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
                         `person_count` int(8) NULL DEFAULT NULL,
                         `logo` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `status` int(8) NULL DEFAULT 1,
                         `is_del` int(4) NULL DEFAULT 0,
                         `phone` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `true_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `unit_master_true_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                         `version` int(20) NULL DEFAULT 0,
                         `export_from_other` int(11) NULL DEFAULT 0,
                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 192 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for unit_group
-- ----------------------------
DROP TABLE IF EXISTS `unit_group`;
CREATE TABLE `unit_group`  (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT,
                               `group_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `status` int(2) NULL DEFAULT 1,
                               `cr_user` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `cr_time` datetime(0) NULL DEFAULT NULL,
                               `update_user` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                               `update_time` datetime(0) NULL DEFAULT NULL,
                               `is_del` int(2) NULL DEFAULT 0,
                               `export_from_other` int(11) NULL DEFAULT 0,
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for unit_group_relation
-- ----------------------------
DROP TABLE IF EXISTS `unit_group_relation`;
CREATE TABLE `unit_group_relation`  (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                        `group_id` bigint(20) NULL DEFAULT NULL,
                                        `unit_id` bigint(20) NULL DEFAULT NULL,
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
                         `id` bigint(20) NOT NULL AUTO_INCREMENT,
                         `cr_time` datetime(0) NULL DEFAULT NULL,
                         `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `status` int(11) NULL DEFAULT NULL,
                         `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `true_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `source_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `source_user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                         `export_from_other` int(11) NULL DEFAULT 0,
                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2320 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_unit_mapping
-- ----------------------------
DROP TABLE IF EXISTS `user_unit_mapping`;
CREATE TABLE `user_unit_mapping`  (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                      `cr_time` datetime(0) NOT NULL,
                                      `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
                                      `export_from_other` int(2) NULL DEFAULT NULL,
                                      `status` int(11) NULL DEFAULT NULL,
                                      `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `true_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `unit_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `unit_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `unit_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                      `is_master` bit(1) NULL DEFAULT NULL,
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 170 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for work_order
-- ----------------------------
DROP TABLE IF EXISTS `work_order`;
CREATE TABLE `work_order`  (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                               `cr_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                               `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                               `work_order_top_type_id` bigint(20) NULL DEFAULT NULL COMMENT '工单顶级类别id',
                               `work_order_top_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工单顶级类别名',
                               `work_order_type_id` bigint(20) NULL DEFAULT NULL COMMENT '工单类别id',
                               `work_order_type_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工单类别名',
                               `cr_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '发起单位id',
                               `cr_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发起单位',
                               `cr_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发起人',
                               `cr_truename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发起人真实姓名',
                               `site_id` bigint(20) NULL DEFAULT NULL COMMENT '站点id',
                               `sitename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '站点',
                               `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
                               `priority` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优先级',
                               `host_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '主办单位id',
                               `host_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主办单位',
                               `host_assign_type` int(11) NULL DEFAULT 0 COMMENT '主办人指定类型（1：指定到人，2：指定到单位）',
                               `host_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主办人',
                               `host_truename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主办人真实姓名',
                               `deal_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '受理单位id',
                               `deal_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '受理单位',
                               `deal_assign_type` int(11) NULL DEFAULT 0 COMMENT '处理人指定类型（1：指定到人，2：指定到单位）',
                               `deal_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '受理人',
                               `deal_truename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '受理人真实姓名',
                               `media_type` int(11) NULL DEFAULT NULL COMMENT '渠道类型',
                               `expected_end_date` datetime(0) NULL DEFAULT NULL COMMENT '期望完成时间',
                               `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                               `receive_time` datetime(0) NULL DEFAULT NULL COMMENT '收到工单时间',
                               `is_delete` int(11) NULL DEFAULT NULL COMMENT '是否删除(0未删除，1已删除)',
                               `status` int(11) NULL DEFAULT 0 COMMENT '\r\n0:待分配, 1: 处理中, 2:已解决, 3:已评价, 4:已公开',
                               `action_time` datetime(0) NULL DEFAULT NULL COMMENT '响应时间',
                               `is_return` int(11) NULL DEFAULT NULL COMMENT '是否是回退(0否，1是)',
                               `finish_time` datetime(0) NULL DEFAULT NULL COMMENT '完成时间',
                               `export_from_other` int(11) NULL DEFAULT 0,
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1206 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for work_order_type
-- ----------------------------
DROP TABLE IF EXISTS `work_order_type`;
CREATE TABLE `work_order_type`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                    `cr_time` datetime(0) NULL DEFAULT NULL,
                                    `cr_user` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    `type_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    `type_desc` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
                                    `parent_id` bigint(20) NULL DEFAULT NULL,
                                    `is_del` int(4) NULL DEFAULT 0,
                                    `status` int(4) NULL DEFAULT -1,
                                    `update_time` datetime(0) NULL DEFAULT NULL,
                                    `level` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    `root_key` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '顶级root的key',
                                    `accept_unit_id` bigint(20) NULL DEFAULT NULL,
                                    `export_from_other` int(11) NULL DEFAULT 0,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of work_order_type
-- ----------------------------
INSERT INTO `work_order_type` VALUES (1, '2020-09-20 23:26:31', 'system', '工单', '工单类型', 0, 0, 1, '2020-09-21 00:11:03', '1', 'ROOT_GONG_DAN_KEY', NULL, 0);
INSERT INTO `work_order_type` VALUES (2, '2020-09-20 23:27:33', 'system', '投诉/建议', '投诉/建议类型', 0, 0, 1, '2020-09-21 00:11:33', '1', 'ROOT_TOU_SU_KEY', NULL, 0);
INSERT INTO `work_order_type` VALUES (3, '2020-09-20 23:32:07', 'system', '通知', '通知类型', 0, 0, 1, '2020-09-20 23:45:57', '1', 'ROOT_TONG_ZHI_KEY', NULL, 0);

-- ----------------------------
-- Table structure for work_time
-- ----------------------------
DROP TABLE IF EXISTS `work_time`;
CREATE TABLE `work_time`  (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                              `cr_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
                              `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
                              `cr_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '创建人单位id',
                              `cr_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人用户名',
                              `work_order_id` bigint(20) NULL DEFAULT NULL COMMENT '工单id',
                              `work_desc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '工作名目',
                              `work_unit_id` bigint(20) NULL DEFAULT NULL COMMENT '工作单位id',
                              `work_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作单位',
                              `working_time` double NULL DEFAULT NULL COMMENT '工作时间（天）',
                              `status` int(11) NULL DEFAULT NULL,
                              `export_from_other` int(11) NULL DEFAULT 0,
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 93 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工时表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for notice_detail
-- ----------------------------
DROP VIEW IF EXISTS `notice_detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `notice_detail` AS (
SELECT
	n.id,
	n.cr_time,
	n.cr_user,
	u.true_name AS cr_truename,
	wo.cr_unit_id,
	dept.unit_name AS cr_unit_name,
	n.work_order_id,
	n.group_id,
	n.target_unit_id,
	n.target_username,
	n.type,
	wo.content
FROM
	notice n
	LEFT JOIN work_order wo ON n.work_order_id = wo.id
	LEFT JOIN `user` u ON u.user_name = wo.cr_user
	LEFT JOIN unit dept ON wo.cr_unit_id = dept.id
    where wo.work_order_top_type_id = 3
	) ;

-- ----------------------------
-- View structure for work_order_detail
-- ----------------------------
DROP VIEW IF EXISTS `work_order_detail`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `work_order_detail` AS (
SELECT
	wo.id,
	wo.cr_time,
	wo.cr_user,
	wo.work_order_top_type_id,
	wo.work_order_top_type_name,
	wo.work_order_type_id,
	wot1.type_name AS work_order_type_name,
	wo.cr_unit_id,
	wo.cr_unit,
	wo.cr_username,
	wo.cr_truename,
	wo.site_id,
	wo.sitename,
	wo.content,
	wo.priority,
	wo.host_unit_id,
	wo.host_unit,
	wo.host_assign_type,
	wo.host_username,
	wo.host_truename,
	wo.deal_unit_id,
	wo.deal_unit,
	wo.deal_assign_type,
	wo.deal_username,
	wo.deal_truename,
	wo.media_type,
	wo.expected_end_date,
	wo.update_time,
	wo.receive_time,
	wo.is_delete,
	wo.`status`,
	wo.action_time,
	wo.is_return,
	wo.finish_time,
	wo.export_from_other,
	wot2.id AS work_order_parent_type_id,
	wot2.type_name AS work_order_parent_type_name
FROM
	work_order wo
	LEFT JOIN work_order_type wot1 ON wo.work_order_type_id = wot1.id
	LEFT JOIN work_order_type wot2 ON wot1.parent_id = wot2.id ); ;

SET FOREIGN_KEY_CHECKS = 1;
