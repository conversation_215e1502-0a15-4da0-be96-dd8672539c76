#cd-dev-yanshi
> XMKFB-26395 后-工单系统部署文档

# guizhou-prod 
> XMKFB-21806 工单系统图片地址替换+漏洞问题修复
```properties
#actuator 信息泄露问题修复+配置新增
management.endpoints.enabled-by-default=false
```
> XMKFB-16068 工单系统-新增工单标题
```sql
ALTER TABLE `work_order` 
 ADD COLUMN `title` varchar(255)  COMMENT '工单标题' AFTER `status`;
```


#guizhou-prod
> XMKFB-15850 贵州-工单系统-ES升级
> XMKFB-15475 nacos漏洞版本升级
```properties
#nacos  新增配置
spring.cloud.nacos.password=Trsadminnacos_2022
spring.cloud.nacos.username=nacos
#nacos  修改配置
dubbo.registry.address=nacos://***********:8848?namespace=f4018135-cfb7-4bec-89a4-277f4dec4771&username=nacos&password=Trsadminnacos_2022

# ES配置
es.dataSource.password=u9gmZpPcWIv2sQCt1bSs
es.dataSource.user=elastic
# save或者Es中的时间格式的配置，其它时间格式如:yyyy-MM-dd'T'HH:mm:ss.SSS'Z',不配置默认为以下格式
es.date.format = yyyy-MM-dd HH:mm:ss
```
#guizhou-prod
> XMKFB-12591 工单系统-通过关键字检索工单并导出，未能得到导出结果

# 更新日志

> XMKFB-10001 工单系统-发送通知-发送短信内容重复
> XMKFB-10246 贵州工单系统，工单状态显示已解决的改成待评价
> XMKFB-9611 工单系统-站点信息和海云同步
>GZSZFSDJS-2500 工单系统-短信异常
> XMKFB-8128 工单系统-短信需求
> XMKFB-8574 工单系统－工单自动响应
#### 配置项修改（请根据实际情况确认是否调整配置e项的值）
``` 
#发送通知时，短信模板
sms.template.hasNoticeMessage=[TRUENAME]：您好，集约化平台运维工作组在集约化平台工单系统发布了一则通知，工单编号：[WORKORDERID]，请您及时登录系统进行查看处理。后续有问题可联系集约化平台运维工作组（联系电话：0851-********）。
#从海云同步站点信息--海云的提供的数据，用户信息不存在，暂停使用该功能
#海云站点信息获取的URL
hy.site.info.url=http://10.11.2.144/pub/sjwtzsk/qt/wzywtj/index.json
#从海云同步站点关系
systemManagementService.syncSiteRelation.cron=0 0 23 * * ?



#开启非工作时间创建工单自动回复内容-默认关闭
workOrder.autoReply.content.open=false

#工单考核，分配工单5分钟内需要响应
#需要自动回复的受理方人员名单，“，”隔开
workOrder.autoResponse.person.name=负责人
#自动回复内容
workOrder.autoReply.person.content=您好，您提的问题／需求已收到，正在处理中，请耐心等待，稍后给您回复。

....properties
#回退工单短信提醒
sms.template.hasRollback=【集约化平台】[TRUENAME]：您好，您在集约化平台运维系统提交的工单（[WORKORDERID]）因缺乏关键信息或需求不明已被回退，请登录系统进行查看并进行完善。后续有问题可联系0851-********集约化平台运维服务热线。
#给主办负责人发送短信
sms.template.hasMasterUser=[TRUENAME]：您好，[UNITNAME]在集约化平台运维系统创建工单（[WORKORDERID]），请登录系统进行查看处理。后续有问题可联系0851-********集约化平台运维服务热线。
#非工作时间创建工单自动回复内容
workOrder.autoReply.content=【自动回复】依据《贵州省政府网站集约化平台工单办理办法（试行）》，非工作时间提交工单，原则上顺延至工作时间处理，如急需处理，请拨打13027880860、0851-********、17152144142，我们将在第一时间处理。谢谢！
#工单流转短信提醒，同步短信消息到微信公众号
#是否开启短信消息同步到微信公众号，默认false
gzh.push.open=false
#公众号接收端用户名
gzh.user-name=jhyptgdxt
#公众号接收端用户密码
gzh.auth-code=+HGR8k$v
#公众号接收端token有效期
gzh.token.expiration.time=119
#redis存放token的index
gzh.token.expiration.index=10
#从公众号获取token的url
gzh.token.url=http://message.guizhou.gov.cn/api/receive/public/xml/getToken
#从公众号推送消息的url
gzh.push.url=http://message.guizhou.gov.cn/api/receive/public/message
```
# `v1.3发版日志`
>XMKFB-4827 后-工单短信内容优化  
>---------------#以上已更新到20211011发版文件中---------------   
>XMKFB-5338 后 - 工单配置项整理  
>GZSZFSDJS-2465[GZSZFSDJS-2456] 后-站点统建相关接口提供  
>GZSZFSDJS-2468[GZSZFSDJS-2456] 后-工单系统查询最新动态接口修改  
>XMKFB-5633 后-工单系统短信切换  
>docs(Dockerfile): 优化Dockerfile，改为使用带arthas的jdk镜像  
>修改文件[Mapping_Table.md]中的索引名字和类型的说明文档  

### v1.3配置项修改（请根据实际情况确认是否调整配置项的值）
```properties
#【修改】了以下配置项的值
sms.template.hasDealEd=[TRUENAME]：您好，您在集约化平台运维系统提交的问题已进行受理，请等待处理。后续有问题可联系0851-********集约化平台运维服务热线。
sms.template.hasFinishEd=[TRUENAME]：您好，您在集约化平台运维系统提交的问题已进行完成处理，请登录系统进行查看。后续有问题可联系0851-********集约化平台运维服务热线。
sms.template.hasAppraise=[TRUENAME]：您好，您在集约化平台运维系统处理的问题已完成评价，请登录系统进行查看。后续有问题可联系0851-********集约化平台运维服务热线。
sms.template.waitDeal=[TRUENAME]：您好，[UNITNAME]在集约化平台运维系统将问题提交到[DEALUNITNAME]进行处理，请登录系统进行查看处理。后续有问题可联系0851-********集约化平台运维服务热线。
sms.template.assignOrCopy=[TRUENAME]：您好，[UNITNAME]在集约化平台运维系统将问题转办/抄送到[DEALUNITNAME]进行处理，请登录系统进行查看处理。后续有问题可联系0851-********集约化平台运维服务热线。
#以上已更新到20211011发版文件中

```



# `v1.2发版日志`
>GZSZFSDJS-2191 异常信息:用户【铜仁市测试人员】处于停用或其他异常状态，无法登录"  
>GZSZFSDJS-2187[GZSZFSDJS-2186] 后 - 后端移植相关工作台的接口  
>GZSZFSDJS-2190 后-提供不需要超管单位id也可以查询运维动态和超期工单预警全部数据的方法  
>GZSZFSDJS-2198 进入工作台，细揽页点击正文的附件，404 Not Found  
>XMKFB-3916 后-增加短信发送策略  
>GZSZFSDJS-2241 单位负责人转交工单，单位负责人会收到短信提醒  
>XMKFB-4044 后-短信抽取成模板从配置注入  
>XMKFB-4065 后-工单系统漏洞（工作台）  
>XMKFB-4069 后 - 工单系统检索报错  
> 
 
### v1.2数据库更新
```sql
#
20210318 褚川宝 GZSZFSDJS-2186 增加知识库浏览记录下载记录和评论表
-- ----------------------------
-- Table structure for doccomment
-- ----------------------------
DROP TABLE IF EXISTS `doccomment`;
CREATE TABLE `doccomment`
(
    `id`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `commentContent` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `commentDate`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `commentType`    int(11) NOT NULL,
    `commentUser`    varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `docId`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `reposeId`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `reposeUser`     varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `userId`         varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `workerId`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `attachment`     longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for knowledgebrowserecord
-- ----------------------------
DROP TABLE IF EXISTS `knowledgebrowserecord`;
CREATE TABLE `knowledgebrowserecord`
(
    `kid`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `createdate`  datetime(0) NULL DEFAULT NULL,
    `docid`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `ipaddr`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `num`         int(11) NULL DEFAULT NULL,
    `operatedate` datetime(0) NULL DEFAULT NULL,
    `truename`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `userid`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `username`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`kid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for knowledgedownloadrecord
-- ----------------------------
DROP TABLE IF EXISTS `knowledgedownloadrecord`;
CREATE TABLE `knowledgedownloadrecord`
(
    `kid`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `createdate`  datetime(0) NULL DEFAULT NULL,
    `docid`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `fileid`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `fileName`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `ipaddr`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `num`         int(11) NULL DEFAULT NULL,
    `operatedate` datetime(0) NULL DEFAULT NULL,
    `truename`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `userid`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `username`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    PRIMARY KEY (`kid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

```
### v1.2配置项修改（请根据实际情况确认是否调整配置项的值）
```properties
#-------------********
# 在nacos中新增参数
KnowledgeBase.hyKnowledgeList=海云知识库地址
#-------------2021/4/12
##注意:该配置不是新增配置,而是修改已有的配置值 ## 新增一个短信渠道
different.channel.list=gz_work_order,sms_platform,third_party_interface
# sms 批量发送短信的地址
sms.batch.url=http://1.207.107.56/smsapi/smsapi/batchSend.json
# sms 发送短信平台的账号
sms.account=ys20100026
# sms 发送短信平台的密码
sms.password=Gzszfwzjyh01@
# ---------********
# 短信模板相关配置,如果不配置默认是以下信息
sms.template.hasDealEd=TRUENAME：您好，您在集约化平台运维系统提交的问题已进行受理，请等待处理。后续有问题可联系0851-********集约化平台运维服务热线。
sms.template.hasFinishEd=TRUENAME：您好，您在集约化平台运维系统提交的问题已进行完成处理，请登录系统进行查看。后续有问题可联系0851-********集约化平台运维服务热线。
sms.template.hasAppraise=TRUENAME：您好，您在集约化平台运维系统处理的问题已完成评价，请登录系统进行查看。后续有问题可联系0851-********集约化平台运维服务热线。
sms.template.waitDeal=TRUENAME：您好，UNITNAME在集约化平台运维系统将问题提交到您进行处理，请登录系统进行查看处理。后续有问题可联系0851-********集约化平台运维服务热线。
sms.template.assignOrCopy=TRUENAME：您好，UNITNAME在集约化平台运维系统将问题转办/抄送到您单位进行处理，请登录系统进行查看处理。后续有问题可联系0851-********集约化平台运维服务热线。

#20210510 褚川宝 XMKFB-4065
# 安全域名列表，多个使用分号分隔
KnowledgeBase.SecureDomains=http://10.11.2.144

```
### v1.2运行后配置
> Getaway增加知识库的代理  
> 权限系统升级到最新版  


# `v1.1发版日志`
>GZSZFSDJS-2034[GZSZFSDJS-2027] 后 - 开发相关后端接口  
>GZSZFSDJS-1807 需求：后台数据权限细化到单位，单位只能维护自己单位的数据  
>GZSZFSDJS-2030【角色权限】增加数据维度权限，拆分原后台管理权限维度，实现用户后台仅维护本单位数据  
>GZSZFSDJS-2064[GZSZFSDJS-2063] 新单位/人员组件接口支持  
>GZSZFSDJS-2076 后 - 回退列表报错  
>GZSZFSDJS-2079 平台运维统计-工单类型统计模块工单类型显示不全  
>GZSZFSDJS-2081 新建工单、通知，选择人员，加载了已停用单位的人员  
>GZSZFSDJS-2080 操作记录新增分组  
>GZSZFSDJS-2078 访问工单系统，系统报如图所示错误  
>GZSZFSDJS-2092 优化消息  
>GZSZFSDJS-2097 推荐站点缺少【六盘水市教育局】  
>GZSZFSDJS-2099 新建通知，选择分组，已阅：0/0  
>GZSZFSDJS-2102	回复通知或工单，回复失败  
>GZSZFSDJS-2092[GZSZFSDJS-2093] 改造消息模块  
>GZSZFSDJS-2112 抄送单位为单位或分组，单位负责人首页的最新动态缺少对应工单动态  
>GZSZFSDJS-2112[GZSZFSDJS-2113] 获取全部工单接口数据维度有误  
>GZSZFSDJS-2111	所有工单模块缺少抄送给本单位的工单  
>GZSZFSDJS-2121 评价单位列表剔除抄送列表  
>GZSZFSDJS-2128 查看导出数据，部分数据的抄送单位显示NULL  
>GZSZFSDJS-2130 系统工单整体分布情况统计了工单、通知、投诉的相关数据  
>GZSZFSDJS-2136 根据抄送单位进行查询，未显示抄送单位选择分组的数据  
>GZSZFSDJS-2136 清除发送单位或发送时间数据，点击查询，页面数据为空  
>GZSZFSDJS-2142 后-工单系统接口优化  
>GZSZFSDJS-2160 一平台-运维统管：工单趋势、通知趋势、投诉趋势 切换到月度，统计数据错误  
>GZSZFSDJS-2162 一平台-站点统建：运维单位、主管单位、主办单位统计的是全量数据  
>GZSZFSDJS-2159 贵州大屏最新运维动态  
>GZSZFSDJS-1972 (工单系统生产环境）服务单位管理，根据拓尔思查询，查询无数据（实际存在数据）  
>GZSZFSDJS-2169 后 - 去除代码中的日志检测  
>GZSZFSDJS-2171 工单系统增加链路追踪 - 集成  
>GZSZFSDJS-2170[GZSZFSDJS-2172] 工单系统增加链路追踪 - 文档  
>

### v1.1数据库更新
```sql
#新增单位分组表
CREATE TABLE `unit_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(20) DEFAULT NULL,
  `status` int(2) DEFAULT 1,
  `cr_user` varchar(20) DEFAULT NULL,
  `cr_time` datetime DEFAULT NULL,
  `update_user` varchar(20) DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `is_del` int(2) DEFAULT 0,
  `export_from_other` int(11) DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;
#新增单位分组和单位关系表
CREATE TABLE `unit_group_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `group_id` bigint(20) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4;

# 20210106 褚川宝 增加人员单位映射表
DROP TABLE IF EXISTS `user_unit_mapping`;
CREATE TABLE IF NOT EXISTS `user_unit_mapping` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cr_time` datetime NOT NULL,
    `cr_user` varchar(255) NOT NULL,
    `export_from_other` int(2) DEFAULT NULL,
    `status` int(11) DEFAULT NULL,
    `user_name` varchar(255) DEFAULT NULL,
    `true_name` varchar(255) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `phone` varchar(255) DEFAULT NULL,
    `unit_id` varchar(255) DEFAULT NULL,
    `unit_type` varchar(50) DEFAULT NULL,
    `unit_name` varchar(255) DEFAULT NULL,
    `is_master` bit(1) DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=170 DEFAULT CHARSET=utf8mb4;

# 20210108 左开元 通知、抄送表修改分组id类型
ALTER TABLE `cc` 
MODIFY COLUMN `group_id` bigint NULL DEFAULT NULL COMMENT '分组id' AFTER `cr_truename`;
ALTER TABLE `notice` 
MODIFY COLUMN `group_id` bigint NULL DEFAULT NULL COMMENT '分组id' AFTER `work_order_id`;

# 20210108 杨鑫 操作记录新增分组字段
alter table opr_record add group_id BIGINT null;

#优化工单索引结构
DROP VIEW IF EXISTS user_info_of_notice;
DROP VIEW IF EXISTS two_level_work_order_type;
DROP VIEW IF EXISTS notice_detail;
DROP VIEW IF EXISTS work_order_detail;
CREATE VIEW work_order_detail AS (
SELECT
	wo.id,
	wo.cr_time,
	wo.cr_user,
	wo.work_order_top_type_id,
	wo.work_order_top_type_name,
	wo.work_order_type_id,
	wot1.type_name AS work_order_type_name,
	wo.cr_unit_id,
	wo.cr_unit,
	wo.cr_username,
	wo.cr_truename,
	wo.site_id,
	wo.sitename,
	wo.content,
	wo.priority,
	wo.host_unit_id,
	wo.host_unit,
	wo.host_assign_type,
	wo.host_username,
	wo.host_truename,
	wo.deal_unit_id,
	wo.deal_unit,
	wo.deal_assign_type,
	wo.deal_username,
	wo.deal_truename,
	wo.media_type,
	wo.expected_end_date,
	wo.update_time,
	wo.receive_time,
	wo.is_delete,
	wo.`status`,
	wo.action_time,
	wo.is_return,
	wo.finish_time,
	wo.export_from_other,
	wot2.id AS work_order_parent_type_id,
	wot2.type_name AS work_order_parent_type_name 
FROM
	work_order wo
	LEFT JOIN work_order_type wot1 ON wo.work_order_type_id = wot1.id
	LEFT JOIN work_order_type wot2 ON wot1.parent_id = wot2.id );
	CREATE VIEW notice_detail AS (
SELECT
	n.id,
	n.cr_time,
	n.cr_user,
	u.true_name AS cr_truename,
	wo.cr_unit_id,
	dept.unit_name AS cr_unit_name,
	n.work_order_id,
	n.group_id,
	n.target_unit_id,
	n.target_username,
	n.type,
	wo.content 
FROM
	notice n
	LEFT JOIN work_order wo ON n.work_order_id = wo.id
	LEFT JOIN `user` u ON u.user_name = wo.cr_user
	LEFT JOIN unit dept ON wo.cr_unit_id = dept.id
    where wo.work_order_top_type_id = 3
	);
# ------------------------20201-02-08 以上SQL已追加到init.sql中

```

### v1.1配置项修改（请根据实际情况确认是否调整配置项的值）
```yml
#推送至微信公众号,返回字段：测试环境为：test,正式环境为:szh
not-send-mobile.field=test
```


# `v1.0发版日志`
>XMKFB-2390、后 - 后端设计开发  
>GZSZFSDJS-1417 根据创建时间、更新时间查询，查询结果有误 修复  
>GZSZFSDJS-1398、主管、主办单位入驻页面，单位负责人未显示所有IDS的人员并且无手机号的显示NULL  
>GZSZFSDJS-1460 我创建的-待处理，点击工单ID，ttp://***********:9091/interaction/record/log报错  
>GZSZFSDJS-1456 关闭沿用父级配置后消息默认选中父级的消息通知（见视频)  
>GZSZFSDJS-1417  根据创建时间、更新时间查询，查询结果有误  
>GZSZFSDJS-1454  新建、编辑站点关系，监测云未添加去重复添加逻辑  
>GZSZFSDJS-1411  新增工单类型，状态默认为启用  
>GZSZFSDJS-1410  启用多个已停用的工单类别，启用失败  
>GZSZFSDJS-1415  新增、编辑工单类型，类别名称、类别简介输入空格，点击确定，操作成功  
>GZSZFSDJS-1422  新增主管、主办、服务单位，单位地址输入超长字符串  
>GZSZFSDJS-1408  编辑工单类别，同一父级类别下，工单类别名称能重复  
>GZSZFSDJS-1484  回复失败保存workorder.handel.huifu操作记录失败， 保存操作记录失败!  
>GZSZFSDJS-1488  工单详情工单记录，每个操作均显示了评分单位  
>GZSZFSDJS-1520  回退工单页面，经办记录的操作单位数据错误  
>GZSZFSDJS-1524  工单系统新增权限系统配置  
>GZSZFSDJS-1504  创建通知，小铃铛提示为协作工单  
>GZSZFSDJS-1580  工单系统配置管理-服务单位管理：新增数据的单位人员：20  
>GZSZFSDJS-1555  编辑工单类型数据，保存时提示获取数据异常,异常信息:该层级类型名称已存在  
>GZSZFSDJS-1581  工单系统配置管理-主管主办单位管理：查询条件已关联查询出了未关联的用户  
>GZSZFSDJS-1538  新增工单工时处理的权限控制  
>GZSZFSDJS-1573  工单系统配置管理-主管、主办、服务单位管：详细介绍页面未显示电话  
>----------------------------2020-10-23------------------------------------  
>GZSZFSDJS-1530 所有工单-待处理：无数据  
>GZSZFSDJS-1532 受理单位责任人转交工单，缺少工单响应消息  
>GZSZFSDJS-1561 发起单位其它用户进入所有工单-待处理，无本单位其它人员创建的待处理的工单  
>GZSZFSDJS-1562	主办单位人员的所有工单-待处理列表无数据  
>GZSZFSDJS-1588	所有工单-全部、待处理、已处理：无投诉类工单  
>GZSZFSDJS-1590	受理单位首页-最新动态模块缺少投诉类工单数据  
>GZSZFSDJS-1592 去掉编辑投诉时修改受理人的情况  
>GZSZFSDJS-1572 用2个不同浏览器访问工单系统，小铃铛消息未实时提示  
>GZSZFSDJS-1598 创建工单（交办工单）到单位，单位负责人进入系统，小铃铛无红点提示  
>XMKFB-2826 后-统计分析  
>GZSZFSDJS-1597 交办、回退提示信息重复  
>GZSZFSDJS-1600	新增工作量权限优化  
>GZSZFSDJS-1604 单位负责人进入所有工单-待处理无抄送数据  
>----------------------------2020-10-26------------------------------------  
>GZSZFSDJS-1602 重新打开的操作记录保存目标单位和用户  
>GZSZFSDJS-1543 删除站点关系，修改删除站点关系的网站的工单，网站显示的是编码  
>GZSZFSDJS-1616 修复所有工单查询为空时的bug  
>GZSZFSDJS-1548 搜索条件为空，点击搜索按钮，无响应  
>GZSZFSDJS-1547 工单记录与需求文档不符  
>GZSZFSDJS-1570 我处理的-待我处理(抄送我的、已处理)、我评价的-待我平（已评价）、我创建的-待处理（已处理）、所有工单-全部（待处理、已处理）、问题知识库-全部列表，不能根据工单ID进行搜索  
>GZSZFSDJS-1617 已处理去掉创建者修改、交办，主办单位交办的工单  
>GZSZFSDJS-1615 更新主办人权限  
>GZSZFSDJS-1620 所有工单-全部、待处理列表：工单数据未显示其他单位修改受理单位的数据  
>GZSZFSDJS-1623 我评价的-已评价：无已公开的数据  
>GZSZFSDJS-1626 新增 抄送单位，原抄送单位会收到提示信息  
>GZSZFSDJS-1619 首页-最新动态无数据（全部工单有数据)  
>GZSZFSDJS-1626 新增抄送单位，原抄送单位会收到提示信息   
>GZSZFSDJS-1627 投诉保存null异常修复  
>----------------------------2020-10-27------------------------------------  
>GZSZFSDJS-1621 完成工单后，主办单位收到的提示信息与需求文档不一致  
>GZSZFSDJS-1619 首页-最新动态无数据（全部工单有数据）  
>GZSZFSDJS-1611 工单系统配置管理-站点关系管理：清除搜索内容，页面显示错误  
>GZSZFSDJS-1610 工单系统配置管理-站点关系管理：根据站点名称进行搜索，搜索结果错误  
>GZSZFSDJS-1469 新增、编辑主管、主办、服务单位，单位负责人不支持模糊匹配  
>GZSZFSDJS-1517 主管、主办单位、服务管理：根据单位名称进行搜索，搜索结果无数据  
>GZSZFSDJS-1585 工单系统配置管理-服务单位管理，根据单位名称查询，查询结果为空  
>GZSZFSDJS-1584 工单系统配置管理-主管主办单位管理：根据单位名称查询，查询结果数据条数显示不正确  
>GZSZFSDJS-1609 工单系统配置管理-主管主办单位管理：根据单位名称进行搜索，搜索条件无效  
>工单查询新增返回站点类型  
>GZSZFSDJS-1538 工时删除异常修复  
>----------------------------2020-10-28------------------------------------  
>GZSZFSDJS-1640  没有绑定单位编码，单位负责人不能切换单位  
>GZSZFSDJS-1516  工单系统配置管理-站点关系管理、主管、主办单位管理、服务单位管理：缺少导出数据功能  
>GZSZFSDJS-1657 通知-我收到的：根据通知内容进行搜索，搜索结果为空  
>GZSZFSDJS-1653 工作记录要根据工作类型名称显示，如新建投诉就显示【创建投诉】、新建通知就显示【创建通知】  
>GZSZFSDJS-1658 通知-我发出的：根据通知内容进行搜索，检索通知列表失败! null  
>GZSZFSDJS-1656 通知-我收到的、我发出的：切换页码后，根据发送单位、发送时间搜索，搜索结果为空  
>GZSZFSDJS-1548 搜索条件为空，点击搜索按钮，无响应  
>单位查询 新增查询单位基本信息接口  
>保存投诉 优化投诉单位获取  
>GZSZFSDJS-1626 新增抄送单位，原抄送单位会收到提示信息  
>----------------------------2020-10-29------------------------------------  
>GZSZFSDJS-1662  工单系统配置管理-主管、主办、服务单位管理：导出数据的启用状态显示的是数据库的值  
>GZSZFSDJS-1663  账号【寇婷】打开工单系统，切换单位，单位数据重复  
>GZSZFSDJS-1626 新增抄送单位，原抄送单位会收到提示信息  
>GZSZFSDJS-1667 http://***********:9091/search/search/workOrder?type=&workOrderTopTypeIdList=&workOrderTypeIdList=&keywords=1028&hostUnitIdList=&crUnitIdList=&ccUnitIdList=&crTime=&expectedEndDate=&pageNum=1&pageSize=30 检索工单类型失败! null  
>GZSZFSDJS-1671 工单创建操作的类型修改  
>GZSZFSDJS-1675 我处理的-待我处理、抄送我的：根据工单ID查询，能够查询到已处理列表的工单  
-----------------------------2020-10-30------------------------------------  
>GZSZFSDJS-1690 后- 贵州统计排行榜接口开发  
>XMKFB-2930 后 - 大屏工单数据接口开发  
>GZSZFSDJS-1701 工单类型为空的工单，评价工单、公开工单、取消公开工单未发送消息  
>GZSZFSDJS-1677 我处理的、我评价的、我创建的、所有工单、问题知识库，根据工单内容查询，分词搜索结果匹配不够  
>GZSZFSDJS-1704 后 - 工单回退，在回退节点增加创建工单节点。  
-----------------------------2020-11-02------------------------------------  
>GZSZFSDJS-1699 工单列表接口中增加一个字段返回系统的当前时间  
>GZSZFSDJS-1706 后 - 工单评分只存了整形数少了半星
>工单查询  新增查询类型-下属主办  
-----------------------------2020-11-03------------------------------------  
>GZSZFSDJS-1720 平台运维统计-排行榜：超期数统计不正确  
>GZSZFSDJS-1723 站点关系列表接口需要返回云监控站点ID  
>GZSZFSDJS-1725 后 - 排行榜数据优化  
>-----------------------------2020-11-04------------------------------------  
>GZSZFSDJS-1721 后 - 工单列表增加超时列表查询  
>GZSZFSDJS-1728 平台运维统计-单位得分统计:得分统计不正确  
>GZSZFSDJS-1716 新增我的工作信息-工单查询接口  
>GZSZFSDJS-1730 将工单退回到创建人，创建人修改、交办工单后，所有工单-待处理、已处理均显示了该工单  
>------------------------2020-11-05------------------------------------  
>GZSZFSDJS-1703 工单受理单位，修改工单功能关闭  
>后 - 工单列表增加超时列表查询 超时工单查询接口  
>------------------------2020-11-06------------------------------------  
>GZSZFSDJS-1735 后 - 增加查询下属组织的操作  
>GZSZFSDJS-1747 后 - 增加查询下属主办工单列表的功能  
>------------------------2020-11-09------------------------------------  
>GZSZFSDJS-1753 所有工单中显示下属主办单位的数据  
------------------------2020-11-10------------------------------------  
>GZSZFSDJS-1756 工单搜索相关条件进行变更，检索模块也需要进行同步  
------------------------2020-11-12------------------------------------  
>GZSZFSDJS-1764 优化工单响应时间策略  
>工时 工时权限优化  
------------------------2020-11-13------------------------------------  
>GZSZFSDJS-1712 运维单位统计：工单类型统计缺少所有工单类型  
>GZSZFSDJS-1768 通知单位用户优化  
------------------------2020-11-16------------------------------------  
>GZSZFSDJS-1733 新增导出工单数据接口  
>------------------------2020-11-19------------------------------------  
>GZSZFSDJS-1786 我处理的、我创建的、我评价的、所有工单、问题知识库：根据工单ID进行查询，受理人、主办人显示错误  
>------------------------2020-11-27------------------------------------  
>GZSZFSDJS-1809[GZSZFSDJS-1742] 投诉回退  
>GZSZFSDJS-1811 未配置监测站点，新建站点关系报错  
>GZSZFSDJS-1812 判断站点名称重复逻辑错误，目前根据站点名称来判重  
>GZSZFSDJS-1810 建议投诉单位未配置，系统后台报错  
>------------------------2020-12-2------------------------------------  
>GZSZFSDJS-1881 合 - 全部工单等界面加载缓慢  
>XMKFB-3277[XMKFB-3117] 后 - 老工单数据转换成新工单迁移DTO的操作  
>XMKFB-3117 后-工单历史数据迁移  
>------------------------2020-12-3------------------------------------  
>GZSZFSDJS-1889 待我处理的-已处理 没有操作记录也能查到当前已处理  
>GZSZFSDJS-1871 后 - 增加下载接口  
>------------------------2020-12-16------------------------------------  
>GZSZFSDJS-1962	新建工单，能够选择网站运维周期外的单位  
>GZSZFSDJS-1961 新建、交办工单，加载了已禁用的用户   
>------------------------2020-12-17------------------------------------  
>XMKFB-3393: 通知模块表结构优化，新建、查询通知优化  
>------------------------2020-12-18------------------------------------  
>XMKFB-3393: 补充查询通知接收人数，提供查询通知阅读数、接收人数接口  
>------------------------2020-12-22------------------------------------  
>GZSZFSDJS-1997: 站点关系管理：站点名称取的是站点唯一标识  
>GZSZFSDJS-1975: 优化工单检索表，以及代码  
>GZSZFSDJS-1972：（工单系统生产环境）服务单位管理，根据拓尔思查询，查询无数据（实际存在数据）  
>GZSZFSDJS-1969： 消息通知的小铃铛默认有红点（点击发现其实无最新消息）  
>------------------------2020-12-24------------------------------------  
>GZSZFSDJS-2011:渠道不同，选择站点名称时提示此站点关系已建立，不能重复添加
>------------------------2020-12-25------------------------------------  
>XMKFB-3429 后-工单登录异常情况  
>------------------------2020-12-28------------------------------------  
>XMKFB-3331：后-消息发送扩展  
>XMKFB-3330：后-调研微信公众号发送可行性  
>XMKFB-3332：后-消息发送策略设计  
>GZSZFSDJS-2035：【站点推荐】新建工单时，显示推荐站点.  
>------------------------2020-12-29------------------------------------  
>XMKFB-3393 发送通知优化  
>GZSZFSDJS-2025 抄送优化  
>------------------------2020-12-30------------------------------------  
>GZSZFSDJS-2038 需要配置超管及管理员角色及权限，可以查看管理全部工单  
>GZSZFSDJS-2056 新建工单，保存时提示用户不存在  
>------------------------2020-12-31------------------------------------  
>XMKFB-3475 后-提供只需用户名密码就可以获取工单系统token的接口  
------------------------2021-01-04------------------------------------  
>GZSZFSDJS-2066 后 - 推荐站点接口报错  
>GZSZFSDJS-2065[GZSZFSDJS-2063] 通知/抄送新增单位分组选择及通知抄送到全部 功能支持  
>XMKFB-3393 后 - 发送通知优化  
>
### v1.0数据库更新
```sql

# 2010/10/15 左开元 
ALTER TABLE `work_order` 
 ADD COLUMN `host_assign_type` int NULL DEFAULT 0 COMMENT '主办人指定类型（1：指定到人，2：指定到单位）' AFTER `host_unit`,
 ADD COLUMN `deal_assign_type` int NULL DEFAULT 0 COMMENT '处理人指定类型（1：指定到人，2：指定到单位）' AFTER `deal_unit`;
ALTER TABLE `opr_record` 
 ADD COLUMN `assign_type` int NULL DEFAULT 0 COMMENT '是否指定到单位： 0都不指定，1指定到人，2指定到单位' AFTER `status`;
# 2020/10/15 兰鑫
# 表 work_order_type 修改status字段默认值为-1
alter table work_order_type MODIFY `status` int(4) default -1; 
# 表 unit 修改unit_addr字段类型为longtext
alter table unit MODIFY `unit_addr` longtext; 
表 site_relation 修改unique_id字段默认长度为50
ALTER TABLE `site_relation` MODIFY COLUMN unique_id VARCHAR(50);
# 2020/10/19 杨鑫 
alter table notice_message add reveive_unit_id bigint null;
alter table notice_message add update_unit_id bigint null;
alter table notice_message add cr_unit_id bigint null;
# 2020/10/23 兰鑫
# 表 grade 增加grade_unit_id字段
ALTER TABLE `grade` ADD COLUMN `grade_unit_id` BIGINT(20); 
# 2020/10/27 兰鑫
# 表 work_order_type 增加accept_unit_id字段
ALTER TABLE work_order_type ADD COLUMN accept_unit_id BIGINT;
#2020/10/29 兰鑫
ALTER TABLE unit ADD VERSION INT DEFAULT 0;
#2020/11/02
ALTER TABLE grade MODIFY attitude DOUBLE;
ALTER TABLE grade MODIFY complete_time DOUBLE;
ALTER TABLE grade MODIFY complete_content DOUBLE;
#表site_relation增加monitor_site_id字段
ALTER TABLE `site_relation` ADD COLUMN `monitor_site_id` BIGINT(20); 

#20201124 褚川宝各个表中增加统一的export_from_other字段，用于标记相关数据是否是迁移过来的。0：不是迁移的数据
ALTER TABLE work_order ADD export_from_other INT DEFAULT 0;
ALTER TABLE opr_record ADD export_from_other INT DEFAULT 0;
ALTER TABLE user ADD export_from_other INT DEFAULT 0;
ALTER TABLE message_config ADD export_from_other INT DEFAULT 0;
ALTER TABLE site_relation ADD export_from_other INT DEFAULT 0;
ALTER TABLE group_table ADD export_from_other INT DEFAULT 0;
ALTER TABLE work_order_type ADD export_from_other INT DEFAULT 0;
ALTER TABLE cc ADD export_from_other INT DEFAULT 0;
ALTER TABLE notice ADD export_from_other INT DEFAULT 0;
ALTER TABLE work_time ADD export_from_other INT DEFAULT 0;
ALTER TABLE group_key_mapping ADD export_from_other INT DEFAULT 0;
ALTER TABLE appendix_table ADD export_from_other INT DEFAULT 0;
ALTER TABLE unit ADD export_from_other INT DEFAULT 0;
ALTER TABLE notice_message ADD export_from_other INT DEFAULT 0;
ALTER TABLE grade ADD export_from_other INT DEFAULT 0;
ALTER TABLE group_user_table ADD export_from_other INT DEFAULT 0;

#20201125 褚川宝 新旧数据映射表
DROP TABLE IF EXISTS `temp_export_mapping_table`;
CREATE TABLE `temp_export_mapping_table`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `cr_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `export_from_other` int(11) NULL DEFAULT 0 COMMENT '是否从其他地方导入',
  `status` int(11) NULL DEFAULT NULL COMMENT '状态',
  `new_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '新ID',
  `old_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '旧ID',
  `new_table` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '新表',
  `old_table` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '旧表',
  `system_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '旧系统标识',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

#20201217 左开元 通知表结构修改
ALTER TABLE `notice` 
DROP COLUMN `cr_unit_id`,
DROP COLUMN `cr_username`,
DROP COLUMN `cr_unit`,
DROP COLUMN `cr_truename`,
MODIFY COLUMN `target_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知目标人单位' AFTER `target_unit_id`,
MODIFY COLUMN `status` int NULL DEFAULT NULL COMMENT '' AFTER `target_truename`,
MODIFY COLUMN `type` int NULL DEFAULT NULL COMMENT '通知类型（1：个人，2：单位，3：全部）' AFTER `status`;
DROP TABLE IF EXISTS `notice_read`;
CREATE TABLE `notice_read`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cr_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `work_order_id` bigint NULL DEFAULT NULL COMMENT '工单id',
  `target_unit_id` bigint NULL DEFAULT NULL COMMENT '通知目标单位id',
  `target_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知目标人',
  `status` int NULL DEFAULT 0 COMMENT '阅读状态(0：未读，1：已读)',
  `export_from_other` int NULL DEFAULT 0 COMMENT '是否来自老工单（0：否，1：是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 84 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知表' ROW_FORMAT = DYNAMIC;

#20201217 杨鑫 增加工单和通知的视图
DROP VIEW IF EXISTS two_level_work_order_type;
DROP VIEW IF EXISTS work_order_detail;
DROP VIEW IF EXISTS user_info_of_notice;
DROP VIEW IF EXISTS notice_detail;
CREATE VIEW two_level_work_order_type AS (
SELECT
	wot1.id,
	wot1.type_name,
	wot2.id AS work_order_parent_type_id,
	wot2.type_name AS work_order_parent_type_name 
FROM
	work_order_type wot1
	LEFT JOIN work_order_type wot2 ON wot1.parent_id = wot2.id 
	);
CREATE VIEW work_order_detail AS (
SELECT
	wo.id,
	wo.cr_time,
	wo.cr_user,
	wo.work_order_top_type_id,
	wo.work_order_top_type_name,
	wo.work_order_type_id,
	wot.type_name AS work_order_type_name,
	wo.cr_unit_id,
	wo.cr_unit,
	wo.cr_username,
	wo.cr_truename,
	wo.site_id,
	wo.sitename,
	wo.content,
	wo.priority,
	wo.host_unit_id,
	wo.host_unit,
	wo.host_assign_type,
	wo.host_username,
	wo.host_truename,
	wo.deal_unit_id,
	wo.deal_unit,
	wo.deal_assign_type,
	wo.deal_username,
	wo.deal_truename,
	wo.media_type,
	wo.expected_end_date,
	wo.update_time,
	wo.receive_time,
	wo.is_delete,
	wo.`status`,
	wo.action_time,
	wo.is_return,
	wo.finish_time,
	wo.export_from_other,
	wot.work_order_parent_type_id,
	wot.work_order_parent_type_name
FROM
	work_order wo
	LEFT JOIN two_level_work_order_type wot ON wo.work_order_type_id = wot.id
	);
CREATE view user_info_of_notice
as
(SELECT
	w.id,
	w.cr_user,
	u.true_name AS cr_username,
	w.cr_unit_id,
	un.unit_name AS cr_unit_name,
	w.content
FROM
	work_order w
	LEFT JOIN `user` u ON w.cr_user = u.user_name
	LEFT JOIN unit un ON w.cr_unit_id = un.id 
WHERE
	w.work_order_top_type_id = 3);
CREATE VIEW notice_detail AS (
SELECT
	n.*,
	wo.work_order_top_type_id,
	wo.work_order_top_type_name,
	wo.content 
FROM
	notice n
	LEFT JOIN work_order wo ON n.work_order_id = wo.id 
	);

#20210104 左开元 通知、抄送表结构修改
ALTER TABLE `cc` 
ADD COLUMN `group_id` int NULL DEFAULT NULL COMMENT '分组id' AFTER `cr_truename`;
ALTER TABLE `notice` 
ADD COLUMN `group_id` int NULL DEFAULT NULL COMMENT '分组id' AFTER `work_order_id`;
```

### v1.0配置项修改（请根据实际情况确认是否调整配置项的值）
```yml
#------------------------2020-12-3------------------------------------  
# 文件下载的Url前缀 [前端访问地址 + 端口 + file/downloadFile]
FileManagerService.downloadUrlPrefix=http://192.168.200.106:81/file/downloadFile
#海云权限key值
hycloud.key=aHG8rFHx3MOeSOvmlkJoa2K1xvKRgIwq
#删除项目中Es相关配置信息，并将Es配置相关信息移入到nacos的workorder.properties
es.dataSource.url=http://10.11.2.114:9200
es.date.format=yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
#------------------------2020-12-28------------------------------------  
#nacos中添加配置消息渠道
#通知消息将要推送至的平台,通过从左到右的顺序进行执行推送,建议将推送至工单写在最前面,防止推送至第三方平台时网络时间的响应问题
# 1.工单,2.第三方接口
different.channel.list=gz_work_order,third_party_interface
#推送至第三方接口中那些渠道,多个用逗号隔开 1.微信公众号
thirdPartyInterface.of.channels=wxgzh
#推送的用户名
push.user-name=szh_tes_user
#推送的授权码
push.auth-code=WOi3PjVsb8JYFSz7
#token 的过期时间 10分钟
token.expiration.time=600
#token存在的索引库
token.expiration.index=10
#获取tokend的Url地址
token.url=http://117.187.141.80:8082/index.php/token
# 推送数据地址
push.url=http://117.187.141.80:8082/index.php/notice
# 第一次发送失败后,需要重试的次数
fail.retry.send.count=2
#------------------------2020-12-30------------------------------------  
#增加Es索引相关配置
workorder.indeces-name=work_order
notice.indices-type=notice_detail
siteRelation.indices-type=site_relation
unit.indices-type=unit
user.indices-type=user
workorder.indices-type=work_order_detail
```
