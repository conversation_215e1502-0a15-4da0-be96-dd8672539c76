<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.trs.gov</groupId>
    <artifactId>workorder-system-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.4</version>
    <modules>
        <module>Configuration</module>
        <module>Core</module>
        <module>UserCenter</module>
        <module>FileManager</module>
        <module>WorkOrder</module>
        <module>SystemManagement</module>
        <module>Message</module>
        <module>Interaction</module>
        <module>ExternalSystem</module>
        <module>Search</module>
        <module>StatisticsCenter</module>
        <module>KnowledgeBase</module>
        <module>Gateway</module>
        <module>Export</module>
    </modules>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <!--<version>2.0.3.RELEASE</version>-->
        <version>2.3.2.RELEASE</version>
    </parent>
    <profiles>
        <profile>
            <id>trscd</id>
            <properties>
                <env>trscd</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>gz-dev</id>
            <properties>
                <env>gz-dev</env>
            </properties>
        </profile>
        <profile>
            <id>chongqing-master</id>
            <properties>
                <env>chongqing-master</env>
            </properties>
        </profile>
        <profile>
            <!-- 贵州内网环境 -->
            <id>gz-master</id>
            <properties>
                <env>gz-master</env>
            </properties>
        </profile>
        <profile>
            <!-- 贵州公网环境 -->
            <id>gz-net</id>
            <properties>
                <env>gz-net</env>
            </properties>
        </profile>
        <profile>
            <!--吉林集约化-->
            <id>jl-master</id>
            <properties>
                <env>jl-master</env>
            </properties>
        </profile>
        <profile>
            <id>ccb</id>
            <properties>
                <env>ccb</env>
                <log.path>logs</log.path>
            </properties>
        </profile>
        <profile>
            <id>yx</id>
            <properties>
                <env>yx</env>
            </properties>
        </profile>
        <profile>
            <id>zky</id>
            <properties>
                <env>zky</env>
            </properties>
        </profile>
        <profile>
            <id>neimeng</id>
            <properties>
                <env>neimeng</env>
            </properties>
        </profile>
    </profiles>

    <properties>
        <log.path>/TRS</log.path>
        <swagger2-version>2.9.2</swagger2-version>
        <alibaba.cloud.groupId>com.alibaba.cloud</alibaba.cloud.groupId>
        <!--<alibaba.cloud.version>2.1.0.RELEASE</alibaba.cloud.version>-->
        <alibaba.cloud.version>2.2.6.RELEASE</alibaba.cloud.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <!--<java.version>11</java.version>-->
        <!-- <spring-cloud.version>Dalston.RELEASE</spring-cloud.version>这个版本的cloud会导致在执行bus/refresh时导致服务从服务注册中心下线-->
        <!--<spring-cloud.version>Finchley.SR2</spring-cloud.version>-->
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <push-syn-version>2.14</push-syn-version>
        <server-version>2.14</server-version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <elasticsearch.version>7.4.0</elasticsearch.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 相关服务 -->
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>Core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>Configuration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>WorkOrder-Service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>SystemManagement-Service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>Search-Service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>Message-Service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>StatisticsCenter-Service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>Interaction-Service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>ExternalSystem-Service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>User-Service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trs.gov</groupId>
                <artifactId>FileManager-Service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!--权限客户端支持-->
            <dependency>
                <groupId>com.trs.upms</groupId>
                <artifactId>upms-client</artifactId>
                <version>[1.2,)</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>${alibaba.cloud.groupId}</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${alibaba.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>1.4.6</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-actuator-autoconfigure</artifactId>
            </dependency>
            <!--定时任务-->
            <dependency>
                <groupId>com.trs</groupId>
                <artifactId>timing_scheduler_client</artifactId>
                <version>0.1.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
            <optional>true</optional>
        </dependency>

        <!--日志版本统一，解决冲突问题 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>1.7.25</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.25</version>
        </dependency>

        <!-- nacos-->
        <dependency>
            <groupId>${alibaba.cloud.groupId}</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>${alibaba.cloud.groupId}</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>${alibaba.cloud.groupId}</groupId>
            <artifactId>spring-cloud-starter-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <version>2.7.8</version>
        </dependency>

        <!-- dubbo整合nacos注册中心 -->
        <!-- https://mvnrepository.com/artifact/org.apache.dubbo/dubbo-registry-nacos -->
        <!--<dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
            <version>2.7.13</version>
        </dependency>-->
        <!--<dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
            <version>3.0.5</version>
        </dependency>-->
        <!--解决dubbo2.7.13jar包冲突问题 -->
        <!--<dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>1.0.11</version>
        </dependency>-->

        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.23.1-GA</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <!--<dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <version>3.1.0</version>
        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--缓存工具-->
        <dependency>
            <groupId>com.trs</groupId>
            <artifactId>media_base_cache</artifactId>
            <version>3.1.9</version>
            <exclusions>
                <!-- 排除自带的logback依赖 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>media_base</artifactId>
                    <groupId>com.trs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>media_base_web_core</artifactId>
                    <groupId>com.trs.web</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <!-- <exclusion>
                     <groupId>redis.clients</groupId>
                     <artifactId>jedis</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>com.alibaba.nacos</groupId>
                     <artifactId>nacos-api</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>com.alibaba.nacos</groupId>
                     <artifactId>nacos-client</artifactId>
                 </exclusion>-->
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.trs.web</groupId>
            <artifactId>media_base_web_core</artifactId>
            <version>1.4.13</version>
            <exclusions>
                <!-- 排除自带的logback依赖 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>media_base</artifactId>
                    <groupId>com.trs</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.trs.web</groupId>
            <artifactId>media_base_web_builder</artifactId>
            <version>1.4.13</version>
            <exclusions>
                <!-- 排除自带的logback依赖 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>media_base</artifactId>
                    <groupId>com.trs</groupId>
                </exclusion>
                <!-- <exclusion>
                     <groupId>io.springfox</groupId>
                     <artifactId>springfox-swagger-ui</artifactId>
                 </exclusion>
                 <exclusion>
                     <groupId>io.springfox</groupId>
                     <artifactId>springfox-swagger2</artifactId>
                 </exclusion>-->
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.trs.web</groupId>
            <artifactId>media_base_web_jpa</artifactId>
            <version>1.3.5</version>
            <exclusions>
                <!-- 排除自带的logback依赖 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>media_base</artifactId>
                    <groupId>com.trs</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.trs</groupId>
            <artifactId>media_base</artifactId>
            <version>1.4.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.trs</groupId>
                    <artifactId>media_base_log</artifactId>
                </exclusion>
                <!--<exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>-->
            </exclusions>
        </dependency>

        <!--引入TRSLog日志模块-->
        <dependency>
            <groupId>com.trs</groupId>
            <artifactId>media_base_log</artifactId>
            <version>1.9.3</version>
            <exclusions>
                <!-- <exclusion>
                     <groupId>redis.clients</groupId>
                     <artifactId>jedis</artifactId>
                 </exclusion>-->
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>8.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-log4j-1.x</artifactId>
            <version>6.4.0</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>TRS Repository</id>
            <url>http://mvn.devdemo.trs.net.cn/repository/maven-releases/</url>
        </repository>
        <repository>
            <id>TRS-nexus</id>
            <url>https://nexus.trscd.com.cn/repository/public/</url>
        </repository>
    </repositories>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>1.2.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
