package com.trs.gov.core;

import com.trs.common.utils.expression.Expression;
import com.trs.gov.core.exception.ServiceException;

import java.util.Optional;

public interface IFieldMgr extends IKey {
    /**
     * @Param [keyWords  参数  值]
     * @return java.util.Optional<com.trs.common.utils.expression.Expression>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 14:38
     **/
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException;
}
