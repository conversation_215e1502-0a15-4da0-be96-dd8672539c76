package com.trs.gov.core.util;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import io.vavr.control.Try;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class BaseUtils {

    /**
     * 检测DTO中是否包含用户信息（只判断有无）<BR>
     *
     * @param dto 相关DTO
     * @return 检测结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 15:28
     */
    public static boolean checkDTOContianUserInfo(BaseDTO dto) {
        return Try.of(() -> {
            checkDTO(dto);
            if (CMyString.isEmpty(dto.getToken()) || CMyString.isEmpty(dto.getUnitId())) {
                return false;
            }
            return true;
        }).getOrElse(false);
    }

    public static void setUserInfoToDTO(BaseDTO... dto) {
        if (dto == null || dto.length == 0) {
            return;
        }
        String unitId = ContextHelper.getLoginUnitId().orElse("");
        String token = ContextHelper.getToken().orElse("");
        for (BaseDTO baseDTO : dto) {
            if (!checkDTOContianUserInfo(baseDTO)) {
                if (CMyString.isEmpty(baseDTO.getToken())) {
                    baseDTO.setToken(token);
                }
                if (CMyString.isEmpty(baseDTO.getUnitId())) {
                    baseDTO.setUnitId(unitId);
                }
            }
        }
    }

    /**
     * 检测DTO中是否包含用户信息（为空时向上抛出异常）<BR>
     *
     * @param dto 相关DTO
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 15:28
     */
    public static void checkDTOIsValidAndHaveLoginInfo(BaseDTO... dto) throws ServiceException {
        checkDTO(dto);
        for (BaseDTO baseDTO : dto) {
            if (checkDTOContianUserInfo(baseDTO)) {
                continue;
            }
            if (!ContextHelper.getToken().isPresent()) {
                throw new ParamInvalidException("用户信息缺失！");
            }
            if (!ContextHelper.getLoginUnitId().isPresent()) {
                throw new ParamInvalidException("单位信息缺失！");
            }
        }
    }

    /**
     * 检测DTO（用于controller中或者不需要用户信息的service中）<BR>
     * 在对外提供服务中建议使用UserUtils.checkDTOAndLoadUserInfoByDTO进行检测
     * @param dto 相关DTO
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-30 11:03
     */
    public static void checkDTO(BaseDTO... dto) throws ServiceException {
        if (dto == null) {
            throw new ParamInvalidException("请求参数数据异常");
        }
        for (BaseDTO d : dto) {
            if (d == null || !d.isValid()) {
                throw new ParamInvalidException("请求参数数据异常");
            }
        }
    }

    public static String[] getAllFieldInClass(Class<?> clazz) {
        if (clazz == null) {
            return new String[0];
        }
        PropertyDescriptor[] sourcePds = BeanUtils.getPropertyDescriptors(clazz);
        return Arrays.asList(sourcePds).stream().map(item -> item.getName()).collect(Collectors.toList()).toArray(new String[sourcePds.length]);
    }

    /**
     * 对象属性拷贝（默认跳过null属性的拷贝）<BR>
     *
     * @param source           源对象
     * @param target           目标对象
     * @param ignoreProperties 需要忽略的属性
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 12:43
     */
    public static void copyProperties(@NonNull Object source, @NonNull Object target, String... ignoreProperties) throws BeansException {
        copyProperties(source, target, true, ignoreProperties);
    }

    /**
     * 对象属性拷贝（默认跳过null属性的拷贝）<BR>
     *
     * @param source           源对象
     * @param target           目标对象
     * @param ignoreNullValue  是否忽略源对象中value为null的属性
     * @param ignoreProperties 需要忽略的属性
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 12:43
     */
    public static void copyProperties(@NonNull Object source, @NonNull Object target, boolean ignoreNullValue, String... ignoreProperties) throws BeansException {
        List<String> old = (ignoreProperties != null ? Arrays.asList(ignoreProperties) : new ArrayList<>(0));
        ArrayList<String> ignoreList = new ArrayList<>(old.size());
        if (old.size() > 0) {
            ignoreList.addAll(old);
        }
        if (ignoreNullValue) {
            PropertyDescriptor[] sourcePds = BeanUtils.getPropertyDescriptors(source.getClass());
            for (PropertyDescriptor sourcePd : sourcePds) {
                String name = sourcePd.getName();
                Method readMethod = sourcePd.getReadMethod();
                if (readMethod != null) {
                    Try.of(() -> {
                        Object value = readMethod.invoke(source);
                        if (value == null) {
                            ignoreList.add(name);
                        }
                        return 0;
                    }).onFailure(err -> log.warn("拷贝数据异常！", err));
                }
            }
        }
        BeanUtils.copyProperties(source, target, ignoreList.toArray(new String[ignoreList.size()]));
    }
}
