package com.trs.gov.core.constant;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/10/21 17:30
 * @version 1.0
 * @since  1.0
 */
public class RightConstant {

    private final static Map<String,String> rightInfo = new HashMap<>();

    public final static String WORKORDER_OPER_XINXUQIU="workorder.create.xinxuqiu";
    public final static String WORKORDER_OPER_XINXUQIU_DESC="提新需求";

    public final static String WORKORDER_OPER_FANKUI="workorder.create.fankui";
    public final static String WORKORDER_OPER_FANKUI_DESC="反馈系统问题";

    public final static String WORKORDER_OPER_TOUSU="workorder.create.tousu";
    public final static String WORKORDER_OPER_TOUSU_DESC="投诉";

    public final static String WORKORDER_OPER_FATONGZHI="workorder.create.fatongzhi";
    public final static String WORKORDER_OPER_FATONGZHI_DESC="发通知";

    public final static String WORKORDER_OPER_GONGZUOXINXI="workorder.create.gongzuoxinxi";
    public final static String WORKORDER_OPER_GONGZUOXINXI_DESC="我的工作信息";

    public final static String WORKORDER_OPER_YUNWEITONGJI="workorder.create.yunweitongji";
    public final static String WORKORDER_OPER_YUNWEITONGJI_DESC="运维统计";

    public final static String WORKORDER_OPER_SITERELATION="workorder.create.siteRelation";
    public final static String WORKORDER_OPER_SITERELATION_DESC="站点关系";

    public final static String WORKORDER_OPER_HOSTANDMASTERUNIT="workorder.create.hostAndMasterUnit";
    public final static String WORKORDER_OPER_HOSTANDMASTERUNIT_DESC="主管主办单位管理";

    public final static String WORKORDER_OPER_OPERATIONUNIT="workorder.create.operationUnit";
    public final static String WORKORDER_OPER_OPERATIONUNIT_DESC="服务单位管理";

    public final static String WORKORDER_OPER_WORKORDERTYPE="workorder.create.workOrderType";
    public final static String WORKORDER_OPER_WORKORDERTYPE_DESC="工单类型管理";

    public final static String WORKORDER_OPER_WORKORDERMESSAGE="workorder.create.workOrderMessage";
    public final static String WORKORDER_OPER_WORKORDERMESSAGE_DESC="工单消息管理";

    static {
        rightInfo.put(WORKORDER_OPER_XINXUQIU,WORKORDER_OPER_XINXUQIU_DESC);
        rightInfo.put(WORKORDER_OPER_FANKUI,WORKORDER_OPER_FANKUI_DESC);
        rightInfo.put(WORKORDER_OPER_TOUSU,WORKORDER_OPER_TOUSU_DESC);
        rightInfo.put(WORKORDER_OPER_FATONGZHI,WORKORDER_OPER_FATONGZHI_DESC);
        rightInfo.put(WORKORDER_OPER_GONGZUOXINXI,WORKORDER_OPER_GONGZUOXINXI_DESC);
        rightInfo.put(WORKORDER_OPER_YUNWEITONGJI,WORKORDER_OPER_YUNWEITONGJI_DESC);
        rightInfo.put(WORKORDER_OPER_SITERELATION,WORKORDER_OPER_SITERELATION_DESC);
        rightInfo.put(WORKORDER_OPER_HOSTANDMASTERUNIT,WORKORDER_OPER_HOSTANDMASTERUNIT_DESC);
        rightInfo.put(WORKORDER_OPER_OPERATIONUNIT,WORKORDER_OPER_OPERATIONUNIT_DESC);
        rightInfo.put(WORKORDER_OPER_WORKORDERTYPE,WORKORDER_OPER_WORKORDERTYPE_DESC);
        rightInfo.put(WORKORDER_OPER_WORKORDERMESSAGE,WORKORDER_OPER_WORKORDERMESSAGE_DESC);
    }

    /**
     * 获取操作类型对应描述<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/10/21 17:37
     * @param  key    操作类型
     * @throws
     * @return  操作描述
     */
    public Optional<String> getOperDesc(String key){
        return Optional.ofNullable(rightInfo.get(key));
    }

}
