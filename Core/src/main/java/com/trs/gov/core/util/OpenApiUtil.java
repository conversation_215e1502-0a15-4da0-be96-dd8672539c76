package com.trs.gov.core.util;

import java.util.Optional;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2025-08-13 09:28
 */
public class OpenApiUtil {

    public static final ThreadLocal<String> openApiThreadLocal = new ThreadLocal<>();

    public static void addIsOpenApi(String isOpenApi){
        openApiThreadLocal.set(isOpenApi);
    }

    public static Optional<String> getIsOpenApi(){
        return Optional.ofNullable(openApiThreadLocal.get());
    }


    public static void remove(){
        openApiThreadLocal.remove();
    }

}
