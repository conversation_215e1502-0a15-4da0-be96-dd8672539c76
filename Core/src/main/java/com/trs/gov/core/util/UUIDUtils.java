package com.trs.gov.core.util;

import java.util.Random;
import java.util.UUID;

public class UUIDUtils {
    public static String createUUID(boolean include) {
        String result = UUID.randomUUID().toString();
        if (include == false) {
            result = result.replace("-", "");
        }
        return result.toUpperCase();
    }

    public static String createRandom(int num) {
        StringBuilder str = new StringBuilder();//定义变长字符串
        Random random = new Random();
        //随机生成数字，并添加到字符串
        for (int i = 0; i < num; i++) {
            str.append(random.nextInt(10));
        }
        //将字符串转换为数字并输出
        return str.toString();
    }
}
