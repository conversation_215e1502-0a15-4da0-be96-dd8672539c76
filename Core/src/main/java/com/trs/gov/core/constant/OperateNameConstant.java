package com.trs.gov.core.constant;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @ClassName：OperateNameConstant
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/10 15:27
 **/
public class OperateNameConstant {

    private static Map<String,String> descMapType = new HashMap<>();
    /**
     * 创建工单
     */
    public static final String CREATE_WORK_ORDER = "workorder.add.create";
    private static final String CREATE_DESC = "创建";

    /**
     * 工单响应
     */
    public static final String RESPONSE_WORK_ORDER = "workorder.handel.xiangying";
    private static final String RESPONSE_DESC = "响应";

    /**
     * 抄送工单
     */
    public static final String COPY_WORK_ORDER = "workorder.handel.xinzengchaosong";
    private static final String COPY_DESC = "共享";

    /**
     * 记录工单工时
     */
    public static final String MAN_HOUR_WORK_ORDER = "workorder.handel.jilugongshi";
    private static final String MAN_HOUR_DESC = "记录工时";

    /**
     * 回复工单
     */
    public static final String REPLY_WORK_ORDER = "workorder.handel.huifu";
    private static final String REPLY_DESC = "回复";

    /**
     * 回退工单
     */
    public static final String ROLLBACK_WORK_ORDER = "workorder.handel.huitui";
    private static final String ROLLBACK_DESC = "回退";

    /**
     * 交办工单
     */
    public static final String ASSIGN_WORK_ORDER = "workorder.handel.jiaoban";
    private static final String ASSIGN_DESC = "交办";

    /**
     * 修改工单
     */
    public static final String UPDATE_WORK_ORDER = "workorder.handel.xiugai";
    private static final String UPDATE_DESC = "修改";

    /**
     * 修改工单时更新了受理人
     */
    public static final String UPDATE_WORK_ORDER_DEAL = "workorder.handel.xiugai.deal";
    private static final String UPDATE_DEAL_DESC = "修改";

    /**
     * 完成工单
     */
    public static final String FINISH_WORK_ORDER = "workorder.handel.wanchenggongdan";
    private static final String FINISH_DESC = "完成";

    /**
     * 评价工单
     */
    public static final String APPRAISE_WORK_ORDER = "workorder.finish.pingjia";
    private static final String APPRAISE_DESC = "评价";

    /**
     * 重新打开工单
     */
    public static final String REOPEN_WORK_ORDER = "workorder.finish.chongxindakai";
    private static final String REOPEN_DESC = "重新打开";

    /**
     * 公开
     */
    public static final String PUBLISH_WORK_ORDER = "workorder.comment.gongkai";
    private static final String PUBLISH_DESC = "公开";

    /**
     * 不公开
     */
    public static final String NOT_PUBLISH_WORK_ORDER = "workorder.comment.bugongkai";
    private static final String NOT_PUBLISH_DESC = "不公开";


    /**
     * 创建通知
     */
    public static final String CREATE_NOTICE = "workorder.create.tongzhi";
    private static final String CREATE_NOTICE_DESC = "创建";

    /**
     * 修改工时
     */
    public static final String UPDATE_MANHOUR = "workorder.update.xiugaigongshi";
    private static final String UPDATE_MANHOUR_DESC = "修改工时";

    /**
     * 删除工时
     */
    public static final String DELETE_MANHOUR = "workorder.delete.shanchugongshi";
    private static final String DELETE_MANHOUR_DESC = "删除工时";

    /**
     * 催办工单
     */
    public static final String HURRY_WORK_ORDER = "workorder.handel.cuiban";
    private static final String HURRY_DESC = "催办";



    static {
        descMapType.put(CREATE_WORK_ORDER,CREATE_DESC);
        descMapType.put(RESPONSE_WORK_ORDER,RESPONSE_DESC);
        descMapType.put(COPY_WORK_ORDER,COPY_DESC);
        descMapType.put(MAN_HOUR_WORK_ORDER,MAN_HOUR_DESC);
        descMapType.put(REPLY_WORK_ORDER,REPLY_DESC);
        descMapType.put(ROLLBACK_WORK_ORDER,ROLLBACK_DESC);
        descMapType.put(ASSIGN_WORK_ORDER,ASSIGN_DESC);
        descMapType.put(UPDATE_WORK_ORDER,UPDATE_DESC);
        descMapType.put(UPDATE_WORK_ORDER_DEAL,UPDATE_DEAL_DESC);
        descMapType.put(FINISH_WORK_ORDER,FINISH_DESC);
        descMapType.put(APPRAISE_WORK_ORDER,APPRAISE_DESC);
        descMapType.put(REOPEN_WORK_ORDER,REOPEN_DESC);
        descMapType.put(PUBLISH_WORK_ORDER,PUBLISH_DESC);
        descMapType.put(NOT_PUBLISH_WORK_ORDER,NOT_PUBLISH_DESC);
        descMapType.put(CREATE_NOTICE,CREATE_NOTICE_DESC);
        descMapType.put(UPDATE_MANHOUR,UPDATE_MANHOUR_DESC);
        descMapType.put(DELETE_MANHOUR,DELETE_MANHOUR_DESC);
    }
    /**
     * @Description  获取对应操作类型的文字描述
     * @Param [type]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/23 16:23
     **/
    public static Optional<String> getTypeDesc(String type){
        return Optional.ofNullable(descMapType.get(type));
    }

}
