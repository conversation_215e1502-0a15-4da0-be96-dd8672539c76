package com.trs.gov.core.util;

import java.util.regex.Pattern;

public class Validator {
    /**
     * 正则表达式：验证用户名
     */
    public static final String REGEX_USERNAME = "^\\w{2,18}$";

    /**
     * 正则表达式：验证正整数
     */
    public static final String REGEX_INTEGER = "^\\d$";

    /**
     * 正则表达式：验证真实姓名
     */
    public static final String REGEX_TRUENAME = "^[\u4e00-\u9fa5]{2,20}$";

    /**
     * 正则表达式：验证机构名称
     */
    public static final String REGEX_ORGNAME = "^[\\w\\W]{2,30}$";

    /**
     * 正则表达式：验证机构代码
     */
    public static final String REGEX_ORGCODE = "^[\\w\\W]{2,20}$";

    /**
     * 正则表达式：验证密码
     */
    public static final String REGEX_PASSWORD = "^[~`!@#$%^&*()_+\\w]{8,20}$";

    /**
     * 正则表达式：验证手机号
     */
    public static final String REGEX_MOBILE = "^((13[0-9])|(14[0-9])|(15[0-9])|(18[0-9])|(17[0-9]))\\d{8}$";

    /**
     * 正则表达式：验证邮箱
     */
//    public static final String REGEX_EMAIL = "^([a-z0-9A-Z]+[_-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
    public static final String REGEX_EMAIL = "^(\\w)+(\\.\\w+)*@(\\w)+((\\.\\w+)+)$" ;

    /**
     * 正则表达式：验证汉字
     */
    // public static final String REGEX_CHINESE = "^[\u4e00-\u9fa5],{0,}$";
    public static final String REGEX_CHINESE = "^[\u4e00-\u9fa5]+$";

    /**
     * 正则表达式：验证身份证
     */
    public static final String REGEX_ID_CARD = "(^\\d{18}$)|(^\\d{15}$)";

    /**
     * 正则表达式：验证URL
     */
//    public static final String REGEX_URL = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";
    public static final String REGEX_URL = "(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";

    /**
     * 正则表达式：验证IP地址
     */
    public static final String REGEX_IP_ADDR = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";

    /**
     * 正则表达式:验证QQ号码
     */

    public static final String REGEX_QQ = "^[1-9][0-9]{4,}$";

    /**
     * 正则表达式:验证邮编
     */
    public static final String REGEX_POSTCODE = "^[0-9]{6}$";

    /**
     * 正则表达式:验证电话号码：格式010-12345678
     */
    public static final String REGEX_TEL = "^[0][1-9]{2,3}-[0-9]{5,10}$";

    /**
     * 正则表达式:验证微信号码，字母数字结合
     */
    public static final String REGEX_WECHAt = "^([a-zA-Z0-9]+)$";

    /**
     * 正则表达式:验证相对路径文件路径
     */
    public static final String REGEX_RELATIVE_FILE_PATH = "^(\\w+/)*([\\da-z\\.-]+)\\.([a-z\\.]{2,8})$";


    /**
     * 校验相对路径
     *
     * @param filePath
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isRelativeFilePath(String filePath){
        return Pattern.matches(REGEX_RELATIVE_FILE_PATH, filePath);
    }

    /**
     * 校验用户名
     *
     * @param username
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isUsername(String username) {
        return Pattern.matches(REGEX_USERNAME, username);
    }

    /**
     * 校验是Ingteger数字
     *
     * @param integer
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isInteger(String integer) {
        return Pattern.matches(REGEX_INTEGER, integer);
    }

    /**
     * Description:z校验真实姓名 <BR>
     *
     * <AUTHOR>
     * @date 2016年6月26日 下午4:32:08
     * @param truename
     * @return
     */
    public static boolean isTruename(String truename) {
        return Pattern.matches(REGEX_TRUENAME, truename);
    }

    /**
     * 校验密码
     *
     * @param password
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isPassword(String password) {
        return Pattern.matches(REGEX_PASSWORD, password);
    }

    /**
     * Description: 校验机构名称 <BR>
     *
     * <AUTHOR>
     * @date 2016年6月26日 下午4:25:11
     * @param orgName
     * @return
     */
    public static boolean isOrgName(String orgName) {
        return Pattern.matches(REGEX_ORGNAME, orgName);
    }

    /**
     * Description: 校验机构代码 <BR>
     *
     * <AUTHOR>
     * @date 2016年6月26日 下午4:25:25
     * @param orgCode
     * @return
     */
    public static boolean isOrgCode(String orgCode) {
        return Pattern.matches(REGEX_ORGCODE, orgCode);
    }

    /**
     * 校验手机号
     *
     * @param mobile
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isMobile(String mobile) {
        return Pattern.matches(REGEX_MOBILE, mobile);
    }

    /**
     * 校验邮箱
     *
     * @param email
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isEmail(String email) {
        return Pattern.matches(REGEX_EMAIL, email);
    }

    /**
     * 校验汉字
     *
     * @param chinese
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isChinese(String chinese) {
        return Pattern.matches(REGEX_CHINESE, chinese);
    }

    /**
     * 校验身份证
     *
     * @param idCard
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isIDCard(String idCard) {
        return Pattern.matches(REGEX_ID_CARD, idCard);
    }

    /**
     * 校验URL
     *
     * @param url
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isUrl(String url) {
        if(url.startsWith("http")){
            return true;
        }
        return false;
    }

    /**
     * 校验IP地址
     *
     * @param ipAddr
     * @return
     */
    public static boolean isIPAddr(String ipAddr) {
        return Pattern.matches(REGEX_IP_ADDR, ipAddr);
    }

    /**
     * 校验电话号码
     *
     * @param tel
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isTel(String tel) {
        return Pattern.matches(REGEX_TEL, tel);
    }

    /**
     * 校验QQ
     *
     * @param qq
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isQQ(String qq) {
        return Pattern.matches(REGEX_QQ, qq);
    }

    /**
     * 校验postCode(邮编)
     *
     * @param postCode
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isPostCode(String postCode) {
        return Pattern.matches(REGEX_POSTCODE, postCode);
    }

    /**
     * 校验weChat(微信)
     *
     * @param weChat
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isWeChat(String weChat) {
        return Pattern.matches(REGEX_WECHAt, weChat);
    }

    private static final String REGEX_USER_AGENT_PHONE = "\\b(ip(hone|od)|android|opera m(ob|in)i|windows (phone|ce)|blackberry|s(ymbian|eries60|amsung)|p(laybook|alm|rofile/midp|laystation portable)|nokia|fennec|htc[-_]|up.browser|[1-4][0-9]{2}x[1-4][0-9]{2})\\b";// mobile|

    private static final String REGEX_USER_AGENT_TABLE = "\\b(ipad|tablet|(Nexus 7)|up.browser|[1-4][0-9]{2}x[1-4][0-9]{2})\\b";

    private static Pattern phonePat = Pattern.compile(REGEX_USER_AGENT_PHONE,
            Pattern.CASE_INSENSITIVE);

    private static Pattern tablePat = Pattern.compile(REGEX_USER_AGENT_TABLE,
            Pattern.CASE_INSENSITIVE);

    /**
     * Description: 检测是否是移动设备-手机访问 <BR>
     *
     * <AUTHOR>
     * @date 2016年6月3日 下午4:45:46
     * @param userAgent
     * @return
     */
    public static boolean isMobileDevices(String _sUserAgent) {
        return phonePat.matcher(_sUserAgent).find();
    }

    /**
     * Description: 检测是否是移动设备-平板访问 <BR>
     *
     * <AUTHOR>
     * @date 2016年6月3日 下午4:45:53
     * @return
     */
    public static boolean isTableDevices(String _sUserAgent) {
        return tablePat.matcher(_sUserAgent).find();
    }

}
