package com.trs.gov.core.util;

import java.util.ArrayList;

public class ContextTL extends ThreadLocal {

    // 最多只存10个变量
    public final static int MAX_THREADLOCAL_LENGTH = 30;

    public ContextTL() {
        super();
    }

    /**
     * 初始化参数对象体（参数对象的结构） 处理逻辑： <BR>
     * 1. 建立两个ArrayList 2. 一个存放系统专用的参数列表，一个提供给用户进行参数扩展
     *
     * @return 包含两个ArrayList的ArrayList对象
     */
    @Override
    protected Object initialValue() {
        return new Object[MAX_THREADLOCAL_LENGTH];
    }

    /**
     * 获得用户自定义的扩展参数列表 处理逻辑： <BR>
     * 1. 第2个ArrayList是用户自定义的扩展参数List
     *
     * @return 如果尚未初始化，则返回NUL；否则返回ArrayList
     */
    public ArrayList getArgs() {

        return (ArrayList) ((ArrayList) (super.get())).get(1);
    }

    /**
     * 设置用户自定义的扩展参数列表 处理逻辑： <BR>
     * 1. 用户构造一个ArrayList，并设置好参数，然后调用该接口设置参数 2. 第2个ArrayList是用户自定义的扩展参数List
     *
     * @param _currArgs
     */
    public void setArgs(ArrayList _currArgs) {
        getArgs().clear();
        getArgs().addAll(_currArgs);
    }

    /**
     * 获取扩展参数列表中第_nIndex个位置上的参数
     *
     * @param _nIndex ：参数位置，从0开始
     * @return 如果没有初始化，则返回NULL；否则返回_nIndex位置上的对象
     */
    public Object getArg(int _nIndex) {
        if (_nIndex < MAX_THREADLOCAL_LENGTH) {
            return ((Object[]) super.get())[_nIndex];
        }
        return null;
    }

    /**
     * 在扩展参数列表的第_nIndex个位置设置参数 处理逻辑： <BR>
     *
     * @param _nIndex  ：扩展参数列表中的位置，为-1表示按顺序增加变量
     * @param _currObj ：设置的参数
     * @return：设置后的位置，返回-1表示设置失败、尚未初始化
     */
    public int setArg(int _nIndex, Object _currObj) {
        if (_nIndex < 0 || _nIndex > MAX_THREADLOCAL_LENGTH) {
            return -1;
        }
        ((Object[]) super.get())[_nIndex] = _currObj;
        return _nIndex;
    }

    public void clear() {
        Object[] args = ((Object[]) super.get());
        for (int i = 0; i < args.length; i++) {
            Object object = args[i];
            if (object != null) {
                args[i] = null;
            }
        }
    }
}
