package com.trs.gov.core.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * Jackson的转换器
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-14 10:06
 * @version 1.0
 * @since 1.0
 */
@Configuration
@Slf4j
public class JacksonConvert extends WebMvcConfigurationSupport {

    @Override
    protected void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(new StringHttpMessageConverter());
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setObjectMapper(getMapper());
        converters.add(converter);
        super.configureMessageConverters(converters);
    }

    public ObjectMapper getMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        SerializerProvider provider = objectMapper.getSerializerProvider();
        provider.setNullValueSerializer(new JsonSerializer<Object>() {
            @Override
            public void serialize(Object s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
                Try.of(() -> {
                    String fieldName = jsonGenerator.getOutputContext().getCurrentName();
                    Class c = BeanUtils.getPropertyDescriptor(jsonGenerator.getCurrentValue().getClass(), fieldName).getPropertyType();
                    if (Objects.equals(c, Map.class)) {
                        jsonGenerator.writeString("{}");
                    } else if (Objects.equals(c, List.class)) {
                        jsonGenerator.writeString("[]");
                    } else if (Objects.equals(c, Boolean.class)) {
                        jsonGenerator.writeString("false");
                    } else if (Objects.equals(c, String.class)) {
                        jsonGenerator.writeString("");
                    } else {
                        jsonGenerator.writeString((String) null);
                    }
                    return 0;
                }).onFailure(err -> log.error("JSON转换失败！", err));
            }
        });
        return objectMapper;
    }
}
