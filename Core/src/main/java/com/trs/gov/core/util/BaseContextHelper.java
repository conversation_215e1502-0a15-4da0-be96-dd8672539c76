package com.trs.gov.core.util;

public class BaseContextHelper {
    private static ContextTL s_oContextTL = null;

    private static int m_nNextIndex = 0;

    /**
     * 获得扩展参数列表中第_nIndex位置上的参数 处理策略： <BR>
     * 1. 从ArrayList中定位到第_nIndex个位置 2. 取出来的对象使用前要做类型强制转换
     *
     * @param _nIndex ：扩展参数列表中参数的存放位置
     * @return 如果指定位置上有对象，则返回该对象；如果没有则返回NULL（异常？）
     */
    public static Object getArg(int _nIndex) {
        return getContextTL().getArg(_nIndex);
    }

    /**
     * 在扩展参数列表的第_nIndex个位置设置参数对象_currObj 处理策略： <BR>
     * 1. 如果该位置上已经存在参数，则替换原有参数
     *
     * @param _nIndex  ：扩展参数列表中的位置
     * @param _currObj ：设置的参数
     * @return：设置后的位置，返回-1表示设置失败
     */
    public static int setArg(int _nIndex, Object _currObj) {
        return getContextTL().setArg(_nIndex, _currObj);
    }

    public synchronized static int getNextIndex() {
        if (m_nNextIndex >= (ContextTL.MAX_THREADLOCAL_LENGTH - 1)) {
            throw new RuntimeException("fatal of ThreadLocal:系统只可以容纳最多"
                    + ContextTL.MAX_THREADLOCAL_LENGTH + "个变量!");
        }

        getContextTL().setArg(++m_nNextIndex, null);
        return m_nNextIndex;
    }

    /**
     * @return
     */
    private static ContextTL getContextTL() {
        if (s_oContextTL == null)
            s_oContextTL = new ContextTL();
        return s_oContextTL;
    }

    public static void clear() {
        getContextTL().clear();
    }
}
