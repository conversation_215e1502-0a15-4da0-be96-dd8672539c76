package com.trs.gov.core.util;

import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.exception.ParamInvalidException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class GenericParamUtil {

    private GenericParamUtil() { // prevent instantiation
    }

    //private static String contextPath = (new ParamOptions()).contextPath;
    //private static String serverPath = (new ParamOptions()).serverPath;//@todo combine 2 line to a static block

    private static DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

    /**
     * 获取请求的参数
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回请求中的参数的去空值
     * <AUTHOR>
     */
    public static String getParameter(HttpServletRequest request, String param) {

        String ret = request.getParameter(param);
        if (ret == null) {
            ret = "";
        }
        return ret.trim();
    }

    /**
     * HTML元素value值过滤处理函数：将 <code> & &lt; &gt;\ </code> 等特殊字符作转化处理
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回过滤掉HTML格式之后的参数值
     * <AUTHOR>
     */
    public static String getParameterFilter(HttpServletRequest request, String param) {
        return CMyString.filterForHTMLValue(getParameter(request, param));
    }

    /**
     * 获取请求的参数，并判断是否为空
     *
     * @param request    HttpRequest对象
     * @param param      要获取请求参数的名称
     * @param checkEmpty 是否为空的标识符
     * @return 返回非空参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameter(HttpServletRequest request, String param, boolean checkEmpty)
            throws ParamInvalidException {

        String ret = request.getParameter(param);
        if (ret == null) {
            ret = "";
        }
        ret = ret.trim();
        if (checkEmpty && (ret.length() == 0)) {
            throw new ParamInvalidException("请求参数" + param + "=" + ret + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断参数是否为空，如果为空则返回一个默认值
     *
     * @param request      HttpRequest对象
     * @param param        要获取请求参数的名称
     * @param defaultValue 默认值
     * @return 返回非空参数值
     * <AUTHOR>
     */
    public static String getParameter(HttpServletRequest request, String param, String defaultValue) {

        String ret = request.getParameter(param);
        if (ret == null) {
            ret = "";
        }
        ret = ret.trim();
        if (ret.length() == 0) {
            ret = defaultValue.trim();
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空，并对其进行HTML格式转换
     *
     * @param request    HttpRequest对象
     * @param param      要获取请求参数的名称
     * @param checkEmpty 是否为空的标识符
     * @return 返回过滤掉HTML格式之后的非空参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterFilter(HttpServletRequest request, String param, boolean checkEmpty)
            throws ParamInvalidException {
        return CMyString.filterForHTMLValue(getParameter(request, param, checkEmpty));
    }
    /**
     * 获取请求的参数，并判断是否为空，并判断是否包含'<'或者'>'
     *
     * @param request
     *             HttpRequest对象
     * @param param
     *            要获取请求参数的名称
     * @param checkEmpty
     *             是否为空的标识符
     * @return 返回不为空并且不包含'<'或者'>'的参数的字符串格式
     * @throws ParamInvalidException
     *             数据参数出现异常信息
     * <AUTHOR>
     */
    /**
     * @todo review this method
     */
    public static String getParameterSafe(HttpServletRequest request, String param, boolean checkEmpty)
            throws ParamInvalidException {
        String ret = getParameter(request, param, checkEmpty);
        if ((ret.indexOf('<') != -1) ||
                (ret.indexOf('>') != -1)) {
            throw new ParamInvalidException("请求参数" + param + "=" + ret + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并将其转换为整型
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换之后的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static int getParameterInt(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, true);
        int ret;
        try {
            ret = Integer.parseInt(inputStr);
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断参数是否为空，如果为空则返回一个默认值，反之则返回其整型值
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @param defalt  是否为空的标识符
     * @return 返回默认值或者转换后的整型参数值
     * <AUTHOR>
     */
    public static int getMParameterInt(HttpServletRequest request, String param, int defalt) {

        String ret = request.getParameter(param);
        int result;
        if (ret == null) {
            ret = "";
        }
        ret = ret.trim();
        if (ret.length() == 0) {
            result = defalt;
        } else {
            result = Integer.parseInt(ret);
        }

        return result;

    }

    /**
     * 获取请求的参数，并判断参数是否为空，如果为空则返回一个默认值，反之则返回其整型值
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @param defalt  是否为空的标识符
     * @return 返回默认值或者转换后的整型参数值
     * <AUTHOR>
     */
    public static int getMParameterInt1(HttpServletRequest request, String param, Integer defalt) throws ParamInvalidException {

        String ret = request.getParameter(param);
        Integer result;
        if (ret == null) {
            ret = "";
        }
        ret = ret.trim();
        try {
            if (ret.length() == 0) {
                result = defalt == null ? 0 : defalt;
            } else {
                result = Integer.parseInt(ret);
            }
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "= " + ret + "不合法");
        }
        return result;

    }

    /**
     * 获取请求的参数，并将其转换成无符号整型，结果小于0则抛出异常
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换成无符号整型的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static int getParameterUnsignedInt(HttpServletRequest request, String param)
            throws ParamInvalidException {
        int retValue = getParameterInt(request, param);
        if (retValue < 0) {
            throw new ParamInvalidException("请求参数" + param + "=" + retValue + "不合法");
        }
        return retValue;
    }

    /**
     * 获取请求的参数，并且判断是否大于defaultValue,大于抛异常，小于0也抛异常
     *
     * @param request      HttpRequest对象
     * @param param        要获取请求参数的名称
     * @param defaultValue
     * @return 返回转换成整型后的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static int getParameterInt1(HttpServletRequest request, String param, int defaultValue) throws ParamInvalidException {
        String inputStr = getParameter(request, param, false);
        int ret;
        try {
            ret = Integer.parseInt(inputStr);
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("字符含有不可解析的整数");
        }
        //参数大于该值抛异常
        if (ret > defaultValue || ret < 0) {
            throw new ParamInvalidException("请求参数" + param + "=" + ret + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空，为空则赋予其默认值，反之则将其转换成整型
     *
     * @param request      HttpRequest对象
     * @param param        要获取请求参数的名称
     * @param defaultValue 是否为空的标识符
     * @return 返回默认值或者转换成整型后的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static int getParameterInt(HttpServletRequest request, String param, int defaultValue)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, false);
        if (inputStr.length() == 0) {
            return defaultValue;
        }
        int ret;
        try {
            ret = Integer.parseInt(inputStr);
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "格式不正确");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并将其与defaultValue进行对比
     *
     * @param request      HttpRequest对象
     * @param param        要获取请求参数的名称
     * @param defaultValue 是否为空的标识符
     * @return 返回转换成整型的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static int getParameterUnsignedInt(HttpServletRequest request, String param, int defaultValue)
            throws ParamInvalidException {

        int retValue = getParameterInt1(request, param, defaultValue);
        if (retValue < 0) {
            throw new ParamInvalidException("请求参数" + param + "=" + retValue + "不合法");
        }
        return retValue;
    }

    /**
     * 获取请求的参数，并将其转换为长整型
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换为长整型格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static long getParameterLong(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, true);
        long ret;
        try {
            ret = Long.parseLong(inputStr);
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "含有不可解析的整数");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空，如果为空则赋予其默认的长整型值，反之则将其转为长整型
     *
     * @param request      HttpRequest对象
     * @param param        要获取请求参数的名称
     * @param defaultValue 默认的长整型值
     * @return 返回默认或者转换为长整型的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static long getParameterLong(HttpServletRequest request, String param, long defaultValue)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, false);
        if (inputStr.length() == 0) {
            return defaultValue;
        }

        long ret;
        try {
            ret = Long.parseLong(inputStr);
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "含有不可解析的长整形数");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空，如果为空则赋予其默认的长整型值，反之则将其转为长整型
     *
     * @param request    HttpRequest对象
     * @param param      要获取请求参数的名称
     * @param checkEmpty 检测是否为空
     * @return 返回默认或者转换为长整型的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static long getParameterLong(HttpServletRequest request, String param, boolean checkEmpty)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, checkEmpty);
        if (checkEmpty && inputStr.length() == 0) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "传入为空");
        } else if (inputStr.length() > 0) {
            long ret;
            try {
                ret = Long.parseLong(inputStr);
            } catch (NumberFormatException e) {
                throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "含有不可解析的长整形数");
            }
            return ret;
        }
        return 0L;
    }

    /**
     * 获取请求的参数，并判断是否为空，如果为空则赋予其默认的字符串值，反之则返回原有的参数
     *
     * @param request      HttpRequest对象
     * @param param        要获取请求参数的名称
     * @param defaultValue 默认的长整型值
     * @return 返回默认或者原有的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterString(HttpServletRequest request, String param, String defaultValue) throws ParamInvalidException {
        String inputStr = getParameter(request, param, false);
        if (inputStr.length() == 0) {
            return defaultValue;
        }
        String ret;
        try {
            ret = inputStr;
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "含有不可解析的字符串数");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回是否为空的布尔值
     * <AUTHOR>
     */
    public static boolean getParameterBoolean(HttpServletRequest request, String param) {

        String inputStr = getParameter(request, param);
        if (inputStr.length() == 0) {
            return false;
        }
        return true;
    }

    /**
     * 获取请求的参数，并判断是否为空，并对其进行字节格式转换
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换成字节格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static byte getParameterByte(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, true);
        byte ret;
        try {
            ret = Byte.parseByte(inputStr);
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空，并对其进行浮点数格式转换
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换成浮点数格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static double getParameterDouble(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, true);
        double ret;
        try {
            ret = Double.parseDouble(inputStr);
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空，并对其进行浮点数格式转换
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换成浮点数格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static double getParameterDouble(HttpServletRequest request, String param, String defaultValue)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, true);
        if (CMyString.isEmpty(inputStr)) {
            inputStr = defaultValue;
        }
        double ret;
        try {
            ret = Double.parseDouble(inputStr);
        } catch (NumberFormatException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并对Url地址进行判断是否以"http://","https://","ftp://"开始
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回过滤掉HTML格式之后的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterUrl(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String ret = getParameter(request, param);
        if (ret.length() > 0) {
            if (!ret.startsWith("http://") &&
                    !ret.startsWith("https://") &&
                    !ret.startsWith("rtmp://") &&
                    !ret.startsWith("rtsp://") &&
                    !ret.startsWith("ftp://")) {
                throw new ParamInvalidException("请求参数" + param + "=" + ret + "不合法");
            }
        }
        return CMyString.filterForHTMLValue(ret);
    }

    /**
     * 获取请求的参数，并对Url地址进行判断是否以"http://","https://","ftp://"开始
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回过滤掉HTML格式之后的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterUrl(HttpServletRequest request, String param, boolean checkEmpty)
            throws ParamInvalidException {

        String ret = getParameter(request, param,checkEmpty);
        if (ret.length() > 0) {
            if (!ret.startsWith("http://") &&
                    !ret.startsWith("https://") &&
                    !ret.startsWith("rtmp://") &&
                    !ret.startsWith("rtsp://") &&
                    !ret.startsWith("ftp://")) {
                throw new ParamInvalidException("请求参数" + param + "=" + ret + "不合法");
            }
        }
        return CMyString.filterForHTMLValue(ret);
    }

    /**
     * 获取请求的参数，如果长度大于0，则进行Url格式转换
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回过滤掉HTML格式之后转换成Url格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterURI(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String ret = getParameter(request, param);
        if (ret.length() > 0) {
            try {
                new URL(ret);
            } catch (MalformedURLException e) {
                throw new ParamInvalidException("请求参数" + param + "=" + ret + "不合法");
            }
        }
        return CMyString.filterForHTMLValue(ret);
    }

    /**
     * 获取请求的参数，并判断参数去空之后的长度是否大于最小长度，如果小于则抛异常，否则返回参数
     *
     * @param request   HttpRequest对象
     * @param param     要获取请求参数的名称
     * @param minLength 最小长度
     * @param option    保留选项
     * @return 返回大于最小长度的去空参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterPassword(HttpServletRequest request, String param, int minLength, int option)
            throws ParamInvalidException {

        if (minLength < 1) {
            minLength = 1;
        }

        String ret = request.getParameter(param);
        if (ret == null) {
            ret = "";
        }
        ret = ret.trim();

        if (ret.length() < minLength) {
            throw new ParamInvalidException("请求参数" + param + "=" + ret + "不合法");
        }

        /** @todo implement this feature */
        //if (option == 1) {//char and number

        //} else if (option == 2) {// lower char, upper char and number

        //}
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否含有'<','>'的非法字符串，之后对其进行Email格式判断，不是Email格式抛异常
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回Email格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterEmail(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String email = getParameterSafe(request, param, true);
        //MailUtil.checkGoodEmail(email);
        if (Validator.isEmail(email)) {
            return email;
        } else {
            throw new ParamInvalidException("Email格式错误：" + param + "=" + email);
        }
    }

    /**
     * 获取请求的参数，并判断是否含有'<','>'的非法字符串，之后对其进行邮编格式判断，不是邮编格式抛异常
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回Email格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterZipCode(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String zipCode = getParameterSafe(request, param, true);
        //MailUtil.checkGoodEmail(email);
        if (Validator.isPostCode(zipCode)) {
            return zipCode;
        } else {
            throw new ParamInvalidException("邮编格式错误：" + param + "=" + zipCode);
        }
    }

    /**
     * 获取请求的参数，并判断是否含有'<','>'的非法字符串，之后对其进行Email格式判断，不是Email格式抛异常
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回Email格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static String getParameterPhone(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String phone = getParameterSafe(request, param, true);
        //MailUtil.checkGoodEmail(email);
        if (Validator.isMobile(phone)) {
            return phone;
        } else {
            throw new ParamInvalidException("手机号码格式错误：\" + param + \"=\" + ret + \"");
        }
    }

    /**
     * 获取请求的参数，并判断是否为空，并对其进行java.sql.Date格式转换
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换成java.sql.Date格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static java.sql.Date getParameterDate(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, true);
        java.util.Date ret;
        try {
            ret = dateFormat.parse(inputStr);
        } catch (java.text.ParseException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "不合法");
        }
        return new java.sql.Date(ret.getTime());
    }

    /**
     * description: 如果没有传入日期返回null 否则返回格式化后的日期
     *
     * @param request    HttpRequest对象
     * @param param      要获取请求参数的名称
     * @param format     日期格式
     * @param checkEmpty 是否有权必传
     * @return 返回转换成java.util.Date格式的参数值
     * @author: yuan.kui
     * @time: 2017/5/4
     * @version 1.0
     */
    public static Date getParameterDateUtil(HttpServletRequest request, String param, String format, boolean checkEmpty)
            throws ParamInvalidException {
        if (checkEmpty == true) {
            return getParameterDateUtil(request, param, format);
        } else {
            String inputStr = getParameter(request, param);
            if (CMyString.isEmpty(inputStr)) {
                return null;
            }
            return TimeUtils.stringToDate(inputStr, format);
        }
    }

    /**
     * 获取请求的参数，并判断是否为空，并对其进行java.util.Date格式转换
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换成java.util.Date格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static Date getParameterDateUtil(HttpServletRequest request, String param)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, true);
        java.util.Date ret;
        try {
            ret = dateFormat.parse(inputStr);
        } catch (java.text.ParseException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空，若空则设置默认值
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换成java.util.Date格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static java.util.Date getParameterDate(HttpServletRequest request, String param, java.util.Date defaultValue)
            throws ParamInvalidException {

        String inputStr = getParameter(request, param, false);
        if (inputStr.length() == 0) {
            return defaultValue;
        }
        java.util.Date ret;
        try {
            ret = dateFormat.parse(inputStr);
        } catch (java.text.ParseException e) {
            throw new ParamInvalidException("请求参数" + param + "=" + inputStr + "不合法");
        }
        return ret;
    }

    /**
     * 获取请求的参数，并判断是否为空，并对其进行java.util.Date格式转换
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @param format  要获取请求参数的时间格式,格式如下：</BR>
     *                yyyy 表示年</BR>
     *                MM 表示月</BR>
     *                dd 表示日</BR>
     *                HH 表示24小时制的时间</BR>
     *                hh 表示12小时制的时间格式</BR>
     *                mm 表示分钟</BR>
     *                ss 表示秒</BR>
     *                SSS 表示毫秒</BR>
     *                例如：2017-02-21 15:23:23 要求是：yyyy-MM-dd HH:mm:ss
     * @return 返回转换成java.util.Date格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static java.util.Date getParameterDateUtil(HttpServletRequest request, String param, String format)
            throws ParamInvalidException {
        String inputStr = getParameter(request, param, true);
        return TimeUtils.stringToDate(inputStr, format);
    }

    /**
     * 获取请求的参数，并判断是否为空，并对其进行java.util.Date格式转换
     *
     * @param request         HttpRequest对象
     * @param param           要获取请求参数的名称
     * @param format
     * @param default_dateStr 默认的日期字符串
     * @return ret
     * 返回转换成java.util.Date格式的参数值
     * @author: yuan.kui
     * @time: 2017/4/5
     * @version 1.0
     */
    public static Date getParameterDateUtil(HttpServletRequest request, String param, String format, String default_dateStr)
            throws ParamInvalidException {
        String dateStr = request.getParameter(param);
        if (CMyString.isEmpty(dateStr)) {
            dateStr = default_dateStr;
        }
        return TimeUtils.stringToDate(dateStr, format);
    }


    /**
     * 获取请求的以年月日为单位的参数，并将其转换为java.sql.Date格式
     *
     * @param request    HttpRequest对象
     * @param paramDay   以日为单位的参数值
     * @param paramMonth 以月为单位的参数值
     * @param paramYear  以年为单位的参数值
     * @return 返回以java.sql.Date为格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static java.sql.Date getParameterDate(HttpServletRequest request, String paramDay, String paramMonth, String paramYear)
            throws ParamInvalidException {

        int day = getParameterInt(request, paramDay);
        int month = getParameterInt(request, paramMonth);
        int year = getParameterInt(request, paramYear);
        StringBuffer buffer = new StringBuffer();
        buffer.append(day).append("/").append(month).append("/").append(year);
        String inputStr = buffer.toString();

        java.util.Date ret;
        try {
            ret = dateFormat.parse(inputStr);
        } catch (java.text.ParseException e) {
            throw new ParamInvalidException("请求参数" + inputStr + "不合法");
        }
        return new java.sql.Date(ret.getTime());
    }

    /**
     * 获取请求的以年月日为单位的参数，并将其转换为java.sql.Date格式，如果格式有问题则抛异常
     *
     * @param request    HttpRequest对象
     * @param paramDay   以日为单位的参数值
     * @param paramMonth 以月为单位的参数值
     * @param paramYear  以年为单位的参数值
     * @return 返回以java.sql.Date为格式的参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static java.sql.Date getParameterDateSafe(HttpServletRequest request, String paramDay, String paramMonth, String paramYear)
            throws ParamInvalidException {

        int day = 0;
        int month = 0;
        int year = 0;
        try {
            day = getParameterInt(request, paramDay);
            month = getParameterInt(request, paramMonth);
            year = getParameterInt(request, paramYear);
        } catch (ParamInvalidException e) {
            //do nothing
        }
        StringBuffer buffer = new StringBuffer();
        buffer.append(day).append("/").append(month).append("/").append(year);
        String inputStr = buffer.toString();

        java.util.Date ret;
        try {
            ret = dateFormat.parse(inputStr);
        } catch (java.text.ParseException e) {
            throw new ParamInvalidException("请求参数" + inputStr + "不合法");
        }
        return new java.sql.Date(ret.getTime());
    }

    /**
     * 获取请求的参数，并对其进行双精度浮点数转换，当小于-12或者大于13时则重新赋值0
     *
     * @param request HttpRequest对象
     * @param param   要获取请求参数的名称
     * @return 返回转换后符合时间条件的双精度浮点格式参数值
     * @throws ParamInvalidException 数据参数出现异常信息
     * <AUTHOR>
     */
    public static double getParameterTimeZone(HttpServletRequest request, String param)
            throws ParamInvalidException {

        double timeZone = getParameterDouble(request, param);
        if (timeZone < -12 || timeZone > 13) {
            timeZone = 0;
        }
        return timeZone;
    }

    /**
     * 获取会话的参数，并得到其对应的属性值
     *
     * @param session HttpSession对象
     * @param name    要获取会话的参数名称
     * @return 返回对应的去空的参数值
     * <AUTHOR>
     */
    public static String getAttribute(HttpSession session, String name) {

        String ret = (String) session.getAttribute(name);
        if (ret == null) {
            ret = "";
        }
        return ret.trim();
    }

    /**
     * 获取请求属性名称，并得到其值
     *
     * @param request HttpRequest对象
     * @param name    要获取请求的参数名称
     * @return 返回对应的去空的参数值
     * <AUTHOR>
     */
    public static String getAttribute(HttpServletRequest request, String name) {

        String ret = (String) request.getAttribute(name);
        if (ret == null) {
            ret = "";
        }
        return ret.trim();
    }

}
