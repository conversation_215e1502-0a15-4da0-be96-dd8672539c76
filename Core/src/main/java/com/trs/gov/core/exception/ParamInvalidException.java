package com.trs.gov.core.exception;

import com.trs.common.exception.ExceptionNumber;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-09 13:43
 * @version 1.0
 * @since 1.0
 * 参数异常
 */
public class ParamInvalidException extends ServiceException {

    public ParamInvalidException(String msg) {
        super(ExceptionNumber.ERR_PARAM_INVALID, msg);
    }

    public ParamInvalidException() {
        super(ExceptionNumber.ERR_PARAM_INVALID, "参数异常");
    }

    public ParamInvalidException(Throwable ex) {
        super(ExceptionNumber.ERR_PARAM_INVALID, ex);
    }

    public ParamInvalidException(String msg, Throwable ex) {
        super(ExceptionNumber.ERR_PARAM_INVALID, msg, ex);
    }

}
