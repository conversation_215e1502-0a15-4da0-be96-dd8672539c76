package com.trs.gov.core.constant;

/**
 * @ClassName：WorkOrderTypeConstant
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/29 17:23
 **/
public class WorkOrderTypeConstant {
    /**
     * 顶级工单key
     **/
    public static final String ROOT_GONG_DAN_KEY = "ROOT_GONG_DAN_KEY";
    public static final String ROOT_GONG_DAN_KEY_DESC = "工单";
    /**
     * 顶级投诉key
     **/
    public static final String ROOT_TOU_SU_KEY = "ROOT_TOU_SU_KEY";
    public static final String ROOT_TOU_SU_KEY_DESC = "投诉";
    /**
     * 顶级通知key
     **/
    public static final String ROOT_TONG_ZHI_KEY = "ROOT_TONG_ZHI_KEY";
    public static final String ROOT_TONG_ZHI_KEY_DESC = "通知";

    /**
     * 工单顶级类型-工单
     */
    public static final Long TOP_TYPE_WORK_ORDER = 1L;

    /**
     * 工单顶级类型-投诉
     */
    public static final Long TOP_TYPE_COMPLAINT = 2L;

    /**
     * 工单顶级类型-通知
     */
    public static final Long TOP_TYPE_NOTICE = 3L;

}
