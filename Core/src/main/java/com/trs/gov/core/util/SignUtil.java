package com.trs.gov.core.util;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Title: 签名工具类 <BR>
 * Description: <BR>
 * Copyright: Copyright (c) 2004-2021 北京拓尔思信息技术股份有限公司 <BR>
 * Company: www.trs.com.cn <BR>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class SignUtil {

    /**
      * Description: 签名校验方法<BR>
      * @param appId 应用ID
      * @param key 应用key
      * @param timestamp 请求传入的时间戳
      * @param sign 请求传入的签名
      * @return boolean 签名是否合法
      * <AUTHOR>
      * @date 10:05 2022/12/14
      */
    public static boolean check(String appId, String key, String timestamp, String sign){
        if(log.isDebugEnabled()){
            log.debug(String.format("appId%stimestamp%s%s", appId, timestamp, key));
        }
        //构造加密参数
        String sParam = String.format("appId%stimestamp%s%s", appId, timestamp, key);
        //加密
        return sign.equals(getSHA256(sParam.toLowerCase()));
    }


    /**
     * Description: 获取MD5加密后的字符串 <BR>
     *
     * <AUTHOR>
     * @date 2016年8月9日 下午8:52:09
     * @param _source
     * @return
     */
    public static String getMD5Hex(String _source) {
        try {

            return DigestUtils.md5Hex(_source);

        } catch (Exception e) {
            e.printStackTrace();
            log.info("获取【" + _source + "】的MD5加密后的串失败！");
            return "";
        }
    }

    /**
     * 利用java原生的类实现SHA256加密
     *
     * @param str 参数拼接的字符串
     * @return
     */
    public static String getSHA256(String str) {
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes("UTF-8"));
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return encodeStr;
    }

    /**
     * 将byte转为16进制
     *
     * @param bytes
     * @return
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        String temp = null;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                // 1得到一位的进行补0操作
                sb.append("0");
            }
            sb.append(temp);
        }
        return sb.toString();
    }
}
