package com.trs.gov.core.util;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.BaseRowModel;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.trs.gov.core.exception.ServiceException;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/10/23 17:29
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public class ExcelUtil {

    /**
     * 借助easyExcel导出数据工具
     *
     * @param response
     * @param models
     * @param fileName
     * @param sheetName
     * @throws Exception
     */
    public static void exportExcel(HttpServletResponse response, List<? extends BaseRowModel> models, String fileName, String sheetName) throws ServiceException {
        Try.of(() -> {
            ServletOutputStream out = response.getOutputStream();
            ExcelWriter writer = new ExcelWriter(out, ExcelTypeEnum.XLSX, true);
            Sheet sheet1 = new Sheet(1, 0, models.get(0).getClass());
            sheet1.setAutoWidth(true);
            sheet1.setSheetName(sheetName);
            response.setContentType("multipart/form-data");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8'zh_cn'" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));
            writer.write(models, sheet1);
            writer.finish();
            out.flush();
            return 0;
        }).onFailure(err -> log.error("导出{}失败！", fileName, err)).getOrElseThrow(err -> new ServiceException("导出" + fileName + "失败！", err));
    }
}
