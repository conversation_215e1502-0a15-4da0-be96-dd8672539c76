package com.trs.gov.core.DTO;

import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 附件的DTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-10 15:18
 * @version 1.0
 * @since 1.0
 */
@Data
public class AppendixDTO extends BaseDTO {
    /**
     * 附件url
     */
    private String url;
    /**
     * 附件名
     */
    private String filename;
    /**
     * 附件描述
     */
    private String filedesc;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
