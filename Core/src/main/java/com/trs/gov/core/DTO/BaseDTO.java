package com.trs.gov.core.DTO;

import com.trs.gov.core.constant.CoreConstant;
import com.trs.gov.core.exception.ServiceException;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 基础的DTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-10 9:54
 * @version 1.0
 * @since 1.0
 */
public abstract class BaseDTO implements Serializable {

    public static final long serialVersionUID = CoreConstant.serialVersionUID;

    @Getter
    @Setter
    private String token;

    @Getter
    @Setter
    private String unitId;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    public abstract boolean isValid() throws ServiceException;
}
