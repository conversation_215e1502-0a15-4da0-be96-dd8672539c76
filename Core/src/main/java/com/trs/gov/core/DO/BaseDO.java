package com.trs.gov.core.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.trs.gov.core.constant.CoreConstant;
import com.trs.gov.core.util.ContextHelper;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-09 13:28
 * @version 1.0
 * @since 1.0
 * 基础的Entity
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@MappedSuperclass
public abstract class BaseDO implements Serializable {

    @ApiModelProperty(value = "所有实体对象的ID", required = true)
    @TableId(type = IdType.AUTO)
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "所有实体对象的创建时间", required = true)
    @Column(name = "cr_time")
    @TableField("cr_time")
    private Date crTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "所有实体对象的创建人", required = true)
    @Column(name = "cr_user")
    @TableField("cr_user")
    private String crUser;

    /**
     * 是否是从其他地方迁移过来的数据
     * */
    @ApiModelProperty(value = "是否是从其他地方迁移过来的数据")
    @Column(name = "export_from_other")
    @TableField("export_from_other")
    private Integer exportFromOther;

    /**
     * 状态
     */
    @ApiModelProperty(value = "所有实体对象的状态", required = true)
    @Column(name = "status")
    @TableField("status")
    private Integer status;

    public Date getCrTime() {
        if (crTime == null) {
            crTime = new Date();
        }
        return crTime;
    }

    public void setCrTime(Date crTime) {
        this.crTime = crTime;
    }

    public String getCrUser() {
        if (crUser == null) {
            crUser = ContextHelper.getLoginUser().orElse(CoreConstant.SYSTEM_USER_NAME);
        }
        return crUser;
    }

    public void setCrUser(String crUser) {
        this.crUser = crUser;
    }

}
