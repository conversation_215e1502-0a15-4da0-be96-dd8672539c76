package com.trs.gov.core.util;

import com.trs.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Slf4j
public class ContextHelper {
    /**
     * 用户
     */
    private static final int ARG_INDEX_USER = BaseContextHelper.getNextIndex();
    /**
     * UNITID
     */
    private static final int ARG_INDEX_UNITID = BaseContextHelper.getNextIndex();

    /**
     * TOKEN
     */
    private static final int ARG_INDEX_TOKEN = BaseContextHelper.getNextIndex();


    public static Optional<String> getLoginUser() {
        return Optional.ofNullable((String) BaseContextHelper.getArg(ARG_INDEX_USER));
    }

    public static Optional<String> getLoginUnitId() {
        return Optional.ofNullable((String) BaseContextHelper.getArg(ARG_INDEX_UNITID));
    }

    public static Optional<String> getToken() {
        return Optional.ofNullable((String) BaseContextHelper.getArg(ARG_INDEX_TOKEN));
    }


    public static void setLoginUser(String _currUser) {
        if ("system".equalsIgnoreCase(_currUser)) {
            log.warn("谁使用System初始化ContextHelper?", new Exception());
        }
        BaseContextHelper.setArg(ARG_INDEX_USER, _currUser);
    }

    public static void setLoginUnitId(String unitId) {
        if (!StringUtils.isNullOrEmpty(unitId)) {
            BaseContextHelper.setArg(ARG_INDEX_UNITID, unitId);
        }
    }

    public static void setToken(String token) {
        if (!StringUtils.isNullOrEmpty(token)) {
            BaseContextHelper.setArg(ARG_INDEX_TOKEN, token);
        }
    }


    public static void initContext(String token, String _loginUser, String unitId) {
        setToken(token);
        setLoginUser(_loginUser);
        setLoginUnitId(unitId);
    }

    public static void initContext(String token, String _loginUser) {
        setLoginUser(_loginUser);
        setToken(token);
        setLoginUnitId("0");
    }

}
