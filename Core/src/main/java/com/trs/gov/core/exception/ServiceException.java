package com.trs.gov.core.exception;

import com.trs.common.exception.ExceptionNumber;
import lombok.Getter;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-09 13:43
 * @version 1.0
 * @since 1.0
 * <br/>
 * 服务异常
 * 定义的异常只所以继承Exception而不继承RuntimeException
 * 是因为RuntimeException可以直接抛出到控制台中
 * 意味着开发人员可以不用显示的去对异常进行处理
 * 在开发人员逻辑不完善且程序没有控制台时
 * 就会造成部分异常被吞噬的情况，从而造成不可挽回的错误
 */
public class ServiceException extends Exception {

    @Getter
    private int code;

    public ServiceException() {
        this(ExceptionNumber.ERR_UNKNOWN, "未知异常");
    }

    public ServiceException(String msg) {
        this(ExceptionNumber.ERR_UNKNOWN, msg);
    }

    public ServiceException(Throwable ex) {
        this(ExceptionNumber.ERR_UNKNOWN, ex);
    }

    public ServiceException(String msg, Throwable ex) {
        this(ExceptionNumber.ERR_UNKNOWN, msg, ex);
    }

    public ServiceException(int code, String msg) {
        super(msg);
        this.code = code;
    }

    public ServiceException(int code, Throwable ex) {
        super(ex);
        this.code = code;
    }

    public ServiceException(int code, String msg, Throwable ex) {
        super(msg, ex);
        this.code = code;
    }

}
