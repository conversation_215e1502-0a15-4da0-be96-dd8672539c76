package com.trs.gov.core.util;

import org.junit.Test;

public class BaseUtilsTest {

    class A {
        private String a;

        public String getA() {
            return a;
        }

        public void setA(String a) {
            this.a = a;
        }
    }

    class B {
        private String a;

        public String getA() {
            return a;
        }

        public void setA(String a) {
            this.a = a;
        }
    }

    @Test
    public void copyProperties() {
        A a = new A();
//        a.setA("123");
        B b = new B();
        b.setA("321");
        BaseUtils.copyProperties(a,b,false);
        System.out.println(b.getA());




    }
}