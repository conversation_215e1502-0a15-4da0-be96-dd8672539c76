/*
 * This file was generated by the Gradle 'init' task.
 */

rootProject.name = "workordersystem"
include(":Gateway")
include(":StatisticsCenter")
include(":ExternalSystem-Web")
include(":Search-Web")
include(":KnowledgeBase")
include(":SystemManagement-Service")
include(":Message")
include(":Core")
include(":Export")
include(":User-Service-Impl")
include(":Search")
include(":Interaction-Web")
include(":WorkOrder-Service")
include(":FileManager-Service")
include(":ExternalSystem-Service")
include(":ExternalSystem")
include(":Interaction")
include(":FileManager-Web")
include(":StatisticsCenter-Web")
include(":Message-Web")
include(":Message-Service")
include(":User-Service")
include(":SystemManagement-Web")
include(":Interaction-Service")
include(":Export-Web")
include(":FileManager")
include(":Configuration")
include(":WorkOrder-Web")
include(":WorkOrder")
include(":SystemManagement")
include(":User-Service-IDS")
include(":Search-Service")
include(":UserCenter")
include(":StatisticsCenter-Service")
project(":ExternalSystem-Web").projectDir = file("ExternalSystem/ExternalSystem-Web")
project(":Search-Web").projectDir = file("Search/Search-Web")
project(":SystemManagement-Service").projectDir = file("SystemManagement/SystemManagement-Service")
project(":User-Service-Impl").projectDir = file("UserCenter/User-Service-Impl")
project(":Interaction-Web").projectDir = file("Interaction/Interaction-Web")
project(":WorkOrder-Service").projectDir = file("WorkOrder/WorkOrder-Service")
project(":FileManager-Service").projectDir = file("FileManager/FileManager-Service")
project(":ExternalSystem-Service").projectDir = file("ExternalSystem/ExternalSystem-Service")
project(":FileManager-Web").projectDir = file("FileManager/FileManager-Web")
project(":StatisticsCenter-Web").projectDir = file("StatisticsCenter/StatisticsCenter-Web")
project(":Message-Web").projectDir = file("Message/Message-Web")
project(":Message-Service").projectDir = file("Message/Message-Service")
project(":User-Service").projectDir = file("UserCenter/User-Service")
project(":SystemManagement-Web").projectDir = file("SystemManagement/SystemManagement-Web")
project(":Interaction-Service").projectDir = file("Interaction/Interaction-Service")
project(":Export-Web").projectDir = file("Export/Export-Web")
project(":WorkOrder-Web").projectDir = file("WorkOrder/WorkOrder-Web")
project(":User-Service-IDS").projectDir = file("UserCenter/User-Service-IDS")
project(":Search-Service").projectDir = file("Search/Search-Service")
project(":StatisticsCenter-Service").projectDir = file("StatisticsCenter/StatisticsCenter-Service")
