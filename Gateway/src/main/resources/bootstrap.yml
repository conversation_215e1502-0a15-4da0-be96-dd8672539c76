spring:
  cloud:
    nacos:
      discovery:
        server-addr: @spring.cloud.nacos.discovery.server-addr@
        namespace: @spring.cloud.nacos.config.namespace@
        username: @spring.cloud.nacos.username@
        password: @spring.cloud.nacos.password@
      config:
        server-addr: @spring.cloud.nacos.config.server-addr@
        file-extension: @gateway.spring.cloud.nacos.config.file-extension@
        namespace: @spring.cloud.nacos.config.namespace@
        username: @spring.cloud.nacos.username@
        password: @spring.cloud.nacos.password@
        group: DEFAULT_GROUP
        ext-config[0]:
          data-id: @gateway.spring.cloud.nacos.config.ext-config[0].data-id@
          refresh: true
  datasource:
    url: @spring.datasource.url@
    username: @spring.datasource.username@
    password: @spring.datasource.password@
    hikari:
      connection-init-sql: SET NAMES utf8mb4
  main:
    allow-bean-definition-overriding: true
dubbo:
  protocol:
    name: dubbo
    port: @Gateway-Service-server.dubbo.port@
  registry:
    address: @dubbo.registry.address@
