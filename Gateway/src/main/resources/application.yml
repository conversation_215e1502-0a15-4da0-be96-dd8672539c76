# 网关配置
spring:
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
      routes:
        - id: User-Service-IDS
          uri: lb://User-Service-IDS
          predicates:
            - Path= /user/**
        - id: ExternalSystem-Service
          uri: lb://ExternalSystem-Service
          predicates:
            - Path= /externalSystem/**
        - id: FileManager-Service
          uri: lb://FileManager-Service
          predicates:
            - Path= /file/**
        - id: Interaction-Service
          uri: lb://Interaction-Service
          predicates:
            - Path= /interaction/**
        - id: Message_Service
          uri: lb://Message_Service
          predicates:
            - Path= /message/**
        - id: Search_Service
          uri: lb://Search_Service
          predicates:
            - Path= /search/**
        - id: SystemManagement-Service
          uri: lb://SystemManagement-Service
          predicates:
            - Path= /management/**
        - id: WorkOrder-Service
          uri: lb://WorkOrder-Service
          predicates:
            - Path= /workorder/**

