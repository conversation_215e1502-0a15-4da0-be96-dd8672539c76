package com.trs.gov.knowledge.service;

import com.trs.gov.knowledge.KnowledgeBaseApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = KnowledgeBaseApplication.class)
@Slf4j
public class KnowledgeServiceTest {

    @Autowired
    private KnowledgeService service;

    @Test
    public void queryBrowseRecord() {
        Map<String, Object> dataMap = new HashMap();
        dataMap.put("docId", "1");
        dataMap.put("pageSize", 20);
        dataMap.put("pageIndex", 1);
        System.out.println(service.queryBrowseRecord(dataMap));
    }
}