package com.trs.gov.knowledge.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

@Data
@TableName("doccomment")
public class DocComment implements Serializable {

    public static final String OBJ_TYPE = "DocComment";

    @TableId("id")
    private String id;

    @TableField(value = "docId")
    private String docId;

    @TableField(value = "commentType")
    private int commentType;

    @TableField(value = "reposeId")
    private String reposeId;

    @TableField(value = "userId")
    private String userId;

    @TableField(value = "commentUser")
    private String commentUser;

    @TableField(value = "reposeUser")
    private String reposeUser;

    @TableField(value = "commentContent")
    private String commentContent;

    @TableField(value = "commentDate")
    private String commentDate;

    /**
     * 附件ID，多个附件 英文逗号隔开
     */
    @TableField(value = "attachment")
    private String attachment;

    /**
     * 工单id
     */
    @TableField(value = "workerId")
    private String workerId;

}
