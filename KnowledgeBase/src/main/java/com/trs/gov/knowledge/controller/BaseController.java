package com.trs.gov.knowledge.controller;

import com.trs.common.context.CommonContext;
import com.trs.gov.knowledge.constant.CommentConstant;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;

public abstract class BaseController {

    public CommonContext initContextAsDefault(HttpServletRequest request) {
        return initContextAsDefault(request, null, null);
    }

    public CommonContext initContextAsDefault(HttpServletRequest request, HttpServletResponse httpServletResponse) {
        return initContextAsDefault(request, httpServletResponse, null);
    }

    public CommonContext initContextAsDefault(HttpServletRequest request, String userId) {
        return initContextAsDefault(request, null, userId);
    }

    public CommonContext initContextAsDefault(HttpServletRequest request, HttpServletResponse httpServletResponse, String userId) {
        //非空校验
        CommonContext context = new CommonContext();
        //封装上下文
        if (!context.containProperty(CommentConstant.HTTP_RESPONSE_CONTENT) && httpServletResponse != null) ;
        context.addProperty(CommentConstant.HTTP_RESPONSE_CONTENT, httpServletResponse);
        if (!context.containProperty(CommentConstant.HTTP_REQUEST_CONTENT) && request != null) ;
        context.addProperty(CommentConstant.HTTP_REQUEST_CONTENT, request);
        //将request中的参数封装进context中
        Enumeration paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = (String) paramNames.nextElement();
            String[] paramValues = request.getParameterValues(paramName);
            if (paramValues.length > 0) {
                //默认取最后传入的参数
                String paramValue = paramValues[paramValues.length - 1];
                context.addProperty(paramName, paramValue);
            }
        }

        return context;
    }
}
