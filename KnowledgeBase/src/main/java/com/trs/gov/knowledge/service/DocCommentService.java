package com.trs.gov.knowledge.service;

import com.alibaba.fastjson.JSONArray;
import com.trs.common.context.CommonContext;
import com.trs.gov.knowledge.constant.CommentConstant;
import com.trs.gov.knowledge.mgr.DocCommentMgr;
import com.trs.web.entity.PageContext;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DocCommentService extends BaseService {

    @Autowired
    private DocCommentMgr commentMgr;

    public String saveComment(CommonContext context) {
        // 1 校验参数
        String resultInfo;
        try {
            ValidateNotNull(context, CommentConstant.PARAMS_DOCID, CommentConstant.PARAMS_COMMENTCONTENT,
                    CommentConstant.PARAMS_COMMENTTYPE);
            if (context.getProperty(CommentConstant.PARAMS_COMMENTTYPE).equals(CommentConstant.PARAMS_TYPE_REPOSE)) {
                ValidateNotNull(context, CommentConstant.PARAMS_REPOSEID, CommentConstant.PARAMS_REPOSEUSER);
            }
            // 2 权限校验
            // 3 构造业务参数对象
            PageContext pageContext = new PageContext(context);
            // 4 调用业务方法
            resultInfo = commentMgr.saveComment(pageContext);
            resultInfo = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK)
                    .addData(resultInfo)
                    .addMsg("评论成功")
                    .toJson();
        } catch (Exception e) {
            log.error("评论异常！", e);
            resultInfo = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.SERVER_ERROR)
                    .addData("")
                    .addMsg("评论异常:" + e)
                    .toJson();
        }
        return resultInfo;
    }

    public String queryComment(CommonContext context) {
        // 1 校验参数
        String resultInfo;
        try {
            ValidateNotNull(context, CommentConstant.PARAMS_DOCID,
                    CommentConstant.PAGE_NUMBER_PROPERTY, CommentConstant.PAGE_SIZE_PROPERTY);

            // 2 权限校验
            // 3 构造业务参数
            PageContext pageContext = new PageContext(context);
            // 4 调用业务方法
            JSONArray array = commentMgr.queryCommentList(pageContext);
            resultInfo = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK)
                    .addData(array)
                    .addMsg("获取评论列表成功")
                    .toJson();
        } catch (Exception e) {
            log.error("获取评论列表异常！", e);
            // 5 异常处理
            resultInfo = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.SERVER_ERROR)
                    .addData("")
                    .addMsg("获取评论列表异常:" + e)
                    .toJson();
        }
        return resultInfo;
    }

    public String delComment(CommonContext context) {
        // 1 参数校验
        String resultInfo;
        try {
            ValidateNotNull(context, CommentConstant.PARAMS_COMMENTID);
            // 2 权限校验
            // 3 构造业务参数
            PageContext pageContext = new PageContext(context);
            // 4 调用业务方法
            resultInfo = commentMgr.deleteComment(pageContext);
            resultInfo = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK)
                    .addData(resultInfo)
                    .addMsg("删除评论成功")
                    .toJson();
        } catch (Exception e) {
            log.error("删除评论异常！", e);
            // 5 异常处理
            resultInfo = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.SERVER_ERROR)
                    .addData("")
                    .addMsg("删除评论异常:" + e)
                    .toJson();
        }
        return resultInfo;
    }
}
