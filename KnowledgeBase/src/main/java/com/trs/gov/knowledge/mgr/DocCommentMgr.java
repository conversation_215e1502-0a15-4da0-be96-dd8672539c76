package com.trs.gov.knowledge.mgr;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.core.util.UUIDUtils;
import com.trs.gov.file.DTO.FileDTO;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.file.util.FileUtils;
import com.trs.gov.knowledge.DO.DocComment;
import com.trs.gov.knowledge.constant.CommentConstant;
import com.trs.gov.knowledge.mapper.DocCommentMapper;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.entity.PageContext;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class DocCommentMgr {

    @Autowired
    private DocCommentMapper mapper;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileService;

    public String deleteComment(PageContext pageContext) throws ServiceException {
        // 获取登录对象
        Optional<String> userName = ContextHelper.getLoginUser();
        if (!userName.isPresent()) {
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK).addMsg("删除评论失败，用户未登录").toJson();
        }
        UserVO loginUser = userService.getBaseUserInfoByUserName(UserDTO.of(userName.get()));
        // 获取评论对象
        String commentId = pageContext.getProperty(CommentConstant.PARAMS_COMMENTID);
        DocComment docComment = mapper.selectById(commentId);
        if (docComment.getCommentUser().equals(loginUser.getUserName()) ==false){
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK).addMsg("删除评论失败，您不是本人").toJson();
        }
        try {
            FileUtils.deleteFile(fileService, DocComment.OBJ_TYPE, docComment.getId(), "attachment");
        } catch (ServiceException e) {
            log.warn("删除评论ID=[{}]附件出现异常！", commentId, e);
        }
        if (docComment != null) {
            mapper.deleteById(docComment.getId());
        }
        return "删除评论成功";
    }

    public String saveComment(PageContext pageContext) throws ServiceException {
        boolean isRepose = false;
        if (pageContext.getProperty(CommentConstant.PARAMS_COMMENTTYPE).equals(CommentConstant.PARAMS_TYPE_REPOSE)) {
            isRepose = true;
        }

        // 2 获取登录用户
        Optional<String> userName = ContextHelper.getLoginUser();
        if (!userName.isPresent()) {
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK).addMsg("添加评论失败，用户未登录").toJson();
        }
        UserVO loginUser = userService.getBaseUserInfoByUserName(UserDTO.of(userName.get()));
        // 3 封装实体
        String commentContent = pageContext.getProperty(CommentConstant.PARAMS_COMMENTCONTENT);
        DocComment docComment = new DocComment();
        docComment.setId(UUIDUtils.createUUID(false));
        docComment.setDocId(pageContext.getProperty(CommentConstant.PARAMS_DOCID));
        docComment.setUserId(loginUser.getUserName());
        docComment.setCommentUser(loginUser.getTrueName());
        docComment.setCommentContent(commentContent);
        docComment.setCommentType(Integer.valueOf(pageContext.getProperty(CommentConstant.PARAMS_COMMENTTYPE)));
        docComment.setCommentDate(TimeUtils.getCurrentDate());
        if (isRepose) {
            docComment.setReposeId(pageContext.getProperty(CommentConstant.PARAMS_REPOSEID));
            docComment.setReposeUser(pageContext.getProperty(CommentConstant.PARAMS_REPOSEUSER));
        }
        String files = pageContext.getProperty("file", "");
        docComment.setAttachment(files);
        // 保存数据
        if (mapper.insert(docComment) < 1) {
            throw new ServiceException("数据保存失败！");
        }
        try {
            if (!CMyString.isEmpty(files)) {
                List<FileDTO> fileList = new ArrayList<>();
                String[] f = CMyString.split(files, ",");
                for (String s : f) {
                    if (!CMyString.isEmpty(s)) {
                        fileList.add(FileDTO.of(s, s, "", 0L));
                    }
                }
                FileUtils.saveFile(fileService, fileList, DocComment.OBJ_TYPE, docComment.getId(), "attachment");
            }
        } catch (ServiceException e) {
            log.error("保存附件出错！", e);
            throw new ServiceException("保存评论成功，但是附件保存失败！", e);
        }
        return docComment.getId();
    }

    public JSONArray queryCommentList(PageContext pageContext) throws ServiceException {
        // 根据文章ID和查询文章评论
        Map<String, Object> map = new HashMap<>(2);
        map.put("docId", pageContext.getProperty(CommentConstant.PARAMS_DOCID));
        map.put("commentType", CommentConstant.PARAMS_TYPE_COMMENT);
        List<DocComment> comments = getCommentList(map);
        JSONArray array = new JSONArray();
        for (DocComment doc : comments) {
            JSONObject docObject = new JSONObject();
            docObject.put("id", doc.getId());
            docObject.put("userId", doc.getUserId());
            docObject.put("commentUser", doc.getCommentUser());
            docObject.put("docId", doc.getDocId());

            docObject.put("commentContent", doc.getCommentContent());
            docObject.put("commentType", doc.getCommentType());
            docObject.put("commentDate", doc.getCommentDate());
            docObject.put("reposeId", doc.getReposeId());
            docObject.put("reposeUser", doc.getReposeUser());
            docObject.put("attachment", doc.getAttachment());
            map = new HashMap<>(3);
            map.put("docId", pageContext.getProperty(CommentConstant.PARAMS_DOCID));
            map.put("commentType", CommentConstant.PARAMS_TYPE_REPOSE);
            map.put("reposeId", doc.getId());
            List<DocComment> reposeList = getCommentList(map);
            JSONArray reposeArray = new JSONArray();
            if (reposeList != null) {
                reposeArray = transferComment(reposeList);
            }
            docObject.put("reposeDoc", reposeArray);
            //增加工单信息
            doWorker(doc, docObject);
            //增加附件相关信息
            doAppdendix(doc, docObject);
            array.add(docObject);
        }
        return array;
    }

    private JSONArray transferComment(List<DocComment> reposeList) throws ServiceException {
        JSONArray array = new JSONArray();
        JSONObject docObject;
        for (DocComment doc : reposeList
        ) {
            docObject = new JSONObject();
            docObject.put("id", doc.getId());
            docObject.put("userId", doc.getUserId());
            docObject.put("commentUser", doc.getCommentUser());
            docObject.put("docId", doc.getDocId());
            docObject.put("commentContent", doc.getCommentContent());
            docObject.put("commentType", doc.getCommentType());
            docObject.put("commentDate", doc.getCommentDate());
            docObject.put("reposeId", doc.getReposeId());
            docObject.put("reposeUser", doc.getReposeUser());
            docObject.put("attachment", doc.getAttachment());
            if (String.valueOf(doc.getCommentType()).equals(CommentConstant.PARAMS_TYPE_REPOSE)) {
                docObject.put("repose", doc.getCommentUser() + "@" + doc.getReposeUser());
            }
            //增加工单信息
            doWorker(doc, docObject);
            //增加附件相关信息
            doAppdendix(doc, docObject);
            array.add(docObject);
        }
        return array;
    }

    public void doWorker(DocComment doc, JSONObject docObject) {
        //增加工单相关信息
        docObject.put("worker", new JSONObject());
    }

    public void doAppdendix(DocComment doc, JSONObject docObject) throws ServiceException {
        //增加附件相关信息
        ObjDTO dto = ObjDTO.of(DocComment.OBJ_TYPE, doc.getId(), "attachment");
        BaseUtils.setUserInfoToDTO(dto);
        docObject.put("attachmentList", fileService.getFileListOfObj(dto));
    }

    private List<DocComment> getCommentList(Map<String, Object> params) {
        QueryWrapper<DocComment> query = new QueryWrapper<>();
        query.orderByAsc("commentDate");
        if (params != null && params.size() > 0) {
            for (String key : params.keySet()) {
                Object obj = params.get(key);
                if (obj != null) {
                    query.eq(key, obj);
                }
            }
        }
        return mapper.selectList(query);
    }
}
