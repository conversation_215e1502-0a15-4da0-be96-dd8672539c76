package com.trs.gov.knowledge;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * KnowledgeBaseApplication
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2021-03-16 16:25
 * @version 1.0
 * @since  1.0
 */
@Slf4j
@MapperScan("com.trs.gov.knowledge")
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.trs")
//@EnableSwagger2
public class KnowledgeBaseApplication {
    public static void main(String[] args) {
        SpringApplication.run(KnowledgeBaseApplication.class, args);
        log.info("启动知识库");
    }
}
