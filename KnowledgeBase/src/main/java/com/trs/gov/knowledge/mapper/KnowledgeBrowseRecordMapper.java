package com.trs.gov.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.knowledge.DO.KnowledgeBrowseRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface KnowledgeBrowseRecordMapper extends BaseMapper<KnowledgeBrowseRecord> {
    public List<Map<String, Object>> getBrowseNum();

    public KnowledgeBrowseRecord getRecordByUserIdAndDocId(@Param("userId") String userId, @Param("docId") String docId);
}
