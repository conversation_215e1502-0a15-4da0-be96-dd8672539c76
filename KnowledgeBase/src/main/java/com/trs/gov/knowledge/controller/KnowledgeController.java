package com.trs.gov.knowledge.controller;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.core.util.GenericParamUtil;
import com.trs.gov.core.util.RequestHandle;
import com.trs.gov.knowledge.service.KnowledgeService;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;
import com.trs.web.builder.util.BeanFactoryHolder;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * KnowledgeController
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2021-03-16 17:00
 * @version 1.0
 * @since 1.0
 */
@RestController
@RequestMapping("/know/knowlege.do")
@Slf4j
public class KnowledgeController extends BaseController {

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Autowired
    private KnowledgeService knowledgeService;

    private String rex = "/pub.{1,}/[0-9]{6}/[P|W]0";

    @RequestMapping(params = "method=download", method = RequestMethod.GET)
    @ResponseBody
    public void download(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            //获取登录用户信息
            if (userService == null) {
                log.error("用户服务不能为空！");
                return;
            }
            Cookie[] cookies = request.getCookies();
            String sessionId = "";
            for (Cookie cookie: cookies) {
                if (cookie.getName()!=null &&"com.trs.idm.coSessionId".equals(cookie.getName())){
                    sessionId = cookie.getValue();
                }
            }
            userService.checkLogin(sessionId);
            String url = GenericParamUtil.getParameterString(request, "url", "");
            if (StringUtils.isEmpty(url) || (!Pattern.compile(rex).matcher(url).find())) {
                log.error("文件{}不存在", url);
                response.sendError(500, "Illegal URL!");
                return;
            }

            // 增加安全域名的校验
            String domains = BeanFactoryHolder.getEnv().getProperty("KnowledgeBase.SecureDomains", "http://10.11.2.144");
            boolean isSecureDomain = false;
            for (String domain : domains.split(";")) {
                if (url.startsWith(domain)) {
                    isSecureDomain = true;
                    break;
                }
            }

            if (!isSecureDomain) {
                log.error("非法连接{}", url);
                response.sendError(500, "Illegal URL!");
                return;
            }

            if (url.indexOf("./") >= 0) {
                log.error("非法连接{}", url);
                response.sendError(500, "Illegal URL!");
                return;
            }
            String filename = url.substring(url.lastIndexOf("/") + 1);
            InputStream ins;
            try {
                ins = new URL(url).openStream();
                /* 设置文件ContentType类型，这样设置，会自动判断下载文件类型 */
                response.setContentType("multipart/form-data");
                /* 设置文件头：最后一个参数是设置下载文件名 适应中文名称*/
                response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + URLEncoder.encode(filename, "UTF-8"));
                OutputStream os = response.getOutputStream();
                byte[] b = new byte[1024];
                int len;
                while ((len = ins.read(b)) > 0) {
                    os.write(b, 0, len);
                }
                os.flush();
                os.close();
                ins.close();
            } catch (FileNotFoundException e) {
                log.error("文件{}不存在", url, e);
            } catch (UnsupportedEncodingException e) {
                log.error(e.getMessage(), e);
            } catch (MalformedURLException e) {
                log.error(e.getMessage(), e);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
        }
    }

    @RequestMapping(params = "method=knowledgeDetail", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    @ResponseBody
    public String knowledgeDetail(String url) {
        Map<String, String> dataMap = new HashMap(1);
        dataMap.put("url", url);
        try {
            return knowledgeService.knowledgeDetail(dataMap);
        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.SERVER_ERROR).addMsg(e.getMessage()).toJson();
        }
    }

    @RequestMapping(params = "method=knowledgeList", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    @ResponseBody
    public String knowledgeList(HttpServletRequest request) {
        try {
            String sPageIndex = GenericParamUtil.getParameterString(request, "pageIndex", "0");
            //知识类型
            String type = GenericParamUtil.getParameterString(request, "type", "");
            //子类型
            String channel = GenericParamUtil.getParameterString(request, "channel", "");
            //检索信息
            String search = GenericParamUtil.getParameterString(request, "search", "");
            //是否需要统计数据
            String needCount = GenericParamUtil.getParameterString(request, "needCount", "false");
            Map<String, String> dataMap = new HashMap();
            dataMap.put("pageIndex", sPageIndex);
            dataMap.put("type", type);
            dataMap.put("search", search);
            dataMap.put("channel", channel);
            dataMap.put("needCount", needCount);
            String jsonData = knowledgeService.knowledgeList(dataMap);
            return jsonData;
        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.SERVER_ERROR).addMsg(e.getMessage()).toJson();
        }
    }

    @RequestMapping(params = "method=addBrowse", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    @ResponseBody
    public String addBrowse(HttpServletRequest request) {

        String ipAddr = RequestHandle.getIpAddress(request);
        //获取登录用户信息
        Optional<String> loginUser = ContextHelper.getLoginUser();
        if (!loginUser.isPresent()) {
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK).addMsg("添加浏览记录失败，用户未登录").toJson();
        }
        try {
            UserVO userVO = userService.getBaseUserInfoByUserName(UserDTO.of(loginUser.get()));
            String docId = GenericParamUtil.getParameter(request, "docId", true);
            Map<String, String> dataMap = new HashMap();
            dataMap.put("ipAddr", ipAddr);
            dataMap.put("docId", docId);
            dataMap.put("rid", userVO.getUserName());
            dataMap.put("trueName", userVO.getTrueName());
            dataMap.put("loginName", userVO.getUserName());
            knowledgeService.saveBrowseRecord(dataMap);
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK).addMsg("添加浏览记录成功").toJson();
        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.SERVER_ERROR).addMsg(e.getMessage()).toJson();
        }
    }

    @RequestMapping(params = "method=queryBrowse", method = RequestMethod.GET, produces = "application/json; charset=utf-8")
    @ResponseBody
    public String queryBrowse(HttpServletRequest request) {
        try {
            String docId = GenericParamUtil.getParameter(request, "docId", true);
            int pageSize = GenericParamUtil.getParameterInt(request, "pageSize", 20);
            int pageIndex = GenericParamUtil.getParameterInt(request, "pageIndex", 1);
            Map dataMap = new HashMap();
            dataMap.put("docId", docId);
            dataMap.put("pageSize", pageSize);
            dataMap.put("pageIndex", pageIndex);

            return knowledgeService.queryBrowseRecord(dataMap);
        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.SERVER_ERROR).addMsg(e.getMessage()).toJson();
        }
    }
}
