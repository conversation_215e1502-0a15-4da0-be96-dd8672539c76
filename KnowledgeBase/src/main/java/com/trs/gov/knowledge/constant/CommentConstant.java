package com.trs.gov.knowledge.constant;

public class CommentConstant {

    /**
     * request
     */
    public static final String HTTP_REQUEST_CONTENT = "HTTP_REQUEST_CONTENT";
    /**
     * response
     */
    public static final String HTTP_RESPONSE_CONTENT = "HTTP_RESPONSE_CONTENT";

    public static final String PAGE_SIZE_PROPERTY = "pageSize";
    public static final String PAGE_NUMBER_PROPERTY = "pageNum";

    /**
     * 参数-评论ID
     */
    public static final String PARAMS_COMMENTID = "COMMENTID";

    /**
     * 参数-文章ID
     */
    public static final String PARAMS_DOCID = "DOCID";

    /**
     * 参数-评论人
     */
    public static final String PARAMS_USERID = "USERID";

    /**
     * 参数-评论名字
     */
    public static final String PARAMS_COMMENTUSER = "COMMENTUSER";

    /**
     * 参数-评论内容
     */
    public static final String PARAMS_COMMENTCONTENT = "COMMENTCONTENT";

    /**
     * 参数-类型：1、评论；2、回复
     */
    public static final String PARAMS_COMMENTTYPE = "COMMENTTYPE";

    /**
     * 参数-回复评论ID
     */
    public static final String PARAMS_REPOSEID = "REPOSEID";

    /**
     * 参数-@人
     */
    public static final String PARAMS_REPOSEUSER = "REPOSEUSER";

    /**
     * 参数-稿件创建者
     */
    public static final String PARAMS_DOCUSER = "DOCUSER";

    /**
     * 参数-类型：1、评论
     */
    public static final String PARAMS_TYPE_COMMENT = "1";

    /**
     * 参数-类型：2、回复
     */
    public static final String PARAMS_TYPE_REPOSE = "2";
}
