package com.trs.gov.knowledge.constant;

public class ForwardConstant {

    /**
     * 用户浏览器标识--cookie中的sessionId
     */
    public static final String COOKIE_SESSIONID = "JSESSIONID";

    /**
     * 用户浏览器标识--cookie中ids的sessionId
     */
    public static final String COOKIE_IDS_SESSIONID = "com.trs.idm.coSessionId";

    /**
     * 用户浏览器标识--cookie中刷新时间
     */
    public static final String COOKIE_REFRESHTIME = "refreshedTimestamp";

    /**
     * 用户浏览器标识---cookie
     */
    public static final String COOKIE = "Cookie";

    /**
     * 转发服务-默认塞入的service
     */
    public static final String FORWARD_SERVICE_DEFAULT = "default";

    /**
     * 转发服务-TYPE_ID
     */
    public static final String TYPE_ID = "TYPE_ID";

    /**
     * 转发服务-SERVICE_ID
     */
    public static final String SERVICE_ID = "SERVICE_ID";

    /**
     * 转发服务-MODEL_ID
     */
    public static final String MODEL_ID = "MODEL_ID";

    /**
     * 转发服务-httpUrl名称
     */
    public static final String FORWARD_HTTP_URL = "httpUrl";

    public static final String HTTP_REQUEST_CONTENT = "HTTP_REQUEST_CONTENT";
    public static final String HTTP_RESPONSE_CONTENT = "HTTP_RESPONSE_CONTENT";
    /**
     * 转发服务-参数
     */
    public static final String HTTP_PARAMETER = "HTTP_PARAMETER";
    /**
     * 转发服务-HTTP_METHOD
     */
    public static final String HTTP_METHOD = "HTTP_METHOD";

    /**
     * 转发服务-pageIndex
     */
    public static final String pageIndex = "pageIndex";

    /**
     * 转发服务-pageSize
     */
    public static final String pageSize = "pageSize";


    /**
     * 转发服务-status
     */
    public static final String status = "status";

    /**
     * 监测云-信息错误类型
     */
    public static final String INFOERROR_TYPE = "3";
    /**
     * 监测云-栏目更新类型
     */
    public static final String CHANNELUPDATE_TYPE = "2";
    /**
     * 监测云-网站可用性类型
     */
    public static final String AVAILABLE_TYPE = "1";

    /**
     * 监测云-信息错误工单类型配置项名称
     */
    public static final String INFOERROR_WORK_TYPE = "INFOERROR_WORK_TYPE";
    /**
     * 监测云-栏目更新工单类型配置项名称
     */
    public static final String CHANNELUPDATE_WORK_TYPE = "CHANNELUPDATE_WORK_TYPE";
    /**
     * 监测云-网站可用性工单类型配置项名称
     */
    public static final String AVAILABLE_WORK_TYPE = "AVAILABLE_WORK_TYPE";
    /**
     * 监测云-自动入库工单审核员角色id配置项名称
     */
    public static final String WORKEXAMINE_ROLEID = "WORKEXAMINE_ROLEID";

}
