package com.trs.gov.knowledge.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@TableName("knowledgedownloadrecord")
public class KnowledgeDownloadRecord implements Serializable {
    /**
     * 主键id
     */

    @TableId(value = "kid")
    private String kId;

    /**
     * userid
     */
    @TableField(value = "userid")
    private String userId;

    /**
     * 用户名称
     */
    @TableField(value = "username")
    private String userName;

    /**
     * 用户真实名称
     */
    @TableField(value = "truename")
    private String trueName;


    /**
     * 知识id
     */
    @TableField(value = "docid")
    private String docId;

    /**
     * 创建时间
     */
    @TableField(value = "createdate")
    private Timestamp createDate;

    /**
     * 修改时间
     */
    @TableField(value = "operatedate")
    private Timestamp operateDate;

    /**
     * 附件ID
     */
    @TableField(value = "fileid")
    private String fileId;


    /**
     * 附件名称
     */
    @TableField(value = "fileName")
    private String fileName;
    /**
     * 下载次数
     */
    @TableField(value = "num")
    private Integer num;

    /**
     * ip访问地址
     */
    @TableField(value = "ipaddr")
    private String ipAddr;

}
