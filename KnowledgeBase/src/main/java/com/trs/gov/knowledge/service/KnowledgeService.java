package com.trs.gov.knowledge.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.knowledge.DO.KnowledgeBrowseRecord;
import com.trs.gov.knowledge.mgr.KnowledgeMgr;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Slf4j
public class KnowledgeService extends BaseService {


    @Autowired
    private KnowledgeMgr knowledgeMgr;

    public String knowledgeDetail(Map<String, String> dataMap) throws ServiceException {
        return knowledgeMgr.knowledgeDetail(dataMap);
    }

    public String knowledgeList(Map<String, String> dataMap) throws ServiceException {
        return knowledgeMgr.knowledgeList(dataMap);
    }

    public void saveBrowseRecord(Map<String, String> dataMap) {
        knowledgeMgr.saveBrowseRecord(dataMap);
    }

    public String queryBrowseRecord(Map dataMap) {
        // 1 构造返回对象
        String resultInfo;
        // 2 参数校验
        try {
            Page<KnowledgeBrowseRecord> resultPage = knowledgeMgr.queryBrowseRecord(dataMap);
            resultInfo = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.OK)
                    .addSummary("pageNum", resultPage.getPages())
                    .addSummary("pageSize", resultPage.getSize())
                    .addSummary("total", resultPage.getTotal())
                    .addData(resultPage.getRecords())
                    .addMsg("查询浏览记录成功")
                    .toJson();
        } catch (Exception e) {
            // 6 异常处理
            resultInfo = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.SERVER_ERROR)
                    .addData(new JSONArray())
                    .addMsg("查询浏览记录异常:" + e)
                    .toJson();
        }
        return resultInfo;
    }
}
