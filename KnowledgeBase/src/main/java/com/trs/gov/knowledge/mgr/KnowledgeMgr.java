package com.trs.gov.knowledge.mgr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.UUIDUtils;
import com.trs.gov.knowledge.DO.KnowledgeBrowseRecord;
import com.trs.gov.knowledge.mapper.DocCommentMapper;
import com.trs.gov.knowledge.mapper.KnowledgeBrowseRecordMapper;
import com.trs.gov.knowledge.mapper.KnowledgeDownloadRecordMapper;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class KnowledgeMgr {

    private HttpRequest httpRequest = new HttpRequest.Builder().build();

    @Autowired
    private KnowledgeBrowseRecordMapper browseRecordMapper;

    @Autowired
    private KnowledgeDownloadRecordMapper downloadRecordMapper;

    @Autowired
    private DocCommentMapper commentMapper;

    @Value("${KnowledgeBase.hyKnowledgeList}")
    private String hyKnowledgeList;

    public void saveBrowseRecord(Map<String, String> dataMap) {
        String ipAddr = dataMap.get("ipAddr");
        String docId = dataMap.get("docId");
        String userId = dataMap.get("rid");
        String username = dataMap.get("loginName");
        String trueName = dataMap.get("trueName");
        if (StringUtils.isEmpty(docId) || StringUtils.isEmpty(userId) || StringUtils.isEmpty(username)) {
            return;
        }
        KnowledgeBrowseRecord knowledgebrowserecord = browseRecordMapper.getRecordByUserIdAndDocId(userId, docId);
        if (knowledgebrowserecord != null) {
            knowledgebrowserecord.setOperateDate(new Timestamp(System.currentTimeMillis()));
            knowledgebrowserecord.setIpAddr(ipAddr);
            knowledgebrowserecord.setNum(knowledgebrowserecord.getNum() + 1);
            browseRecordMapper.updateById(knowledgebrowserecord);
        } else {
            knowledgebrowserecord = new KnowledgeBrowseRecord();
            knowledgebrowserecord.setKId(UUIDUtils.createUUID(false));
            knowledgebrowserecord.setDocId(docId);
            knowledgebrowserecord.setUserId(userId);
            knowledgebrowserecord.setUserName(username);
            knowledgebrowserecord.setCreateDate(new Timestamp(System.currentTimeMillis()));
            knowledgebrowserecord.setOperateDate(new Timestamp(System.currentTimeMillis()));
            knowledgebrowserecord.setIpAddr(ipAddr);
            knowledgebrowserecord.setNum(1);
            knowledgebrowserecord.setTrueName(trueName);
            browseRecordMapper.insert(knowledgebrowserecord);
        }
    }

    public Page<KnowledgeBrowseRecord> queryBrowseRecord(Map dataMap) {
        Integer pageSize = (Integer) dataMap.get("pageSize");

        Integer pageIndex = (Integer) dataMap.get("pageIndex");
        //分页从0开始。
        pageIndex = pageIndex - 1;
        String docId = (String) dataMap.get("docId");
        String userId = (String) dataMap.get("rid");
        String username = (String) dataMap.get("loginName");
        QueryWrapper<KnowledgeBrowseRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("docId", docId);
        // 3 查询
        return browseRecordMapper.selectPage(new Page<KnowledgeBrowseRecord>(pageIndex, pageSize), queryWrapper);
    }

    public String knowledgeDetail(Map<String, String> dataMap) throws ServiceException {
        String result = "{}";
        String url = dataMap.get("url");
        if (url.contains("http") == false && url.contains("https") ==false &&url.contains("10.11.2") ==false){
            log.error("请求URL格式不正确：{}",url);
            throw new ServiceException("请求url未生成或不正确");
        }
        //发送请求
        String data = Try.of(() -> httpRequest.doGet(url)).getOrElseThrow(err -> new ServiceException("HTTP请求异常", err));
        if (data.contains("404 Not Found")) {
            throw new ServiceException("请求url未生成或不正确");
        }
        try {
            JSONObject jsonObject = JSON.parseObject(data);
            String docUrl = jsonObject.getJSONObject("datas").getString("docUrl");
            docUrl = docUrl.substring(0, docUrl.lastIndexOf("/"));
            String docContent = jsonObject.getJSONObject("datas").getString("docContent");
            if (!StringUtils.isEmpty(docContent)) {
                docContent = docContent.replaceAll("=\"./", String.format("=\"%s%s/", "/workorder_gw/know/knowlege.do?method=download&url=", docUrl));
                jsonObject.getJSONObject("datas").put("docContent", docContent);
            }
            result= jsonObject.toJSONString();
        }catch (Exception e){
            log.error("knowledgeDetail解析数据报错：{}",e.toString());
        }
        return result;
    }

    public String knowledgeList(Map<String, String> dataMap) throws ServiceException {
        //返回结果参数定义
        JSONObject jsonData;

        String sPageIndex = dataMap.get("pageIndex");
        //知识类型
        String sType = dataMap.get("type");
        //子类型
        String sChannel = dataMap.get("channel");
        //是否统计浏览下载记录   false 不统计  true 统计
        String sNeedCount = dataMap.get("needCount");

        //构造请求url
        String url = hyKnowledgeList;

        //检索信息
        String search = dataMap.get("search");
        if (!StringUtils.isNullOrEmpty(search)) {
            //根据检索构造返回结果
            if (!StringUtils.isNullOrEmpty(sChannel)) {
                url = String.format("%s/%s/%s/list", url, sType, sChannel);
            } else {
                url = String.format("%s/%s/list", url, sType);
            }
            JSONArray dataJsonarry = new JSONArray();
            int pageSize = searchKnowledgeList(url, search, "0", dataJsonarry);
            //构造分页信息
            jsonData = doPageList(dataJsonarry, "docpubTime", Integer.valueOf(sPageIndex), pageSize);
            return jsonData.toJSONString();
        }

        //非检索查询
        if (!StringUtils.isNullOrEmpty(sChannel)) {
            if (sChannel.equals("allIndex")) {
                url = String.format("%s/%s/index", url, sType);
            } else {
                url = String.format("%s/%s/%s/list", url, sType, sChannel);
            }
        } else {
            url = String.format("%s/%s/list", url, sType);

        }
        jsonData = getKnowledgeList(url, sNeedCount, sPageIndex);
        return jsonData.toJSONString();
    }

    public static JSONArray jsonArraySort(JSONArray jsonArr, final String sortKey, final boolean is_desc) {
        //存放排序结果json数组
        JSONArray sortedJsonArray;
        //将参数json数组每一项取出，放入list
        List<JSONObject> list = JSONArray.parseArray(jsonArr.toJSONString(), JSONObject.class);
        //快速排序，重写compare方法，完成按指定字段比较，完成排序
        //重写compare方法
        Collections.sort(list, (a, b) -> {
            String valA = new String();
            String valB = new String();
            try {
                valA = a.getString(sortKey);
                valB = b.getString(sortKey);
            } catch (JSONException e) {
                log.warn("JSON读取出错！", e);
            }
            //是升序还是降序
            if (is_desc) {
                return -valA.compareTo(valB);
            } else {
                return -valB.compareTo(valA);
            }

        });
        sortedJsonArray = JSONArray.parseArray(list.toString());
        return sortedJsonArray;
    }

    private JSONObject doPageList(JSONArray dataJsonarry, String sortKey, int pageIndex, int pageSize) {
        JSONObject jsonData = new JSONObject();
        //构造返回信息
        jsonData.put("code", 200);
        jsonData.put("message", "获取信息成功！");
        //先排序
        dataJsonarry = jsonArraySort(dataJsonarry, sortKey, true);
        //构造分页信息
        JSONObject re = new JSONObject();
        int dataCount = dataJsonarry.size();
        if (dataCount == 0) {
            JSONObject page_info = new JSONObject();
            page_info.put("page_index", pageIndex);
            page_info.put("page_count", 0);
            page_info.put("page_size", pageSize);
            re.put("page_info", page_info);
        } else {//计算分页
            int pageCount = (dataCount + pageSize - 1) / pageSize;
            int fromIndex = pageIndex * pageSize;
            if (fromIndex > dataCount) {
                fromIndex = dataCount;
            }
            int toIndex = (pageIndex + 1) * pageSize;
            if (toIndex > dataCount) {
                toIndex = dataCount;
            }
            re.put("list_datas", dataJsonarry.subList(fromIndex, toIndex));
            JSONObject page_info = new JSONObject();
            page_info.put("page_index", pageIndex);
            page_info.put("page_count", pageCount);
            page_info.put("page_size", pageSize);
            re.put("page_info", page_info);
        }
        //返回数据
        jsonData.put("response", re);
        return jsonData;
    }

    private int searchKnowledgeList(String url, String search, String sPageIndex, JSONArray reJsonData) throws ServiceException {
        //设置分页信息
        String sUrl;
        if (StringUtils.isEmpty(sPageIndex) || "0".equals(sPageIndex)) {
            sUrl = String.format("%s.json", url);
        } else {
            sUrl = String.format("%s_%s.json", url, sPageIndex);
        }
        //发送请求
        String data = Try.of(() -> httpRequest.doGet(sUrl)).getOrElseThrow(err -> new ServiceException("HTTP请求异常", err));
        if (data.contains("404 Not Found")) {
            return 10;
        }
        JSONObject jsonData = JSON.parseObject(data);
        JSONArray jsonArray = jsonData.getJSONObject("response").getJSONArray("list_datas");
        for (int i = 0; i < jsonArray.size(); i++) {
            String docTitle = jsonArray.getJSONObject(i).getString("docTitle");
            //检索判断
            if (StringUtils.isEmpty(search) || docTitle.contains(search)) {
                reJsonData.add(jsonArray.getJSONObject(i));
            }

        }
        //判断是否还有分页
        int pageSize = jsonData.getJSONObject("response").getJSONObject("page_info").getInteger("page_size");
        int page_count = jsonData.getJSONObject("response").getJSONObject("page_info").getInteger("page_count");
        int pageIndex = Integer.valueOf(sPageIndex) + 1;
        if (page_count > pageIndex) {
            searchKnowledgeList(url, search, pageIndex + "", reJsonData);
        }
        return pageSize;
    }

    private JSONObject getKnowledgeList(String url, String sNeedCount, String sPageIndex) throws ServiceException {
        //设置分页信息
        String sUrl;
        if (StringUtils.isEmpty(sPageIndex) || "0".equals(sPageIndex)) {
            sUrl = String.format("%s.json", url);
        } else {
            sUrl = String.format("%s_%s.json", url, sPageIndex);
        }
        JSONObject jsonData = new JSONObject();
        //发送请求
        String data = Try.of(() -> httpRequest.doGet(sUrl)).getOrElseThrow(err -> new ServiceException("HTTP请求异常", err));
        if (data.contains("404 Not Found")) {
            return jsonData;
        }
        jsonData = JSON.parseObject(data);
        if ("true".equals(sNeedCount)) {
            JSONArray jsonArray = jsonData.getJSONObject("response").getJSONArray("list_datas");
            //统计评论量
            doDocCommentNum(jsonArray);
            //统计下载量
            doDownloadNum(jsonArray);
            //统计浏览量
            doBrowseNum(jsonArray);
        }
        return jsonData;
    }

    private void doDocCommentNum(JSONArray dataJsonarry) {
        //统计消息类型数据
//        String sql = "select count(id) as num ,docid,MAX(commentDate) as commentDate from doccomment GROUP BY docid";
        List<Map<String, Object>> pinglunSqlCountList = commentMapper.getDocCommentNum();
        Map<String, Object> pinglunMap = new HashMap<>();
        for (Map<String, Object> map : pinglunSqlCountList) {
            pinglunMap.put(map.get("docid").toString(), map.get("num"));
            pinglunMap.put(String.format("D%s", map.get("docid").toString()), map.get("commentDate"));
        }
        for (int i = 0; i < dataJsonarry.size(); i++) {
            JSONObject jsonObject = dataJsonarry.getJSONObject(i);
            Object num = pinglunMap.get(jsonObject.getString("docId"));
            Object commentDate = pinglunMap.get(String.format("D%s", jsonObject.get("docId").toString()));
            jsonObject.put("commentNum", num == null ? 0 : num);
            jsonObject.put("commentTime", commentDate == null ? "" : commentDate);
        }
    }


    /**
     * @return
     * <AUTHOR>
     * @Description 统计知识浏览次数
     * @Date 13:34 2020/6/2
     * @Param [dataJsonarry]
     */
    private void doBrowseNum(JSONArray dataJsonarry) {
        //统计消息类型数据
        String sql = "select count(kid) as num ,docid,DATE_FORMAT(MAX(operatedate),'%Y-%m-%d %H:%i:%s') as operatedate from knowledgebrowserecord GROUP BY docid";
        List<Map<String, Object>> pinglunSqlCountList = browseRecordMapper.getBrowseNum();
        Map<String, Object> browseMap = new HashMap<>();
        for (Map<String, Object> map : pinglunSqlCountList) {
            browseMap.put(map.get("docid").toString(), map.get("num"));
            browseMap.put(String.format("D%s", map.get("docid").toString()), map.get("operatedate"));
        }
        for (int i = 0; i < dataJsonarry.size(); i++) {
            JSONObject jsonObject = dataJsonarry.getJSONObject(i);
            Object num = browseMap.get(jsonObject.getString("docId"));
            Object browseDate = browseMap.get(String.format("D%s", jsonObject.get("docId").toString()));
            jsonObject.put("browseNum", num == null ? 0 : num);
            jsonObject.put("browseTime", browseDate == null ? "" : browseDate);
        }

    }

    /**
     * @return
     * <AUTHOR>
     * @Description 统计知识下载次数
     * @Date 13:34 2020/6/2
     * @Param [dataJsonarry]
     */
    private void doDownloadNum(JSONArray dataJsonarry) {
        //统计消息类型数据
        String sql = "select count(kid) as num ,docid,DATE_FORMAT(MAX(operatedate),'%Y-%m-%d %H:%i:%s') as operatedate from knowledgedownloadrecord GROUP BY docid";
        List<Map<String, Object>> pinglunSqlCountList = downloadRecordMapper.getDownloadNum();
        Map<String, Object> downloadMap = new HashMap<>();
        for (Map<String, Object> map : pinglunSqlCountList) {
            downloadMap.put(map.get("docid").toString(), map.get("num"));
            downloadMap.put(String.format("D%s", map.get("docid").toString()), map.get("operatedate"));
        }
        for (int i = 0; i < dataJsonarry.size(); i++) {
            JSONObject jsonObject = dataJsonarry.getJSONObject(i);
            Object num = downloadMap.get(jsonObject.getString("docId"));
            Object downloadDate = downloadMap.get(String.format("D%s", jsonObject.get("docId").toString()));
            jsonObject.put("downloadNum", num == null ? 0 : num);
            jsonObject.put("downloadTime", downloadDate == null ? "" : downloadDate);
        }
    }
}
