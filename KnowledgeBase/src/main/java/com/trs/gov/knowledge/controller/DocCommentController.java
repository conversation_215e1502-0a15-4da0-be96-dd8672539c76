package com.trs.gov.knowledge.controller;

import com.trs.common.context.CommonContext;
import com.trs.gov.knowledge.constant.CommentConstant;
import com.trs.gov.knowledge.service.DocCommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * DocCommentController
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2021-03-18 10:22
 * @version 1.0
 * @since 1.0
 */
@RestController
@RequestMapping("/know/comment.do")
@Slf4j
public class DocCommentController extends BaseController {

    @Autowired
    private DocCommentService commentService;

    @ResponseBody
    @RequestMapping(params = "method=saveComment", method = {RequestMethod.POST, RequestMethod.GET}, produces = "application/json; charset=utf-8")
    public String saveComment(HttpServletRequest request,
                              @RequestParam String docId,
                              @RequestParam String commentContent,
                              @RequestParam Integer commentType,
                              @RequestParam(required = false) String reposeId,
                              @RequestParam(required = false) String reposeUser,
                              @RequestParam(required = false) String workerId,
                              @RequestParam(defaultValue = "", required = false) String attachment) {
        CommonContext context = initContextAsDefault(request);
        context.addProperty(CommentConstant.PARAMS_DOCID, docId);
        context.addProperty(CommentConstant.PARAMS_COMMENTCONTENT, commentContent);
        context.addProperty(CommentConstant.PARAMS_COMMENTTYPE, commentType);
        context.addProperty(CommentConstant.PARAMS_REPOSEID, reposeId);
        context.addProperty(CommentConstant.PARAMS_REPOSEUSER, reposeUser);
        context.addProperty("workOrderId", workerId);
        context.addProperty("file", attachment);
        return commentService.saveComment(context);
    }

    @ResponseBody
    @RequestMapping(params = "method=queryComment", method = {RequestMethod.GET}, produces = "application/json; charset=utf-8")
    public String queryComment(HttpServletRequest request,
                               @RequestParam String docId,
                               @RequestParam(required = false, defaultValue = "0") Integer pageNum,
                               @RequestParam(required = false, defaultValue = "50") Integer pageSize) {
        CommonContext context = initContextAsDefault(request);
        context.addProperty(CommentConstant.PARAMS_DOCID, docId);
        context.addProperty(CommentConstant.PAGE_NUMBER_PROPERTY, pageNum);
        context.addProperty(CommentConstant.PAGE_SIZE_PROPERTY, pageSize);
        return commentService.queryComment(context);
    }

    @ResponseBody
    @RequestMapping(params = "method=delComment", method = {RequestMethod.GET}, produces = "application/json; charset=utf-8")
    public String delComment(HttpServletRequest request, @RequestParam(required = true) String commentId) {
        CommonContext context = initContextAsDefault(request);
        context.addProperty(CommentConstant.PARAMS_COMMENTID, commentId);
        return commentService.delComment(context);
    }
}
