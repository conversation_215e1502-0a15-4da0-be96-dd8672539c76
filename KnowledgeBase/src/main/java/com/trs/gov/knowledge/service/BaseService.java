package com.trs.gov.knowledge.service;

import com.trs.common.context.CommonContext;

import static com.trs.common.base.PreConditionCheck.checkNotNull;
import static com.trs.common.utils.StringUtils.isNullOrEmpty;

public abstract class BaseService {

    /**
     * 校验字段是否为空，为空则会抛出空指针异常
     *
     * @param fieldNames
     */
    public void ValidateNotNull(CommonContext context, String... fieldNames) {
        Boolean isAvailable = false;
        checkNotNull(context);
        //检查核心属性
        for (String fieldName : fieldNames) {
            checkNotNull(fieldName);
            isAvailable = parseContextAndValidateNotNull(context.getProperty(fieldName));
            //若核心属性中有空值，则记录nullPointException
            if (!isAvailable) {
                throw new NullPointerException(fieldName + " can't be empty");
            }
        }
    }

    /**
     * 对上下文进行解析，并对传入的制定属性进行非空校验
     *
     * @param property
     * @return
     */
    public boolean parseContextAndValidateNotNull(String property) {
        //非空校验,便利传入属性并进行检验
//		String propertyValue = context.getProperty(property);
        //如果出现不存该属性的情况，则返回false
        //通过返回true
        return isNullOrEmpty(property) || property.equals("null") ? false : true;
    }
}
