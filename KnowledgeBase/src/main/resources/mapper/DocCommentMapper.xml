<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.knowledge.mapper.DocCommentMapper">


    <select id="getDocCommentNum" resultType="java.util.Map">
        select count(id) as num, docid, MAX(commentDate) as commentDate
        from doccomment
        GROUP BY docid;
    </select>
</mapper>