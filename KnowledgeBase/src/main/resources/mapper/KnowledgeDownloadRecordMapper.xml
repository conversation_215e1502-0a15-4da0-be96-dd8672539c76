<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.knowledge.mapper.KnowledgeDownloadRecordMapper">


    <select id="getDownloadNum" resultType="java.util.Map">
        select count(kid) as num, docid, DATE_FORMAT(MAX(operatedate), '%Y-%m-%d %H:%i:%s') as operatedate
        from knowledgedownloadrecord
        GROUP BY docid;
    </select>
</mapper>