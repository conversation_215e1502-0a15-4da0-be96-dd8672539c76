```sql
#20201217 左开元 通知表结构修改
ALTER TABLE `notice` 
DROP COLUMN `cr_unit_id`,
DROP COLUMN `cr_username`,
DROP COLUMN `cr_unit`,
DROP COLUMN `cr_truename`,
MODIFY COLUMN `target_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知目标人单位' AFTER `target_unit_id`,
MODIFY COLUMN `status` int NULL DEFAULT NULL COMMENT '' AFTER `target_truename`,
MODIFY COLUMN `type` int NULL DEFAULT NULL COMMENT '通知类型（1：个人，2：单位，3：全部）' AFTER `status`;
DROP TABLE IF EXISTS `notice_read`;
CREATE TABLE `notice_read`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cr_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `cr_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `work_order_id` bigint NULL DEFAULT NULL COMMENT '工单id',
  `target_unit_id` bigint NULL DEFAULT NULL COMMENT '通知目标单位id',
  `target_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通知目标人',
  `status` int NULL DEFAULT 0 COMMENT '阅读状态(0：未读，1：已读)',
  `export_from_other` int NULL DEFAULT 0 COMMENT '是否来自老工单（0：否，1：是）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 84 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知表' ROW_FORMAT = DYNAMIC;

#20201217 杨鑫 增加工单和通知的视图
DROP VIEW IF EXISTS two_level_work_order_type;
DROP VIEW IF EXISTS work_order_detail;
DROP VIEW IF EXISTS user_info_of_notice;
DROP VIEW IF EXISTS notice_detail;
CREATE VIEW two_level_work_order_type AS (
SELECT
	wot1.id,
	wot1.type_name,
	wot2.id AS work_order_parent_type_id,
	wot2.type_name AS work_order_parent_type_name 
FROM
	work_order_type wot1
	LEFT JOIN work_order_type wot2 ON wot1.parent_id = wot2.id 
	);
CREATE VIEW work_order_detail AS (
SELECT
	wo.id,
	wo.cr_time,
	wo.cr_user,
	wo.work_order_top_type_id,
	wo.work_order_top_type_name,
	wo.work_order_type_id,
	wot.type_name AS work_order_type_name,
	wo.cr_unit_id,
	wo.cr_unit,
	wo.cr_username,
	wo.cr_truename,
	wo.site_id,
	wo.sitename,
	wo.content,
	wo.priority,
	wo.host_unit_id,
	wo.host_unit,
	wo.host_assign_type,
	wo.host_username,
	wo.host_truename,
	wo.deal_unit_id,
	wo.deal_unit,
	wo.deal_assign_type,
	wo.deal_username,
	wo.deal_truename,
	wo.media_type,
	wo.expected_end_date,
	wo.update_time,
	wo.receive_time,
	wo.is_delete,
	wo.`status`,
	wo.action_time,
	wo.is_return,
	wo.finish_time,
	wo.export_from_other,
	wot.work_order_parent_type_id,
	wot.work_order_parent_type_name
FROM
	work_order wo
	LEFT JOIN two_level_work_order_type wot ON wo.work_order_type_id = wot.id
	);
CREATE view user_info_of_notice
as
(SELECT
	w.id,
	w.cr_user,
	u.true_name AS cr_username,
	w.cr_unit_id,
	un.unit_name AS cr_unit_name,
	w.content
FROM
	work_order w
	LEFT JOIN `user` u ON w.cr_user = u.user_name
	LEFT JOIN unit un ON w.cr_unit_id = un.id 
WHERE
	w.work_order_top_type_id = 3);
CREATE VIEW notice_detail AS (
SELECT
	n.*,
	wo.work_order_top_type_id,
	wo.work_order_top_type_name,
	wo.content 
FROM
	notice n
	LEFT JOIN work_order wo ON n.work_order_id = wo.id 
	);
#20210104 左开元 通知、抄送表结构修改
ALTER TABLE `cc`
    ADD COLUMN `group_id` int NULL DEFAULT NULL COMMENT '分组id' AFTER `cr_truename`;
ALTER TABLE `notice`
    ADD COLUMN `group_id` int NULL DEFAULT NULL COMMENT '分组id' AFTER `work_order_id`;

#20201231 兰鑫
#新增单位分组表
CREATE TABLE `unit_group` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `group_name` varchar(20) DEFAULT NULL,
    `status` int(2) DEFAULT 1,
    `cr_user` varchar(20) DEFAULT NULL,
    `cr_time` datetime DEFAULT NULL,
    `update_user` varchar(20) DEFAULT NULL,
    `update_time` datetime DEFAULT NULL,
    `is_del` int(2) DEFAULT 0,
    `export_from_other` int(11) DEFAULT 0,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4;
#新增单位分组和单位关系表
CREATE TABLE `unit_group_relation` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `group_id` bigint(20) DEFAULT NULL,
    `unit_id` bigint(20) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4;


# 20210106 褚川宝 增加人员单位映射表
DROP TABLE IF EXISTS `user_unit_mapping`;
CREATE TABLE IF NOT EXISTS `user_unit_mapping` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `cr_time` datetime NOT NULL,
    `cr_user` varchar(255) NOT NULL,
    `export_from_other` int(2) DEFAULT NULL,
    `status` int(11) DEFAULT NULL,
    `user_name` varchar(255) DEFAULT NULL,
    `true_name` varchar(255) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `phone` varchar(255) DEFAULT NULL,
    `unit_id` varchar(255) DEFAULT NULL,
    `unit_type` varchar(50) DEFAULT NULL,
    `unit_name` varchar(255) DEFAULT NULL,
    `is_master` bit(1) DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=170 DEFAULT CHARSET=utf8mb4;

# 20210108 左开元 通知、抄送表修改分组id类型
ALTER TABLE `cc` 
MODIFY COLUMN `group_id` bigint NULL DEFAULT NULL COMMENT '分组id' AFTER `cr_truename`;
ALTER TABLE `notice` 
MODIFY COLUMN `group_id` bigint NULL DEFAULT NULL COMMENT '分组id' AFTER `work_order_id`;

# 20210108 杨鑫 操作记录新增分组字段
alter table opr_record add group_id BIGINT null;

#优化工单索引结构
DROP VIEW IF EXISTS user_info_of_notice;
DROP VIEW IF EXISTS two_level_work_order_type;
DROP VIEW IF EXISTS notice_detail;
DROP VIEW IF EXISTS work_order_detail;
CREATE VIEW work_order_detail AS (
SELECT
	wo.id,
	wo.cr_time,
	wo.cr_user,
	wo.work_order_top_type_id,
	wo.work_order_top_type_name,
	wo.work_order_type_id,
	wot1.type_name AS work_order_type_name,
	wo.cr_unit_id,
	wo.cr_unit,
	wo.cr_username,
	wo.cr_truename,
	wo.site_id,
	wo.sitename,
	wo.content,
	wo.priority,
	wo.host_unit_id,
	wo.host_unit,
	wo.host_assign_type,
	wo.host_username,
	wo.host_truename,
	wo.deal_unit_id,
	wo.deal_unit,
	wo.deal_assign_type,
	wo.deal_username,
	wo.deal_truename,
	wo.media_type,
	wo.expected_end_date,
	wo.update_time,
	wo.receive_time,
	wo.is_delete,
	wo.`status`,
	wo.action_time,
	wo.is_return,
	wo.finish_time,
	wo.export_from_other,
	wot2.id AS work_order_parent_type_id,
	wot2.type_name AS work_order_parent_type_name 
FROM
	work_order wo
	LEFT JOIN work_order_type wot1 ON wo.work_order_type_id = wot1.id
	LEFT JOIN work_order_type wot2 ON wot1.parent_id = wot2.id );
	CREATE VIEW notice_detail AS (
SELECT
	n.id,
	n.cr_time,
	n.cr_user,
	u.true_name AS cr_truename,
	wo.cr_unit_id,
	dept.unit_name AS cr_unit_name,
	n.work_order_id,
	n.group_id,
	n.target_unit_id,
	n.target_username,
	n.type,
	wo.content 
FROM
	notice n
	LEFT JOIN work_order wo ON n.work_order_id = wo.id
	LEFT JOIN `user` u ON u.user_name = wo.cr_user
	LEFT JOIN unit dept ON wo.cr_unit_id = dept.id
    where wo.work_order_top_type_id = 3
	);

# ------------------------20201-02-08 以上SQL已追加到init.sql中	
```