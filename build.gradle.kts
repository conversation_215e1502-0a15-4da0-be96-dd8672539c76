import java.io.FileInputStream
import java.util.*

plugins {
    id("java")
    id("org.springframework.boot") version "2.0.3.RELEASE" apply false
    id("io.spring.dependency-management") version "1.0.11.RELEASE"
    id("io.freefair.lombok") version "5.1.0" apply false
}

subprojects {
    apply(plugin = "java")
    apply(plugin = "io.freefair.lombok")
    apply(plugin = "io.spring.dependency-management")
    dependencyManagement {
        imports {
            mavenBom("${property("alibabaCloudGroupId")}:spring-cloud-alibaba-dependencies:${property("alibabaCloudVersion")}")
            mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
            mavenBom("org.springframework.boot:spring-boot-dependencies:${property("springBootVersion")}")
        }
    }

    configurations.all {
        exclude("ch.qos.logback", "logback-classic")
    }

    group = "${property("group")}"
    version = "${property("version")}"
    java.sourceCompatibility = JavaVersion.VERSION_1_8

    dependencies {
        implementation("org.slf4j:slf4j-log4j12:1.7.25")
        implementation("org.slf4j:slf4j-api:1.7.25")
        implementation("${property("alibabaCloudGroupId")}:spring-cloud-starter-alibaba-nacos-discovery")
        implementation("${property("alibabaCloudGroupId")}:spring-cloud-starter-alibaba-nacos-config")
        implementation("${property("alibabaCloudGroupId")}:spring-cloud-starter-dubbo")
        implementation("org.javassist:javassist:3.23.1-GA")
        implementation("com.google.guava:guava:20.0")
        implementation("org.springframework.boot:spring-boot-starter-actuator")
        implementation("com.trs:media_base_cache:1.3.4")
        implementation("com.trs.web:media_base_web_core:${property("mediaBaseWebVersion")}")
        implementation("com.trs.web:media_base_web_builder:${property("mediaBaseWebVersion")}")
        implementation("com.trs.web:media_base_web_jpa:${property("mediaBaseWebVersion")}")
        implementation("com.trs:media_base:1.3.6")
        implementation("com.trs:media_base_log:1.9.2")
        implementation("com.github.xiaoymin:knife4j-spring-boot-starter:2.0.3")
        implementation("com.baomidou:mybatis-plus-boot-starter:3.4.0")
        implementation("org.apache.skywalking:apm-toolkit-trace:8.4.0")
        implementation("org.apache.skywalking:apm-toolkit-log4j-1.x:6.4.0")
        testImplementation("junit:junit:4.12")
        testImplementation("org.springframework.boot:spring-boot-starter-test")
    }
    tasks.withType<JavaCompile>() {
        options.encoding = "UTF-8"
        options.compilerArgs.add("-Xlint:none")
    }
    fun deleteFileDir(f: File) {
        if (f.isDirectory) {
            f.listFiles().forEach { file ->
                deleteFileDir(file)
            }
        }
        f.delete()
    }

    tasks.getByName("clean").doFirst {
        // 清除maven的生成目录
        var file = File(projectDir.absolutePath + "/target")
        if (file.exists()) {
            deleteFileDir(file)
        }
        // 清除日志的目录
        file = File(projectDir.absolutePath + "/logs")
        if (file.exists()) {
            deleteFileDir(file)
        }
    }

    fun parseParam(env: String): Map<String, String> {
        var map = HashMap<String, String>()

        map["log.path"] = "/TRS"

        if (System.getProperty("os", "").toLowerCase().indexOf("mac") != -1) {
            map["log.path"] = "target/logs"
        }

        if (env.isEmpty()) {
            return map
        }
        var file = File(rootDir.absolutePath + "/Configuration/filters/${env}/resources/bootstrap.properties")
        if (file.exists()) {
            var p = Properties()
            p.load(FileInputStream(file))
            p.forEach { t, u ->
                map[t.toString()] = u.toString()
            }
        }
        return map
    }

    tasks.withType<ProcessResources> {
        var env: String = System.getProperty("env", "")
        var param = parseParam(env)
        filter(org.apache.tools.ant.filters.ReplaceTokens::class, mapOf("tokens" to param))
    }

    tasks.withType<org.springframework.boot.gradle.tasks.bundling.BootJar> {
        archiveFileName.set("${project.name}-1.0.jar")
        doFirst {
            manifest {
                attributes(
                    Pair("Implementation-Title", project.name),
                    Pair("GroupId", project.group),
                    Pair("Implementation-Version", project.version),
                    Pair("Timestamp", System.currentTimeMillis()),
                    Pair("Build-Jdk", java.targetCompatibility.majorVersion)
                )
            }
        }
        doLast {
            copy {
                from("build/libs")
                into(rootDir.absolutePath + "/build/libs")
            }
        }
    }
}