package com.trs.gov.file.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class ObjDTO extends BaseDTO {

    /**
     * 文件所属对象的类型
     */
    private String objtype;

    /**
     * 文件所属对象ID
     */
    private String objid;

    /**
     * 文件可能属于对象的不同字段，所以增加该字段标识
     */
    private String fieldname;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isNullOrEmpty(objtype)) {
            throw new ParamInvalidException("文件所属对象的类型不能为空！");
        }
        if (StringUtils.isNullOrEmpty(objid)) {
            throw new ParamInvalidException("文件所属对象ID不能为空！");
        }
        if (StringUtils.isNullOrEmpty(fieldname)) {
            throw new ParamInvalidException("文件所属对象fieldname不能为空！");
        }
        return true;
    }

    public static ObjDTO of(String objtype, String objid, String fieldname) throws ServiceException {
        ObjDTO dto = new ObjDTO();
        dto.setObjtype(objtype);
        dto.setObjid(objid);
        dto.setFieldname(fieldname);
        dto.isValid();
        return dto;
    }
}
