package com.trs.gov.file.constant;

import java.util.TimeZone;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件相关常量
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-18 16:44
 * @version 1.0
 * @since  1.0
 */
public class FileConstant {

    // 常量定义：关于文件名称
    /**
     * 文件名称最小值
     */
    public final static int FILENAME_MIN_LENGTH = 22;

    /**
     * 目录标识长度
     */
    public final static int FILENAME_FLAG_LENGTH = 2;

    /**
     * 日期长度
     */
    public final static int FILENAME_DATE_LENGTH = 8;

    /**
     * 时间值长度
     */
    public final static int FILENAME_TIME_LENGTH = 8;

    /**
     * 随机数长度
     */
    public final static int FILENAME_RANDOM_LENGTH = 4;

    // 常量定义：目录类型 3类
    /**
     * 目录类型：本地目录，用于Server( for Server )
     */
    public final static int PATH_LOCAL = 0;

    /**
     * 目录类型：Http目录，用于Web( for Client )
     */
    public final static int PATH_HTTP = 1;

    // 1 天的时间数（毫秒数）
    public final static long ADAY_MILLIS = 86400000;

    /**
     * 当前时区时间差
     */
    public final static int TIMEZONE_RAWOFFSET = TimeZone.getDefault().getRawOffset();

    /**
     * 上传文件的标识
     */
    public static final String FLAG_UPLOAD = "U0";
    public static final String FLAG_WEBFILE = "W0";
}
