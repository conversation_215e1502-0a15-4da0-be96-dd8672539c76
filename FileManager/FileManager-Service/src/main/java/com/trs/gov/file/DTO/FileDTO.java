package com.trs.gov.file.DTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyFile;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件DTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-17 23:41
 * @version 1.0
 * @since 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileDTO extends BaseDTO {
    /**
     * 文件名
     */
    private String filename;
    /**
     * 文件描述
     */
    private String filedesc;
    /**
     * 文件后缀
     */
    private String fileext;

    /**
     * 文件大小
     */
    private Long filesize;


    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isNullOrEmpty(filename)) {
            throw new ParamInvalidException("文件名不能为空！");
        }
        return true;
    }

    public static FileDTO of(String filename, String filedesc, String fileext, Long filesize) throws ServiceException {
        FileDTO dto = new FileDTO();
        dto.filename = filename;
        dto.filedesc = CMyString.showEmpty(filedesc, filename);
        dto.fileext = CMyString.showEmpty(fileext, CMyFile.extractFileExt(filename));
        dto.filesize = filesize;
        BaseUtils.checkDTO(dto);
        return dto;
    }
}
