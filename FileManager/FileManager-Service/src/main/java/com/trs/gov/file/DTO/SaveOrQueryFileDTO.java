package com.trs.gov.file.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件DTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-17 23:41
 * @version 1.0
 * @since 1.0
 */
@Data
public class SaveOrQueryFileDTO extends BaseDTO {

    /**
     * 文件信息
     */
    private FileDTO file;

    /**
     * 对象信息
     */
    private ObjDTO obj;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (file == null || !file.isValid()) {
            throw new ParamInvalidException("文件信息异常！");
        }
        if (obj == null || !obj.isValid()) {
            throw new ParamInvalidException("对象信息异常！");
        }
        if (StringUtils.isNullOrEmpty(obj.getFieldname())) {
            throw new ParamInvalidException("对象字段不能为空！");
        }
        return true;
    }

    public static SaveOrQueryFileDTO of(FileDTO file, ObjDTO obj) throws ServiceException {
        BaseUtils.checkDTO(file, obj);
        SaveOrQueryFileDTO dto = new SaveOrQueryFileDTO();
        dto.setObj(obj);
        dto.setFile(file);
        return dto;
    }
}
