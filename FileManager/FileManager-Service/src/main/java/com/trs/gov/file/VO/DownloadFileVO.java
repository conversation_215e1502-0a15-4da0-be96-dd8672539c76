package com.trs.gov.file.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.io.File;

/**
 * @ClassName：DoanloadFileVO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/3 17:20
 **/
@Data
public class DownloadFileVO extends BaseVO {
    /**
     * 文件byte数组
     */
    private byte[] fileByte;
    /**
     * 原 文件名 称
     */
    private String orginalFileName;
    /**
     * 文件
     */
    private File file;
    /**
     * 是否成功
     */
    private Boolean isSuccess;

    /**
     * @Description  失败
     * @Param [fileByte, isSuccess]
     * @return
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/3 17:59
     **/
    public DownloadFileVO(byte[] fileByte, Boolean isSuccess) {
        this.fileByte = fileByte;
        this.isSuccess = isSuccess;
    }

    /**
     * @Description  成功
     * @Param [fileByte, orginalFileName, file, isSuccess]
     * @return
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/3 17:59
     **/
    public DownloadFileVO(byte[] fileByte, String orginalFileName, File file, Boolean isSuccess) {
        this.fileByte = fileByte;
        this.orginalFileName = orginalFileName;
        this.file = file;
        this.isSuccess = isSuccess;
    }
}
