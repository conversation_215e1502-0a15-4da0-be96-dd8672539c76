package com.trs.gov.file.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.file.DTO.FileDTO;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.DTO.ObjSaveFilesDTO;
import com.trs.gov.file.service.IFileManagerService;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件工具类（方便在第三方中处理文件）
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-23 19:40
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public class FileUtils {

    private static ObjectMapper mapper = new ObjectMapper();

    public static void saveFile(IFileManagerService service, String fileList, String objType, String objId, String filedName) throws ServiceException {
        List<FileDTO> files = Try.of(() -> Arrays.asList(mapper.readValue(fileList, FileDTO[].class))).getOrElseThrow(err -> {
            log.warn("文件转换失败", err);
            return new ParamInvalidException("文件列表异常", err);
        });
        if (files == null || files.size() == 0) {
            return;
        }
        saveFile(service, files, objType, objId, filedName);
    }

    public static void saveFile(IFileManagerService service, List<FileDTO> files, String objType, String objId, String filedName) throws ServiceException {
        if (service == null) {
            throw new ParamInvalidException("文件服务不能为空");
        }
        ObjDTO objDTO = ObjDTO.of(objType, objId, filedName);
        ObjSaveFilesDTO dto = ObjSaveFilesDTO.of(objDTO, files);
        BaseUtils.checkDTO(dto);
        BaseUtils.setUserInfoToDTO(dto);
        service.saveFiles(dto);
    }

    public static void deleteFile(IFileManagerService service, String objType, String objId, String filedName) throws ServiceException {
        if (service == null) {
            throw new ParamInvalidException("文件服务不能为空");
        }
        ObjDTO objDTO = ObjDTO.of(objType, objId, filedName);
        BaseUtils.checkDTO(objDTO);
        BaseUtils.setUserInfoToDTO(objDTO);
        service.deleteFileByObj(objDTO);
    }
}
