package com.trs.gov.file.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 上传文件DTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-17 23:33
 * @version 1.0
 * @since 1.0
 */
@Data
public class UploadFileDTO extends BaseDTO {

    /**
     * 文件描述
     */
    private String filedesc;

    /**
     * 上传的文件
     */
    private MultipartFile file;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (file == null || file.isEmpty()) {
            throw new ParamInvalidException("上传文件不能为空！");
        }
        return true;
    }
}
