package com.trs.gov.file.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

/**
 * @ClassName：DownLoadFileDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/1 16:02
 **/
@Data
public class DownLoadFileDTO extends BaseDTO {

    /**
     * 文件名
     */
    private String filename;

    @Override
    public boolean isValid() throws ServiceException {
        if(CMyString.isEmpty(filename)){
            throw new ServiceException("文件名称不能为空!");
        }
        return true;
    }
}
