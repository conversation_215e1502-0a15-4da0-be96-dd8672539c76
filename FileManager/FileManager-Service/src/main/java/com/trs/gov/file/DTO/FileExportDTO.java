package com.trs.gov.file.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

@Data
public class FileExportDTO extends BaseDTO {

    /**
     * 文件路径标识
     */
    private Integer pathFlag;

    /**
     * 文件描述
     */
    private String filedesc;

    /**
     * 文件后缀
     */
    private String fileext;

    /**
     * 文件大小
     */
    private Long filesize;

    /**
     * 文件路径（本地路径或HTTP链接）
     */
    private String filePath;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (pathFlag == null) {
            throw new ParamInvalidException("路径标识不能为空！");
        }
        if (CMyString.isEmpty(filePath)) {
            throw new ParamInvalidException("文件路径不能为空！");
        }
        return true;
    }
}
