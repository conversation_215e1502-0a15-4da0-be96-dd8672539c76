package com.trs.gov.file.VO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.trs.gov.core.VO.BaseVO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

import java.util.Optional;

@Data
public class FileVO extends BaseVO {
    /**
     * 文件名
     */
    private String filename;
    /**
     * 文件描述
     */
    private String filedesc;
    /**
     * 文件后缀
     */
    private String fileext;

    @JsonIgnore
    private Long id;


    /**
     * 文件大小
     */
    private Long filesize;

    /**
     * url
     */
    private String url;
    /**
     * 下载列表 url
     */
    private String downloadUrl;


    public static FileVO of(String fileName, String fileDesc, String fileExt, Long fileSize, String url) throws ServiceException {
        FileVO vo = new FileVO();
        if (CMyString.isEmpty(fileName)) {
            throw new ParamInvalidException("文件名不能为空！");
        }
        if (CMyString.isEmpty(fileExt)) {
            throw new ParamInvalidException("文件后缀不能为空！");
        }
        if (CMyString.isEmpty(url)) {
            throw new ParamInvalidException("文件url不能为空！");
        }
        vo.filename = fileName;
        vo.fileext = fileExt;
        vo.url = url;
        vo.filedesc = CMyString.showEmpty(fileDesc, fileName);
        vo.filesize = Optional.ofNullable(fileSize).orElse(0L);
        return vo;
    }
}
