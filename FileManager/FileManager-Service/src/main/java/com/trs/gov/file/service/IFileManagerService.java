package com.trs.gov.file.service;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.file.DTO.*;
import com.trs.gov.file.VO.DownloadFileVO;
import com.trs.gov.file.VO.FileVO;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-18 9:56
 * @version 1.0
 * @since 1.0
 */
public interface IFileManagerService {
    /**
     * 上传文件<BR>
     *
     * @param dto 上传的文件
     * @return 上传结果（返回的文件是是临时文件）
     * @throws ServiceException 上传的异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 23:38
     */
    public FileVO uploadFile(UploadFileDTO dto) throws ServiceException;

    /**
     * 迁移上传文件<BR>
     *
     * @param dto 迁移上传的文件
     * @return 上传结果（返回的文件是是临时文件）
     * @throws ServiceException 上传的异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-24 18:21
     */
    public FileVO uploadFile(FileExportDTO dto) throws ServiceException;

    /**
     * 保存相关文件（uploadFile上传得到的是临时文件，需要通过方法永久保存）<BR>
     *
     * @param dto 待保存的文件信息
     * @return 保存后的结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 23:41
     */
    public FileVO saveOrUpdateOneFile(SaveOrQueryFileDTO dto) throws ServiceException;

    /**
     * 保存对象文件列表<BR>
     *
     * @param dto 请求数据
     * @return 保存情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 18:11
     */
    public Report saveFiles(ObjSaveFilesDTO dto) throws ServiceException;


    /**
     * 删除对象下的相关文件记录<BR>
     *
     * @param dto 对象信息
     * @return 删除情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 18:01
     */
    public Report deleteFileByObj(ObjDTO dto) throws ServiceException;

    /**
     * 根据文件名获取文件信息<BR>
     *
     * @param dto 文件名
     * @return 文件信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 23:41
     */
    public FileVO getFileByName(FileDTO dto) throws ServiceException;

    /**
     * 根据文件名和对象获取相关图片（该方法从数据库中捞取相关文件对象，不判断存在性）<BR>
     *
     * @param dto 请求信息
     * @return 文件信息
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 17:00
     */
    public FileVO getFileByNameAndObj(SaveOrQueryFileDTO dto) throws ServiceException;

    /**
     * 根据对象获取对应的文件列表<BR>
     *
     * @param dto 对象
     * @return 文件列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-18 9:47
     */
    public List<FileVO> getFileListOfObj(ObjDTO dto) throws ServiceException;
    /**
     * @Description 下载文件
     * @Param [dto, response]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/2 16:48
     **/
    public DownloadFileVO downloadFile(DownLoadFileDTO dto) throws ServiceException;
}
