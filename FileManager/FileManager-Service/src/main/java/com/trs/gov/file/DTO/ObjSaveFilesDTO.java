package com.trs.gov.file.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import lombok.Data;

import java.util.List;

@Data
public class ObjSaveFilesDTO extends BaseDTO {

    /**
     * 文件列表
     */
    private List<FileDTO> files;

    /**
     * 对象信息
     */
    private ObjDTO obj;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (files == null || files.isEmpty()) {
            throw new ParamInvalidException("文件列表不能为空");
        }
        for (FileDTO fileDTO : files) {
            if (!fileDTO.isValid()) {
                throw new ParamInvalidException("文件请求数据异常！");
            }
        }
        if (obj == null || !obj.isValid()) {
            throw new ParamInvalidException("对象信息异常！");
        }
        return true;
    }

    public static ObjSaveFilesDTO of(ObjDTO obj, List<FileDTO> files) throws ServiceException {
        ObjSaveFilesDTO dto = new ObjSaveFilesDTO();
        dto.setFiles(files);
        dto.setObj(obj);
        BaseUtils.checkDTO(dto);
        return dto;
    }
}
