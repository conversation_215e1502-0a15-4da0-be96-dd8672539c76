package com.trs.gov.file.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.file.DTO.FileDTO;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.VO.FileVO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Data
@TableName("appendix_table")
public class FileDO extends BaseDO {
    /**
     * 文件名
     */
    @Column(name = "file_name")
    @TableField("file_name")
    private String filename;
    /**
     * 文件描述
     */
    @Column(name = "file_desc")
    @TableField("file_desc")
    private String filedesc;
    /**
     * 文件后缀
     */
    @Column(name = "file_ext")
    @TableField("file_ext")
    private String fileext;

    /**
     * 文件所属对象的类型
     */
    @Column(name = "obj_type")
    @TableField("obj_type")
    private String objtype;

    /**
     * 文件所属对象ID
     */
    @Column(name = "obj_id")
    @TableField("obj_id")
    private String objid;

    /**
     * 文件可能属于对象的不同字段，所以增加该字段标识
     */
    @Column(name = "field_name")
    @TableField("field_name")
    private String fieldname;

    /**
     * 文件大小
     */
    @Column(name = "file_size")
    @TableField("file_size")
    private Long filesize;

    public void putValues(FileDTO fileDTO, ObjDTO objDTO) throws ServiceException {
        BaseUtils.checkDTO(fileDTO, objDTO);
        String[] ignore = BaseUtils.getAllFieldInClass(BaseDO.class);
        BaseUtils.copyProperties(fileDTO, this, ignore);
        BaseUtils.copyProperties(objDTO, this, ignore);
    }

    public FileVO convertToVO() {
        FileVO vo = new FileVO();
        BaseUtils.copyProperties(this, vo);
        return vo;
    }
}
