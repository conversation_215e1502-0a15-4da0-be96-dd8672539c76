package com.trs.gov.file.DO;

import lombok.Getter;

@Getter
public class PathConfig {

    /**
     * 本地路径
     */
    private String localDir;

    /**
     * URL前缀
     */
    private String urlPreFix;

    private PathConfig() {

    }

    public static PathConfig of(String localDir, String urlPreFix) {
        PathConfig pathConfig = new PathConfig();
        pathConfig.urlPreFix = urlPreFix;
        pathConfig.localDir = localDir;
        return pathConfig;
    }
}
