package com.trs.gov.file.mgr.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.Report;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyFile;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.file.DO.FileDO;
import com.trs.gov.file.DTO.*;
import com.trs.gov.file.VO.DownloadFileVO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.constant.FileConstant;
import com.trs.gov.file.mapper.FileMapper;
import com.trs.gov.file.mgr.IFileMgr;
import com.trs.gov.file.util.FileUtils;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件业务类
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-21 14:45
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class FileMgr implements IFileMgr {

    @Autowired
    private FileMapper fileMapper;

    @Autowired
    private FileUtils fileUtils;

    @Value("${FileManagerService.tempUrlPrefix:}")
    private String tempUrlPrefix;

    private HttpRequest httpRequest = new HttpRequest.Builder(new OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES)
            .readTimeout(1, TimeUnit.MINUTES)
    ).setUseConnectionClose(true).setSkipSSLCheck(true).build();

    /**
     * 上传文件<BR>
     *
     * @param dto 请求参数
     * @return 上传结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 14:41
     */
    @Override
    public Optional<FileVO> uploadFile(UploadFileDTO dto) throws ServiceException {
        MultipartFile file = dto.getFile();
        long size = file.getSize();
        String originalFileName = dto.getFile().getOriginalFilename();
        String contentType = dto.getFile().getContentType();
        String fileExt = CMyFile.extractFileExt(originalFileName);
        if (CMyString.isEmpty(fileExt)) {
            fileExt = contentType.substring(contentType.indexOf("/") + 1);
        }
        String tempFileName = fileUtils.getNextFlieName(FileConstant.FLAG_UPLOAD, fileExt, true);
        if (CMyString.isEmpty(tempFileName)) {
            throw new ParamInvalidException("无法获取到保存的文件路径！");
        }
        try {
            file.transferTo(new File(tempFileName));
        } catch (IOException e) {
            log.error("文件上传失败！", e);
            throw new ServiceException("文件上传失败！", e);
        }
        String fileName = CMyFile.extractFileName(tempFileName);
        Optional<FileVO> fileVOOptional = getFileByName(FileDTO.of(fileName, dto.getFiledesc(), fileExt, size));
        FileVO fileVO =  fileVOOptional.get();
        if (fileVO==null){
            throw new ServiceException("文件上传失败！");
        }
        String url = fileVO.getUrl();
        if (StringUtils.isNullOrEmpty(url)==false){
            fileVO.setUrl(tempUrlPrefix + url.substring(url.indexOf("/upload")));
        }
        return Optional.of(fileVO);
    }

    /**
     * 迁移上传文件<BR>
     *
     * @param dto 迁移上传的文件
     * @return 上传结果（返回的文件是是临时文件）
     * @throws ServiceException 上传的异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-24 18:21
     */
    @Override
    public Optional<FileVO> uploadFile(FileExportDTO dto) throws ServiceException {
        String fileExt = dto.getFileext();
        if (CMyString.isEmpty(fileExt)) {
            throw new ParamInvalidException("后缀名不能为空！");
        }
        String tempFileName = fileUtils.getNextFlieName(FileConstant.FLAG_UPLOAD, fileExt, true);
        switch (dto.getPathFlag()) {
            case FileConstant.PATH_LOCAL:
                CMyFile.copyFile(dto.getFilePath(), tempFileName);
                break;
            case FileConstant.PATH_HTTP:
                Try.of(() -> {
                    InputStream input = httpRequest.doGetStream(dto.getFilePath());
                    File file = new File(tempFileName);
                    OutputStream out = new FileOutputStream(file);
                    int len;
                    byte[] bytes = new byte[1024];
                    while ((len = input.read(bytes)) != -1) {
                        out.write(bytes, 0, len);
                    }
                    out.flush();
                    out.close();
                    input.close();
                    return 0;
                }).getOrElseThrow(err -> new ServiceException("下载文件失败！", err));
                break;
            default:
                throw new ParamInvalidException("路径标识异常！");
        }
        Long size = Optional.ofNullable(dto.getFilesize()).orElse(0L);
        if (size <= 0) {
            size = new File(tempFileName).length();
        }
        String fileName = CMyFile.extractFileName(tempFileName);
        return getFileByName(FileDTO.of(fileName, dto.getFiledesc(), fileExt, size));
    }

    /**
     * 保存文件<BR>
     *
     * @param dto 请求参数
     * @return 保存结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 14:41
     */
    @Override
    public Optional<FileVO> saveOrUpdateOneFile(SaveOrQueryFileDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        FileDTO fileDTO = dto.getFile();
        ObjDTO objDTO = dto.getObj();
        FileDO fileDO = new FileDO();
        String oldFileName = CMyFile.extractFileName(fileDTO.getFilename());
        if (!CMyFile.fileExists(fileUtils.mapFilePath(oldFileName, FileConstant.PATH_LOCAL) + oldFileName)) {
            throw new ParamInvalidException("文件" + oldFileName + "不存在，保存失败！");
        }
        String fileFlag = fileUtils.getFileFlag(oldFileName).orElseThrow(() -> new ParamInvalidException("根据文件名" + oldFileName + "无法获取其文件标识"));
        if (FileConstant.FLAG_UPLOAD.equalsIgnoreCase(fileFlag)) {
            String newFileName = fileUtils.getNextFlieName(FileConstant.FLAG_WEBFILE, CMyFile.extractFileExt(fileDTO.getFilename()), true);
            CMyFile.copyFile(fileUtils.mapFilePath(oldFileName, FileConstant.PATH_LOCAL) + oldFileName, newFileName);
            fileDTO.setFilename(CMyFile.extractFileName(newFileName));
        } else {
            Optional<FileVO> fileVO = getFileByNameAndObj(dto);
            if (fileVO.isPresent()) {
                fileDO.setId(fileVO.get().getId());
            }
        }
        fileDO.putValues(fileDTO, objDTO);
        if (fileDO.getId() == null) {
            fileMapper.insert(fileDO);
        } else {
            fileMapper.updateById(fileDO);
        }
        return getFileByName(fileDTO);
    }

    /**
     * 根据文件名获取文件信息（该方法只是构造相关文件对象，不判断存在性）<BR>
     *
     * @param dto 请求参数
     * @return 文件信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 14:41
     */
    @Override
    public Optional<FileVO> getFileByName(FileDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        String fileName = CMyFile.extractFileName(CMyString.showEmpty(dto.getFilename()));
        return Optional.ofNullable(FileVO.of(fileName, dto.getFiledesc(), CMyString.showEmpty(dto.getFileext(), CMyFile.extractFileExt(fileName)), dto.getFilesize(),
                fileUtils.mapFilePath(fileName, FileConstant.PATH_HTTP) + fileName));
    }

    /**
     * @return java.util.Optional<com.trs.gov.file.VO.FileVO>
     * @Description 根据fileName查询文件数据
     * @Param [dto]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/12/1 17:26
     **/
    @Override
    public Optional<FileVO> getFileVOByfileName(DownLoadFileDTO dto) throws ServiceException {
        List<FileDO> fileDOS = fileMapper.selectList(new QueryWrapper<FileDO>().eq("file_name", dto.getFilename()));
        if (CollectionUtils.isEmpty(fileDOS)) {
            return Optional.empty();
        }
        FileVO fileVO = new FileVO();
        BaseUtils.copyProperties(fileDOS.get(0), fileVO);
        return Optional.of(fileVO);
    }

    /**
     * 根据文件名和对象获取相关图片（该方法从数据库中捞取相关文件对象，不判断存在性）<BR>
     *
     * @param dto 请求信息
     * @return 文件信息
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 17:00
     */
    @Override
    public Optional<FileVO> getFileByNameAndObj(SaveOrQueryFileDTO dto) throws ServiceException {
        FileDTO file = dto.getFile();
        ObjDTO obj = dto.getObj();
        BaseUtils.checkDTO(file, obj);
        QueryWrapper<FileDO> query = new QueryWrapper<>();
        query.eq("file_name", file.getFilename())
                .eq("obj_id", obj.getObjid())
                .eq("obj_type", obj.getObjtype())
                .eq("field_name", obj.getFieldname());
        return Optional.ofNullable(fileMapper.selectOne(query)).map(item -> {
            FileVO vo = item.convertToVO();
            try {
                vo.setUrl(fileUtils.mapFilePath(item.getFilename(), FileConstant.PATH_HTTP) + item.getFilename());
            } catch (ServiceException e) {
                log.error("文件URL转换异常", e);
            }
            return vo;
        });
    }

    /**
     * 根据对象信息获取文件列表<BR>
     *
     * @param obj 请求参数
     * @return 文件列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 14:41
     */
    @Override
    public List<FileVO> getFileListOfObj(ObjDTO obj) throws ServiceException {
        BaseUtils.checkDTO(obj);
        QueryWrapper<FileDO> query = new QueryWrapper<>();
        query.eq("obj_id", obj.getObjid())
                .eq("obj_type", obj.getObjtype())
                .eq("field_name", obj.getFieldname());
        return fileMapper.selectList(query).stream().map(item -> {
            FileVO vo = item.convertToVO();
            try {
                vo.setUrl(fileUtils.mapFilePath(item.getFilename(), FileConstant.PATH_HTTP) + item.getFilename());
                vo.setDownloadUrl(fileUtils.getDownloadUrl(item.getFilename(), obj));
            } catch (ServiceException e) {
                log.error("文件URL转换异常", e);
            }
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 保存对象文件列表(会删除原来已存在数据记录)<BR>
     *
     * @param dto 请求数据
     * @return 保存情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 18:11
     */
    @Override
    public Report saveFiles(ObjSaveFilesDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        Report one = deleteFileByObj(dto.getObj());
        if (one == null || one.getResult() != Report.RESULT.SUCCESS) {
            if (one != null) {
                log.error(
                        "清除历史数据失败,ERR=[title={},DETAIL={}]"
                        , one.getTitle()
                        , one.getDetail()
                        , new Exception("who call me！")
                );
            }
            throw new ServiceException("清除历史数据失败!");
        }
        for (FileDTO fileDTO : dto.getFiles()) {
            saveOrUpdateOneFile(SaveOrQueryFileDTO.of(fileDTO, dto.getObj()));
        }
        return new Report("数据保存", "成功保存数据！", Report.RESULT.SUCCESS);
    }

    /**
     * 删除对象下的相关文件记录<BR>
     *
     * @param dto 对象信息
     * @return 删除情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 18:01
     */
    @Override
    public Report deleteFileByObj(ObjDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        QueryWrapper<FileDO> query = new QueryWrapper<>();
        query.eq("obj_id", dto.getObjid())
                .eq("obj_type", dto.getObjtype())
                .eq("field_name", dto.getFieldname());
        fileMapper.delete(query);
        return new Report("数据删除", "成功删除数据！", Report.RESULT.SUCCESS);
    }

    @Override
    public DownloadFileVO downloadFile(DownLoadFileDTO dto) throws ServiceException {
        if (fileUtils.checkDownLoadAllow(dto.getFilename())) {
            FileVO fileVO = this.getFileVOByfileName(dto).orElseThrow(() -> new ServiceException("不存在文件名为【" + dto.getFilename() + "】的文件信息!"));
            String filePath = fileUtils.mapFilePath(fileVO.getFilename(), FileConstant.PATH_LOCAL) + fileVO.getFilename();
            File file = new File(filePath);
            byte[] bytes = fileUtils.downloadFile(file, CMyString.showEmpty(fileVO.getFiledesc(), dto.getFilename()));
            if (bytes == null) {
                throw new ServiceException("文件内容为空!");
            }
            String loginUser = ContextHelper.getLoginUser().orElseThrow(() -> new ServiceException("获取当前登录用户的用户名失败!"));
            log.info("用户【"+loginUser+","+dto.getToken()+"】下载文件【"+fileVO.getFilename()+","+fileVO.getFiledesc()+"】成功!");
            return new DownloadFileVO(bytes, fileVO.getFiledesc() + "." + fileVO.getFileext(), file, true);
        } else {
            throw new ParamInvalidException(dto.getFilename() + "不支持下载");
        }
    }

    /**
     * 相关Key<BR>
     *
     * @return 类的Key
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String key() {
        return "FileMgr";
    }

    /**
     * 相关描述<BR>
     *
     * @return 类功能的描述
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String desc() {
        return "FileMgr";
    }
}
