package com.trs.gov.file.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.file.DTO.DownLoadFileDTO;
import com.trs.gov.file.DTO.UploadFileDTO;
import com.trs.gov.file.VO.DownloadFileVO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Optional;

@RestController
@RequestMapping("/file")
@Slf4j
public class FileManagerController {
    @Autowired
    private IFileManagerService service;

    @PostMapping("/upload")
    public RestfulResults<FileVO> uploadFile(UploadFileDTO dto) {
        return Try.of(() -> {
            BaseUtils.checkDTO(dto);
            return RestfulResults.ok(Optional.ofNullable(service.uploadFile(dto)).orElseThrow(() -> new ServiceException("文件上传失败！"))).addMsg("上传成功！");
        }).getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @GetMapping(value = "downloadFile")
    @ResponseBody
    public void downloadFile(DownLoadFileDTO dto, HttpServletResponse response) throws ServiceException {
        BaseUtils.checkDTO(dto);
        downLoad(response,service.downloadFile(dto));
    }

    public void downLoad(HttpServletResponse response, DownloadFileVO vo) {
        OutputStream out = null;
        try{
            if(vo.getIsSuccess()){
                response.setHeader("content-disposition", "attachment;filename=" + new String(vo.getOrginalFileName().getBytes("utf-8"),"ISO8859-1"));
                response.setHeader("Content-Length", "" + vo.getFile().length());
                response.setHeader("Content-type", "application/octet-stream");
            }else{
                response.setHeader("Content-type", "text/html;charset=UTF-8");
            }
            out = response.getOutputStream();
            out.write(vo.getFileByte());
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }finally {
            try {
                out.flush();
                out.close();
            } catch (IOException e) {
                log.error(e.getMessage(),e);
            }
        }
    }

}
