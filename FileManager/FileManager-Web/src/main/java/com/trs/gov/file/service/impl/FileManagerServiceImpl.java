package com.trs.gov.file.service.impl;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyFile;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.DTO.*;
import com.trs.gov.file.VO.DownloadFileVO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.mgr.IFileMgr;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.file.util.FileUtils;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-17 23:57
 * @version 1.0
 * @since 1.0
 */
@Service
@Primary
@Slf4j
public class FileManagerServiceImpl implements IFileManagerService {

    @Autowired
    private FileUtils fileUtils;

    @Autowired
    private IFileMgr fileMgr;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    /**
     * 上传文件<BR>
     *
     * @param dto 上传的文件
     * @return 上传结果（返回的文件是是临时文件）
     * @throws ServiceException 上传的异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 23:38
     */
    @Override
    public FileVO uploadFile(UploadFileDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        String originalFileName = dto.getFile().getOriginalFilename();
        String contentType = dto.getFile().getContentType();
        String fileExt = CMyFile.extractFileExt(originalFileName);
        if (CMyString.isEmpty(fileExt)) {
            fileExt = contentType.substring(contentType.indexOf("/") + 1);
        }
        if (!fileUtils.checkFileExtAllow(fileExt)) {
            throw new ParamInvalidException("不允许上传后缀名为" + fileExt + "的文件！");
        }
        return fileMgr.uploadFile(dto).orElseThrow(() -> new ServiceException("上传失败！"));
    }

    /**
     * 迁移上传文件<BR>
     *
     * @param dto 迁移上传的文件
     * @return 上传结果（返回的文件是是临时文件）
     * @throws ServiceException 上传的异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-24 18:21
     */
    @Override
    public FileVO uploadFile(FileExportDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        if (CMyString.isEmpty(dto.getFileext())){
            dto.setFileext(CMyFile.extractFileExt(dto.getFilePath()));
        }
        return fileMgr.uploadFile(dto).orElseThrow(() -> new ServiceException("上传失败！"));
    }

    /**
     * 保存相关文件（uploadFile上传得到的是临时文件，需要通过方法永久保存）<BR>
     *
     * @param dto 待保存的文件信息
     * @return 保存后的结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 23:41
     */
    @Override
    public FileVO saveOrUpdateOneFile(SaveOrQueryFileDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return fileMgr.saveOrUpdateOneFile(dto).orElseThrow(() -> new ServiceException("数据保存异常！"));
    }

    /**
     * 保存对象文件列表<BR>
     *
     * @param dto 请求数据
     * @return 保存情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 18:11
     */
    @Override
    public Report saveFiles(ObjSaveFilesDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return fileMgr.saveFiles(dto);
    }

    /**
     * 删除对象下的相关文件记录<BR>
     *
     * @param dto 对象信息
     * @return 删除情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 18:01
     */
    @Override
    public Report deleteFileByObj(ObjDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return fileMgr.deleteFileByObj(dto);
    }

    /**
     * 根据文件名获取文件信息（该方法只是构造相关文件对象，不判断存在性）<BR>
     *
     * @param dto 文件名
     * @return 文件信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 23:41
     */
    @Override
    public FileVO getFileByName(FileDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return fileMgr.getFileByName(dto).orElseThrow(() -> new ServiceException("数据异常"));
    }

    /**
     * 根据文件名和对象获取相关图片（该方法从数据库中捞取相关文件对象，不判断存在性）<BR>
     *
     * @param dto 请求信息
     * @return 文件信息
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 17:00
     */
    @Override
    public FileVO getFileByNameAndObj(SaveOrQueryFileDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return fileMgr.getFileByNameAndObj(dto).orElseThrow(() -> new ServiceException("数据异常"));
    }

    /**
     * 根据对象获取对应的文件列表<BR>
     *
     * @param dto 对象
     * @return 文件列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-18 9:47
     */
    @Override
    public List<FileVO> getFileListOfObj(ObjDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return fileMgr.getFileListOfObj(dto);
    }

    /**
     * @Description 下载文件，获取文件
     * @Param [dto, response]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/2 16:48
     **/
    @Override
    public DownloadFileVO downloadFile(DownLoadFileDTO dto){
        Try<DownloadFileVO> of = Try.of(() -> {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
            return fileMgr.downloadFile(dto);
        });
        if(of.isFailure()){
            log.error(of.getCause().getMessage(), of.getCause());
            return fileUtils.failDownLoad(of.getCause().getMessage());
        }
        return of.get();
    }
}
