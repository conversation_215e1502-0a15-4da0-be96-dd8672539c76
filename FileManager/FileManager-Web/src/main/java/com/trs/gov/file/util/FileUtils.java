package com.trs.gov.file.util;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.trs.common.exception.ExceptionNumber;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyFile;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.DO.PathConfig;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.VO.DownloadFileVO;
import com.trs.gov.file.constant.FileConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Optional;

import static com.trs.gov.file.constant.FileConstant.*;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 文件工具
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-18 16:39
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class FileUtils {
    /**
     * 文件上传到本地的路径
     */
    @Value("${FileManagerService.UploadDir}")
    private String uploadDir;
    /**
     * 文件的Url前缀
     */
    @Value("${FileManagerService.UrlPrefix}")
    private String urlPrefix;
    /**
     * 文件的Url前缀
     */
    @Value("${FileManagerService.downloadUrlPrefix}")
    private String downloadUrlPrefix;

    /**
     * 允许上传的文件后缀
     */
    @Value("${FileManagerService.AllowFileExts}")
    private String allowFileExts;

    private final HashMap<String, PathConfig> m_hPathConfig;

    public FileUtils() {
        m_hPathConfig = new HashMap(2);
    }

    private void loadFilesMan() {
        m_hPathConfig.put(FLAG_UPLOAD, PathConfig.of(uploadDir + File.separator + "upload" + File.separator, urlPrefix + "/upload/"));
        m_hPathConfig.put(FLAG_WEBFILE, PathConfig.of(uploadDir + File.separator + "webpic" + File.separator, urlPrefix + "/webpic/"));
    }


    public Optional<String> getFileFlag(String _sFileName) {

        String sFlag = null;

        // 校验参数
        if (CMyString.isEmpty(_sFileName)) {
            return Optional.empty();
        }

        _sFileName = CMyFile.extractFileName(_sFileName);

        // 根据头返回类型
        String sFileHeader = _sFileName.substring(0, 2);

        if (sFileHeader.equals(FLAG_UPLOAD)) {
            sFlag = FLAG_UPLOAD;
        }

        if (sFileHeader.equals(FLAG_WEBFILE)) {
            sFlag = FLAG_WEBFILE;
        }

        return Optional.ofNullable(sFlag);
    }

    public HashMap<String, PathConfig> getPathFlag() throws ServiceException {
        if (StringUtils.isNullOrEmpty(uploadDir)) {
            throw new ParamInvalidException("本地上传路径不能为空！");
        }
        if (StringUtils.isNullOrEmpty(urlPrefix)) {
            throw new ParamInvalidException("Url前缀不能为空！");
        }
        if (m_hPathConfig ==null
                || m_hPathConfig.isEmpty()
                || m_hPathConfig.get("W0")==null
                || m_hPathConfig.get("U0") ==null) {
            loadFilesMan();
        }
        return m_hPathConfig;
    }

    public boolean checkFileExtAllow(String fileExt) throws ServiceException {
        if (CMyString.isEmpty(fileExt)) {
            throw new ParamInvalidException("文件名后缀不能为空！");
        }
        if (CMyString.isEmpty(allowFileExts)) {
            throw new ParamInvalidException("FileManagerService.AllowFileExts属性未配置！");
        }
        return Arrays.stream(CMyString.split(allowFileExts, ";")).filter(item -> CMyString.isEmpty(item) == false && item.equalsIgnoreCase(fileExt)).count() > 0;
    }

    public boolean checkDownLoadAllow(String fileName) throws ParamInvalidException {
        if (CMyString.isEmpty(fileName)) {
            throw new ParamInvalidException("文件名不能为空!");
        }
        String flag = getFileFlag(fileName).orElseThrow(() -> new ParamInvalidException(fileName + "非法文件名！"));
        if (!FileConstant.FLAG_WEBFILE.equalsIgnoreCase(flag)) {
            throw new ParamInvalidException(fileName + "该类型的文件不支持下载!");
        }
        return true;
    }

    public synchronized String getNextFlieName(String flag, String fileExt) throws ServiceException {
        return getNextFlieName(flag, fileExt, false);
    }

    public synchronized String getNextFlieName(String flag, String fileExt, boolean _bIncludePath) throws ServiceException {
        return getNextFlieName(flag, fileExt, new Date(), _bIncludePath);
    }

    public synchronized String getNextFlieName(String flag, String fileExt, Date _crTime) throws ServiceException {
        return getNextFlieName(flag, fileExt, _crTime, false);
    }

    public synchronized String getNextFlieName(String _sPathFlag, String _sFileExt, Date _crTime, boolean _bIncludePath) throws ServiceException {
        // 安全性检查：_sFileExt不能含有..信息
        if (_sFileExt.indexOf("..") >= 0) {
            throw new ParamInvalidException("非法文件后缀信息，不能获取相应的文件名");
        }
        _sFileExt = _sFileExt.replace("?", "");
        if (_sFileExt.length() > 8) {
            throw new ParamInvalidException("不是常规后缀信息，不能获取相应的文件名");
        }
        if (StringUtils.isNullOrEmpty(_sPathFlag)) {
            throw new ParamInvalidException("文件标识不能为空！");
        }
        _sPathFlag = _sPathFlag.trim().toUpperCase();
        String pathConfig = getPathConfig(_sPathFlag).getLocalDir();
        if (StringUtils.isNullOrEmpty(pathConfig)) {
            throw new ParamInvalidException("无效的文件标识！");
        }

        String sDate, sTime, sRandom;
        long lTime;
        String sFilePath, sFileName, sFileExt; // 构造的文件名（含扩展名）
        int i;
        _crTime = Optional.ofNullable(_crTime).orElse(new Date());
        sDate = TimeUtils.dateToString(_crTime, TimeUtils.YYYYMMDD5);
        sFilePath = pathConfig + _sPathFlag
                + sDate.substring(0, 6) + File.separatorChar + _sPathFlag
                + sDate.substring(0, 8) + File.separatorChar;
        try {
            if (!CMyFile.fileExists(sFilePath)) {
                CMyFile.makeDir(sFilePath, true);
            }
        } catch (Exception e) {
            log.error("文件路径" + sFilePath + "创建失败！", e);
            throw new ServiceException(ExceptionNumber.ERR_PARAM_INVALID, "文件路径" + sFilePath + "创建失败！");
        }

        lTime = (_crTime.getTime() + TIMEZONE_RAWOFFSET) % ADAY_MILLIS;
        sTime = CMyString.numberToStr(lTime, 8, '0');

        // 取4位随机数
        sRandom = CMyString.numberToStr(Math.round(Math.random() * 10000), 4,
                '0');

        // 处理扩展名
        sFileExt = _sFileExt.trim();
        // 检查第一个字符是否为'.'
        if ((sFileExt.length() > 0) && (sFileExt.charAt(0) != '.')) {
            sFileExt = "." + sFileExt;
        }

        // 构造文件名称
        // 在检查文件名重复时，最多允许构造两次。
        sFileName = _sPathFlag + sDate + sTime + sRandom;
        for (i = 0; i < 2; i++) {
            if (i > 0) {
                // 扩展随机数以解决重复问题，每次向后扩展两位
                sFileName += CMyString.numberToStr(
                        Math.round(Math.random() * 100), 2, '0');
            }
            // 检查文件名是否重复
            if (!CMyFile.fileExists(sFilePath + sFileName + sFileExt)) {
                // 找到不重复的文件名
                return (_bIncludePath ? sFilePath : "") + sFileName + sFileExt;
            }
        }// end for
        return null;
    }


    /**
     * 取指定目录标识的配置信息
     *
     * @param _sPathFlag 目录标志（和文件标志为同一标志，FLAG_NORMAL等常量）
     * @return 指定目录标识的配置信息
     */
    public PathConfig getPathConfig(String _sPathFlag) throws ServiceException {
        PathConfig config = getPathFlag().get(_sPathFlag);
        if (config == null) {
            throw new ParamInvalidException(_sPathFlag + "不存在对应的目录配置");
        }
        return config;
    }

    /**
     * 目录映射：根据文件名映射文件所在的路径
     * <p>
     * 文件所在路径
     * <p>
     * 说明：不检查文件路径是否真正存在
     *
     * @param _sFileName 文件名
     * @param _nPathType 目录类型
     * @return 根据文件名映射文件所在的路径
     * @throws ServiceException
     */
    public String mapFilePath(String _sFileName, int _nPathType)
            throws ServiceException {
        if (_sFileName == null) {
            throw new ServiceException(ExceptionNumber.ERR_PARAM_INVALID, "文件名为空");
        }

        // 检查文件名长度是否符合最小文件长度
        _sFileName = _sFileName.trim();

        // add by liuhm@20140114 安全性问题处理，需要先判断再获取文件名
        if (_sFileName.indexOf("..") >= 0) {
            throw new ServiceException(ExceptionNumber.ERR_PARAM_INVALID, "[" + _sFileName + "]无效的文件格式");
        }

        _sFileName = CMyFile.extractFileName(_sFileName);

        if (_sFileName.length() < FILENAME_MIN_LENGTH) {
            throw new ServiceException(ExceptionNumber.ERR_PARAM_INVALID, "[" + _sFileName + "]无效的文件格式");
        }

        // 取文件存储目录配置信息，并检查类型标识（前2位）是否正确
        PathConfig pathConfig = getPathConfig(_sFileName.substring(0, 2));
        if (pathConfig == null) {
            throw new ServiceException(ExceptionNumber.ERR_PARAM_INVALID, "_sFileName:[" + _sFileName + "]，文件格式不匹配：类型标识无效");
        }

        // 构造文件路径
        String sPath; // 文件路径
        char chrPathSeparator = (_nPathType == PATH_LOCAL ? File.separatorChar
                : '/'); // 路径分割符
        switch (_nPathType) {
            case PATH_LOCAL: {
                sPath = pathConfig.getLocalDir();
                sPath = getPath(sPath, _sFileName, chrPathSeparator);
                break;
            }
            case PATH_HTTP: {
                sPath = pathConfig.getUrlPreFix();
                sPath = getPath(sPath, _sFileName, chrPathSeparator);
                break;
            }
            default:
                throw new ServiceException(ExceptionNumber.ERR_PARAM_INVALID, "无效的路径类别");
        }
        return sPath;
    }

    public String getPath(String sPath, String _sFileName, char chrPathSeparator) {
        sPath += _sFileName.substring(0, 8) + chrPathSeparator
                + _sFileName.substring(0, 10) + chrPathSeparator;
        return sPath;
    }

    /**
     * @return java.lang.String
     * @Description 获取下载地址
     * @Param [fileName, obj]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/12/3 17:12
     **/
    public String getDownloadUrl(String fileName, ObjDTO obj) {
        return downloadUrlPrefix + "?" + getParamStr(fileName, obj);
    }

    private String getParamStr(String fileName, ObjDTO obj) {
        return String.format("filename=%s&token=%s&unitId=%s", fileName, obj.getToken(), obj.getUnitId());
    }

    public byte[] downloadFile(File file, String orginalName) throws ServiceException {
        FileInputStream fileInputStream = null;
        BufferedInputStream bufferedInputStream = null;
        byte[] buffer = null;
        try {
            if (!file.exists()) {
                throw new ServiceException("不存在文件【" + orginalName + "】");
            }
            fileInputStream = new FileInputStream(file);
            bufferedInputStream = new BufferedInputStream(fileInputStream);
            buffer = new byte[fileInputStream.available()];
            fileInputStream.read(buffer);
            fileInputStream.close();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        } finally {
            if (ObjectUtils.isNotNull(fileInputStream)) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                    throw new ServiceException(e.getMessage());
                }
            }
            if (ObjectUtils.isNotNull(bufferedInputStream)) {
                try {
                    bufferedInputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                    throw new ServiceException(e.getMessage());
                }
            }
            return buffer;
        }
    }

    public DownloadFileVO failDownLoad(String msg) {
        byte[] bytes = null;
        String errorMsg = null;
        try {
            errorMsg = "下载文件失败! " + msg;
            bytes = errorMsg.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        } finally {
            return new DownloadFileVO(bytes, false);
        }
    }

}
