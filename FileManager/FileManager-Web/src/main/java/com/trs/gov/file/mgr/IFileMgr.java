package com.trs.gov.file.mgr;

import com.trs.common.base.Report;
import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.file.DTO.*;
import com.trs.gov.file.VO.DownloadFileVO;
import com.trs.gov.file.VO.FileVO;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public interface IFileMgr extends IKey {
    /**
     * 上传文件<BR>
     *
     * @param dto 请求参数
     * @return 上传结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 14:41
     */
    public Optional<FileVO> uploadFile(UploadFileDTO dto) throws ServiceException;

    /**
     * 迁移上传文件<BR>
     *
     * @param dto 迁移上传的文件
     * @return 上传结果（返回的文件是是临时文件）
     * @throws ServiceException 上传的异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-24 18:21
     */
    public Optional<FileVO> uploadFile(FileExportDTO dto) throws ServiceException;

    /**
     * 保存文件<BR>
     *
     * @param dto 请求参数
     * @return 保存结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 14:41
     */
    public Optional<FileVO> saveOrUpdateOneFile(SaveOrQueryFileDTO dto) throws ServiceException;

    /**
     * 根据文件名获取文件信息（该方法只是构造相关文件对象，不判断存在性）<BR>
     *
     * @param dto 请求参数
     * @return 文件信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 14:41
     */
    public Optional<FileVO> getFileByName(FileDTO dto) throws ServiceException;
    /**
     * @Description  根据fileName获取文件的 路径【FileVO】
     * @Param [dto]
     * @return java.util.Optional<com.trs.gov.file.VO.FileVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/1 16:17
     **/
    public Optional<FileVO> getFileVOByfileName(DownLoadFileDTO dto) throws ServiceException;

    /**
     * 根据文件名和对象获取相关图片（该方法从数据库中捞取相关文件对象，不判断存在性）<BR>
     *
     * @param dto 请求信息
     * @return 文件信息
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 17:00
     */
    public Optional<FileVO> getFileByNameAndObj(SaveOrQueryFileDTO dto) throws ServiceException;

    /**
     * 根据对象信息获取文件列表<BR>
     *
     * @param dto 请求参数
     * @return 文件列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 14:41
     */
    public List<FileVO> getFileListOfObj(ObjDTO dto) throws ServiceException;

    /**
     * 保存对象文件列表<BR>
     *
     * @param dto 请求数据
     * @return 保存情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 18:11
     */
    public Report saveFiles(ObjSaveFilesDTO dto) throws ServiceException;


    /**
     * 删除对象下的相关文件记录<BR>
     *
     * @param dto 对象信息
     * @return 删除情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 18:01
     */
    public Report deleteFileByObj(ObjDTO dto) throws ServiceException;

    /**
     * @Description  获取文件名称  和  文件字节
     * @Param [dto, response]
     * @return com.trs.gov.file.VO.DownloadFileVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/3 17:24
     **/
    public DownloadFileVO downloadFile(DownLoadFileDTO dto) throws ServiceException, IOException;
}
