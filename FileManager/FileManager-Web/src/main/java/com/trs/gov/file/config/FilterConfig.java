package com.trs.gov.file.config;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.user.filter.LoginFilter;
import com.trs.user.service.IUserService;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

@Configuration
@Slf4j
public class FilterConfig {

    @Reference(check = false, timeout = 60000)
    private IUserService service;

    @Bean
    public FilterRegistrationBean registFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        LoginFilter filter = new LoginFilter(service);
        filter.setSkipFilter((request,response)->{
            if(request.getRequestURI().contains("file/downloadFile")){
                String token = request.getParameter("token");
                String unitId = request.getParameter("unitId");
                try {
                    boolean result = Optional.ofNullable(service.getLoginUserInfoByToken(token)).map(item ->
                            Try.of(() -> {
                                ContextHelper.initContext(token, item.getUserName(), unitId);
                                return true;
                            }).getOrElse(false)
                    ).orElse(false);
                    if(!result){
                        throw new ServiceException("系统异常!");
                    }
                } catch (ServiceException e) {
                    try {
                        throw new ServiceException(e.getMessage(),e);
                    } catch (ServiceException serviceException) {
                        log.error(e.getMessage(),e);
                    }
                }
                return true;
            }
            return false;
        });
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("LoginFilter");
        registration.setOrder(1);
        return registration;
    }
}
