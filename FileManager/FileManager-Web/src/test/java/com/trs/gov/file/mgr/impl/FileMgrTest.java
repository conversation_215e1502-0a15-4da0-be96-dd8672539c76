package com.trs.gov.file.mgr.impl;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.file.DTO.FileDTO;
import com.trs.gov.file.DTO.FileExportDTO;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.DTO.SaveOrQueryFileDTO;
import com.trs.gov.file.FileManagerApplication;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.constant.FileConstant;
import com.trs.gov.file.service.impl.FileManagerServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

import static org.junit.Assert.fail;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = FileManagerApplication.class)
@Slf4j
public class FileMgrTest {

    @Autowired
    private FileManagerServiceImpl service;

    @Test
    public void saveOrUpdateOneFile() {
        try {
            service.saveOrUpdateOneFile(SaveOrQueryFileDTO.of(FileDTO.of("U020200921564891638668.jpg", "xxx", "jpg", 0L), ObjDTO.of("WORKORDER", "1", "piclist")));
        } catch (ServiceException e) {
            log.error("saveOrUpdateOneFile", e);
            fail(e.getMessage());
        }
    }

    @Test
    public void getFileByName() {
    }

    @Test
    public void getFileByNameAndObj() {
    }

    @Test
    public void getFileListOfObj() {
        try {
            List<FileVO> li = service.getFileListOfObj(ObjDTO.of("WORKORDER", "1", "piclist"));
            System.out.println(li.get(0));
        } catch (ServiceException e) {
            log.error("test[getFileListOfObj]异常", e);
            fail("test[getFileListOfObj]异常,ERR=" + e.getMessage());
        }
    }

    @Test
    public void uploadFile() throws ServiceException {
        FileExportDTO dto = new FileExportDTO();
        dto.setFileext("jpg");
        dto.setFiledesc("测试文件");
        dto.setFilePath("http://news.beiww.com/yayw1763/201812/W020181213342115992004.jpg");
        dto.setPathFlag(FileConstant.PATH_HTTP);
        FileVO vo = service.uploadFile(dto);
        System.out.println(vo);
    }

    @Test
    public void deleteFileByObj() {
        byte[] bytes = hexToByteArray("byteStr");
        getFile(bytes,"D:\\application","aaaaaaaa.jpg");
    }
    public static byte[] hexToByteArray(String inHex){
        int hexlen = inHex.length();
        byte[] result;
        if (hexlen % 2 == 1){
            //奇数
            hexlen++;
            result = new byte[(hexlen/2)];
            inHex="0"+inHex;
        }else {
            //偶数
            result = new byte[(hexlen/2)];
        }
        int j=0;
        for (int i = 0; i < hexlen; i+=2){
            result[j]=(byte)Integer.parseInt(inHex.substring(i,i+2),16);
            j++;
        }
        return result;
    }
    /**
     * 根据byte数组，生成文件
     */
    public static void getFile(byte[] bfile, String filePath,String fileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            File dir = new File(filePath);
            if(!dir.exists()&&dir.isDirectory()){//判断文件目录是否存在
                dir.mkdirs();
            }
            file = new File(filePath+"\\"+fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bfile);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }
}
