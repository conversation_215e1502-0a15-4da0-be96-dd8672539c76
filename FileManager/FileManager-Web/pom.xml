<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>FileManager</artifactId>
        <groupId>com.trs.gov</groupId>
        <version>1.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>FileManager-Web</artifactId>
    <properties>
        <!--suppress UnresolvedMavenProperty -->
        <filter-env>${env}</filter-env>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.trs.gov</groupId>
            <artifactId>FileManager-Service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.trs.gov</groupId>
            <artifactId>User-Service</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}-1.0</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <filters>
            <filter>../../Configuration/filters/${filter-env}/resources/bootstrap.properties</filter>
        </filters>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <!-- 上传到仓库中的镜像名 -->
                    <imageName>harbor.trscd.com.cn/trs-workorder/filemanager</imageName>
                    <!-- 指定 Dockerfile 路径-->
                    <dockerDirectory>${basedir}/</dockerDirectory>
                    <!-- 这里是复制 jar 包到 docker 容器指定目录配置，也可以写到 Docokerfile 中 -->
                    <resources>
                        <resource>
                            <targetPath>/</targetPath>
                            <directory>${project.build.directory}</directory>
                            <include>*.jar</include>
                        </resource>
                    </resources>
                    <!-- 相关镜像的tag -->
                    <imageTags>
                        <imageTag>${project.version}</imageTag>
                    </imageTags>
                    <!-- 上传仓库的认证配置（账户密码），在mavensetting.xml中配置 -->
                    <serverId>harbor-trscd-registry</serverId>
                </configuration>
                <!-- 在install操作中绑定镜像的构建和推送操作 -->
                <!-- 执行mvn install即可将镜像构建并推送到仓库中 -->
                <executions>
                    <execution>
                        <id>docker-build</id>
                        <phase>install</phase>
                        <goals>
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>