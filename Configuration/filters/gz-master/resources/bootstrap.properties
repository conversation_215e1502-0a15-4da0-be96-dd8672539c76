#\u7528\u6237\u670D\u52A1\u76F8\u5173Redis\u7684DB
UserService.Redis.Index=1
#\u7528\u6237\u670D\u52A1\u76F8\u5173redis Key\uFF08token\uFF09\u7684\u6709\u6548\u671F\uFF08\u5355\u4F4D\uFF1A\u79D2\uFF09
UserService.Redis.KeyExpireSecond=3600
#\u5355\u70B9\u6A21\u5F0F\u65F6redis\u7684IP\uFF0C\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u586B\u5199
spring.redis.host=***********
#\u5355\u70B9\u6A21\u5F0F\u65F6redis\u7684\u7AEF\u53E3
spring.redis.port=6379
#redis\u7684\u5BC6\u7801\uFF0C\u672A\u8BBE\u7F6E\u65F6\u8BE5\u5B57\u6BB5\u4E3A\u7A7A
spring.redis.pwd=trs_redis
#redis\u96C6\u7FA4
spring.redis.nodes=
#redis\u54E8\u5175\u6A21\u5F0F\u7684masterName\uFF08\u4F7F\u7528\u5355\u70B9\u6A21\u5F0F\u65F6\uFF0C\u8BE5\u5C5E\u6027\u5FC5\u987B\u4E3A\u7A7A\uFF09
spring.redis.masterName=
#\u4E0A\u4F20\u6587\u4EF6\u7684\u5927\u5C0F
spring.servlet.multipart.max-file-size=10000MB
#\u4E0A\u4F20\u8BF7\u6C42\u7684\u5927\u5C0F
spring.servlet.multipart.max-request-size=10000MB
#\u6587\u4EF6\u4E0A\u4F20\u7684\u6839\u8DEF\u5F84
FileManagerService.UploadDir=/TRS/DATA/WORKORDER/
#FileManagerService.UploadDir\u7684HTTP\u6620\u5C04
FileManagerService.UrlPrefix=http://***************
#\u5141\u8BB8\u4E0A\u4F20\u7684\u6587\u4EF6\u540E\u7F00\uFF08\u4F7F\u7528;\u5206\u9694\uFF09
FileManagerService.AllowFileExts=jpg;jpeg;png;gif;xls;xlsx;doc;docx;ppt;pdf;pptx;zip;rar;mp4
#\u6743\u9650\u7CFB\u7EDF\u7684\u8BF7\u6C42\u63A5\u53E3
upmsApiUrl.vaule=http://***********/upms/group.do
#\u6743\u9650\u7CFB\u7EDF\u5B50\u7CFB\u7EDF\u6807\u8BC6\uFF0C\u7531\u6743\u9650\u7CFB\u7EDF\u751F\u6210\uFF0C\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u586B\u5199
upms.client.tenant=UPMS
#\u6743\u9650\u7CFB\u7EDF\u5B50\u7CFB\u7EDF\u8D44\u6E90\u6807\u8BC6\uFF0C\u4E00\u822C\u7B49\u4EF7\u4E8Eupms.client.tenant
upms.client.resourcetype=TENANT
#\u6743\u9650\u7CFB\u7EDF\u670D\u52A1\u5668\u5730\u5740
upms.service.url=http://***********/upms
#\u6D77\u4E91\u5DF2\u6DFB\u52A0\u5230\u767D\u540D\u5355\u7684\u7528\u6237(\u7528\u6237\u8BBF\u95EE\u6D77\u4E91\u63A5\u53E3\u6743\u9650)
hycloud.currUserName=dev
#\u6D77\u4E91\u63D0\u4F9B\u5916\u90E8\u63A5\u53E3\u7684\u8282\u70B9\u5730\u5740
hycloud.url=http://***********:8080/gov/opendata.do
#\u6D77\u4E91\u63A5\u53E3\u670D\u52A1\u540D\u79F0
hycloud.serviceid=gov_site
#\u6D77\u4E91\u63A5\u53E3\u65B9\u6CD5\u540D\u79F0
hycloud.methodname=querySitesOnEditorCenter
#\u6D77\u4E91\u6743\u9650key\u503C
hycloud.key=aHG8rFHx3MOeSOvmlkJoa2K1xvKRgIwq
#\u670D\u52A1\u5355\u4F4D\u5728IDS\u4E2D\u9ED8\u8BA4\u7684\u7236\u7EC4\u7EC7\u7684GroupKey\uFF0C\u9700\u8981\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u586B\u5199
IDS.Group.parentGroupId=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u5730\u5740
monitorSite.url=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.param.isp_h=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.param.isp_a=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.param.isp_s=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.param.isp_v=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u53C2\u6570\uFF0C\u76D1\u6D4B\u4E91\u79DF\u6237id
monitorSite.param.tenantId=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u53C2\u6570\uFF0C\u76D1\u6D4B\u4E91\u4E2D\u7684\u7528\u6237\u540D
monitorSite.param.userName=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u53C2\u6570\uFF0C\u76D1\u6D4B\u4E91\u7528\u6237\u7684\u7535\u8BDD\u53F7\u7801
monitorSite.param.phone=
#\u878D\u5408\u670D\u52A1\u5E73\u53F0\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.access_token.url=
#nacos\u7684\u5730\u5740
spring.cloud.nacos.config.server-addr=**********:8848
#nacos\u4E2D\u914D\u7F6E\u7684\u683C\u5F0F\uFF08\u4E00\u822C\u4E0D\u7528\u52A8\uFF09
spring.cloud.nacos.config.file-extension=properties
#nacos\u547D\u540D\u7A7A\u95F4ID
spring.cloud.nacos.config.namespace=
#nacos\u914D\u7F6E\u7684DataId
spring.cloud.nacos.config.ext-config[0].data-id=workorder.properties
#\u540Cspring.cloud.nacos.config.server-addr
spring.cloud.nacos.discovery.server-addr=**********:8848
spring.cloud.nacos.password=9oph3RWFb_hO65met
spring.cloud.nacos.username=nacos
#\u670D\u52A1\u6CE8\u518C\u7684\u5730\u5740\uFF0C\u4E00\u822C\u4F7F\u7528nacos\u6765\u505A\uFF0C\u586B\u5199nacos\u7684IP\u548C\u7AEF\u53E3\u5373\u53EF
dubbo.registry.address=nacos://**********:8848?username=nacos&password=9oph3RWFb_hO65met
#\u6570\u636E\u5E93jdbc\u8FDE\u63A5\u4FE1\u606F
spring.datasource.url=**************************************************************************************************************************************************
#\u6570\u636E\u5E93\u7528\u6237\u540D
spring.datasource.username=worksheet2
#\u6570\u636E\u5E93\u5BC6\u7801
spring.datasource.password=JUyyh76
#IDSsso\u5730\u5740
ids.ssoUrl=http://***********/ids/LoginServlet
#IDS\u4F7F\u7528HTTP\u534F\u8BAE\u901A\u8BAF
ids.protocol.http=true
#IDS\u8BF7\u6C42\u63A5\u53E3
ids.protocol.http.url=http://***********/ids/protocol
#\u6240\u8981\u8FDE\u63A5IDS\u7684IP(\u6216\u4E3B\u673A)
ids.idm.server.host=***********
#\u6240\u8981\u8FDE\u63A5IDS\u7684\u540E\u53F0SSLServer\u7AEF\u53E3.\u5FC5\u586B.\u5982\u679C\u4E0D\u586B\u9ED8\u8BA4\u4E3A2005.
ids.idm.server.port=2005
#IDS\u521B\u5EFAsocket\u6570
ids.idm.sockets.amount=5
#\u534F\u4F5C\u5E94\u7528\u540D. \u5FC5\u586B, \u5E76\u4E14\u5FC5\u987B\u548C\u5728\u6240\u8981\u8FDE\u63A5IDS\u4E0A\u6CE8\u518C\u7684\u534F\u4F5C\u5E94\u7528\u540D\u4FDD\u6301\u4E00\u81F4.
ids.agent.name=workorder
#\u534F\u52A9\u5E94\u7528\u52A0\u5BC6key
ids.des.key=12345678
#ids\u767B\u5F55\u6210\u529F\u540E\u8DF3\u8F6C\u5730\u5740\uFF08\u524D\u7AEF\u5305\u7684\u8BBF\u95EE\u5730\u5740\uFF09
ids.afterLoginOk.gotoUrl=http://***********/wos/#/homePage
#ids\u767B\u5F55\u5931\u8D25\u540E\u8DF3\u8F6C\u5730\u5740\uFF08\u524D\u7AEF\u5305\u7684\u8BBF\u95EE\u5730\u5740\uFF09
ids.afterLoginFail.gotoUrl=http://***********/wos/#/homePage
#\u7528\u6237\u6A21\u5757\u7AEF\u53E3
User-Service-server.port=18080
#\u5DE5\u5355\u6A21\u5757\u7AEF\u53E3
WorkOrder-Service-server.port=18081
#\u6587\u4EF6\u6A21\u5757\u7AEF\u53E3
FileManager-Service-server.port=18082
#\u6D88\u606F\u6A21\u5757\u7AEF\u53E3
Message-Service-server.port=18083
#\u7CFB\u7EDF\u914D\u7F6E\u6A21\u5757\u7AEF\u53E3
SystemManagement-Service-server.port=18084
#\u68C0\u7D22\u6A21\u5757\u7AEF\u53E3
Search-Service-server.port=18085
#\u7EDF\u8BA1\u6A21\u5757\u7AEF\u53E3
StatisticsCenter-Service-server.port=18086
#\u4E92\u52A8\u6A21\u5757\u7AEF\u53E3
Interaction-Service-server.port=18087
#\u5916\u90E8\u7CFB\u7EDF\u6A21\u5757\u7AEF\u53E3
ExternalSystem-Service-server.port=18088
#\u7F51\u5173\u6A21\u5757\u7AEF\u53E3
Gateway-Service-server.port=18089
KnowledgeBase-Service-server.port=18090

#\u5404\u670D\u52A1dubbo\u6240\u5360\u7AEF\u53E3\uFF08\u53EF\u4EE5\u4FEE\u6539\u53EA\u8981\u4E0D\u51B2\u7A81\u5373\u53EF\uFF09
User-Service-server.dubbo.port=28080
WorkOrder-Service-server.dubbo.port=28081
FileManager-Service-server.dubbo.port=28082
Message-Service-server.dubbo.port=28083
SystemManagement-Service-server.dubbo.port=28084
Search-Service-server.dubbo.port=28085
StatisticsCenter-Service-server.dubbo.port=28086
Interaction-Service-server.dubbo.port=28087
ExternalSystem-Service-server.dubbo.port=28088
Gateway-Service-server.dubbo.port=28089
Export-Service-server.dubbo.port=28090
KnowledgeBase-Service-server.dubbo.port=28090

#gateway\u914D\u7F6E\u7684DataId
gateway.spring.cloud.nacos.config.ext-config[0].data-id=gateway.yml
#gateway\u4E2D\u914D\u7F6E\u7684\u683C\u5F0F
gateway.spring.cloud.nacos.config.file-extension=yml
#ES \u76F8\u5173\u914D\u7F6E,\u96C6\u7FA4\u7528\u9017\u53F7\u9694\u5F00
es.dataSource.url=http://**********4:9200
es.dataSource.password=AvAVqpUpNLc0ADnwDp9R
es.dataSource.user=elastic
