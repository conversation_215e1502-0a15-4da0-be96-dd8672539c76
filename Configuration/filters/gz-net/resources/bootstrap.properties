#ES \u76F8\u5173\u914D\u7F6E,\u96C6\u7FA4\u7528\u9017\u53F7\u9694\u5F00
es.dataSource.url=http://**********:9200,http://***********:9200
#nacos\u7684\u5730\u5740
spring.cloud.nacos.config.server-addr=localhost:8848
#nacos\u4E2D\u914D\u7F6E\u7684\u683C\u5F0F\uFF08\u4E00\u822C\u4E0D\u7528\u52A8\uFF09
spring.cloud.nacos.config.file-extension=properties
#nacos\u547D\u540D\u7A7A\u95F4ID
spring.cloud.nacos.config.namespace=
#nacos\u914D\u7F6E\u7684DataId
spring.cloud.nacos.config.ext-config[0].data-id=workorder.properties
#\u540Cspring.cloud.nacos.config.server-addr
spring.cloud.nacos.discovery.server-addr=localhost:8848
spring.cloud.nacos.password=Trsadminnacos@2022
spring.cloud.nacos.username=nacos
#\u670D\u52A1\u6CE8\u518C\u7684\u5730\u5740\uFF0C\u4E00\u822C\u4F7F\u7528nacos\u6765\u505A\uFF0C\u4FEE\u6539\u6210\u771F\u5B9E\u7684nacos\u7684IP\u548C\u7AEF\u53E3\u5373\u53EF
dubbo.registry.address=nacos://localhost:8848
#\u6570\u636E\u5E93jdbc\u8FDE\u63A5\u4FE1\u606Fls
spring.datasource.url=*********************************************************************************************************************
#\u6570\u636E\u5E93\u7528\u6237\u540D
spring.datasource.username=worksheet2
#\u6570\u636E\u5E93\u5BC6\u7801
spring.datasource.password=JUyyh76
#IDSsso\u5730\u5740
ids.ssoUrl=http://***************/ids/LoginServlet
#IDS\u4F7F\u7528HTTP\u534F\u8BAE\u901A\u8BAF
ids.protocol.http=true
#IDS\u8BF7\u6C42\u63A5\u53E3
ids.protocol.http.url=http://***************/ids/protocol
#\u6240\u8981\u8FDE\u63A5IDS\u7684IP(\u6216\u4E3B\u673A)
ids.idm.server.host=***************
#\u6240\u8981\u8FDE\u63A5IDS\u7684\u540E\u53F0SSLServer\u7AEF\u53E3.\u5FC5\u586B.\u5982\u679C\u4E0D\u586B\u9ED8\u8BA4\u4E3A2005.
ids.idm.server.port=2005
#IDS\u521B\u5EFAsocket\u6570
ids.idm.sockets.amount=5
#\u534F\u4F5C\u5E94\u7528\u540D. \u5FC5\u586B, \u5E76\u4E14\u5FC5\u987B\u548C\u5728\u6240\u8981\u8FDE\u63A5IDS\u4E0A\u6CE8\u518C\u7684\u534F\u4F5C\u5E94\u7528\u540D\u4FDD\u6301\u4E00\u81F4.
ids.agent.name=networkorder
#\u534F\u52A9\u5E94\u7528\u52A0\u5BC6key
ids.des.key=12345678
#ids\u767B\u5F55\u6210\u529F\u540E\u8DF3\u8F6C\u5730\u5740\uFF08\u524D\u7AEF\u5305\u7684\u8BBF\u95EE\u5730\u5740\uFF09
ids.afterLoginOk.gotoUrl=http://***************/wos/#/homePage
#ids\u767B\u5F55\u5931\u8D25\u540E\u8DF3\u8F6C\u5730\u5740\uFF08\u524D\u7AEF\u5305\u7684\u8BBF\u95EE\u5730\u5740\uFF09
ids.afterLoginFail.gotoUrl=http://***************/wos/#/homePage
#\u7528\u6237\u6A21\u5757\u7AEF\u53E3
User-Service-server.port=18080
#\u5DE5\u5355\u6A21\u5757\u7AEF\u53E3
WorkOrder-Service-server.port=18081
#\u6587\u4EF6\u6A21\u5757\u7AEF\u53E3
FileManager-Service-server.port=18082
#\u6D88\u606F\u6A21\u5757\u7AEF\u53E3
Message-Service-server.port=18083
#\u7CFB\u7EDF\u914D\u7F6E\u6A21\u5757\u7AEF\u53E3
SystemManagement-Service-server.port=18084
#\u68C0\u7D22\u6A21\u5757\u7AEF\u53E3
Search-Service-server.port=18085
#\u7EDF\u8BA1\u6A21\u5757\u7AEF\u53E3
StatisticsCenter-Service-server.port=18086
#\u4E92\u52A8\u6A21\u5757\u7AEF\u53E3
Interaction-Service-server.port=18087
#\u5916\u90E8\u7CFB\u7EDF\u6A21\u5757\u7AEF\u53E3
ExternalSystem-Service-server.port=18088
#\u7F51\u5173\u6A21\u5757\u7AEF\u53E3
Gateway-Service-server.port=18089
KnowledgeBase-Service-server.port=18090
#gateway\u914D\u7F6E\u7684DataId
gateway.spring.cloud.nacos.config.ext-config[0].data-id=gateway.yml
#gateway\u4E2D\u914D\u7F6E\u7684\u683C\u5F0F
gateway.spring.cloud.nacos.config.file-extension=yml
#\u7528\u6237\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
User-Service-server.dubbo.port=28080
#\u5DE5\u5355\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
WorkOrder-Service-server.dubbo.port=28081
#\u6587\u4EF6\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
FileManager-Service-server.dubbo.port=28082
#\u6D88\u606F\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
Message-Service-server.dubbo.port=28083
#\u7CFB\u7EDF\u914D\u7F6E\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
SystemManagement-Service-server.dubbo.port=28084
#\u68C0\u7D22\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
Search-Service-server.dubbo.port=28085
#\u7EDF\u8BA1\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
StatisticsCenter-Service-server.dubbo.port=28086
#\u4E92\u52A8\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
Interaction-Service-server.dubbo.port=28087
#\u5916\u90E8\u7CFB\u7EDF\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
ExternalSystem-Service-server.dubbo.port=28088
#\u7F51\u5173\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
Gateway-Service-server.dubbo.port=28089
KnowledgeBase-Service-server.dubbo.port=28090
