# Kubernetesçäº§ç¯å¢éç½®
# nacosç¸å³éç½®
spring.cloud.nacos.config.server-addr=${NACOS_SERVER_ADDR:nacos-svc:8848}
spring.cloud.nacos.config.file-extension=${NACOS_FILE_EXTENSION:properties}
spring.cloud.nacos.config.namespace=${NACOS_NAMESPACE:d63ce391-3c11-4d00-8c58-03992ae3c683}
spring.cloud.nacos.config.ext-config[0].data-id=${NACOS_CONFIG_EXT_CONFIG_DATA_ID:workorder.properties}
spring.cloud.nacos.discovery.server-addr=${NACOS_DISCOVERY_SERVER_ADDR:nacos-svc:8848}
spring.cloud.nacos.username=${NACOS_USERNAME:nacos}
spring.cloud.nacos.password=${NACOS_PASSWORD:nacos}
dubbo.registry.address=${DUBBO_REGISTRY_ADDRESS:nacos://nacos-svc:8848}

###################################################################################
# æ°æ®åºç¸å³éç½®
spring.datasource.url=${DATABASE_URL:***********************************************************************************}
spring.datasource.username=${DATABASE_USERNAME:root}
spring.datasource.password=${DATABASE_PASSWORD:trsadmin@123}

###################################################################################
# Rediséç½®
spring.redis.host=${REDIS_HOST:hycloud-redis-svc}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.pwd=${REDIS_PWD:HAjsIqcg9xE17UmI}
spring.redis.nodes=${REDIS_NODES:}
spring.redis.masterName=${REDIS_MASTER_NAME:}

###################################################################################
# ElasticSearchéç½®
es.dataSource.url=${ELASTICSEARCH_DATA_SOURCE_URL:elasticsearch-svc:9200}

###################################################################################
#IDSç¸å³éç½®
ids.ssoUrl=https://hycloud.trscd.com.cn/ids/LoginServlet
ids.protocol.http=true
ids.protocol.http.url=https://hycloud.trscd.com.cn/ids/protocol
ids.idm.server.host=hycloud.trscd.com.cn
ids.idm.server.port=2005
ids.idm.sockets.amount=5
ids.agent.name=workorder
ids.des.key=12345678
ids.afterLoginOk.gotoUrl=https://hycloud.trscd.com.cn/workorder-app/#/homePage
ids.afterLoginFail.gotoUrl=https://hycloud.trscd.com.cn/workorder-app/#/homePage

###################################################################################
#åæå¡æå ç«¯å£
User-Service-server.port=18080
WorkOrder-Service-server.port=18081
FileManager-Service-server.port=18082
Message-Service-server.port=18083
SystemManagement-Service-server.port=18084
Search-Service-server.port=18085
StatisticsCenter-Service-server.port=18086
Interaction-Service-server.port=18087
ExternalSystem-Service-server.port=18088
Gateway-Service-server.port=18089
Export-Service-server.port=18090
KnowledgeBase-Service-server.port=18090

#åæå¡dubboæå ç«¯å£
User-Service-server.dubbo.port=20880
WorkOrder-Service-server.dubbo.port=20881
FileManager-Service-server.dubbo.port=20882
Message-Service-server.dubbo.port=20883
SystemManagement-Service-server.dubbo.port=20884
Search-Service-server.dubbo.port=20885
StatisticsCenter-Service-server.dubbo.port=20886
Interaction-Service-server.dubbo.port=20887
ExternalSystem-Service-server.dubbo.port=20888
Gateway-Service-server.dubbo.port=20889
Export-Service-server.dubbo.port=20890
KnowledgeBase-Service-server.dubbo.port=20890

# gatewayéç½®çDataId
gateway.spring.cloud.nacos.config.ext-config[0].data-id=gateway.yml
# gatewayä¸­éç½®çæ ¼å¼
gateway.spring.cloud.nacos.config.file-extension=yml

# æ¥å¿è·¯å¾éç½®
log.path=/tmp/logs 

#ç¨æ·æå¡ç¸å³RedisçDB
UserService.Redis.Index=3
#ç¨æ·æå¡ç¸å³redis Keyï¼tokenï¼çæææï¼åä½ï¼ç§ï¼
UserService.Redis.KeyExpireSecond=3600

#ä¸ä¼ æä»¶çå¤§å°
spring.servlet.multipart.max-file-size=10000MB
#ä¸ä¼ è¯·æ±çå¤§å°
spring.servlet.multipart.max-request-size=10000MB
#æä»¶ä¸ä¼ çæ ¹è·¯å¾
FileManagerService.UploadDir=/TRS/DATA/WORKORDER/
#FileManagerService.UploadDirçHTTPæ å°
FileManagerService.UrlPrefix=${FILE_MANAGER_SERVICE_URL_PREFIX:http://**************/WORKORDER/}
#åè®¸ä¸ä¼ çæä»¶åç¼ï¼ä½¿ç¨;åéï¼
FileManagerService.AllowFileExts=jpg;jpeg;png;gif;xls;xlsx;doc;docx;ppt;pdf;pptx;zip;rar;mp4
#æéç³»ç»çè¯·æ±æ¥å£
upmsApiUrl.vaule=${UPMS_API_URL:http://**************/upms/group.do}
#æéç³»ç»å­ç³»ç»æ è¯ï¼ç±æéç³»ç»çæï¼æ ¹æ®å®éæåµå¡«å
upms.client.tenant=${UPMS_CLIENT_TENANT:vbXobq}
#æéç³»ç»å­ç³»ç»èµæºæ è¯ï¼ä¸è¬ç­ä»·äºupms.client.tenant
upms.client.resourcetype=${UPMS_CLIENT_RESOURCETYPE:vbXobq}
#æéç³»ç»æå¡å¨å°å
upms.service.url=${UPMS_SERVICE_URL:http://**************/upms}
#æµ·äºç³»ç»éç½®æä»¶ç½ååç¨æ· (æµ·äºç³»ç»éè¦è¿è¡ipç½ååéç½®,éç½®è¯´æè§å³ä¾§å¾ç)
hycloud.currUserName=dev
#æµ·äºæä¾æå¡æ¥å£èç¹çå°å
hycloud.url=http://************/gov/opendata.do
#æµ·äºæ¥å£æå¡åç§°
hycloud.serviceid=gov_site
#æµ·äºæ¥å£æ¹æ³åç§°
hycloud.methodname=querySitesOnEditorCenter
#æµ·äºæékeyå¼
hycloud.key=aHG8rFHx3MOeSOvmlkJoa2K1xvKRgIwq
#æå¡åä½å¨IDSä¸­é»è®¤çç¶ç»ç»çGroupKeyï¼éè¦æ ¹æ®å®éæåµå¡«å
IDS.Group.parentGroupId=0
#èåæå¡å¹³å°æ°æ®æ¥å£å°å
monitorSite.url=${MONITOR_SITE_URL:http://************/isp/http/exposion}
#èåæå¡å¹³å°æ°æ®æ¥å£åæ°
monitorSite.param.isp_h=
#èåæå¡å¹³å°æ°æ®æ¥å£åæ°
monitorSite.param.isp_a=
#èåæå¡å¹³å°æ°æ®æ¥å£åæ°
monitorSite.param.isp_s=
#èåæå¡å¹³å°æ°æ®æ¥å£åæ°
monitorSite.param.isp_v=
#èåæå¡å¹³å°æ°æ®æ¥å£åæ°ï¼çæµäºç§æ·id
monitorSite.param.tenantId=
#èåæå¡å¹³å°æ°æ®æ¥å£åæ°ï¼çæµäºä¸­çç¨æ·å
monitorSite.param.userName=
#èåæå¡å¹³å°æ°æ®æ¥å£åæ°ï¼çæµäºç¨æ·ççµè¯å·ç 
monitorSite.param.phone=
#èåæå¡å¹³å°æ°æ®æ¥å£åæ°
monitorSite.access_token.url=

#æä»¶ä¸è½½çUrlåç¼ [ç½å³è®¿é®å°å/file/downloadFile]
FileManagerService.downloadUrlPrefix=${FILE_MANAGER_SERVICE_DOWNLOAD_URL_PREFIX:http://**************/workorder_gw/file/downloadFile}

#éç¥æ¶æ¯å°è¦æ¨éè³çå¹³å°,éè¿ä»å·¦å°å³çé¡ºåºè¿è¡æ§è¡æ¨é,å»ºè®®å°æ¨éè³å·¥ååå¨æåé¢,é²æ­¢æ¨éè³ç¬¬ä¸æ¹å¹³å°æ¶ç½ç»æ¶é´çååºé®é¢
# 1.å·¥å,2.ç¬¬ä¸æ¹æ¥å£
#different.channel.list=gz_work_order,third_party_interface
different.channel.list=gz_work_order,sms_platform
#æ¨éè³ç¬¬ä¸æ¹æ¥å£ä¸­é£äºæ¸ é,å¤ä¸ªç¨éå·éå¼ 1.å¾®ä¿¡å¬ä¼å·
thirdPartyInterface.of.channels=wxgzh
#æ¨éçç¨æ·å
push.user-name=szh_tes_user
#æ¨éçææç 
push.auth-code=WOi3PjVsb8JYFSz7
#token çè¿ææ¶é´ 10åé
token.expiration.time=600
#tokenå­å¨çç´¢å¼åº
token.expiration.index=10
#è·åtokendçUrlå°å
token.url=http://117.187.141.80:8082/index.php/token
# æ¨éæ°æ®å°å
push.url=http://117.187.141.80:8082/index.php/notice
# ç¬¬ä¸æ¬¡åéå¤±è´¥å,éè¦éè¯çæ¬¡æ°
fail.retry.send.count=2
#æ¨éè³å¾®ä¿¡å¬ä¼å·,è¿åå­æ®µï¼æµè¯ç¯å¢ä¸ºï¼test,æ­£å¼ç¯å¢ä¸º:szh
not-send-mobile.field=szh
# sms æ¹éåéç­ä¿¡çå°å
sms.batch.url=http://sendsms.gzdata.com.cn1/smsapi/smsapi/batchSend.json01
#sms.batch.url=https://ulink.028lk.com/api/SendSms
# sms  åéç­ä¿¡å¹³å°çè´¦å·
sms.account=ys20100026
#sms.account=NC82200257
# sms åéç­ä¿¡å¹³å°çå¯ç 
sms.password=Gzszfwzjyh01@
#sms.password=ul@2019

#ç­ä¿¡æ¨¡æ¿
sms.template.hasDealEd=[TRUENAME]ï¼æ¨å¥½ï¼æ¨å¨å·¥åç³»ç»åå»º[[TITLE]]ï¼[WORKORDERID]ï¼å·²è¿è¡åçï¼è¯·ç­å¾å¤çãåç»­æé®é¢å¯èç³»0851-********éçº¦åå¹³å°è¿ç»´æå¡ç­çº¿ã
sms.template.hasFinishEd=[TRUENAME]ï¼æ¨å¥½ï¼æ¨å¨å·¥åç³»ç»åå»º[[TITLE]]ï¼[WORKORDERID]ï¼å·²è¿è¡å®æå¤çï¼è¯·ç»å½ç³»ç»è¿è¡æ¥çãåç»­æé®é¢å¯èç³»0851-********éçº¦åå¹³å°è¿ç»´æå¡ç­çº¿ã
sms.template.hasAppraise=[TRUENAME]ï¼æ¨å¥½ï¼æ¨å¨å·¥åç³»ç»å¤ç[[TITLE]]ï¼[WORKORDERID]ï¼å·²å®æè¯ä»·ï¼è¯·ç»å½ç³»ç»è¿è¡æ¥çãåç»­æé®é¢å¯èç³»0851-********éçº¦åå¹³å°è¿ç»´æå¡ç­çº¿ã
sms.template.waitDeal=[TRUENAME]ï¼æ¨å¥½ï¼[UNITNAME]å¨å·¥åç³»ç»åå»º[[TITLE]]ï¼[WORKORDERID]ï¼æäº¤å°[DEALUNITNAME]è¿è¡å¤çï¼è¯·ç»å½ç³»ç»è¿è¡æ¥çå¤çãåç»­æé®é¢å¯èç³»0851-********éçº¦åå¹³å°è¿ç»´æå¡ç­çº¿ã
sms.template.assignOrCopy=[TRUENAME]ï¼æ¨å¥½ï¼[UNITNAME]å¨å·¥åç³»ç»å°[[TITLE]]ï¼[WORKORDERID]ï¼è½¬å/æéå°[DEALUNITNAME]è¿è¡å¤çï¼è¯·ç»å½ç³»ç»è¿è¡æ¥çå¤çãåç»­æé®é¢å¯èç³»0851-********éçº¦åå¹³å°è¿ç»´æå¡ç­çº¿ã
sms.template.hasRollback=[TRUENAME]ï¼æ¨å¥½ï¼æ¨å¨å·¥åç³»ç»å¤ç[[TITLE]]ï¼[WORKORDERID]ï¼å ç¼ºä¹å³é®ä¿¡æ¯æéæ±ä¸æå·²è¢«åéï¼è¯·ç»å½ç³»ç»è¿è¡æ¥çå¹¶è¿è¡å®åãåç»­æé®é¢å¯èç³»0851-********éçº¦åå¹³å°è¿ç»´æå¡ç­çº¿ã




#å·¥åç´¢å¼åç§°åç´¢å¼ç±»åç¸å³éç½®
workorder.indeces-name=work_order
#notice.indices-type=notice_detail
notice.indices-type=noticedetail
#siteRelation.indices-type=site_relation
siteRelation.indices-type=siterelation
#unit.indices-type=unit
unit.indices-type=unit
#user.indices-type=user
user.indices-type=user
#workorder.indices-type=work_order_detail
workorder.indices-type=workorderdetail

#tokenè·åå°å
monitorSite.access_token.url = http://isp.devdemo.trs.net.cn:80/isp/oauth/token?username=service_144,application_21,version_1&password=service_144,application_21,version_1&grant_type=password&scope=select&client_id=client_1&client_secret=123456

# æµ·äºç¥è¯åºå°å
KnowledgeBase.hyKnowledgeList=http://10.11.2.144/pub/ywszsk

#éå·¥ä½æ¶é´åå»ºå·¥åèªå¨åå¤åå®¹
workOrder.autoReply.content=ãèªå¨åå¤ãä¾æ®ãè´µå·çæ¿åºç½ç«éçº¦åå¹³å°å·¥ååçåæ³ï¼è¯è¡ï¼ãï¼éå·¥ä½æ¶é´æäº¤å·¥åï¼ååä¸é¡ºå»¶è³å·¥ä½æ¶é´å¤çï¼å¦æ¥éå¤çï¼è¯·æ¨æ13027880860ã0851-********ã17152144142ï¼æä»¬å°å¨ç¬¬ä¸æ¶é´å¤çãè°¢è°¢ï¼test
#å¼å¯éå·¥ä½æ¶é´åå»ºå·¥åèªå¨åå¤åå®¹-é»è®¤å³é­
workOrder.autoReply.content.open=true

#å·¥åæµè½¬ç­ä¿¡æéï¼åæ­¥ç­ä¿¡æ¶æ¯å°å¾®ä¿¡å¬ä¼å·
#æ¯å¦å¼å¯ç­ä¿¡æ¶æ¯åæ­¥å°å¾®ä¿¡å¬ä¼å·ï¼é»è®¤false
gzh.push.open=true
#å¬ä¼å·æ¥æ¶ç«¯ç¨æ·å
gzh.user-name=jhyptgdxt
#å¬ä¼å·æ¥æ¶ç«¯ç¨æ·å¯ç 
gzh.auth-code=+HGR8k$v
#å¬ä¼å·æ¥æ¶ç«¯tokenæææ
gzh.token.expiration.time=119
#rediså­æ¾tokençindex
gzh.token.expiration.index=10
#ä»å¬ä¼å·è·åtokençurl
gzh.token.url=http://message.guizhou.gov.cn/api/receive/public/xml/getToken
#ä»å¬ä¼å·æ¨éæ¶æ¯çurl
gzh.push.url=http://message.guizhou.gov.cn/api/receive/public/message

#éè¦èªå¨åå¤çåçæ¹äººåååï¼âï¼âéå¼
workOrder.autoResponse.person.name=è°·å°å
#èªå¨åå¤åå®¹
workOrder.autoReply.person.content=æ¨å¥½ï¼æ¨æçé®é¢ï¼éæ±å·²æ¶å°ï¼æ­£å¨å¤çä¸­ï¼è¯·èå¿ç­å¾ï¼ç¨åç»æ¨åå¤ã

#åæ­¥æ´æ°åä½ç¼ç 
systemManagementService.syncUnitCode.cron=0 0/30 * * * ?