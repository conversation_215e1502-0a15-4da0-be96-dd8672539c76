#\u7528\u6237\u670D\u52A1\u76F8\u5173redis Key\uFF08token\uFF09\u7684\u6709\u6548\u671F\uFF08\u5355\u4F4D\uFF1A\u79D2\uFF09
UserService.Redis.KeyExpireSecond=3600
#\u6743\u9650\u7CFB\u7EDF\u7528\u6237\u7F13\u5B58\u65F6\u95F4\uFF0C\u9ED8\u8BA41800
management.unitMgr.redisExpireTime=1800
#\u6743\u9650\u7CFB\u7EDF\u7528\u6237\u7F13\u5B58\u7D22\u5F15\uFF0C\u9ED8\u8BA49
management.unitMgr.redisIndex=12
#\u6743\u9650\u7F13\u5B58\u65F6\u95F4\uFF0C\u9ED8\u8BA43600
externalSys.RightService.redisExpireTime=3600
#\u6743\u9650\u7F13\u5B58\u7D22\u5F15\uFF0C\u9ED8\u8BA47
externalSys.RightService.redisIndex=13
#\u5916\u90E8\u7CFB\u7EDF\u7AD9\u70B9\u7F13\u5B58\u65F6\u95F4\uFF0C\u9ED8\u8BA47200
externalSys.SiteService.redisExpireTime=7200
#\u5916\u90E8\u7CFB\u7EDF\u7AD9\u70B9\u7F13\u5B58\u7D22\u5F15\uFF0C\u9ED8\u8BA46
externalSys.SiteService.redisIndex=6
#\u5355\u70B9\u6A21\u5F0F\u65F6redis\u7684IP\uFF0C\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u586B\u5199
spring.redis.host=**********
#\u5355\u70B9\u6A21\u5F0F\u65F6redis\u7684\u7AEF\u53E3
spring.redis.port=6379
#redis\u7684\u5BC6\u7801\uFF0C\u672A\u8BBE\u7F6E\u65F6\u8BE5\u5B57\u6BB5\u4E3A\u7A7A
spring.redis.pwd=TRS@123!@#
#redis\u96C6\u7FA4
spring.redis.nodes=
#redis\u54E8\u5175\u6A21\u5F0F\u7684masterName\uFF08\u4F7F\u7528\u5355\u70B9\u6A21\u5F0F\u65F6\uFF0C\u8BE5\u5C5E\u6027\u5FC5\u987B\u4E3A\u7A7A\uFF09
spring.redis.masterName=
#\u4E0A\u4F20\u6587\u4EF6\u7684\u5927\u5C0F
spring.servlet.multipart.max-file-size=10000MB
#\u4E0A\u4F20\u8BF7\u6C42\u7684\u5927\u5C0F
spring.servlet.multipart.max-request-size=10000MB
#\u6587\u4EF6\u4E0A\u4F20\u7684\u6839\u8DEF\u5F84,\u8FD9\u4E2A\u8DEF\u5F84\u4E0B\u4F1A\u81EA\u52A8\u751F\u6210\u591A\u4E2A\u6587\u4EF6\u5939\uFF0C\u7528\u4E8E\u5B58\u653E\u4E0D\u540C\u7684\u6570\u636E
FileManagerService.UploadDir=/TRS/appWebSite/DATA/WORKORDER/
#FileManagerService.UploadDir\u7684HTTP\u6620\u5C04
FileManagerService.UrlPrefix=http://***********/appWebSiteUpload
#\u5141\u8BB8\u4E0A\u4F20\u7684\u6587\u4EF6\u540E\u7F00\uFF08\u4F7F\u7528;\u5206\u9694\uFF09
FileManagerService.AllowFileExts=jpg;jpeg;png;gif;xls;xlsx;doc;docx;ppt;pdf;pptx;zip;rar;mp4
#\u6743\u9650\u7CFB\u7EDF\u7684\u8BF7\u6C42\u63A5\u53E3
upmsApiUrl.vaule=http://***********/upms/group.do
#\u6743\u9650\u7CFB\u7EDF\u5B50\u7CFB\u7EDF\u6807\u8BC6\uFF0C\u7531\u6743\u9650\u7CFB\u7EDF\u751F\u6210\uFF0C\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u586B\u5199
upms.client.tenant= Hw710N
#\u6743\u9650\u7CFB\u7EDF\u5B50\u7CFB\u7EDF\u8D44\u6E90\u6807\u8BC6\uFF0C\u4E00\u822C\u7B49\u4EF7\u4E8Eupms.client.tenant
upms.client.resourcetype=Hw710N
#\u6743\u9650\u7CFB\u7EDF\u670D\u52A1\u5668\u5730\u5740
upms.service.url=http://***********/upms
#\u6D77\u4E91\u7CFB\u7EDF\u914D\u7F6E\u6587\u4EF6\u767D\u540D\u5355\u7528\u6237 (\u6D77\u4E91\u7CFB\u7EDF\u9700\u8981\u8FDB\u884Cip\u767D\u540D\u5355\u914D\u7F6E,\u914D\u7F6E\u8BF4\u660E\u89C1\u53F3\u4FA7\u56FE\u7247)
hycloud.currUserName=dev
#\u6D77\u4E91\u63D0\u4F9B\u670D\u52A1\u63A5\u53E3\u8282\u70B9\u7684\u5730\u5740
hycloud.url=http://***********/gov/opendata.do
#\u6D77\u4E91\u63A5\u53E3\u670D\u52A1\u540D\u79F0
hycloud.serviceid=gov_site
#\u6D77\u4E91\u63A5\u53E3\u65B9\u6CD5\u540D\u79F0
hycloud.methodname=querySitesOnEditorCenter
#\u6D77\u4E91\u6743\u9650key\u503C
hycloud.key=
#\u6587\u4EF6\u4E0B\u8F7D\u7684Url\u524D\u7F00 [\u7F51\u5173\u8BBF\u95EE\u5730\u5740/file/downloadFile]
FileManagerService.downloadUrlPrefix=
#\u670D\u52A1\u5355\u4F4D\u5728IDS\u4E2D\u9ED8\u8BA4\u7684\u7236\u7EC4\u7EC7\u7684GroupKey\uFF0C\u9700\u8981\u6839\u636E\u5B9E\u9645\u60C5\u51B5\u586B\u5199(IDS\u6570\u636E\u5E93idsgroup\u8868\u7684grpname\u5B57\u6BB5\u7684\u503C)
IDS.Group.parentGroupId=221
#\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u5730\u5740
monitorSite.url=
#\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.param.isp_h=
#\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.param.isp_a=
#\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.param.isp_s=
#\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u53C2\u6570
monitorSite.param.isp_v=
#\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u53C2\u6570\uFF0C\u76D1\u6D4B\u4E91\u79DF\u6237id
monitorSite.param.tenantId=
#\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u53C2\u6570\uFF0C\u76D1\u6D4B\u4E91\u4E2D\u7684\u7528\u6237\u540D
monitorSite.param.userName=
#\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u53C2\u6570\uFF0C\u76D1\u6D4B\u4E91\u7528\u6237\u7684\u7535\u8BDD\u53F7\u7801
monitorSite.param.phone=
#\u83B7\u53D6\u4E34\u65F6token\u7684url,\u83B7\u53D6\u76D1\u63A7\u4E91\u7AD9\u70B9\u5217\u8868\u6570\u636E\u63A5\u53E3\u9700\u8981\u7528\u5230\u8BE5token
monitorSite.access_token.url=
#\u8BE5\u503C\u9700\u8981\u4E0Eadpter\u4E2Des\u7684\u65F6\u95F4\u683C\u5F0F\u4E00\u81F4\uFF0C\u5426\u5219\u4F1A\u62A5\u9519
es.date.format=
#ES \u76F8\u5173\u914D\u7F6E,\u96C6\u7FA4\u7528\u9017\u53F7\u9694\u5F00
es.dataSource.url=http://**********:9200
#\u5DE5\u5355\u7684\u7D22\u5F15\u540D\u79F0
workorder.indeces-name=work_order
#\u901A\u77E5\u7684\u7D22\u5F15\u7C7B\u578B\u540D\u79F0
notice.indices-type=notice_detail
#\u7AD9\u70B9\u7684\u7D22\u5F15\u7C7B\u578B\u540D\u79F0
siteRelation.indices-type=site_relation
#\u5355\u4F4D\u7684\u7D22\u5F15\u7C7B\u578B\u540D\u79F0
unit.indices-type=unit
#\u7528\u6237\u7684\u7D22\u5F15\u7C7B\u578B\u540D\u79F0
user.indices-type=user
#\u5DE5\u5355\u7684\u7D22\u5F15\u7C7B\u578B\u540D\u79F0
workorder.indices-type=work_order_detail
#\u901A\u77E5\u6D88\u606F\u5C06\u8981\u63A8\u9001\u81F3\u7684\u5E73\u53F0,\u901A\u8FC7\u4ECE\u5DE6\u5230\u53F3\u7684\u987A\u5E8F\u8FDB\u884C\u6267\u884C\u63A8\u9001,\u5EFA\u8BAE\u5C06\u63A8\u9001\u81F3\u5DE5\u5355\u5199\u5728\u6700\u524D\u9762,\u9632\u6B62\u63A8\u9001\u81F3\u7B2C\u4E09\u65B9\u5E73\u53F0\u65F6\u7F51\u7EDC\u65F6\u95F4\u7684\u54CD\u5E94\u95EE\u9898
# 1.\u5DE5\u5355,2.\u7B2C\u4E09\u65B9\u63A5\u53E3
\u6CE8\uFF1A\u5DE5\u5355\u7CFB\u7EDF\u7684\u5185\u7F51\u73AF\u5883\u53EA\u9700\u8981\u914D\u7F6E 1 \u5373\u53EF
different.channel.list=gz_work_order
#\u63A8\u9001\u81F3\u7B2C\u4E09\u65B9\u63A5\u53E3\u4E2D\u90A3\u4E9B\u6E20\u9053,\u591A\u4E2A\u7528\u9017\u53F7\u9694\u5F00 1.\u5FAE\u4FE1\u516C\u4F17\u53F7
thirdPartyInterface.of.channels=
#\u63A8\u9001\u7684\u7528\u6237\u540D
push.user-name=
#\u63A8\u9001\u7684\u6388\u6743\u7801
push.auth-code=
#token \u7684\u8FC7\u671F\u65F6\u95F4 10\u5206\u949F
token.expiration.time=
#token\u5B58\u5728\u7684redis\u5E93
token.expiration.index=
#\u83B7\u53D6tokend\u7684Url\u5730\u5740
token.url=
#\u63A8\u9001\u6570\u636E\u5730\u5740
push.url=
#\u7B2C\u4E00\u6B21\u53D1\u9001\u5931\u8D25\u540E,\u9700\u8981\u91CD\u8BD5\u7684\u6B21\u6570
fail.retry.send.count=
#\u63A8\u9001\u81F3\u5FAE\u4FE1\u516C\u4F17\u53F7,\u8FD4\u56DE\u5B57\u6BB5\uFF1A\u6D4B\u8BD5\u73AF\u5883\u4E3A\uFF1Atest,\u6B63\u5F0F\u73AF\u5883\u4E3A:szh
not-send-mobile.field=
#nacos\u7684\u5730\u5740
spring.cloud.nacos.config.server-addr=***********:8848
#nacos\u4E2D\u914D\u7F6E\u7684\u683C\u5F0F\uFF08\u4E00\u822C\u4E0D\u7528\u52A8\uFF09
spring.cloud.nacos.config.file-extension=properties
#nacos\u547D\u540D\u7A7A\u95F4ID
spring.cloud.nacos.config.namespace=
#nacos\u914D\u7F6E\u7684DataId
spring.cloud.nacos.config.ext-config[0].data-id=workorder.properties
#\u540Cspring.cloud.nacos.config.server-addr
spring.cloud.nacos.discovery.server-addr=***********:8848
#\u670D\u52A1\u6CE8\u518C\u7684\u5730\u5740\uFF0C\u4E00\u822C\u4F7F\u7528nacos\u6765\u505A\uFF0C\u4FEE\u6539\u6210\u771F\u5B9E\u7684nacos\u7684IP\u548C\u7AEF\u53E3\u5373\u53EF
dubbo.registry.address=nacos://***********:8848
#\u6570\u636E\u5E93jdbc\u8FDE\u63A5\u4FE1\u606F
spring.datasource.url=*************************************************************************
#\u6570\u636E\u5E93\u7528\u6237\u540D
spring.datasource.username=root
#\u6570\u636E\u5E93\u5BC6\u7801
spring.datasource.password=trsadmin
#IDSsso\u5730\u5740
ids.ssoUrl=http://***********/ids/LoginServlet
#IDS\u4F7F\u7528HTTP\u534F\u8BAE\u901A\u8BAF
ids.protocol.http=true
#IDS\u8BF7\u6C42\u63A5\u53E3
ids.protocol.http.url=http://***********/ids/protocol
#\u6240\u8981\u8FDE\u63A5IDS\u7684IP(\u6216\u4E3B\u673A)
ids.idm.server.host=***********
#\u6240\u8981\u8FDE\u63A5IDS\u7684\u540E\u53F0SSLServer\u7AEF\u53E3.\u5FC5\u586B.\u5982\u679C\u4E0D\u586B\u9ED8\u8BA4\u4E3A2005.
ids.idm.server.port=2005
#IDS\u521B\u5EFAsocket\u6570
ids.idm.sockets.amount=5
#\u534F\u4F5C\u5E94\u7528\u540D. \u5FC5\u586B, \u5E76\u4E14\u5FC5\u987B\u548C\u5728\u6240\u8981\u8FDE\u63A5IDS\u4E0A\u6CE8\u518C\u7684\u534F\u4F5C\u5E94\u7528\u540D\u4FDD\u6301\u4E00\u81F4.
ids.agent.name=workorder
#\u534F\u52A9\u5E94\u7528\u52A0\u5BC6key
ids.des.key=12345678
#ids\u767B\u5F55\u6210\u529F\u540E\u8DF3\u8F6C\u5730\u5740\uFF08\u524D\u7AEF\u5305\u7684\u8BBF\u95EE\u5730\u5740\uFF09
ids.afterLoginOk.gotoUrl=http://***********/workorderweb/#/homePage
#ids\u767B\u5F55\u5931\u8D25\u540E\u8DF3\u8F6C\u5730\u5740\uFF08\u524D\u7AEF\u5305\u7684\u8BBF\u95EE\u5730\u5740\uFF09
ids.afterLoginFail.gotoUrl=http://***********/workorderweb/#/homePage
#\u7528\u6237\u6A21\u5757\u7AEF\u53E3
User-Service-server.port=18080
#\u5DE5\u5355\u6A21\u5757\u7AEF\u53E3
WorkOrder-Service-server.port=18081
#\u6587\u4EF6\u6A21\u5757\u7AEF\u53E3
FileManager-Service-server.port=18082
#\u6D88\u606F\u6A21\u5757\u7AEF\u53E3
Message-Service-server.port=18083
#\u7CFB\u7EDF\u914D\u7F6E\u6A21\u5757\u7AEF\u53E3
SystemManagement-Service-server.port=18084
#\u68C0\u7D22\u6A21\u5757\u7AEF\u53E3
Search-Service-server.port=18085
#\u7EDF\u8BA1\u6A21\u5757\u7AEF\u53E3
StatisticsCenter-Service-server.port=18086
#\u4E92\u52A8\u6A21\u5757\u7AEF\u53E3
Interaction-Service-server.port=18087
#\u5916\u90E8\u7CFB\u7EDF\u6A21\u5757\u7AEF\u53E3
ExternalSystem-Service-server.port=18088
#\u7F51\u5173\u6A21\u5757\u7AEF\u53E3
Gateway-Service-server.port=18089
#gateway\u914D\u7F6E\u7684DataId
gateway.spring.cloud.nacos.config.ext-config[0].data-id=gateway.yml
#gateway\u4E2D\u914D\u7F6E\u7684\u683C\u5F0F
gateway.spring.cloud.nacos.config.file-extension=yml
#\u7528\u6237\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
User-Service-server.dubbo.port=28080
#\u5DE5\u5355\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
WorkOrder-Service-server.dubbo.port=28081
#\u6587\u4EF6\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
FileManager-Service-server.dubbo.port=28082
#\u6D88\u606F\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
Message-Service-server.dubbo.port=28083
#\u7CFB\u7EDF\u914D\u7F6E\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
SystemManagement-Service-server.dubbo.port=28084
#\u68C0\u7D22\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
Search-Service-server.dubbo.port=28085
#\u7EDF\u8BA1\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
StatisticsCenter-Service-server.dubbo.port=28086
#\u4E92\u52A8\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
Interaction-Service-server.dubbo.port=28087
#\u5916\u90E8\u7CFB\u7EDF\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
ExternalSystem-Service-server.dubbo.port=28088
#\u7F51\u5173\u6A21\u5757\u4F7F\u7528\u7684dubbo\u7AEF\u53E3
Gateway-Service-server.dubbo.port=28089
