# nacos\u76F8\u5173\u914D\u7F6E
spring.cloud.nacos.config.server-addr=127.0.0.1:8848
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.config.namespace=cd5910a1-01db-43f1-bdc5-cc78faf4d850
spring.cloud.nacos.config.ext-config[0].data-id=workorder.properties
spring.cloud.nacos.discovery.server-addr=127.0.0.1:8848
dubbo.registry.address=nacos://127.0.0.1:8848
###################################################################################
# \u6570\u636E\u5E93\u76F8\u5173\u914D\u7F6E
#spring.datasource.url=*******************************************
#spring.datasource.username=ccb
#spring.datasource.password=123456
spring.datasource.url=*******************************************************************************
spring.datasource.username=worksheet
spring.datasource.password=rutR53nr
###################################################################################
#IDS\u76F8\u5173\u914D\u7F6E
ids.ssoUrl=http://***********/ids/LoginServlet
#\u662F\u5426\u4F7F\u7528HTTP\u534F\u8BAE\u7684\u65B9\u5F0F\u4E0EIDS\u8FDE\u63A5\u3002\u5982\u679C\u6B64\u5904\u586B\u5199true\uFF0C\u90A3\u4E48Agent\u4F1A\u4F18\u5148\u4F7F\u7528HTTP\u901A\u9053\u7684\u65B9\u5F0F\u4E0EIDS\u8FDB\u884C\u8FDE\u63A5\uFF0Cidm.server.*\u7684\u914D\u7F6E\u9879\u6240\u914D\u7F6E\u7684\u53C2\u6570\u4F1A\u81EA\u52A8\u5931\u6548
ids.protocol.http=true
ids.protocol.http.url=http://***********/ids/protocol
#\u6240\u8981\u8FDE\u63A5IDS\u7684IP(\u6216\u4E3B\u673A). \u5FC5\u586B.
ids.idm.server.host=***********
#\u6240\u8981\u8FDE\u63A5IDS\u7684\u540E\u53F0SSLServer\u7AEF\u53E3.\u5FC5\u586B.\u5982\u679C\u4E0D\u586B\u9ED8\u8BA4\u4E3A2005.
ids.idm.server.port=2005
#\u521B\u5EFAsocket\u6570
ids.idm.sockets.amount=5
#\u534F\u4F5C\u5E94\u7528\u540D. \u5FC5\u586B, \u5E76\u4E14\u5FC5\u987B\u548C\u5728\u6240\u8981\u8FDE\u63A5IDS\u4E0A\u6CE8\u518C\u7684\u534F\u4F5C\u5E94\u7528\u540D\u4FDD\u6301\u4E00\u81F4.
ids.agent.name=workorder
#\u534F\u52A9\u5E94\u7528\u52A0\u5BC6key
ids.des.key=12345678
###ids\u767B\u5F55\u6210\u529F\u540E\u8DF3\u8F6C\u5730\u5740\uFF08\u524D\u7AEF\u5305\u7684\u8BBF\u95EE\u5730\u5740\uFF09
ids.afterLoginOk.gotoUrl=http://***********:9091/workorderweb/#/homePage
###ids\u767B\u5F55\u5931\u8D25\u540E\u8DF3\u8F6C\u5730\u5740\uFF08\u524D\u7AEF\u5305\u7684\u8BBF\u95EE\u5730\u5740\uFF09
ids.afterLoginFail.gotoUrl=http://***********:9091/workorderweb/#/homePage
###################################################################################
#\u5404\u670D\u52A1\u6240\u5360\u7AEF\u53E3\uFF08\u53EF\u4EE5\u4FEE\u6539\u53EA\u8981\u4E0D\u51B2\u7A81\u5373\u53EF\uFF09
User-Service-server.port=18080
WorkOrder-Service-server.port=18081
FileManager-Service-server.port=18082
Message-Service-server.port=18083
SystemManagement-Service-server.port=18084
Search-Service-server.port=18085
StatisticsCenter-Service-server.port=18086
Interaction-Service-server.port=18087
ExternalSystem-Service-server.port=18088
Gateway-Service-server.port=18089
Export-Service-server.port=18090
#\u5404\u670D\u52A1dubbo\u6240\u5360\u7AEF\u53E3\uFF08\u53EF\u4EE5\u4FEE\u6539\u53EA\u8981\u4E0D\u51B2\u7A81\u5373\u53EF\uFF09
User-Service-server.dubbo.port=28080
WorkOrder-Service-server.dubbo.port=28081
FileManager-Service-server.dubbo.port=28082
Message-Service-server.dubbo.port=28083
SystemManagement-Service-server.dubbo.port=28084
Search-Service-server.dubbo.port=28085
StatisticsCenter-Service-server.dubbo.port=28086
Interaction-Service-server.dubbo.port=28087
ExternalSystem-Service-server.dubbo.port=28088
Gateway-Service-server.dubbo.port=28089
Export-Service-server.dubbo.port=28090

# gateway\u914D\u7F6E\u7684DataId
gateway.spring.cloud.nacos.config.ext-config[0].data-id=gateway.yml
# gateway\u4E2D\u914D\u7F6E\u7684\u683C\u5F0F
gateway.spring.cloud.nacos.config.file-extension=yml
