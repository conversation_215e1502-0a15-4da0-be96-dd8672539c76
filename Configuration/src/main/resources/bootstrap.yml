spring:
  cloud:
    nacos:
      discovery:
        server-addr: @spring.cloud.nacos.discovery.server-addr@
        namespace: @spring.cloud.nacos.config.namespace@
        username: @spring.cloud.nacos.username@
        password: @spring.cloud.nacos.password@
      config:
        server-addr: @spring.cloud.nacos.config.server-addr@
        file-extension: @spring.cloud.nacos.config.file-extension@
        namespace: @spring.cloud.nacos.config.namespace@
        username: @spring.cloud.nacos.username@
        password: @spring.cloud.nacos.password@
        ext-config[0]:
          data-id: @spring.cloud.nacos.config.ext-config[0].data-id@
          refresh: true
  datasource:
    url: @spring.datasource.url@
    username: @spring.datasource.username@
    password: @spring.datasource.password@
    hikari:
      connection-init-sql: SET NAMES utf8mb4
  main:
    allow-bean-definition-overriding: true
dubbo:
  protocol:
    name: dubbo
  registry:
    address: @dubbo.registry.address@

