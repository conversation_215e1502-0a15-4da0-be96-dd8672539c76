<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>workorder-system-parent</artifactId>
        <groupId>com.trs.gov</groupId>
        <version>1.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>Search</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>Search-Web</module>
        <module>Search-Service</module>
    </modules>
    <dependencies>
        <dependency>
            <groupId>com.trs.gov</groupId>
            <artifactId>Core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.trs.gov</groupId>
            <artifactId>Configuration</artifactId>
        </dependency>
        <dependency>
            <artifactId>WorkOrder-Service</artifactId>
            <groupId>com.trs.gov</groupId>
        </dependency>

        <dependency>
            <groupId>com.trs.es</groupId>
            <artifactId>esbean</artifactId>
            <version>ES7.4.28-fix</version>
        </dependency>
    </dependencies>
</project>