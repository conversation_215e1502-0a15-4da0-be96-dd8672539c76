package com.trs.gov.search.DO;

import com.trs.es.esbean.annotation.EsColumn;
import com.trs.es.esbean.annotation.EsId;
import com.trs.es.esbean.annotation.EsIndicesName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：UnitDO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/21 10:08
 **/
@Data
@EsIndicesName(indicesName = "${unit.indices-type}")
public class UnitDO {

    public static final String OBJ_TYPE = "unit";

    @EsId
    private Long id;

    /**
     * 创建时间
     */
    @EsColumn(field = "cr_time")
    private Date crTime;

    /**
     * 创建人
     */
    @EsColumn(field = "cr_user")
    private String crUser;

    /**
     * 单位类型
     */
    @EsColumn(field = "unit_type")
    private String unitType;

    /**
     * 单位名称
     */
    @EsColumn(field = "unit_name")
    private String unitName;

    /**
     * 单位负责人
     */
    @EsColumn(field = "unit_master")
    private String unitMaster;

    /**
     * 运维单位负责人电话
     */
    @EsColumn(field = "phone")
    private String phone;

    /**
     * 联系邮箱
     */
    @EsColumn(field = "email")
    private String email;

    /**
     * 单位地址
     */
    @EsColumn(field = "unit_addr")
    private String unitAddr;

    /**
     * 单位简介
     */
    @EsColumn(field = "unit_desc")
    private String unitDesc;

    /**
     * 单位编码
     */
    @EsColumn(field = "unit_code")
    private String unitCode;

    /**
     * 业务领域
     */
    @EsColumn(field = "business_area")
    private String businessArea;

    /**
     * 单位人员数量
     */
    @EsColumn(field = "person_count")
    private Integer personCount;

    /**
     * 单位状态（1,启用;-1,停用)
     */
    @EsColumn(field = "status")
    private Integer status;

    /**
     * 更新时间
     */
    @EsColumn(field = "update_time")
    private Date updateTime;

    /**
     *单位负责人用于显示的名称
     */
    @EsColumn(field = "unit_master_true_name")
    private String unitMasterTrueName;

    /**
     * 创建人用于显示的名称
     */
    @EsColumn(field = "true_name")
    private String trueName;
}
