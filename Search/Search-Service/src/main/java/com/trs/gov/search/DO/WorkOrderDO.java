package com.trs.gov.search.DO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.es.esbean.annotation.EsColumn;
import com.trs.es.esbean.annotation.EsId;
import com.trs.es.esbean.annotation.EsIndicesName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-09 14:21
 * @version 1.0
 * @since 1.0
 * 工单对象
 */
@EsIndicesName(indicesName = "${workorder.indices-type}")
public class WorkOrderDO implements Serializable {
    @EsId
    private Long id;

    /**
     * 创建时间
     */
    @EsColumn(field = "cr_time",highlight = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    /**
     * 创建人
     */
    @EsColumn(field = "cr_user",highlight = false)
    private String crUser;

    /**
     * 状态
     */
    @EsColumn(field = "status",highlight = false)
    private Integer status;
    /**
     * 工单顶级类别id work_order_top_type_id
     */
    @EsColumn(field = "work_order_top_type_id",highlight = false)
    private Long workOrderTopTypeId;

    /**
     * 工单顶级类别id
     */
    @EsColumn(field = "work_order_top_type_name",highlight = false)
    private String workOrderTopTypeName;

    /**
     * 工单类别id
     */
    @EsColumn(field = "work_order_type_id",highlight = false)
    private Long workOrderTypeId;

    /**
     * 工单类别名
     */
    @EsColumn(field = "work_order_type_name",highlight = false)
    private String workOrderTypeName;

    /**
     * 发起单位id
     */
    @EsColumn(field = "cr_unit_id",highlight = false)
    private Long crUnitId;

    /**
     * 发起单位
     */
    @EsColumn(field = "cr_unit",highlight = false)
    private String crUnit;

    /**
     * 发起人
     */
    @EsColumn(field = "cr_username",highlight = false)
    private String crUsername;

    /**
     * 发起人
     */
    @EsColumn(field = "cr_truename",highlight = false)
    private String crTruename;

    /**
     * 站点id
     */
    @EsColumn(field = "site_id",highlight = false)
    private Long siteId;

    /**
     * 站点名
     */
    @EsColumn(field = "sitename",highlight = false)
    private String sitename;

    /**
     * 描述
     */
    @EsColumn(field = "content",highlight = false)
    private String content;

    /**
     * 标题
     */
    @EsColumn(field = "title",highlight = false)
    private String title;

    /**
     * 优先级
     */
    @EsColumn(field = "priority",highlight = false)
    private String priority;

    /**
     * 主办单位id
     */
    @EsColumn(field = "host_unit_id",highlight = false)
    private Long hostUnitId;

    /**
     * 主办单位
     */
    @EsColumn(field = "host_unit",highlight = false)
    private String hostUnit;

    /**
     * 主办人指定类型（1：指定到人，2：指定到单位）
     */
    @EsColumn(field = "host_assign_type")
    private Integer hostAssignType;

    public Integer getHostAssignType() {
        return hostAssignType;
    }

    public void setHostAssignType(Integer hostAssignType) {
        this.hostAssignType = hostAssignType;
    }

    public Integer getDealAssignType() {
        return dealAssignType;
    }

    public void setDealAssignType(Integer dealAssignType) {
        this.dealAssignType = dealAssignType;
    }

    /**
     * 主办人用户名
     */
    @EsColumn(field = "host_username",highlight = false)
    private String hostUsername;

    /**
     * 主办人真实姓名
     */
    @EsColumn(field = "host_truename",highlight = false)
    private String hostTruename;

    /**
     * 受理单位id
     */
    @EsColumn(field = "deal_unit_id",highlight = false)
    private Long dealUnitId;

    /**
     * 受理单位
     */
    @EsColumn(field = "deal_unit",highlight = false)
    private String dealUnit;

    /**
     * 处理人指定类型（1：指定到人，2：指定到单位）
     */
    @EsColumn(field = "deal_assign_type")
    private Integer dealAssignType;
    /**
     * 受理人用户名
     */
    @EsColumn(field = "deal_username",highlight = false)
    private String dealUsername;

    /**
     * 受理人真实姓名
     */
    @EsColumn(field = "deal_truename",highlight = false)
    private String dealTruename;

    /**
     * 渠道类型
     */
    @EsColumn(field = "media_type",highlight = false)
    private Integer mediaType;

    /**
     * 期望完成时间
     */
    @EsColumn(field = "expected_end_date",highlight = false)
    private Date expectedEndDate;

    /**
     * 更新时间
     */
    @EsColumn(field = "update_time",highlight = false)
    private Date updateTime;

    /**
     * 收到工单时间
     */
    @EsColumn(field = "receive_time",highlight = false)
    private Date receiveTime;

    /**
     * 是否删除
     */
    @EsColumn(field = "is_delete",highlight = false)
    private Integer isDelete;

    /**
     * 响应时间
     */
    @EsColumn(field = "action_time",highlight = false)
    private Date actionTime;

    /**
     * 是否是回退
     */
    @EsColumn(field = "is_return",highlight = false)
    private Integer isReturn;

    /**
     * 工单父类型 Id
     */
    @EsColumn(field = "work_order_parent_type_id",highlight = false)
    private Long workOrderParentTypeId;
    /**
     * 工单父类型名称
     */
    @EsColumn(field = "work_order_parent_type_name",highlight = false)
    private String workOrderParentTypeName;

    /**
     * 来源
     */
    @EsColumn(field = "source",highlight = false)
    private String source;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCrTime() {
        return crTime;
    }

    public void setCrTime(Date crTime) {
        this.crTime = crTime;
    }

    public String getCrUser() {
        return crUser;
    }

    public void setCrUser(String crUser) {
        this.crUser = crUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getWorkOrderTopTypeId() {
        return workOrderTopTypeId;
    }

    public void setWorkOrderTopTypeId(Long workOrderTopTypeId) {
        this.workOrderTopTypeId = workOrderTopTypeId;
    }

    public String getWorkOrderTopTypeName() {
        return workOrderTopTypeName;
    }

    public void setWorkOrderTopTypeName(String workOrderTopTypeName) {
        this.workOrderTopTypeName = workOrderTopTypeName;
    }

    public Long getWorkOrderTypeId() {
        return workOrderTypeId;
    }

    public void setWorkOrderTypeId(Long workOrderTypeId) {
        this.workOrderTypeId = workOrderTypeId;
    }

    public String getWorkOrderTypeName() {
        return workOrderTypeName;
    }

    public void setWorkOrderTypeName(String workOrderTypeName) {
        this.workOrderTypeName = workOrderTypeName;
    }

    public Long getCrUnitId() {
        return crUnitId;
    }

    public void setCrUnitId(Long crUnitId) {
        this.crUnitId = crUnitId;
    }

    public String getCrUnit() {
        return crUnit;
    }

    public void setCrUnit(String crUnit) {
        this.crUnit = crUnit;
    }

    public String getCrUsername() {
        return crUsername;
    }

    public void setCrUsername(String crUsername) {
        this.crUsername = crUsername;
    }

    public String getCrTruename() {
        return crTruename;
    }

    public void setCrTruename(String crTruename) {
        this.crTruename = crTruename;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public String getSitename() {
        return sitename;
    }

    public void setSitename(String sitename) {
        this.sitename = sitename;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public Long getHostUnitId() {
        return hostUnitId;
    }

    public void setHostUnitId(Long hostUnitId) {
        this.hostUnitId = hostUnitId;
    }

    public String getHostUnit() {
        return hostUnit;
    }

    public void setHostUnit(String hostUnit) {
        this.hostUnit = hostUnit;
    }

    public String getHostUsername() {
        return hostUsername;
    }

    public void setHostUsername(String hostUsername) {
        this.hostUsername = hostUsername;
    }

    public String getHostTruename() {
        return hostTruename;
    }

    public void setHostTruename(String hostTruename) {
        this.hostTruename = hostTruename;
    }

    public Long getDealUnitId() {
        return dealUnitId;
    }

    public void setDealUnitId(Long dealUnitId) {
        this.dealUnitId = dealUnitId;
    }

    public String getDealUnit() {
        return dealUnit;
    }

    public void setDealUnit(String dealUnit) {
        this.dealUnit = dealUnit;
    }

    public String getDealUsername() {
        return dealUsername;
    }

    public void setDealUsername(String dealUsername) {
        this.dealUsername = dealUsername;
    }

    public String getDealTruename() {
        return dealTruename;
    }

    public void setDealTruename(String dealTruename) {
        this.dealTruename = dealTruename;
    }

    public Integer getMediaType() {
        return mediaType;
    }

    public void setMediaType(Integer mediaType) {
        this.mediaType = mediaType;
    }

    public Date getExpectedEndDate() {
        return expectedEndDate;
    }

    public void setExpectedEndDate(Date expectedEndDate) {
        this.expectedEndDate = expectedEndDate;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getActionTime() {
        return actionTime;
    }

    public void setActionTime(Date actionTime) {
        this.actionTime = actionTime;
    }

    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }

    public Long getWorkOrderParentTypeId() {
        return workOrderParentTypeId;
    }

    public void setWorkOrderParentTypeId(Long workOrderParentTypeId) {
        this.workOrderParentTypeId = workOrderParentTypeId;
    }

    public String getWorkOrderParentTypeName() {
        return workOrderParentTypeName;
    }

    public void setWorkOrderParentTypeName(String workOrderParentTypeName) {
        this.workOrderParentTypeName = workOrderParentTypeName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
