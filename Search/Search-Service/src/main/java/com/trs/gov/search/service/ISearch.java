package com.trs.gov.search.service;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.search.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.NoticeSearchDTO;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.VO.NoticeByMonthVO;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;
import java.util.Map;


/**
 * @ClassName：CurdWorkOrderService
 * @Description :  只需要提供一个查询接口
 * <AUTHOR> YangXin
 * @Date 2020/9/10 15:56
 **/
public interface ISearch {

    /**
     * @Description  检索相关的工单
     * @Param [esDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/15 15:45
     **/
    RestfulResults listSearchWorkOrder(WorkOrderSearchDTO workOrderSearchDTO);

    /**
     * @Description  检索通知相关内容
     * @Param [noticeSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.workorder.VO.NoticeByMonthVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/15 16:10
     **/
    RestfulResults<List<NoticeByMonthVO>> listSearchNotice(NoticeSearchDTO noticeSearchDTO);

    /**
     * @Description  通过关键词搜索用户
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/20 19:53
     **/
    RestfulResults<List<UserVO>> listSearchUser(UserSearchDTO dto) throws ServiceException;

    /**
     * @Description  检索站点关系管理
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.management.VO.SiteRelationVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/21 10:02
     **/
    RestfulResults<List<SiteRelationVO>> querySiteRelationList(SiteRelationSearchDTO dto);

    /**
     * @Description  查询单位
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.management.VO.UnitVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/21 10:04
     **/
    RestfulResults<List<UnitVO>> queryUnitList(UnitSearchDTO dto);

    /**
     * @Description  根据配置文件的名字进行全量同步数据
     * @Param [configNames]
     * @return com.trs.web.builder.base.RestfulResults<java.util.Map<java.lang.String,java.lang.String>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/6 17:09
     **/
    RestfulResults<Map<String,String>> adapterMysqlToES(String configNames);

    long saveOrUpdateWorkOrder(WorkOrderDO orderDO);
 }
