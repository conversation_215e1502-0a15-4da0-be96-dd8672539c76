package com.trs.gov.search.service;

import com.trs.common.utils.expression.Expression;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;

/**
 * @ClassName：IBaseMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 17:51
 **/
public interface IBaseMgr {

    /**
     * @Description  返回不同 dto 的 Expression
     * @Param [dto]
     * @return com.trs.common.utils.expression.Expression
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/16 17:52
     **/
    public <T extends BaseDTO> Expression buildExpression(T dto) throws ServiceException, IllegalAccessException;
}
