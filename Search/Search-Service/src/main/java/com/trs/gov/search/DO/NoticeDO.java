package com.trs.gov.search.DO;

import com.trs.es.esbean.annotation.EsColumn;
import com.trs.es.esbean.annotation.EsId;
import com.trs.es.esbean.annotation.EsIndicesName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：NoticeDO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/16 15:30
 **/
@Data
@EsIndicesName(indicesName = "${notice.indices-type}")
public class NoticeDO {
    /**
     * id
     */
    @EsId
    private Long id;

    /**
     * 创建时间
     */
    @EsColumn(field = "cr_time")
    private Date crTime;

    /**
     * 创建人
     */
    @EsColumn(field = "cr_user")
    private String crUser;

    /**
     * 工单id
     */
    @EsColumn(field = "work_order_id")
    private Long workOrderId;

    /**
     * 通知单位id
     */
    @EsColumn(field= "cr_unit_id")
    private Long crUnitId;

    /**
     * 通知单位
     */
    @EsColumn(field = "cr_unit_name")
    private String crUnit;

    /**
     * 通知人员用户名
     */
    @EsColumn(field = "cr_user")
    private String crUserName;

    /**
     * 通知人员真实姓名
     */
    @EsColumn(field = "cr_truename")
    private String crTrueName;

    /**
     * 通知的目标单位id
     */
    @EsColumn(field = "target_unit_id")
    private Long targetUnitId;

    /**
     * 通知的目标单位
     */
    @EsColumn(field = "target_unit")
    private String targetUnit;

    /**
     * 通知的目标人员用户名
     */
    @EsColumn(field = "target_username")
    private String targetUsername;

    /**
     * 通知的目标人员真实姓名
     */
    @EsColumn(field = "target_truename")
    private String targetTruename;

    /**
     * 阅读状态 （0: 未读， 1：已读）
     */
    @EsColumn(field = "status")
    private Integer status = 1;

    /**
     * 发通知类型（1：发送给人，2：发送给单位）
     */
    @EsColumn(field = "type")
    private Integer type;

    @EsColumn(field = "content")
    private String content;



}
