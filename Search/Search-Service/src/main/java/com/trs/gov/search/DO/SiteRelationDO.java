package com.trs.gov.search.DO;

import com.trs.es.esbean.annotation.EsColumn;
import com.trs.es.esbean.annotation.EsId;
import com.trs.es.esbean.annotation.EsIndicesName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：SiteRelationDO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/21 10:08
 **/
@Data
@EsIndicesName(indicesName = "${siteRelation.indices-type}")
public class SiteRelationDO {
    @EsId
    private Long id;

    /**
     * 创建时间
     */
    @EsColumn(field = "cr_time")
    private Date crTime;

    /**
     * 创建人
     */
    @EsColumn(field = "cr_user")
    private String crUser;

    /**
     * 状态
     */
    @EsColumn(field = "status")
    private Integer status;
    /**
     * 站点id
     */
    @EsColumn(field = "site_id")
    private Long siteId;

    /**
     * 站点名称
     */
    @EsColumn(field = "site_name")
    private String siteName;

    /**
     * 渠道类型
     */
    @EsColumn(field = "media_type")
    private Integer mediaType;

    /**
     * 渠道类型名称
     */
    @EsColumn(field = "media_name")
    private String mediaName;

    /**
     * 唯一标识
     */
    @EsColumn(field = "unique_id")
    private String uniqueId;

    /**
     * 备案时间
     */
    @EsColumn(field = "filing_time")
    private Date filingTime;

    /**
     * 建设时间
     */
    @EsColumn(field = "construction_time")
    private Date constructionTime;

    /**
     * 主办单位id
     */
    @EsColumn(field = "host_unit_id")
    private Long hostUnitId;

    /**
     * 主办单位名称
     */
    @EsColumn(field = "host_unit")
    private String hostUnit;

    /**
     * 主管单位id
     */
    @EsColumn(field = "master_unit_id")
    private Long masterUnitId;

    /**
     * 主管单位名称
     */
    @EsColumn(field = "master_unit")
    private String masterUnit;

    /**
     * 运维单位id
     */
    @EsColumn(field = "operation_unit_id")
    private Long operationUnitId;

    /**
     * 运维单位名称
     */
    @EsColumn(field = "operation_unit")
    private String operationUnit;

    /**
     * 运维单位负责人
     */
    @EsColumn(field = "operation_host")
    private String operationHost;

    /**
     * 运维单位负责人电话
     */
    @EsColumn(field = "phone")
    private String phone;

    /**
     * 运维周期 开始
     */
    @EsColumn(field = "operation_start_time")
    private Date operationStartTime;

    /**
     * 运维周期 结束
     */
    @EsColumn(field = "operation_end_time")
    private Date operationEndTime;

    /**
     * 监控云站点
     */
    @EsColumn(field = "monitor_site")
    private String monitorSite;

    /**
     * 更新时间
     */
    @EsColumn(field = "update_time")
    private Date updateTime;

    /**
     * 是否删除(0:未删除，1:删除)
     */
    @EsColumn(field = "is_del")
    private Integer isDel;

    /**
     * 创建人用于显示的名称
     */
    @EsColumn(field = "true_name")
    private String trueName;
}
