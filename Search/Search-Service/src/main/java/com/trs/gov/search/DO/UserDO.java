package com.trs.gov.search.DO;

import com.trs.es.esbean.annotation.EsColumn;
import com.trs.es.esbean.annotation.EsId;
import com.trs.es.esbean.annotation.EsIndicesName;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：UserDO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/20 20:08
 **/
@Data
@EsIndicesName(indicesName = "${user.indices-type}")
public class UserDO {
    @EsId
    private Long id;

    /**
     * 创建时间
     */
    @EsColumn(field = "cr_time")
    private Date crTime;

    /**
     * 创建人
     */
    @EsColumn(field = "cr_user")
    private String crUser;

    /**
     * 状态
     */
    @EsColumn(field = "status")
    private Integer status;
    /**
     * 用户名
     */
    @EsColumn(field = "user_name")
    private String userName;

    /**
     * 密码
     */
    @EsColumn(field = "password")
    private String password;

    /**
     * 真实姓名
     */
    @EsColumn(field = "true_name")
    private String trueName;

    /**
     * 邮箱
     */
    @EsColumn(field = "email")
    private String email;

    /**
     * 电话
     */
    @EsColumn(field = "phone")
    private String phone;

    /**
     * 用户数据源的Key，例如IDS
     */
    @EsColumn(field = "source_key")
    private String sourceKey;

    /**
     * 用户数据源的用户ID
     */
    @EsColumn(field = "source_user_id")
    private String sourceUserId;

    /**
     * 头像
     */
    @EsColumn(field = "avatar")
    private String avatar;
}
