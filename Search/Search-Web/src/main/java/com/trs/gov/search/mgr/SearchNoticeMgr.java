package com.trs.gov.search.mgr;

import com.trs.common.utils.TimeUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.builder.EsSearchBuilder;
import com.trs.es.esbean.builder.EsSearchBuilderFactory;
import com.trs.es.esbean.page.PageList;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.interaction.DTO.StatisticsCountDTO;
import com.trs.gov.interaction.service.OprRecordService;
import com.trs.gov.search.DO.NoticeDO;
import com.trs.gov.search.DO.WorkOrderDO;
import com.trs.gov.search.constant.NoticeSearchDtoFieldContants;
import com.trs.gov.search.repository.NoticeRepository;
import com.trs.gov.workorder.DTO.NoticeSearchDTO;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.VO.NoticeByMonthVO;
import com.trs.gov.workorder.VO.NoticeStatisticsVO;
import com.trs.gov.workorder.VO.NoticeVO;
import com.trs.gov.workorder.constant.FileConstant;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName：NoticeSearchMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/15 16:17
 **/
@Component
@Slf4j
public class SearchNoticeMgr extends BaseMgr{
    @Autowired
    private SearchWorkOrderMgr searchWorkOrderMgr;
    @Reference(check = false, timeout = 60000)
    private IUserService userService;
    @Reference(check = false, timeout = 60000)
    private OprRecordService oprRecordService;
    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileService;
    @Reference(check = false,timeout = 6000)
    private IWorkOrderService workOrderService;
    @Autowired
    private NoticeRepository noticeRepository;

    public RestfulResults<List<NoticeByMonthVO>> listSearchNotice(NoticeSearchDTO dto) throws Exception {
        List<NoticeByMonthVO> noticeByMonthVOS = new ArrayList<>();
        List<NoticeVO> noticeVOS = new ArrayList<>();
        Map<Long, List<NoticeStatisticsVO>> noticeStatistics = null;
        try {
            // 我收到的
            if(dto.getType().intValue() == 0){
                EsSearchBuilder esSearchBuilder = EsSearchBuilderFactory.createNewBuilder(dto.getPageNum(),dto.getPageSize()).where(buildExpression(dto));
                PageList<NoticeDO> esNoticeDOS = noticeRepository.findByExpression(esSearchBuilder);
                if(esNoticeDOS == null || esNoticeDOS.getEsData().size() == 0){
                    return RestfulResults.ok(noticeByMonthVOS).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(0L).addMsg("检索出的数据为空!!");
                }
                String workOrderIds = esNoticeDOS.getEsData().stream().map(a->{ return String.valueOf(a.getWorkOrderId());}).collect(Collectors.joining(","));
                noticeStatistics = findNoticeStatistics(workOrderIds);
                //根据工单Id去重
                for (NoticeDO noticeDO : esNoticeDOS.getEsData()) {
                    NoticeVO noticeVO = new NoticeVO();
                    BaseUtils.copyProperties(noticeDO,noticeVO);
                    //存入其它值
                    setNoticeVOValues(noticeVO,noticeStatistics);
                    noticeVOS.add(noticeVO);
                }
            }
            else if (dto.getType().intValue() == 1){ //我创建的  1
                EsSearchBuilder esSearchBuilder = EsSearchBuilderFactory.createNewBuilder(dto.getPageNum(),dto.getPageSize()).where(buildExpression(dto));
                PageList<WorkOrderDO> workOrderDOPageList = searchWorkOrderMgr.listWorkOrderDO(esSearchBuilder);
                if(CollectionUtils.isEmpty(workOrderDOPageList.getEsData())){
                    return RestfulResults.ok(noticeByMonthVOS).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(0L).addMsg("检索出的数据为空!!");
                }
                String workOrderIds = workOrderDOPageList.getEsData().stream().map(a->{ return String.valueOf(a.getId());}).collect(Collectors.joining(","));
                noticeStatistics = findNoticeStatistics(workOrderIds);
                for (WorkOrderDO workOrderDO : workOrderDOPageList.getEsData()) {
                    NoticeVO noticeVO = new NoticeVO();
                    BaseUtils.copyProperties(workOrderDO,noticeVO);
                    noticeVO.setWorkOrderId(workOrderDO.getId());
                    //存入其它值
                    setNoticeVOValues(noticeVO,noticeStatistics);
                    noticeVOS.add(noticeVO);
                }
            }else {
                throw new ServiceException("请求参数type【"+dto.getType()+"】不合法!");
            }
            if(noticeVOS.size() != 0){
                //按照指定时间格式分组
                Map<String, List<NoticeVO>> collect = noticeVOS.stream().sorted(Comparator.comparing(NoticeVO::getCrTime).reversed())
                        .collect(Collectors.groupingBy(a -> {
                            String dateType = TimeUtils.dateToString(a.getCrTime(), "yyyy-MM");
                            return dateType;
                        }));
                for(Map.Entry<String, List<NoticeVO>> map : collect.entrySet()){
                    NoticeByMonthVO noticeByMonthVO = new NoticeByMonthVO();
                    noticeByMonthVO.setTimeKey(map.getKey());
                    noticeByMonthVO.setList(map.getValue());
                    noticeByMonthVOS.add(noticeByMonthVO);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw new ServiceException(e.getMessage());
        }
        return RestfulResults.ok(noticeByMonthVOS).addPageNum(dto.getPageNum())
                .addPageSize(dto.getPageSize())
                .addTotalCount(Long.valueOf(noticeVOS.size())).addMsg("检索成功!");
    }

    public void setNoticeVOValues(NoticeVO noticeVO,Map<Long, List<NoticeStatisticsVO>> noticeStatistics) throws ServiceException {
        //工单附带的文件
        noticeVO.setPicList(getFile(noticeVO.getWorkOrderId(), FileConstant.WORK_ORDER_FILE_TYPE, FileConstant.PICLIST_FILE_NAME));
        noticeVO.setFileList(getFile(noticeVO.getWorkOrderId(), FileConstant.WORK_ORDER_FILE_TYPE, FileConstant.FILELIST_FILE_NAME));
        //回复数
        noticeVO.setReplyCount(getCommentNum(noticeVO.getWorkOrderId(),OperateNameConstant.REPLY_WORK_ORDER));
        //总数
        //已读数
        //是否未读
        List<NoticeStatisticsVO> noticeStatisticsVOList = noticeStatistics.get(noticeVO.getWorkOrderId());
        if(noticeStatisticsVOList ==null || noticeStatisticsVOList.size() == 0){
            throw new ServiceException("统计出错，未正常获取到工单【"+noticeVO.getWorkOrderId()+"】的总数和阅读数!");
        }
        noticeVO.setReadCount(noticeStatisticsVOList.get(0).getReadCount());
        noticeVO.setTotalCount(noticeStatisticsVOList.get(0).getTotalCount());
        noticeVO.setStatus(noticeStatisticsVOList.get(0).getStatus());
    }

    /**
     * @Description  获取工单的统计数量
     * @Param [noticeDOS]
     * @return java.util.Map<java.lang.Long,java.util.List<com.trs.gov.workorder.VO.NoticeVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/18 16:24
     **/
    public Map<Long, List<NoticeStatisticsVO>> findNoticeStatistics(String workOrderIds) throws Exception {
        WorkOrderSearchDTO workOrderSearchDTO = new WorkOrderSearchDTO();
        workOrderSearchDTO.setIdList(workOrderIds);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderSearchDTO);
        RestfulResults<List<NoticeStatisticsVO>> noticeStatistics = workOrderService.findNoticeStatistics(workOrderSearchDTO);
        checkRestfulResults(noticeStatistics,"findNoticeStatistics");
        Map<Long, List<NoticeStatisticsVO>> collect = noticeStatistics.getDatas().stream().collect(Collectors.groupingBy(NoticeStatisticsVO::getWorkOrderId));
        if(collect == null || collect.size()==0){
            throw new ServiceException("获取工单的统计数量为空!");
        }
        return collect;
    }

    /**
     * @Description  获取文件列表
     * @Param [id, objType, fileName]
     * @return java.util.List<com.trs.gov.file.VO.FileVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/15 18:46
     **/
    public List<FileVO> getFile(Long id, String objType, String fileName){
        List<FileVO> fileVOS = null;
        try {
            ObjDTO objDTO = ObjDTO.of(objType, String.valueOf(id), fileName);
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, objDTO);
            return fileService.getFileListOfObj(objDTO);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return fileVOS;
        }
    }

    /**
     * @Description  获取回复数量
     * @Param [workOrderId, type]
     * @return long
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/16 13:19
     **/
    public long getCommentNum(long workOrderId, String type) throws ServiceException {
        StatisticsCountDTO statisticsCountDTO = new StatisticsCountDTO();
        statisticsCountDTO.setWorkOrderId(workOrderId);
        statisticsCountDTO.setType(type);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, statisticsCountDTO);
        RestfulResults<Long> restfulResults = oprRecordService.addCommentNum(statisticsCountDTO);
        checkRestfulResults(restfulResults,"getCommentNum");
        return restfulResults.getDatas();
    }

    public void checkRestfulResults(RestfulResults restfulResults,String methodName) throws ServiceException {
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException("Method:"+methodName+"调用服务出错! "+restfulResults.getMsg());
        }
    }

    @Override
    public <T extends BaseDTO> Expression buildExpression(T dto) throws ServiceException, IllegalAccessException {
        return super.getFinalExpression(dto, NoticeSearchDtoFieldContants.NOTICE_PRE);
    }
}
