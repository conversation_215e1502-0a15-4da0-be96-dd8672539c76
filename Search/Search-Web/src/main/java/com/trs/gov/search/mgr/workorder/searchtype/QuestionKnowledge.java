package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：QuestionKnowledge
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:52
 **/
@Component
public class QuestionKnowledge extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_QUESTION_KNOWLEDGE);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_QUESTION_KNOWLEDGE);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        return Optional.ofNullable(Condition("status", EsOperator.Equal,WorkOrderConstant.STATUS_OPENED));
    }
}
