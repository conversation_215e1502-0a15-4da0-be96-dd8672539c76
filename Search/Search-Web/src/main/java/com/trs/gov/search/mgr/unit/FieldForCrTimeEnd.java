package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.search.util.TimeUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForCrTimeEnd
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/18 14:36
 **/
@Component
public class FieldForCrTimeEnd extends BaseCommonFieldMgr {
    @Override
    public Optional<Expression> buildCondition(String crTimeEnd) throws ServiceException {
        if(!CMyString.isEmpty(crTimeEnd)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.LessThanOrEqual, TimeUtil.convertUtcTime(crTimeEnd+" 23:59:59")));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.CR_TIME_END;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.CR_TIME_END;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_CR_TIME_START;
    }
}
