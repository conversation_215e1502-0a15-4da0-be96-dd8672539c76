package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForMasterUnitId
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 13:23
 **/
@Component
public class FieldForMasterUnitId extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.MASTER_UNIT_ID;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.MASTER_UNIT_ID;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_MASTER_UNIT_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String masterUnitId) throws ServiceException {
        if(!CMyString.isEmpty(masterUnitId)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.Equal,masterUnitId));
        }
        return Optional.empty();
    }
}
