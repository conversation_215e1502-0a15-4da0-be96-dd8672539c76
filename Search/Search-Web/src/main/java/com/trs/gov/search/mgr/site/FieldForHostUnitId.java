package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForHostUnitID
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 13:23
 **/
@Component
public class FieldForHostUnitId extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.HOST_UNIT_ID;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.HOST_UNIT_ID;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_HOST_UNIT_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String hostUnitId) throws ServiceException {
        if(!CMyString.isEmpty(hostUnitId)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.Equal,hostUnitId));
        }
        return Optional.empty();
    }
}
