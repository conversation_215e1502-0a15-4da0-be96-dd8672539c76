package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.search.mgr.workorder.searchtype.CopyMy;
import com.trs.gov.workorder.DTO.CCSearchDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForCcUnitIdList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:42
 **/
@Component
public class FieldFoCcUnitIdList extends BaseCommonFieldMgr {
    @Autowired
    private CopyMy copyMy;

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.HOST_CC_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.HOST_CC_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.ES_FIELD_WORK_ORDER__ID;
    }

    @Override
    public Optional<Expression> buildCondition(String ccUnitIdList) throws ServiceException {
        if(!CMyString.isEmpty(ccUnitIdList)){
            CCSearchDTO ccSearchDTO = new CCSearchDTO();
            ccSearchDTO.setCcUnitIdList(ccUnitIdList);
            List<Long> workOrderIdsByCCSearch = copyMy.findWorkOrderIdsByCCSearch(ccSearchDTO);
            if(workOrderIdsByCCSearch != null && workOrderIdsByCCSearch.size() != 0){
                return Optional.ofNullable(Condition(searchField(), EsOperator.In,workOrderIdsByCCSearch.toArray()));
            }else{
                return Optional.ofNullable(Condition(searchField(), EsOperator.Equal,"-1"));
            }
        }
        return Optional.empty();
    }
}
