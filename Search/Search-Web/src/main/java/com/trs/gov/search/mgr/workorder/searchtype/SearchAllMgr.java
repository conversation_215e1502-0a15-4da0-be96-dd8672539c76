package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;
import static com.trs.common.utils.expression.ExpressionBuilder.Or;

/**
 * @ClassName：SearchAll
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:50
 **/
@Component
public class SearchAllMgr extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        String loginUser = getLoginUser();
        UnitVO unitVO = getUnitVO();
        List<Long> relatedWorkOrderId = getRelatedWorkOrderId(false, false,
                new String[]{OperateNameConstant.CREATE_WORK_ORDER,
                        OperateNameConstant.UPDATE_WORK_ORDER_DEAL,
                        OperateNameConstant.ASSIGN_WORK_ORDER,
                        OperateNameConstant.ROLLBACK_WORK_ORDER,
                        OperateNameConstant.COPY_WORK_ORDER}, loginUser, unitVO.getId());
        Expression expression1 = null;
        if(!CollectionUtils.isEmpty(relatedWorkOrderId)){
            expression1 = Condition("id", EsOperator.In,relatedWorkOrderId.toArray());
        }
        Expression expression2 = queryByMyUnit(unitVO.getId());
        return Optional.ofNullable(Or(expression1,expression2));
    }
}
