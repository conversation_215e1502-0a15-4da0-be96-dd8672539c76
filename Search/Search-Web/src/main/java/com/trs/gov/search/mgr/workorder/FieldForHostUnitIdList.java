package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForId
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 14:48
 **/
@Component
public class FieldForHostUnitIdList extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.HOST_UNIT_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.HOST_UNIT_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.ES_FIELD_HOST_UNIT_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String hostUnitIdList) throws ServiceException {
        if(!CMyString.isEmpty(hostUnitIdList)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.In,hostUnitIdList.split(",")));
        }
        return Optional.empty();
    }
}
