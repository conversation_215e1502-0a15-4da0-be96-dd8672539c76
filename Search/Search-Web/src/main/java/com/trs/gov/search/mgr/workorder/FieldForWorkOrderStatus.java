package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForStatus
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:51
 **/
@Component
public class FieldForWorkOrderStatus extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.STATUS;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.STATUS;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.ES_FIELD_STATUS;
    }

    @Override
    public Optional<Expression> buildCondition(String status) throws ServiceException {
        if(!CMyString.isEmpty(status)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.In,status.split(",")));
        }
        return Optional.empty();
    }
}
