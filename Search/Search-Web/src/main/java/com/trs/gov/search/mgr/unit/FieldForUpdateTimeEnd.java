package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.search.util.TimeUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForUpdateTimeEnd
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/18 14:45
 **/
@Component
public class FieldForUpdateTimeEnd extends BaseCommonFieldMgr {
    @Override
    public Optional<Expression> buildCondition(String updateTimeEnd) throws ServiceException {
        if(!CMyString.isEmpty(updateTimeEnd)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.LessThanOrEqual, TimeUtil.convertUtcTime(updateTimeEnd+" 23:59:59")));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.UPDATE_TIME_END;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.UPDATE_TIME_END;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_UPDATE_TIME_START;
    }
}
