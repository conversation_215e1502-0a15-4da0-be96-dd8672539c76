package com.trs.gov.search.mgr.notice;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.NoticeSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForNoticeContent
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 18:57
 **/
@Component
public class FieldForNoticeContent extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return NoticeSearchDtoFieldContants.CONTENT;
    }

    @Override
    public String desc() {
        return NoticeSearchDtoFieldContants.CONTENT;
    }

    @Override
    public String searchField() {
        return NoticeSearchDtoFieldContants.ES_CONTENT;
    }

    @Override
    public  Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        if(!CMyString.isEmpty(keyWords)){
            return Optional.ofNullable(Condition(searchField(),EsOperator.Match,keyWords.trim()));
        }
        return Optional.empty();
    }
}
