package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForOperationUnitId
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 13:24
 **/
@Component
public class FieldForOperationUnitId extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.OPERATION_UNIT_ID;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.OPERATION_UNIT_ID;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_OPERATION_UNIT_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String operationUnitId) throws ServiceException {
        if(!CMyString.isEmpty(operationUnitId)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.Equal,operationUnitId));
        }
        return Optional.empty();
    }
}
