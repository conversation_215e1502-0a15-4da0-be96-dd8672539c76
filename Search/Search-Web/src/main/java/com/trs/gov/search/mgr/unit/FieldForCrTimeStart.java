package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.search.util.TimeUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForCrTimeStart
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 11:01
 **/
@Component
public class FieldForCrTimeStart extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.CR_TIME_START;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.CR_TIME_START;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_CR_TIME_START;
    }

    @Override
    public Optional<Expression> buildCondition(String crTimeStart) throws ServiceException {
        if(!CMyString.isEmpty(crTimeStart)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.GreaterThanOrEqual, TimeUtil.convertUtcTime(crTimeStart+" 00:00:00")));
        }
        return Optional.empty();
    }
}
