package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.search.util.TimeUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForUpdateTimeStart
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 11:08
 **/
@Component
public class FieldForUpdateTimeStart extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.UPDATE_TIME_START;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.UPDATE_TIME_START;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_UPDATE_TIME_START;
    }

    @Override
    public Optional<Expression> buildCondition(String updateTimeStart) throws ServiceException {
        if(!CMyString.isEmpty(updateTimeStart)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.GreaterThanOrEqual, TimeUtil.convertUtcTime(updateTimeStart+" 00:00:00")));
        }
        return Optional.empty();
    }
}
