package com.trs.gov.search.mgr;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.builder.EsSearchBuilder;
import com.trs.es.esbean.builder.EsSearchBuilderFactory;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.es.esbean.page.PageList;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.search.DO.SiteRelationDO;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.repository.SiteRelationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：SearchSiteRelation
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/21 10:06
 **/
@Component
public class SearchSiteRelationMgr extends BaseMgr{
    @Autowired
    private SiteRelationRepository siteRelationRepository;

    /**
     * @Description  获取站点检索结果
     * @Param [dto]
     * @return com.trs.es.esbean.page.PageList<com.trs.gov.management.VO.SiteRelationVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/16 18:20
     **/
    public PageList<SiteRelationVO> querySiteRelationList(SiteRelationSearchDTO dto) throws Exception {
        // 构造检索Expression 和  EsSearchBuilder
        EsSearchBuilder esSearchBuilder = EsSearchBuilderFactory.createNewBuilder(dto.getPageNum(),dto.getPageSize()).where(buildExpression(dto));
        PageList<SiteRelationDO> siteRelationDOPageList = siteRelationRepository.findByExpression(esSearchBuilder);
        //构造返回结果
        PageList<SiteRelationVO> resultPage = new PageList<>(dto.getPageNum(),dto.getPageSize());
        resultPage.setTotalCount(siteRelationDOPageList.getTotalCount());
        if(siteRelationDOPageList.getEsData() == null || siteRelationDOPageList.getTotalCount().equals(0L)){
            resultPage.setEsData(new ArrayList<SiteRelationVO>());
        }else {
            //有数据则 将 do 转换成 VO 返回
            List<SiteRelationVO> siteRelationVOs = siteRelationDOPageList.getEsData().stream()
                    .map(a->{
                        SiteRelationVO vo = new SiteRelationVO();
                        BaseUtils.copyProperties(a,vo);
                        return vo;
                    }).collect(Collectors.toList());
            resultPage.setEsData(siteRelationVOs);
        }
        return resultPage;
    }

    /**
     * @Description  添加额外的Expression
     * @Param [dto]
     * @return com.trs.common.utils.expression.Expression
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/16 18:21
     **/
    @Override
    public <T extends BaseDTO> Expression buildExpression(T dto) throws ServiceException, IllegalAccessException {
        return super.getFinalExpression(dto, SiteRelationSearchDtoFieldContants.SITE_RELATION_PRE,Condition(UnitSearchDtoFieldContants.ES_IS_DELETE, EsOperator.Equal, "0"));
    }
}

