package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForUnitCode
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 10:59
 **/
@Component
public class FieldForUnitCode extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.UNIT_CODE;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.UNIT_CODE;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_UNIT_CODE;
    }

    @Override
    public Optional<Expression> buildCondition(String isConnect) throws ServiceException {
        if("1".equals(isConnect)){
            return Optional.ofNullable(Condition("unit_code", EsOperator.FieldExist,""));
        }
        if("0".equals(isConnect)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.FieLdNotExist,""));
        }
        return Optional.empty();
    }
}
