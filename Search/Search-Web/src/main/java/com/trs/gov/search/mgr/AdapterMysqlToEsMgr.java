package com.trs.gov.search.mgr;

import com.trs.common.http2.HttpRequest;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.util.ConfigUtil;
import com.trs.log.exception.RecordableException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName：AdapterMysqlToEsMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/1/6 16:48
 **/
@Component
@Slf4j
public class AdapterMysqlToEsMgr {

    private HttpRequest httpRequest = new HttpRequest.Builder(new OkHttpClient.Builder()
            .connectTimeout(5, TimeUnit.MINUTES)
            .readTimeout(3, TimeUnit.SECONDS)
    ).build();

    public Map<String, String> adapterMysqlToES(String configNames) throws RecordableException, ServiceException {
        Map<String,String> result = new HashMap<>();
        String configUrl = ConfigUtil.getConfigTemplate().getPropertyValue("canal.config.url");
        if(CMyString.isEmpty(configUrl)){
            throw new ServiceException("配置【canal.config.url】不能为空!");
        }
        String[] configName = configNames.split(",");
        if(!configUrl.endsWith("/")){
            configUrl += "/";
        }
        for (String name : configName) {
            String response = httpRequest.doPost(configUrl + name, null);
            log.info("name:"+name+"  response:"+response);
            result.put(name,response);
        }
        return result;
    }
}
