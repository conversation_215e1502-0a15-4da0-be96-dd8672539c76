package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @ClassName：WaitMyReview
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:47
 **/
@Component
public class WaitMyReview extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_REVIEM_REVIEWING);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_REVIEM_REVIEWING);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        Expression expression = consumerMyHost(getLoginUser(), getUnitVO(), false, WorkOrderConstant.STATUS_FINISHED);
        return Optional.ofNullable(expression);
    }
}
