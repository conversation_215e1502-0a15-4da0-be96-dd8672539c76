package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.*;

/**
 * @ClassName：AllDealing
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:50
 **/
@Component
public class AllDealing extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL_DEALING);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL_DEALING);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        String loginUser = getLoginUser();
        UnitVO unitVO = getUnitVO();
        List<Long> ccTargetUnitId = queryCCToMe(true);

        Expression expression = consumerMyDeal(loginUser, unitVO, true, null);

        Expression expression2 = And(
                Or(
                        expression,
                        Condition("id",EsOperator.In,ccTargetUnitId.toArray())
                )
        );
        Expression expression3 = queryByMyUnit(unitVO.getId());

        Expression expression4 = Condition("status",EsOperator.In,new Integer[]{WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING});

        return Optional.ofNullable(And(Or(expression2,expression3),expression4));
    }
}
