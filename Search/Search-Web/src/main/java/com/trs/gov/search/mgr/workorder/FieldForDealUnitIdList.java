package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForDealUnitIdList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:37
 **/
@Component
public class FieldForDealUnitIdList extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.DEAL_UNIT_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.DEAL_UNIT_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.ES_FIELD_DEAL_UNIT_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String dealUnitIdList) throws ServiceException {
        if(!CMyString.isEmpty(dealUnitIdList)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.In,dealUnitIdList.split(","))) ;
        }
        return Optional.empty();
    }
}
