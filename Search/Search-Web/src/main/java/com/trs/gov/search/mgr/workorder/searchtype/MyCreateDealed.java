package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.And;
import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：MyCreateDealed
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:49
 **/
@Component
public class MyCreateDealed extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_CREATED_DEALED);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_CREATED_DEALED);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        return Optional.ofNullable(
                And(
                        Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_CR_UNIT_ID, EsOperator.Equal,getUnitVO().getId()),
                        Condition(WorkOrderSearchDtoFieldContants.Es_CR_USERNAME,EsOperator.Equal,getLoginUser()),
                        Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_STATUS,EsOperator.In,new Integer[]{WorkOrderConstant.STATUS_FINISHED, WorkOrderConstant.STATUS_REVIEWED, WorkOrderConstant.STATUS_OPENED})
                ));
    }
}
