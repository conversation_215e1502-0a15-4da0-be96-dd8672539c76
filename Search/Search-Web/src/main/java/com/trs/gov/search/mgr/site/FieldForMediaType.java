package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForMediaType
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 13:21
 **/
@Component
public class FieldForMediaType extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.MEDIA_TYPE;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.MEDIA_TYPE;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_MEDIA_TYPE;
    }

    @Override
    public Optional<Expression> buildCondition(String mediaType) throws ServiceException {
        if(!CMyString.isEmpty(mediaType)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.Equal,mediaType));
        }
        return Optional.empty();
    }
}
