package com.trs.gov.search.mgr;

import com.trs.gov.core.IFieldMgr;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DTO.WorkOrderTypeSearchDTO;
import com.trs.gov.management.service.UnitGroupRelationService;
import com.trs.gov.management.service.WorkOrderTypeService;
import com.trs.gov.search.util.TimeUtil;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import org.apache.dubbo.config.annotation.Reference;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName：BaseCommonFieldMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 11:11
 **/
public abstract class BaseCommonFieldMgr implements IFieldMgr {

    @Reference(check = false, timeout = 60000)
    private WorkOrderTypeService workOrderTypeService;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Reference(check = false, timeout = 60000)
    private UnitGroupRelationService uitGroupRelationService;

    /**
     * @Description  获取查询字段
     * @Param []
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 14:58
     **/
    public String searchField(){
        return key();
    }

    /**
     * 根据工单类型id获取子级类型id
     *
     * @param ids
     * @return
     * @throws ServiceException
     */
    public List<Long> getWorkTypeChildList(String ids) throws ServiceException {
        WorkOrderTypeSearchDTO workOrderTypeSearchDTO = new WorkOrderTypeSearchDTO();
        workOrderTypeSearchDTO.setId(ids);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderTypeSearchDTO);
        RestfulResults<List<Long>> restfulResults = workOrderTypeService.listAllRelateTypeById(workOrderTypeSearchDTO);
        checkRestfulResults(restfulResults,this.getClass().getName(),"getWorkTypeChildList");
        return restfulResults.getDatas();
    }

    /**
     * @Description  转换成对应的时间格式
     * @Param [inputTime]
     * @return java.lang.String[]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/16 11:12
     **/
    public String[] convertUtcTime(String[] inputTime) throws ServiceException {
        if(inputTime.length != 2){
            throw new ServiceException("传入的时间参数有误");
        }
        return new String[]{TimeUtil.convertUtcTime(inputTime[0]+" 00:00:00"),TimeUtil.convertUtcTime(inputTime[1]+" 23:59:59")};
    }

    /**
     * @Description  检验接口参数
     * @Param [restfulResults]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/16 14:58
     **/
    public void checkRestfulResults(RestfulResults restfulResults,String className,String methodName) throws ServiceException {
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException("方法【" + className +"." + methodName+"()】调用服务失败!"+restfulResults.getMsg());
        }
    }

    public List<Long> listLoginGroupIds(String loginUnitId) throws ServiceException {
        List<Long> groupIdList = uitGroupRelationService.getGroupByUnitId(loginUnitId);
        return groupIdList !=null ? groupIdList : new ArrayList<>();
    }

}
