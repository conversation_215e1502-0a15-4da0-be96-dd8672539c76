package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.constant.WorkOrderTypeConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import io.vavr.control.Try;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;
import static com.trs.common.utils.expression.ExpressionBuilder.Or;

/**
 * @ClassName：FieldForKeywords
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:12
 **/
@Component
public class FieldForKeywords extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.KEYWORDS;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.KEYWORDS;
    }

    /**
     * @Description  获取数据中的数字
     * @Param [content]
     * @return java.util.List<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/28 17:14
     **/
    public List<String> listNumber(String content){
        Pattern p = Pattern.compile("-?\\d+");
        Matcher m = p.matcher(content);
        List<String> numbers = new ArrayList<>();
        while (m.find()) {
            numbers.add(m.group());
        }
        return numbers;
    }

    /**
     * @Description  检验是要查id还是内容中包含的数字
     * @Param [keyWord]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/28 17:33
     **/
    public boolean checkKeyWords(String keyWord){
        Try tryResult = Try.of(()->{return Long.valueOf(keyWord);});
        if(keyWord.contains("#") || tryResult.isSuccess()){
            return true;
        }else {
            return false;
        }
    }

    /**
     * @Description  检查keyWord中是否包含顶级类型
     * @Param [keyword]
     * @return java.util.List<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 16:21
     **/
    public List<String> listKeyWord(String keyword){
        List<String> keywordList = new ArrayList<>();
        if(keyword.contains(WorkOrderTypeConstant.ROOT_GONG_DAN_KEY_DESC)){
            keywordList.add((WorkOrderTypeConstant.ROOT_GONG_DAN_KEY_DESC));
        }
        if(keyword.contains(WorkOrderTypeConstant.ROOT_TOU_SU_KEY_DESC)){
            keywordList.add(WorkOrderTypeConstant.ROOT_TOU_SU_KEY_DESC);
        }
        if(keyword.contains(WorkOrderTypeConstant.ROOT_TONG_ZHI_KEY_DESC)){
            keywordList.add(WorkOrderTypeConstant.ROOT_TONG_ZHI_KEY_DESC);
        }
        return keywordList;
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        if(!CMyString.isEmpty(keyWords)){
            String keyWord = keyWords.trim();
            List<String> strings = listNumber(keyWords);
            Expression expression = null;
            if(strings != null && strings.size() > 0 && checkKeyWords(keyWord)){
                return Optional.ofNullable(Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_WORK_ORDER__ID, EsOperator.In,strings.toArray()));
            }else {
                List<String> listKeyWord = listKeyWord(key());
                Expression expression1 = null;
                if(listKeyWord!=null && listKeyWord.size() > 0){
                    expression = Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_WORK_ORDER_TOP_TYPE_NAME,EsOperator.In,listKeyWord.toArray());
                }
                return Optional.ofNullable(
                        Or(
                                expression,
                                Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_Content,EsOperator.MatchPhrase,keyWord),
                                Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_Title,EsOperator.MatchPhrase,keyWord)
                        )
                );
            }
        }
        return Optional.empty();
    }
}
