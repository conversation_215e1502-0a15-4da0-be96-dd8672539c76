package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldIdList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 14:51
 **/
@Component
public class FieldIdList extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.ES_FIELD_WORK_ORDER__ID;
    }

    @Override
    public Optional<Expression> buildCondition(String ids) throws ServiceException {
        if(!CMyString.isEmpty(ids)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.In,ids.split(",")));
        }
        return Optional.empty();
    }
}
