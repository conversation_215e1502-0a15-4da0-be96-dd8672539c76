package com.trs.gov.search.mgr;

import com.alibaba.fastjson.JSONArray;
import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.builder.EsSearchBuilder;
import com.trs.es.esbean.builder.EsSearchBuilderFactory;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.es.esbean.page.PageList;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.DTO.UnitUserDTO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.service.UnitService;
import com.trs.gov.search.DO.UnitDO;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.repository.UnitRepository;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：SearchUnitMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/21 10:07
 **/
@Component
@Slf4j
public class SearchUnitMgr extends BaseMgr{
    @Autowired
    private UnitRepository unitRepository;
    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileService;
    @Reference(check = false, timeout = 60000)
    private UnitService unitService;
    @Reference(check = false, timeout = 60000)
    private IUserService userService;


    public PageList<UnitVO> queryUnitList(UnitSearchDTO dto) throws Exception {
        // 1.构造expresiion   2.构建esSearchBuilder
        EsSearchBuilder esSearchBuilder = EsSearchBuilderFactory.createNewBuilder(dto.getPageNum(),dto.getPageSize()).where(buildExpression(dto));
        PageList<UnitDO> unitDOPageList = unitRepository.findByExpression(esSearchBuilder);
        // 3.构造返回结果
        PageList<UnitVO> resultPage = new PageList<>(dto.getPageNum(),dto.getPageSize());
        resultPage.setTotalCount(unitDOPageList.getTotalCount());
        if(unitDOPageList.getEsData() == null || unitDOPageList.getTotalCount().equals(0L)){
            resultPage.setEsData(new ArrayList<UnitVO>());
        }else {
            //获取用户相关数据
            List<UnitVO> unitVOS = unitDOPageList.getEsData().stream()
                    .map(a->{
                        UnitVO unitVO = new UnitVO();
                        BaseUtils.copyProperties(a, unitVO);
                        ObjDTO logoDTO = null;
                        try {
                            logoDTO = ObjDTO.of(UnitDO.OBJ_TYPE, String.valueOf(a.getId()), "picList");
                            BaseUtils.setUserInfoToDTO(logoDTO);
                            List<FileVO> logo = fileService.getFileListOfObj(logoDTO);
                            //获取 单位的 logo
                            unitVO.setLogo(logo);
                            List<UserVO> userVOS = new ArrayList<>();
                            if(StringUtils.isEmpty(a.getUnitCode())){
                                unitVO.setUserVOS(userVOS);
                            }else{
                                UnitUserDTO unitUserDTO = new UnitUserDTO();
                                unitUserDTO.setGroupId(a.getUnitCode());
                                unitUserDTO.setPageSize(9999);
                                UserUtils.checkDTOAndLoadUserInfoByDTO(userService, unitUserDTO);
                                RestfulResults<JSONArray> results = unitService.queryUnitUserList(unitUserDTO);
                                checkRestfulResults(results);
                                JSONArray jsonArray = results.getDatas();
                                for (int i = 0; i < jsonArray.size(); i++) {
                                    UserDTO uDTO = new UserDTO();
                                    uDTO.setUserName(jsonArray.getJSONObject(i).getString("username"));
                                    //构造返回单位下的所有用户
                                    UserVO baseUserInfo = userService.getBaseUserInfo(uDTO);
                                    userVOS.add(baseUserInfo);
                                }
                                unitVO.setUserVOS(userVOS);
                            }
                        } catch (ServiceException e) {
                            log.error(e.getMessage(),e);
                        }
                        return unitVO;
                    }).collect(Collectors.toList());
            resultPage.setEsData(unitVOS);
        }
        return resultPage;
    }

    /**
     * @Description  添 额外的 Expression
     * @Param [dto]
     * @return com.trs.common.utils.expression.Expression
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/16 18:17
     **/
    @Override
    public <T extends BaseDTO> Expression buildExpression(T dto) throws ServiceException, IllegalAccessException {
        return super.getFinalExpression(dto,UnitSearchDtoFieldContants.UNIT_PRE,Condition(UnitSearchDtoFieldContants.ES_IS_DELETE, EsOperator.Equal, "0"));
    }

    public void checkRestfulResults(RestfulResults restfulResults) throws ServiceException {
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
    }
}
