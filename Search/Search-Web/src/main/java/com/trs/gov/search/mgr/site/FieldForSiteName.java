package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForSiteName
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 12:31
 **/
@Component
public class FieldForSiteName extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.SITE_NAME;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.SITE_NAME;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_SITE_NAME;
    }

    @Override
    public Optional<Expression> buildCondition(String siteName) throws ServiceException {
        if(!CMyString.isEmpty(siteName)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.MatchPhrasePrefix,siteName.trim()));
        }
        return Optional.empty();
    }
}
