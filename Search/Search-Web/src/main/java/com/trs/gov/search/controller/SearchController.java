package com.trs.gov.search.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.search.service.impl.SearchServiceImpl;
import com.trs.gov.workorder.DTO.NoticeSearchDTO;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：SearchWorkOrderController
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/24 13:30
 **/
@RestController
@RequestMapping("/search/search")
@Api(value = "工单检索类",tags = "SearchController")
public class SearchController {
    @Autowired
    private SearchServiceImpl searchService;

    /**
     * @Description  检索我的工单
     * @Param [workOrderSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/24 19:04
     **/
    @ApiOperation("检索工单内容")
    @ApiImplicitParam(name = "keywords",value = "检索值",required = true,type = "string")
    @RequestMapping(value = "workOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults listSearchWorkOrder(WorkOrderSearchDTO workOrderSearchDTO) throws ServiceException {
        BaseUtils.checkDTO(workOrderSearchDTO);
        return searchService.listSearchWorkOrder(workOrderSearchDTO);
    }

    /**
     * @Description  检索notice
     * @Param [noticeSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/16 13:03
     **/
    @ApiOperation("检索通知信息")
    @ApiImplicitParam(name = "content",value = "检索值",required = true,type = "string")
    @RequestMapping(value = "notice", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults listSearchNotice(NoticeSearchDTO noticeSearchDTO) throws ServiceException {
        BaseUtils.checkDTO(noticeSearchDTO);
        return searchService.listSearchNotice(noticeSearchDTO);
    }

    /**
     * @Description  检索用户
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/20 19:53
     **/
    @ApiOperation("检索用户")
    @ApiImplicitParam(name = "keyWords",value = "检索值",required = true,type = "string")
    @RequestMapping(value = "user", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults listSearchUser(UserSearchDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        return searchService.listSearchUser(dto);
    }

    /**
     * @Description  检索站点关系管理-站点名称
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.management.VO.SiteRelationVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/21 10:02
     **/
    @ApiOperation("检索站点")
    @ApiImplicitParam(name = "siteName",value = "站点名称",required = true,type = "string")
    @RequestMapping(value = "querySiteRelationList", method = {RequestMethod.POST, RequestMethod.GET})
    public RestfulResults<List<SiteRelationVO>> querySiteRelationList(SiteRelationSearchDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        return searchService.querySiteRelationList(dto);
    }

    /**
     * @Description  检索单位
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/21 10:19
     **/
    @ApiOperation("检索单位")
    @ApiImplicitParam(name = "unitName",value = "检索值",required = true,type = "string")
    @RequestMapping(value = "queryUnitList",method = {RequestMethod.POST,RequestMethod.GET})
    public RestfulResults queryUnitList(UnitSearchDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        return searchService.queryUnitList(dto);
    }

    /**
     * @Description  根据配置文件的名字进行全量同步数据
     * @Param [configNames]
     * @return com.trs.web.builder.base.RestfulResults<java.util.Map<java.lang.String,java.lang.String>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/6 17:08
     **/
    @RequestMapping(value = "adapterMysqlToES",method = {RequestMethod.POST,RequestMethod.GET})
    public RestfulResults<Map<String, String>> adapterMysqlToES(String configNames){
        return searchService.adapterMysqlToES(configNames);
    }

}
