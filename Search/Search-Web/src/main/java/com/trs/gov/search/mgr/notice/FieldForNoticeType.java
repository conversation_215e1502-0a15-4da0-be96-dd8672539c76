package com.trs.gov.search.mgr.notice;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.search.constant.NoticeSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.*;

/**
 * @ClassName：FieldForNoticeType
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 18:46
 **/
@Component
public class FieldForNoticeType extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return NoticeSearchDtoFieldContants.TYPE;
    }

    @Override
    public String desc() {
        return NoticeSearchDtoFieldContants.TYPE;
    }

    @Override
    public Optional<Expression> buildCondition(String type) throws ServiceException {
        String loginUser = ContextHelper.getLoginUser().orElseThrow(()->new ServiceException("获取当前登录用户的用户名字失败!"));
        String loginUnitId = ContextHelper.getLoginUnitId().orElseThrow(()->new ServiceException("获取当前登录用户的单位ID失败!"));
        //1：我发出的)
        if(type.equals("1")){
            return Optional.ofNullable(And(Condition("cr_user",EsOperator.Equal,loginUser),Condition("cr_unit_id",EsOperator.Equal,loginUnitId)));
        }else if(type.equals("0")){
            //(0；我收到的，
            List<Long> groupIds = listLoginGroupIds(loginUnitId);
            Expression expression = null;
            if (groupIds.size() > 0){
                expression = And(Condition("type",EsOperator.Equal,"3"),Condition("group_id",EsOperator.In,groupIds.toArray()));
            }
            return Optional.ofNullable(
                    Or(
                            And(Condition("type",EsOperator.Equal,"1"), Condition("target_username",EsOperator.Equal,loginUser), Condition("target_unit_id",EsOperator.Equal,loginUnitId)),
                            And(Condition("type",EsOperator.Equal,"2"), Condition("target_unit_id",EsOperator.Equal,loginUnitId)),
                            expression,
                            Condition("type",EsOperator.Equal,"4")
                    )
            );
        }else {
            throw new ServiceException("请求参数type【"+type+"】不合法!");
        }
    }

}
