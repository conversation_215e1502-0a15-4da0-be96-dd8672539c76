package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.search.util.TimeUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForConstructionEndTime
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/18 14:50
 **/
@Component
public class FieldForConstructionEndTime  extends BaseCommonFieldMgr {
    @Override
    public Optional<Expression> buildCondition(String constructionEndTime) throws ServiceException {
        if(!CMyString.isEmpty(constructionEndTime)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.LessThanOrEqual, TimeUtil.convertUtcTime(constructionEndTime+" 23:59:59")));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.CONSTRUCTION_END_TIME;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.CONSTRUCTION_END_TIME;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_CONSTRUCTION_START_TIME;
    }
}
