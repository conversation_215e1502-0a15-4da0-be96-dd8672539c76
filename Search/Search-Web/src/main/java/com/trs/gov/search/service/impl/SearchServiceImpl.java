package com.trs.gov.search.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.es.esbean.page.PageList;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.search.DO.WorkOrderDO;
import com.trs.gov.search.mgr.*;
import com.trs.gov.search.service.ISearch;
import com.trs.gov.search.util.ConfigUtil;
import com.trs.gov.workorder.DTO.NoticeSearchDTO;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.VO.NoticeByMonthVO;
import com.trs.gov.workorder.constant.NoticeConstant;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：SearchWorkOrderServiceImpl
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/24 13:30
 **/
@Service
@Slf4j
public class SearchServiceImpl implements ISearch {
    @Autowired
    private SearchWorkOrderMgr searchWorkOrderMgr;
    @Autowired
    private SearchNoticeMgr searchNoticeMgr;
    @Autowired
    private SearchUserMgr searchUserMgr;
    @Autowired
    private SearchUnitMgr searchUnitMgr;
    @Autowired
    private SearchSiteRelationMgr searchSiteRelationMgr;
    @Autowired
    private AdapterMysqlToEsMgr adapterMysqlToEsMgr;
    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Override
    public RestfulResults listSearchWorkOrder(WorkOrderSearchDTO workOrderSearchDTO){
        try {
            if(workOrderSearchDTO.getType() == null){
                throw new ServiceException("type参数不能为空!");
            }
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderSearchDTO);
            return searchWorkOrderMgr.listSearchWorkOrder(workOrderSearchDTO);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return RestfulResults.error("检索工单类型失败! "+e.getMessage());
        }
    }

    @Override
    public RestfulResults<List<NoticeByMonthVO>> listSearchNotice(NoticeSearchDTO noticeSearchDTO) {
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, noticeSearchDTO);
            PreConditionCheck.checkArgument(NoticeConstant.MY_RECEIVED.equals(noticeSearchDTO.getType()) || NoticeConstant.MY_CREATED.equals(noticeSearchDTO.getType()), "type类型错误！");
            return searchNoticeMgr.listSearchNotice(noticeSearchDTO);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return RestfulResults.error("检索通知列表失败! "+ e.getMessage());
        }

    }

    @Override
    public RestfulResults<List<UserVO>> listSearchUser(UserSearchDTO dto) throws ServiceException {
        try {
            if(CMyString.isEmpty(dto.getKeyWords())){
                throw new ServiceException("参数值keyWords 不能为空!");
            }
            return searchUserMgr.listSearchUser(dto);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return RestfulResults.error("检索通知列表失败! "+ e.getMessage());
        }
    }

    @Override
    public RestfulResults<List<SiteRelationVO>> querySiteRelationList(SiteRelationSearchDTO dto) {
        try {
            dto.isValid();
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
            PageList<SiteRelationVO> page = searchSiteRelationMgr.querySiteRelationList(dto);
            return RestfulResults.ok(page.getEsData()).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(page.getTotalCount()).addMsg("检索数据成功!");
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return RestfulResults.error("检索通知列表失败! "+ e.getMessage());
        }
    }

    @Override
    public RestfulResults<List<UnitVO>> queryUnitList(UnitSearchDTO dto) {
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
            PageList<UnitVO> page = searchUnitMgr.queryUnitList(dto);
            return RestfulResults.ok(page.getEsData()).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(page.getTotalCount()).addMsg("检索数据成功!");
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return RestfulResults.error("检索通知列表失败! "+ e.getMessage());
        }
    }

    @Override
    public RestfulResults<Map<String, String>> adapterMysqlToES(String configNames) {
        if(CMyString.isEmpty(configNames)){
            configNames = ConfigUtil.getConfigTemplate().getPropertyValue("canal.config.fileName");;
        }
        if(CMyString.isEmpty(configNames)){
            return RestfulResults.error("传入的参数和配置的配置文件都为为空!");
        }
        try {
            return RestfulResults.ok(adapterMysqlToEsMgr.adapterMysqlToES(configNames));
        } catch (Exception e) {
            log.error("数据迁移异常！", e);
            return RestfulResults.error(e.getMessage());
        }
    }

    @Override
    public long saveOrUpdateWorkOrder(WorkOrderDO orderDO) {
        try {
            return searchWorkOrderMgr.saveOrUpdateWorkOrder(orderDO);
        } catch (Exception e) {
            log.error("保存或更新工单异常！", e);
            return 0;
        }
    }
}
