package com.trs.gov.search.mgr;

import com.trs.common.utils.expression.Expression;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.service.IBaseMgr;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.And;

/**
 * @ClassName：BaseMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 17:31
 **/
public abstract class BaseMgr implements IBaseMgr {
    @Autowired
    private List<BaseCommonFieldMgr> fieldMgrs;

    /**
     * @Description 获取expression表达式
     * @Param [dto, 检索查询体]
     * @Param [entityTypePre 实体对应的前缀]
     * @Param [otherExpressions 额外的expression]
     * @return com.trs.common.utils.expression.Expression
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/18 17:50
     **/
    public <T extends BaseDTO> Expression getFinalExpression(T dto,String entityTypePre,Expression... otherExpressions) throws ServiceException, IllegalAccessException {
        // 构造查询语句
        List<Expression> query = new ArrayList<>();
        Field[] declaredFields = dto.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            Object object = declaredField.get(dto);
            Optional<BaseCommonFieldMgr> fieldMgrs = getFieldMgrs(declaredField.getName(),entityTypePre);
            if (fieldMgrs.isPresent()){
                Optional<Expression> expression = fieldMgrs.get().buildCondition(object == null?null:String.valueOf(object));
                expression.ifPresent(expression1 -> query.add(expression1));
            }
        }
        //添加额外条件
        for (Expression otherExpression : otherExpressions) {
            query.add(otherExpression);
        }
        return And(query.toArray(new Expression[query.size()]));
    }

    /**
     * @Description  获取对应的BaseWorkOrderFieldMgr实现类
     * @Param [field]
     * @return java.util.Optional<com.trs.gov.search.mgr.BaseWorkOrderFieldMgr>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 22:23
     **/
    private Optional<BaseCommonFieldMgr> getFieldMgrs(String field,String entityTypePre) {
        field = entityTypePre + field;
        for (BaseCommonFieldMgr fieldmgr : fieldMgrs) {
            if(fieldmgr.key().startsWith(entityTypePre) && fieldmgr.key().equalsIgnoreCase(field)){
                return Optional.ofNullable(fieldmgr);
            }
        }
        return Optional.empty();
    }




}
