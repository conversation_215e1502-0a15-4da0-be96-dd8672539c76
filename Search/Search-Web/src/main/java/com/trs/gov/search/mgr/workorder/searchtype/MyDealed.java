package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.*;

/**
 * @ClassName：MyDealed
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:46
 **/
@Component
public class MyDealed extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_DEALED);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_DEALED);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        String loginUser = getLoginUser();
        UnitVO unitVO = getUnitVO();
        List<Long> relatedWorkIdList = getRelatedWorkOrderId(false, true,
                new String[]{OperateNameConstant.CREATE_WORK_ORDER,
                        OperateNameConstant.ASSIGN_WORK_ORDER,
                        OperateNameConstant.ROLLBACK_WORK_ORDER,
                        OperateNameConstant.UPDATE_WORK_ORDER_DEAL,
                        OperateNameConstant.COPY_WORK_ORDER
                }, loginUser, unitVO.getId());
        List<Long> relatedWorkOrderId = getRelatedWorkOrderId(true, true,
                new String[]{OperateNameConstant.ASSIGN_WORK_ORDER,
                        OperateNameConstant.UPDATE_WORK_ORDER_DEAL}, loginUser, unitVO.getId());
        if(CollectionUtils.isEmpty(relatedWorkIdList)){
            relatedWorkIdList = new ArrayList<>();
            relatedWorkIdList.add(0L);
        }
        if(!CollectionUtils.isEmpty(relatedWorkOrderId)){
            relatedWorkIdList.addAll(relatedWorkOrderId);
        }

        Integer[] status = new Integer[]{WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING};
        Expression consumerMyDeal = consumerMyDeal(loginUser, unitVO, false, status);
        List<Long> ccToMe = queryCCToMe(false,status);

        Integer[] statusFinish = new Integer[]{WorkOrderConstant.STATUS_FINISHED, WorkOrderConstant.STATUS_REVIEWED, WorkOrderConstant.STATUS_OPENED};
        Expression consumerMyDealFinished = consumerMyDeal(loginUser,unitVO,false,statusFinish);
        List<Long> ccToMeFinished = queryCCToMe(false,statusFinish);

        Expression expression = unConsumerMyDeal(loginUser, unitVO, false, status);

        Expression expression1 = Or(
                Condition("id", EsOperator.In,relatedWorkIdList.toArray()),
                consumerMyDealFinished,
                (Condition("id", EsOperator.In,ccToMeFinished.toArray()))
        );
        // not in (,,,)  == in !(,,,)
        Expression expression2 = And(
                Condition("id", EsOperator.NotIn,ccToMe.toArray()),
                expression
        );
        return Optional.ofNullable(And(expression1,expression2));
    }
}
