package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.IFieldMgr;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.interaction.DTO.QueryWorkOrderIdsDTO;
import com.trs.gov.interaction.service.OprRecordService;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.service.UnitService;
import com.trs.gov.management.service.WorkOrderTypeService;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.DTO.CCSearchDTO;
import com.trs.gov.workorder.DTO.CCToMyDTO;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import org.apache.commons.lang.ArrayUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.*;

/**
 * @ClassName：WorkOrderSearchType
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:34
 **/
@Component
public abstract class WorkOrderSearchTypeMgr implements IFieldMgr {
    @Reference(check = false, timeout = 60000)
    private UnitService unitService;

    @Reference(check = false, timeout = 60000)
    private WorkOrderTypeService workOrderTypeService;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Reference(check = false, timeout = 60000)
    private IWorkOrderService workOrderService;

    @Reference(check = false, timeout = 60000)
    private OprRecordService oprRecordService;

    /**
     * @Description  获取myDeal公共部分
     * @Param [loginUser, loginUnit, isUnit, workOrderStatus]
     * @return com.trs.common.utils.expression.Expression
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 17:24
     **/
    public Expression consumerMyDeal(String loginUser, UnitVO loginUnit, boolean isUnit, Integer[] workOrderStatus){
        Expression expression = Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_DEAL_UNIT_ID, EsOperator.Equal,loginUnit.getId());
        if(!ArrayUtils.isEmpty(workOrderStatus)){
            expression = And(expression,Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_STATUS,EsOperator.In,workOrderStatus));
        }
        if(!isUnit){
            Expression tempExpression = null;
            if(!loginUser.equals(loginUnit.getUnitMaster())){
                tempExpression = Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_WORK_ORDER__ID,EsOperator.Equal,-1);
            }
            expression = And(expression,
                    Or(
                            And(
                                    Condition(WorkOrderSearchDtoFieldContants.Es_DEAL_ASSIGN_TYPE,EsOperator.Equal, AssignConstant.ASSIGN_TO_UNIT),
                                    tempExpression),
                            And(
                                    Condition(WorkOrderSearchDtoFieldContants.Es_DEAL_ASSIGN_TYPE,EsOperator.Equal,AssignConstant.ASSIGN_TO_USER),
                                    Condition(WorkOrderSearchDtoFieldContants.Es_DEAL_USERNAME,EsOperator.Equal,loginUser))
                    ));
        }
        return expression;
    }

    // [In (!A | !B | ((F|G) & (H|J)))]
    public Expression unConsumerMyDeal(String loginUser, UnitVO loginUnit, boolean isUnit, Integer[] workOrderStatus){
        Expression expression = Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_DEAL_UNIT_ID, EsOperator.NotEqual,loginUnit.getId());
        if(!ArrayUtils.isEmpty(workOrderStatus)){
            expression = Or(expression,Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_STATUS,EsOperator.NotIn,workOrderStatus));
        }
        if(!isUnit){
            Expression tempExpression = null;
            if(!loginUser.equals(loginUnit.getUnitMaster())){
                tempExpression = Condition(WorkOrderSearchDtoFieldContants.ES_FIELD_WORK_ORDER__ID,EsOperator.Equal,-1);
            }
            expression = Or(expression,
                    And(
                            Or(
                                    Condition(WorkOrderSearchDtoFieldContants.Es_DEAL_ASSIGN_TYPE,EsOperator.Equal, AssignConstant.ASSIGN_TO_UNIT),
                                    tempExpression),
                            Or(
                                    Condition(WorkOrderSearchDtoFieldContants.Es_DEAL_ASSIGN_TYPE,EsOperator.Equal,AssignConstant.ASSIGN_TO_USER),
                                    Condition(WorkOrderSearchDtoFieldContants.Es_DEAL_USERNAME,EsOperator.Equal,loginUser))
                    ));
        }
        return expression;
    }

    /**
     * @Description  主办人是我的公共部分
     * @Param [loginUser, loginUnit, isUnit, workOrderStatus]
     * @return com.trs.common.utils.expression.Expression
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 17:54
     **/
    public Expression consumerMyHost(String loginUser,UnitVO loginUnit, boolean isUnit, Integer... workOrderStatus){
        Expression expression = Condition("host_unit_id",EsOperator.Equal,loginUnit.getId());
        if(!ArrayUtils.isEmpty(workOrderStatus)){
            expression = And(expression,Condition("status",EsOperator.In,workOrderStatus));
        }
        Expression tempExpression = null;
        if(loginUser.equals(loginUnit.getUnitMaster())){
            tempExpression = Condition("id",EsOperator.NotEqual,0);
        }
        if(!isUnit){
            expression = And(expression,Or(
                    And(
                            Condition("host_assign_type",EsOperator.Equal,AssignConstant.ASSIGN_TO_USER),
                            Condition("host_username",EsOperator.Equal,loginUser)
                    ),tempExpression)
            );
        }
        return expression;
    }

    /**
     * @Description  获取抄送给我的Expression公共部分
     * @Param [loginUser, loginUnit, isUnit, workOrderStatus]
     * @return java.util.List<java.lang.Long>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 17:30
     **/
    public List<Long> queryCCToMe(boolean isUnit,Integer... workOrderStatus) throws ServiceException {
        CCToMyDTO ccToMyDTO = new CCToMyDTO();
        ccToMyDTO.setUnit(isUnit);
        if (workOrderStatus != null && workOrderStatus.length > 0){
            ccToMyDTO.setWorkOrderStatus(workOrderStatus);
        }
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, ccToMyDTO);
        RestfulResults<List<Long>> cCtoMeWorkOrderIds = workOrderService.findCCtoMeWorkOrderIds(ccToMyDTO);
        checkRestfulResults(cCtoMeWorkOrderIds,this.getClass().getName(),"queryCCToMe");
        return cCtoMeWorkOrderIds.getDatas().stream().distinct().collect(Collectors.toList());
    }

    /**
     * @Description  获取类型相关联的工单的id串
     * @Param [ids, loginUser, unitId]
     * @return java.util.List<java.lang.Long>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/16 14:16
     **/
    public List<Long> getRelatedWorkOrderId(boolean myCreated, boolean onlyUser,String[] ids,String loginUser,Long unitId) throws ServiceException {
        List<String> oprRecordTypeList = Arrays.asList(ids);
        QueryWorkOrderIdsDTO queryWorkOrderIdsDTO = new QueryWorkOrderIdsDTO();
        if(onlyUser){
            queryWorkOrderIdsDTO.setAssignType(AssignConstant.ASSIGN_TO_USER);
        }else{
            queryWorkOrderIdsDTO.setAssignType(AssignConstant.ASSIGN_TO_UNIT);
        }
        if(myCreated){
            queryWorkOrderIdsDTO.setCrUnit(unitId);
            queryWorkOrderIdsDTO.setCrUser(loginUser);
        }else {
            queryWorkOrderIdsDTO.setTargetUnit(unitId);
            queryWorkOrderIdsDTO.setTargetUser(loginUser);
        }
        queryWorkOrderIdsDTO.setOprRecordType(oprRecordTypeList);
        RestfulResults<List<Long>> restfulResults = oprRecordService.listWorkOrderIdByUserAndUnitAndType(queryWorkOrderIdsDTO);
        checkRestfulResults(restfulResults,this.getClass().getName(),"getRelatedWorkOrderId");
        return restfulResults.getDatas().stream().distinct().collect(Collectors.toList());
    }

    /**
     * @Description  根据抄送单位来 查对应的工单
     * @Param [dto]
     * @return java.util.List<java.lang.Long>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/20 19:40
     **/
    public List<Long> findWorkOrderIdsByCCSearch(CCSearchDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<Long>> workOrderIdsByCCSearch = workOrderService.findWorkOrderIdsByCCSearch(dto);
        checkRestfulResults(workOrderIdsByCCSearch,this.getClass().getName(),"findWorkOrderIdsByCCSearch");
        return workOrderIdsByCCSearch.getDatas().stream().distinct().collect(Collectors.toList());
    }


    /**
     * @Description  获取当前登录用户的用户名
     * @Param []
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 17:09
     **/
    public String getLoginUser() throws ServiceException {
        return ContextHelper.getLoginUser().orElseThrow(() -> new ServiceException("获取当前登录用户信息失败!"));
    }
    /**
     * @Description 根据单位id获取unitVO
     * @Param [unitId]
     * @return com.trs.gov.management.VO.UnitVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/15 13:30
     **/
    public UnitVO getUnitVO() throws ServiceException {
        String unitId = ContextHelper.getLoginUnitId().orElseThrow(() -> new ServiceException("获取当前登录用户信息失败!"));
        UnitSearchDTO dto = new UnitSearchDTO();
        dto.setRpcTag(true);
        dto.setId(unitId);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<UnitVO>> restfulResults = unitService.queryUnitList(dto);
        checkRestfulResults(restfulResults,this.getClass().getName(),"getUnitVO");
        if (CollectionUtils.isEmpty(restfulResults.getDatas())) {
            throw new ServiceException("单位【" + unitId + "】未找到");
        }
        return restfulResults.getDatas().get(0);
    }



    public Expression queryByMyUnit(Long unitId) throws ServiceException {
        Expression childrenHostUnitIdExpression= findChildrenUnitIdsByUnitId(unitId);
        return Or(
                Condition("cr_unit_id",EsOperator.Equal,unitId),
                Condition("host_unit_id",EsOperator.Equal,unitId),
                Condition("deal_unit_id",EsOperator.Equal,unitId),
                childrenHostUnitIdExpression);
    }
    private Expression findChildrenUnitIdsByUnitId(Long unitId) throws ServiceException {
        Expression expression = null;
        List<UnitVO> childrenUnits = findChildrenUnitByUnitId(unitId);
        List<Long> childrenUnitIds = new ArrayList<>();
        if(!CollectionUtils.isEmpty(childrenUnits)){
            childrenUnitIds = childrenUnits.stream().map(UnitVO::getId).collect(Collectors.toList());
        }
        if(childrenUnitIds.size() != 0){
            expression = Condition("host_unit_id",EsOperator.In,childrenUnitIds.toArray());
        }
        return expression;
    }
    private List<UnitVO> findChildrenUnitByUnitId(Long unitId) throws ServiceException {
        if (unitId == null) {
            throw new ServiceException("单位id不能为空: ");
        }
        UnitSearchDTO dto = new UnitSearchDTO();
        dto.setId(String.valueOf(unitId));
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<UnitVO>> restfulResults = unitService.queryBaseChildrenUnitList(dto);
        checkRestfulResults(restfulResults,this.getClass().getName(),"findChildrenUnitByUnitId");
        return restfulResults.getDatas();
    }


    public void checkRestfulResults(RestfulResults restfulResults,String className,String methodName) throws ServiceException {
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException("类【"+className+"】,方法:【"+methodName+"】调用其它服务时，出现异常！"+restfulResults.getMsg());
        }
    }

}
