package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForUnitName
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 10:56
 **/
@Component
public class FieldForUnitName extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.UNIT_NAME;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.UNIT_NAME;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_UNIT_NAME;
    }

    @Override
    public Optional<Expression> buildCondition(String unitName) throws ServiceException {
        if(!CMyString.isEmpty(unitName)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.MatchPhrasePrefix,unitName.trim()));
        }
        return Optional.empty();
    }
}
