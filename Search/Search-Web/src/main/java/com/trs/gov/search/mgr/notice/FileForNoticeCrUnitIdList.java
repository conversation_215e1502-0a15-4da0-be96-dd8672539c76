package com.trs.gov.search.mgr.notice;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.NoticeSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FileForNoticeCrUnitIdList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 18:55
 **/
@Component
public class FileForNoticeCrUnitIdList extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return NoticeSearchDtoFieldContants.CR_UNIT_ID_LIST;
    }

    @Override
    public String desc() {
        return NoticeSearchDtoFieldContants.CR_UNIT_ID_LIST;
    }

    @Override
    public String searchField() {
        return NoticeSearchDtoFieldContants.ES_CR_UNIT_ID_LIST;
    }

    @Override
    public Optional<Expression> buildCondition(String crUnitIdList) throws ServiceException {
        if(!CMyString.isEmpty(crUnitIdList)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.In,crUnitIdList.split(",")));
        }
        return Optional.empty();
    }
}
