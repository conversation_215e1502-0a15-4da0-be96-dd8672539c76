package com.trs.gov.search.constant;

/**
 * @ClassName：UnitSearchDtoFieldContants
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 10:46
 **/
public class UnitSearchDtoFieldContants {
    public static final String UNIT_PRE = "UNIT_";
    /**
     * 单位id
     **/
    public static final String ID = UNIT_PRE + "id";
    /**
     * 单位id
     **/
    public static final String ES_ID = "id";
    /**
     * 单位名称
     **/
    public static final String UNIT_NAME = UNIT_PRE +  "unitName";

    /**
     * 单位名称
     **/
    public static final String ES_UNIT_NAME = "unit_name";
    /**
     * 单位编码
     **/
    public static final String UNIT_CODE =  UNIT_PRE +  "isConnect";

    /**
     * 单位编码
     **/
    public static final String ES_UNIT_CODE = "unit_code";
    /**
     * 单位类型
     **/
    public static final String UNIT_TYPE = UNIT_PRE +   "unitType";

    /**
     * es字段 蛋类类型
     **/
    public static final String ES_UNIT_TYPE = "unit_type";
    /**
     * 状态值
     **/
    public static final String STATUS =  UNIT_PRE +  "status";

    /**
     * 状态值
     **/
    public static final String ES_STATUS = "status";
    /**
     * 创建开始时间
     **/
    public static final String CR_TIME_START = UNIT_PRE +  "crTimeStart";
    public static final String CR_TIME_END = UNIT_PRE +  "crTimeEnd";
    /**
     * es字段 创建时间
     **/
    public static final String ES_CR_TIME_START = "cr_time";
    /**
     * 更新时间
     **/
    public static final String UPDATE_TIME_START = UNIT_PRE +  "updateTimeStart";
    public static final String UPDATE_TIME_END =  UNIT_PRE + "updateTimeEnd";
    /**
     * es字段 更新时间
     **/
    public static final String ES_UPDATE_TIME_START = "update_time";
    /**
     * es 是否删除
     **/
    public static final String ES_IS_DELETE = "is_del";


}
