package com.trs.gov.search.util;

import com.trs.common.config.ConfigTemplate;

/**
 * @ClassName：ConfigUtil
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/1/6 17:41
 **/
public class ConfigUtil {

    private static ConfigTemplate configTemplate = null;

    /**
     * @Description  获取配置文件
     * @Param []
     * @return com.trs.common.config.ConfigTemplate
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/30 11:27
     **/
    public static ConfigTemplate getConfigTemplate(){
        if(configTemplate == null){
            configTemplate = ConfigTemplate.buildDefaultConfigTemplate();
        }
        return configTemplate;
    }

}
