package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.*;

/**
 * @ClassName：AllDealed
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:51
 **/
@Component
public class AllDealed extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL_DEALED);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL_DEALED);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        String loginUser = getLoginUser();
        UnitVO unitVO = getUnitVO();
        List<Long> relatedWorkOrderId = getRelatedWorkOrderId(false, false,
                new String[]{OperateNameConstant.CREATE_WORK_ORDER,
                        OperateNameConstant.UPDATE_WORK_ORDER_DEAL,
                        OperateNameConstant.ASSIGN_WORK_ORDER,
                        OperateNameConstant.ROLLBACK_WORK_ORDER,
                        OperateNameConstant.COPY_WORK_ORDER}, loginUser, unitVO.getId());
        Expression expression1 = null;
        Expression expression2 = null;
        if(!CollectionUtils.isEmpty(relatedWorkOrderId)){
            expression1 = Condition("id", EsOperator.In,relatedWorkOrderId.toArray());
            List<Long> longs2 = queryCCToMe(true,WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING);
            // A & B  取反为  !A | !B
            expression2 = And(
                    Or(
                            Condition("status",EsOperator.NotIn,new Integer[]{WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING}),
                            Condition("deal_unit_id",EsOperator.NotEqual,unitVO.getId())
                    ),
                    Condition("id",EsOperator.NotIn,longs2.toArray()));
        }

        Expression expression3 = Condition("status",EsOperator.In,new Integer[]{WorkOrderConstant.STATUS_FINISHED, WorkOrderConstant.STATUS_REVIEWED, WorkOrderConstant.STATUS_OPENED});
        Expression expression4 = queryByMyUnit(unitVO.getId());

        return Optional.ofNullable(Or(And(expression1,expression2),And(expression3,expression4)));
    }

}
