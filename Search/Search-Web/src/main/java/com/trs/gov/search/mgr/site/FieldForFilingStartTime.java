package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.search.util.TimeUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForFilingTime
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 13:27
 **/
@Component
public class FieldForFilingStartTime extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.FILING_START_TIME;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.FILING_START_TIME;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_FILING_START_TIME;
    }

    @Override
    public Optional<Expression> buildCondition(String filingStartTime) throws ServiceException {
        if(!CMyString.isEmpty(filingStartTime)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.GreaterThanOrEqual, TimeUtil.convertUtcTime(filingStartTime+" 00:00:00")));
        }
        return Optional.empty();
    }
}
