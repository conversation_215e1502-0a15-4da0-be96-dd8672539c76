package com.trs.gov.search.util;

import com.trs.common.utils.TimeUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * @ClassName：TimeUtil
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/19 22:13
 **/
public class TimeUtil {

    private static final String UTC_TIME = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    /**
     * @Description  Es中时间为UTC，需要做一次转换
     * @Param [inputDate]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/19 22:11
     **/
    public static String convertUtcTime(String inputDate){
        Date date = TimeUtils.stringToDate(inputDate, TimeUtils.YYYYMMDD_HHMMSS);
        SimpleDateFormat format = new SimpleDateFormat(UTC_TIME);
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        return format.format(date);
    }


    /**
     * @Description  Es中时间为UTC，需要做一次转换
     * @Param [inputDate]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/19 22:11
     **/
    public static Date dateAddHour(Date inputDate,int hour){
        if (inputDate !=null) {
            Calendar c = Calendar.getInstance();
            c.setTime(inputDate);
            c.add(Calendar.HOUR_OF_DAY, hour);
            return c.getTime();
        }
        return inputDate;
    }




}
