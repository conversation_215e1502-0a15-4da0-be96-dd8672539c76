package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForSiteId
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 12:35
 **/
@Component
public class FieldForSiteId extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.SITE_ID;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.SITE_ID;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_SITE_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String siteId) throws ServiceException {
        if (!CMyString.isEmpty(siteId)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.In,siteId.split(",")));
        }
        return Optional.empty();
    }
}
