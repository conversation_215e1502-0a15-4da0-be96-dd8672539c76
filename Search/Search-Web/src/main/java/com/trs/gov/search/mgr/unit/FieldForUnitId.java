package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForId
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 10:54
 **/
@Component
public class FieldForUnitId extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.ID;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.ID;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String ids) throws ServiceException {
        if(!CMyString.isEmpty(ids)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.In,ids.split(",")));
        }
        return Optional.empty();
    }
}
