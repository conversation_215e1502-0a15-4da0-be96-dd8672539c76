package com.trs.gov.search.mgr;

import com.trs.common.utils.TimeUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.builder.EsSearchBuilder;
import com.trs.es.esbean.builder.EsSearchBuilderFactory;
import com.trs.es.esbean.exception.EsOperateException;
import com.trs.es.esbean.page.PageList;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.base.enums.MediaType;
import com.trs.gov.search.DO.WorkOrderDO;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.repository.WorkOrderRepository;
import com.trs.gov.search.util.TimeUtil;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.constant.CommonConstant;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName：SearchWorkOrderMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/24 13:30
 **/
@Component
@Slf4j
public class SearchWorkOrderMgr extends BaseMgr{
    @Autowired
    private WorkOrderRepository workOrderRepository;

    /**
     * @Description
     * @Param [workOrderSearchDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/24 19:55
     **/
    public RestfulResults<List<WorkOrderVO>> listSearchWorkOrder(WorkOrderSearchDTO workOrderSearchDTO){
        PageList<WorkOrderDO> pageList = null;
        List<WorkOrderVO> workOrderVOList = new ArrayList<>();
        try {
            //构建表达式
            EsSearchBuilder esSearchBuilder = EsSearchBuilderFactory
                    .createNewBuilder(workOrderSearchDTO.getPageNum(),workOrderSearchDTO.getPageSize())
                    .where(buildExpression(workOrderSearchDTO));
            log.info("查询es的表达式为：{}",esSearchBuilder.asEsQuery());
            //得到分页结果
            pageList = listWorkOrderDO(esSearchBuilder);
            //判断结果是否为空
            if(pageList.getEsData() == null || pageList.getTotalCount().equals(0L)){
                return RestfulResults.ok(workOrderVOList).addPageNum(pageList.getPageNumber()).addPageSize(pageList.getPageSize()).addTotalCount(pageList.getTotalCount()).addMsg("检索成功,但数据为空!");
            }
            List<WorkOrderDO> workOrderDOS = pageList.getEsData();
            //父级工单类型信息
            workOrderVOList = pageList.getEsData().stream().map(a->{
                WorkOrderVO workOrderVO = new WorkOrderVO();
                BaseUtils.copyProperties(a,workOrderVO);
                workOrderVO.setStatusName(WorkOrderConstant.statusName.get(workOrderVO.getStatus()));
                workOrderVO.setReturn(a.getIsReturn() != null && a.getIsReturn().equals(CommonConstant.YES));
                workOrderVO.setAction(workOrderVO.getActionTime() != null);
                if(workOrderVO.getMediaType() != null){
                    workOrderVO.setMediaName(MediaType.getMediaType().get(workOrderVO.getMediaType()));
                }
                workOrderVO.setCrTime(TimeUtil.dateAddHour(workOrderVO.getCrTime(),8));
                workOrderVO.setUpdateTime(TimeUtil.dateAddHour(workOrderVO.getUpdateTime(),8));
                workOrderVO.setExpectedEndDate(TimeUtil.dateAddHour(workOrderVO.getExpectedEndDate(),8));
                workOrderVO.setSource(a.getSource());
                return workOrderVO;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询es是出现异常异常！", e.getMessage(),e);
            return RestfulResults.error(e.getMessage());
        }
        return RestfulResults.ok(workOrderVOList).addPageNum(pageList.getPageNumber()).addPageSize(pageList.getPageSize()).addTotalCount(pageList.getTotalCount()).addMsg("检索成功!");
    }

    public PageList<WorkOrderDO> listWorkOrderDO(EsSearchBuilder esSearchBuilder) throws Exception {
        return workOrderRepository.findByExpression(esSearchBuilder);
    }

    @Override
    public <T extends BaseDTO> Expression buildExpression(T dto) throws ServiceException, IllegalAccessException {
        return super.getFinalExpression(dto, WorkOrderSearchDtoFieldContants.WORK_ORDER_PRE);
    }

    public long saveOrUpdateWorkOrder(WorkOrderDO orderDO) throws EsOperateException {
        return workOrderRepository.save(orderDO);
    }
}
