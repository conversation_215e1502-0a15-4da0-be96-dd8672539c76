package com.trs.gov.search.constant;

/**
 * @ClassName：WorkOrderFieldContants
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:00
 **/
public class WorkOrderSearchDtoFieldContants {

    public static final String WORK_ORDER_PRE = "WORKORDER_";
    /**
     * 顶级工单的id串
     **/
    public static final String WORK_ORDER_TOP_TYPE_ID_LIST =  WORK_ORDER_PRE +"workOrderTopTypeIdList";
    /**
     * es 顶级工单类型id
     **/
    public static final String ES_FIELD_WORK_ORDER_TOP_TYPE_ID = "work_order_top_type_id";

    /**
     * 工单类型id
     **/
    public static final String WORK_ORDER_TYPE_ID_LIST = WORK_ORDER_PRE + "workOrderTypeIdList";
    /**
     * es 工单类型id
     **/
    public static final String ES_FIELD_WORK_ORDER_TYPE_ID = "work_order_type_id";

    /**
     * 工单id串
     **/
    public static final String WORK_ORDER_ID_LIST = WORK_ORDER_PRE + "idList";
    /**
     * es 工单id检索字段
     **/
    public static final String ES_FIELD_WORK_ORDER__ID = "id";

    /**
     * 主办单位id串
     **/
    public static final String HOST_UNIT_ID_LIST = WORK_ORDER_PRE + "hostUnitIdList";
    /**
     * es 主办单位检索字段
     **/
    public static final String ES_FIELD_HOST_UNIT_ID = "host_unit_id";
    /**
     * 发起单位id串
     **/
    public static final String CR_UNIT_ID_LIST = WORK_ORDER_PRE + "crUnitIdList";
    /**
     * es 发起单位检索字段
     **/
    public static final String ES_FIELD_CR_UNIT_ID = "cr_unit_id";
    /**
     * 受理单位id串
     **/
    public static final String DEAL_UNIT_ID_LIST = WORK_ORDER_PRE + "dealUnitIdList";
    /**
     * es 受理单位检索字段
     **/
    public static final String ES_FIELD_DEAL_UNIT_ID = "deal_unit_id";
    /**
     * 抄送单位id串
     **/
    public static final String HOST_CC_ID_LIST = WORK_ORDER_PRE + "ccUnitIdList";
    /**
     * es 抄送单位检索字段
     **/
    public static final String ES_FIELD_CC_UNIT_ID = "cc_unit_id";
    /**
     * 主办单位id串
     **/
    public static final String STATUS = WORK_ORDER_PRE + "status";
    /**
     * es 主办单位检索字段
     **/
    public static final String ES_FIELD_STATUS = "status";
    /**
     * 主办单位id串
     **/
    public static final String CR_TIME = WORK_ORDER_PRE + "crTime";
    /**
     * es 主办单位检索字段
     **/
    public static final String ES_FIELD_CR_TIME = "cr_time";
    /**
     * 主办单位id串
     **/
    public static final String EXPECT_END_TIME = WORK_ORDER_PRE + "expectedEndDate";
    /**
     * es 主办单位检索字段
     **/
    public static final String ES_FIELD_EXPECT_END_TIME = "expected_end_date";
    /**
     * 检索内容
     **/
    public static final String KEYWORDS =  WORK_ORDER_PRE +"keywords";
    /**
     * es 检索对应的字段
     **/
    public static final String ES_FIELD_WORK_ORDER_TOP_TYPE_NAME = "work_order_top_type_name";
    public static final String ES_FIELD_Content = "content";
    public static final String ES_FIELD_Title = "title";
    /**
     * es 处理类型
     **/
    public static final String Es_DEAL_ASSIGN_TYPE = "deal_assign_type";
    /**
     * es 受理人
     **/
    public static final String Es_DEAL_USERNAME = "deal_username";
    /**
     * es 创建人用户名
     **/
    public static final String Es_CR_USERNAME = "cr_username";
    /**
     * es 受理人
     **/
    public static final String SITE_ID = WORK_ORDER_PRE + "siteIdList";
    /**
     * es 创建人用户名
     **/
    public static final String Es_SITE_ID = "site_id";
    /**
     * es 创建人用户名
     **/
    public static final String WORK_ORDER_SEARCH_TYPE = WORK_ORDER_PRE + "type";

}
