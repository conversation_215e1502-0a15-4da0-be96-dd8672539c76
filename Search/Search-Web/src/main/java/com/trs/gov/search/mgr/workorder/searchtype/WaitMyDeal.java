package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;

import java.util.Optional;


/**
 * @ClassName：WaitForMeDeal
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:36
 **/
@Component
public class WaitMyDeal extends WorkOrderSearchTypeMgr {


    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_DEALING);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_DEALING);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        Integer[] status = new Integer[]{WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING};
        Expression expression = consumerMyDeal(getLoginUser(), getUnitVO(), false, status);
        return Optional.ofNullable(expression);
    }
}
