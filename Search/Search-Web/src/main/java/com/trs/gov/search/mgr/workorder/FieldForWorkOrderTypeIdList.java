package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.common.utils.expression.ExpressionBuilder;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @ClassName：FieldWorkOrderTopTypeName
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 14:49
 **/
@Component
public class FieldForWorkOrderTypeIdList extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_TYPE_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_TYPE_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.ES_FIELD_WORK_ORDER_TYPE_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String workOrderTypeIdList) throws ServiceException {
        if(!CMyString.isEmpty(workOrderTypeIdList)){
            return Optional.of(ExpressionBuilder.Condition(searchField(), EsOperator.In, getWorkTypeChildList(workOrderTypeIdList).toArray()));
        }
        return Optional.empty();
    }
}
