package com.trs.gov.search.constant;

/**
 * @ClassName：SiteRelationSearchDtoFieldContants
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 11:31
 **/
public class SiteRelationSearchDtoFieldContants {
    
    public static final String SITE_RELATION_PRE = "SITERELATION_";
    /**
     * 站点名称
     **/
    public static final String SITE_NAME =  SITE_RELATION_PRE + "siteName";
    /**
     * es字段 单位id
     **/
    public static final String ES_SITE_NAME = "site_name";
    /**
     * 媒体类型
     **/
    public static final String MEDIA_TYPE = SITE_RELATION_PRE +  "mediaType";

    /**
     * 媒体类型
     **/
    public static final String ES_MEDIA_TYPE = "media_type";
    /**
     * 站点id
     **/
    public static final String SITE_ID =  SITE_RELATION_PRE + "siteId";

    /**
     * 站点id
     **/
    public static final String ES_SITE_ID = "site_id";
    /**
     * 主办单位id
     **/
    public static final String HOST_UNIT_ID = SITE_RELATION_PRE +  "hostUnitId";

    /**
     * es字段 主办单位
     **/
    public static final String ES_HOST_UNIT_ID = "host_unit_id";
    /**
     * 主管单位id
     **/
    public static final String MASTER_UNIT_ID = SITE_RELATION_PRE +  "masterUnitId";

    /**
     * 主管单位id
     **/
    public static final String ES_MASTER_UNIT_ID = "master_unit_id";
    /**
     * 惭怍单位id
     **/
    public static final String OPERATION_UNIT_ID = SITE_RELATION_PRE +  "operationUnitId";
    /**
     * 操作单位id
     **/
    public static final String ES_OPERATION_UNIT_ID = "operation_unit_id";
    /**
     * 备案时间
     **/
    public static final String FILING_START_TIME = SITE_RELATION_PRE +  "filingStartTime";
    public static final String FILING_END_TIME =  SITE_RELATION_PRE + "filingEndTime";
    /**
     * es字段 备案时间
     **/
    public static final String ES_FILING_START_TIME = "filing_time";
    /**
     * 建设周期
     **/
    public static final String CONSTRUCTION_START_TIME = SITE_RELATION_PRE + "constructionStartTime";
    public static final String CONSTRUCTION_END_TIME = SITE_RELATION_PRE + "constructionEndTime";
    /**
     * es字段 更新时间
     **/
    public static final String ES_CONSTRUCTION_START_TIME = "construction_time";
    /**
     * 运维周期
     **/
    public static final String OPERATION_START_TIME = SITE_RELATION_PRE +  "operationStartTime";
    public static final String OPERATION_END_TIME =  SITE_RELATION_PRE +  "operationEndTime";
    /**
     * es字段 运维周期
     **/
    public static final String ES_OPERATION_START_TIME = "operation_start_time";
    public static final String ES_OPERATION_END_TIME = "operation_end_time";
    /**
     * es 是否删除
     **/
    public static final String ES_IS_DELETE = "is_del";

}
