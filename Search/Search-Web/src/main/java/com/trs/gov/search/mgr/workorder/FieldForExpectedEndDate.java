package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldExpectedEndDate
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:08
 **/
@Component
public class FieldForExpectedEndDate extends BaseCommonFieldMgr {
    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.EXPECT_END_TIME;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.EXPECT_END_TIME;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.ES_FIELD_EXPECT_END_TIME;
    }

    @Override
    public Optional<Expression> buildCondition(String expectEndDate) throws ServiceException {
        if(!CMyString.isEmpty(expectEndDate)){
            String [] times = expectEndDate.split(",");
            if(times.length != 2){
                throw new ServiceException("传入的期望结束时间参数范围有误!");
            }
            return Optional.ofNullable(Condition(searchField(), EsOperator.Between,convertUtcTime(times)));
        }
        return Optional.empty();
    }
}
