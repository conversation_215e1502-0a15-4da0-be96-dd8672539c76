package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForStatus
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 10:57
 **/
@Component
public class FieldForStatus extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.STATUS;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.STATUS;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_STATUS;
    }

    @Override
    public Optional<Expression> buildCondition(String status) throws ServiceException {
        if(!CMyString.isEmpty(status)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.Equal,status));
        }
        return Optional.empty();
    }
}
