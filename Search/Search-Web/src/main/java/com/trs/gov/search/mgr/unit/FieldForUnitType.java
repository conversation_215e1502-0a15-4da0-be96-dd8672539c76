package com.trs.gov.search.mgr.unit;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.constant.UnitSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.*;

/**
 * @ClassName：FieldForUnitType
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 10:51
 **/
@Component
public class FieldForUnitType extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return UnitSearchDtoFieldContants.UNIT_TYPE;
    }

    @Override
    public String desc() {
        return UnitSearchDtoFieldContants.UNIT_TYPE;
    }

    @Override
    public String searchField() {
        return UnitSearchDtoFieldContants.ES_UNIT_TYPE;
    }

    @Override
    public Optional<Expression> buildCondition(String unitType) throws ServiceException {
        Expression expression = null;
        if(StringUtils.isNotEmpty(unitType)){
            Expression tempExpression = null;
            String[] split = unitType.split(",");
            for (String s : split) {
                tempExpression = Or(tempExpression,Condition(searchField(), EsOperator.Match,s.trim()));
            }
            expression = And(tempExpression,expression);
        }
        return Optional.ofNullable(expression);
    }
}
