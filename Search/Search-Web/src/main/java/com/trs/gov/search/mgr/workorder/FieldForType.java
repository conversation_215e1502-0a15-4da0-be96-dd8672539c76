package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * @ClassName：FieldForType
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:30
 **/
@Component
public class FieldForType extends BaseCommonFieldMgr {
    @Autowired
    private List<WorkOrderSearchTypeMgr> searchTypes;

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_SEARCH_TYPE;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_SEARCH_TYPE;
    }

    @Override
    public Optional<Expression> buildCondition(String type) throws ServiceException {
        for (WorkOrderSearchTypeMgr searchType : searchTypes) {
            if(searchType.key().equalsIgnoreCase(type)){
                return searchType.buildCondition("");
            }
        }
        return Optional.empty();
    }
}
