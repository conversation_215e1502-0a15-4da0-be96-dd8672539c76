package com.trs.gov.search.mgr;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.builder.EsSearchBuilder;
import com.trs.es.esbean.builder.EsSearchBuilderFactory;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.es.esbean.page.PageList;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.DO.UserDO;
import com.trs.gov.search.repository.UserRepository;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.common.utils.expression.ExpressionBuilder.And;
import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：SearchUserMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/20 19:55
 **/
@Component
public class SearchUserMgr {

    @Autowired
    private UserRepository userRepository;

    public RestfulResults<List<UserVO>> listSearchUser(UserSearchDTO dto) throws Exception {
        BaseUtils.checkDTO(dto);
        Expression resultExpression = null;
        if (dto.getStatus() != null) {
            resultExpression = And(resultExpression,Condition("status", EsOperator.Equal,dto.getStatus()));
        }
        if (!CMyString.isEmpty(dto.getKeyWords())) {
            resultExpression = And(resultExpression,Condition("true_name",EsOperator.MatchPhrasePrefix,dto.getKeyWords().trim()));
        }
        EsSearchBuilder esSearchBuilder = EsSearchBuilderFactory.createNewBuilder(dto.getPageNum(),dto.getPageSize()).where(resultExpression);
        PageList<UserDO> userDOPageList = userRepository.findByExpression(esSearchBuilder);
        if(CollectionUtils.isEmpty(userDOPageList.getEsData())){
            return RestfulResults.ok(new ArrayList<UserVO>()).addMsg("查询用户数据为空!").addTotalCount(0L).addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize());
        }
        return RestfulResults.ok(userDOPageList.getEsData().stream().map(item -> {
            UserVO vo = new UserVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList())).addMsg("成功获取数据").addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(userDOPageList.getTotalCount());

    }

}
