package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：CopyMy
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:36
 **/
@Component
public class CopyMy extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_CC);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_CC);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        List<Long> longs = queryCCToMe(false,WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING);
        return Optional.ofNullable(Condition("id", EsOperator.In,longs.toArray()));
    }
}
