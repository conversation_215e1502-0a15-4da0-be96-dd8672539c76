package com.trs.gov.search.mgr.workorder;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForWorkOrderTopTypeList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 14:52
 **/
@Component
public class FieldForWorkOrderTopTypeIdList extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_TOP_TYPE_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_TOP_TYPE_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.ES_FIELD_WORK_ORDER_TOP_TYPE_ID;
    }

    @Override
    public Optional<Expression> buildCondition(String workOrderTypeIdList) throws ServiceException {
        if(CMyString.isEmpty(workOrderTypeIdList)){
            return Optional.of(Condition(searchField(), EsOperator.NotEqual, WorkOrderConstant.TOP_TYPE_NOTICE));
        }else{
            return Optional.of(Condition(searchField(),EsOperator.In,workOrderTypeIdList.split(",")));
        }
    }
}
