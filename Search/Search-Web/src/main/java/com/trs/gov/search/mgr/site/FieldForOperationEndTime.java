package com.trs.gov.search.mgr.site;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.search.constant.SiteRelationSearchDtoFieldContants;
import com.trs.gov.search.mgr.BaseCommonFieldMgr;
import com.trs.gov.search.util.TimeUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：FieldForOperationEndTime
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 13:32
 **/
@Component
public class FieldForOperationEndTime extends BaseCommonFieldMgr {

    @Override
    public String key() {
        return SiteRelationSearchDtoFieldContants.OPERATION_END_TIME;
    }

    @Override
    public String desc() {
        return SiteRelationSearchDtoFieldContants.OPERATION_END_TIME;
    }

    @Override
    public String searchField() {
        return SiteRelationSearchDtoFieldContants.ES_OPERATION_END_TIME;
    }

    @Override
    public Optional<Expression> buildCondition(String operationEndTime) throws ServiceException {
        if(!CMyString.isEmpty(operationEndTime)){
            return Optional.ofNullable(Condition(searchField(), EsOperator.LessThanOrEqual, TimeUtil.convertUtcTime(operationEndTime+" 23:59:59")));
        }
        return Optional.empty();
    }
}
