package com.trs.gov.search.mgr.workorder.searchtype;

import com.trs.common.utils.expression.Expression;
import com.trs.es.esbean.expression.EsOperator;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.search.mgr.workorder.WorkOrderSearchTypeMgr;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;

/**
 * @ClassName：MyReviewed
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:48
 **/
@Component
public class MyReviewed extends WorkOrderSearchTypeMgr {

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_REVIEM_REVIEWED);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_REVIEM_REVIEWED);
    }

    @Override
    public Optional<Expression> buildCondition(String keyWords) throws ServiceException {
        List<Long> relatedWorkOrderId = getRelatedWorkOrderId(true, true,
                new String[]{OperateNameConstant.APPRAISE_WORK_ORDER}, getLoginUser(), getUnitVO().getId());
        if(relatedWorkOrderId != null && relatedWorkOrderId.size() > 0){
            return Optional.ofNullable(Condition("id", EsOperator.In,relatedWorkOrderId.toArray()));
        } else {
            //没找到，加个false条件
            return Optional.ofNullable(Condition("id",EsOperator.Equal,-1));
        }
    }
}
