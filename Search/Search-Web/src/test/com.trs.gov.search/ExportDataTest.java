package com.trs.gov.search;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.es.esbean.factory.ESClientFactory;
import com.trs.gov.search.DO.WorkOrderDO;
import com.trs.gov.search.mapper.WorkOrderMapper;
import io.searchbox.client.JestClient;
import io.searchbox.core.DocumentResult;
import io.searchbox.core.Index;
import io.searchbox.core.Search;
import io.searchbox.core.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * @ClassName：ExportDataTest
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/12 15:18
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SearchApplication.class)
public class ExportDataTest {
    @Autowired
    private WorkOrderMapper workOrderMapper;

    @Test
    public void exportData(){
        JestClient jestClient = ESClientFactory.createJestClient("","","http://***************:9200");
        List<WorkOrderDO> workOrderDOList = workOrderMapper.selectList(new QueryWrapper<WorkOrderDO>().isNotNull("id"));
        System.out.println(workOrderDOList.size());
        workOrderDOList.forEach(a->{
            Index index = new Index.Builder(a).index("work_order_test").type("work_order_test_doc").id(String.valueOf(a.getId())).build();
            try {
                DocumentResult execute = jestClient.execute(index);
                System.out.println(String.valueOf(a.getId())+ execute.isSucceeded());
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        });

    }
    @Test
    public void searchData() throws IOException {
        JestClient jestClient = ESClientFactory.createJestClient("","","http://***************:9200");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        QueryBuilder queryBuilder = QueryBuilders.matchAllQuery();
        searchSourceBuilder.query(queryBuilder);
        Search search = new Search.Builder(searchSourceBuilder.toString()).addIndex("work_order").addType("work_order_doc").build();
        SearchResult searchResult = jestClient.execute(search);
        System.out.println(searchResult.getJsonObject());
    }


}
