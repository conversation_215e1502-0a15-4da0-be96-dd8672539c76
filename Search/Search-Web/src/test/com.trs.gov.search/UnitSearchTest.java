package com.trs.gov.search;

import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.search.service.ISearch;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @ClassName：UnitSearchTest
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 15:03
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SearchApplication.class)
public class UnitSearchTest {
    @Reference(check = false, timeout = 60000)
    private ISearch iSearch;

    @Test
    public void testUnit(){
        UnitSearchDTO dto = new UnitSearchDTO();
//        dto.setUnitName();



    }

}
