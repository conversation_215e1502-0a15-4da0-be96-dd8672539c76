package com.trs.user.service.impl;

import com.trs.user.constant.IDSConstant;
import com.trs.user.service.AbstractGroupUserService;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.context.annotation.Primary;

@Service
@Primary
public class IDSGroupUserServiceImpl extends AbstractGroupUserService {

    /**
     * 相关Key<BR>
     *
     * @return 类的Key
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String key() {
        return IDSConstant.KEY;
    }

    /**
     * 相关描述<BR>
     *
     * @return 类功能的描述
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String desc() {
        return IDSConstant.DESC;
    }
}
