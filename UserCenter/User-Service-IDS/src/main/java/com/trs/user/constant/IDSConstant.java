package com.trs.user.constant;

import com.trs.gov.core.constant.CoreConstant;
import com.trs.user.VO.GroupVO;

import java.util.Date;

public class IDSConstant {
    public final static String KEY = "IDS";
    public final static String DESC = "IDS服务";
    public final static String ROOT_GROUP_KEY = "EveryOne";
    public final static String ROOT_GROUP_DESC = "根组织";


    public static GroupVO rootGroup;

    static {
        rootGroup = new GroupVO();
        rootGroup.setId(0L);
        rootGroup.setGroupKey(ROOT_GROUP_KEY);
        rootGroup.setCrTime(new Date(0));
        rootGroup.setCrUser(CoreConstant.SYSTEM_USER_NAME);
        rootGroup.setGroupDesc(ROOT_GROUP_DESC);
        rootGroup.setGroupName(ROOT_GROUP_DESC);
        rootGroup.setGroupOrder(1);
        rootGroup.setGroupSource(KEY);
        rootGroup.setParentId(-1L);
        rootGroup.setStatus(1);
    }

    public static GroupVO getRootGroupVO() {
        return rootGroup;
    }
}
