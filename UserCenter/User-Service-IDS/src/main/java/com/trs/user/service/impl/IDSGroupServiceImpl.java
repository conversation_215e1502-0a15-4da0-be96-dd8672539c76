package com.trs.user.service.impl;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.ids.api.AdminServiceApi;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.VO.GroupVO;
import com.trs.user.constant.IDSConstant;
import com.trs.user.mgr.IDSGroupMgr;
import com.trs.user.service.AbstractGroupService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 组织服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-16 17:36
 * @version 1.0
 * @since 1.0
 */
@Service
@Primary
@Slf4j
public class IDSGroupServiceImpl extends AbstractGroupService {

    private AdminServiceApi serviceApi = new AdminServiceApi();

    @Autowired
    private IDSGroupMgr groupMgr;

    @Value("${IDS.Group.parentGroupId}")
    private String parentGroupId;


    /**
     * 从第三方平台查询相关组织信息（不存在时返回empty）<BR>
     *
     * @param dto 接收到的数据
     * @return 查询到的组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 17:01
     */
    @Override
    public Optional<GroupVO> findGroupIn3rdSystem(GroupDTO dto) throws ServiceException {
        if (!CMyString.isEmpty(dto.getGroupKey())) {
            JSONObject group = serviceApi.groupQueryByGroupId(dto.getGroupKey());
            log.info("查询组织时，返回的数据为：{}", group.toString());
            if (group != null && group.optInt("code", -1) == 0) {
                group = group.optJSONArray("groups").optJSONObject(0);
                if (group != null) {
                    return groupMgr.findByGroupKey(GroupDTO.of(group.optString("id")));
                }
            }
        } else if (!CMyString.isEmpty(dto.getGroupName())) {
            Optional<GroupVO> parent = groupMgr.findByGroupKey(GroupDTO.of(parentGroupId));
            if (parent.isPresent()) {
                dto.setParentId(parent.get().getId());
                return groupMgr.findByName(dto);
            } else {
                throw new ParamInvalidException("不存在ID=[" + parentGroupId + "]的组织，请联系运维人员重新配置服务单位默认父组织ID！");
            }
        }
        return Optional.empty();
    }

    /**
     * 添加组织到第三方平台<BR>
     *
     * @param dto 请求数据
     * @return 第三方保存的结果（groupKey不能为空）
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 17:22
     */
    @Override
    public Optional<GroupVO> addGroupTo3rdSystem(GroupDTO dto) throws ServiceException {
        Optional<GroupVO> vo = findGroupIn3rdSystem(dto);
        if (vo.isPresent()) {
            return vo;
        }
        if (CMyString.isEmpty(dto.getGroupName())) {
            throw new ParamInvalidException("添加组织时组织名称不能为空！");
        }
        Map<String, String> map = new HashMap<>(1);
        map.put("parentGroupId", parentGroupId);
        JSONObject result = serviceApi.managerAddGroup(dto.getGroupName(), map);
        log.info("添加组织时，返回的数据为：{}", result.toString());
        if (result.optInt("code", -1) == 0) {
            String groupKey = result.optString("desc");
            vo = groupMgr.findByGroupKey(GroupDTO.of(groupKey));
            if (vo.isPresent()) {
                return vo;
            } else {
                GroupVO groupVO = new GroupVO();
                groupVO.setGroupKey(groupKey);
                groupVO.setGroupSource(CMyString.isEmpty(dto.getGroupSource()) ? key() : dto.getGroupSource());
                groupVO.setGroupName(dto.getGroupName());
                groupVO.setGroupDesc(dto.getGroupDesc());
                groupVO.setParentId(dto.getParentId());
                groupVO.setGroupOrder(dto.getGroupOrder());
                groupVO.setStatus(dto.getStatus());
                return Optional.ofNullable(groupVO);
            }
        } else {
            String msg = result.optString("desc");
            throw new ServiceException(CMyString.isEmpty(msg) ? "数据添加失败，原因未知!" : msg);
        }
    }

    /**
     * 从第三方平台中删除相关组织<BR>
     *
     * @param dto 信息（需要包含GroupKey）
     * @return 删除情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-19 12:42
     */
    @Override
    public RestfulResults<Report> deleteGroupIn3rdSystem(GroupDTO dto) throws ServiceException {
        JSONObject group = serviceApi.groupQueryByGroupId(dto.getGroupKey());
        log.info("查询组织时，返回的数据为：{}", group.toString());
        if (group != null && group.optInt("code", -1) == 0) {
            group = group.optJSONArray("groups").optJSONObject(0);
            if (group != null) {
                if (parentGroupId.equals(group.optString("parentId"))) {
                    JSONObject result = serviceApi.managerRemoveGroup(dto.getGroupKey());
                    log.info("删除组织时，返回的数据为：{}", result.toString());
                    if (result.optInt("code", -1) == 0) {
                        return RestfulResults.ok(new Report<>("删除组织", "成功删除组织！"));
                    } else {
                        String msg = result.optString("desc");
                        throw new ServiceException(CMyString.isEmpty(msg) ? "数据添加失败，原因未知!" : msg);
                    }
                } else {
                    throw new ServiceException("工单系统暂时不允许删除其他分组下的组织！");
                }
            }
        }
        return RestfulResults.ok(new Report<>("删除组织", "相关组织不存在不用删除！"));
    }

    /**
     * 相关Key<BR>
     *
     * @return 类的Key
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String key() {
        return IDSConstant.KEY;
    }

    /**
     * 相关描述<BR>
     *
     * @return 类功能的描述
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String desc() {
        return IDSConstant.DESC;
    }

}
