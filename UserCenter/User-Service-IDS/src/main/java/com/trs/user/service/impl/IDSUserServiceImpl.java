package com.trs.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.ids.api.AdminServiceApi;
import com.trs.ids.api.UserServiceApi;
import com.trs.user.DTO.UserDTO;
import com.trs.user.constant.IDSConstant;
import com.trs.user.mgr.IDSUserMgr;
import com.trs.user.mgr.UserMgr;
import com.trs.user.service.AbstractUserService;
import io.vavr.control.Either;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * IDS用户服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-16 14:06
 * @version 1.0
 * @since 1.0
 */
@Service
@Primary
public class IDSUserServiceImpl extends AbstractUserService {

    private UserServiceApi userServiceApi = new UserServiceApi();
    private AdminServiceApi adminServiceApi = new AdminServiceApi();

    @Resource
    private IDSUserMgr idsUserMgr;

    /**
     * 获取相关用户的业务类<BR>
     *
     * @return 用户的业务类
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 15:21
     */
    @Override
    public UserMgr getUserMgr() {
        return idsUserMgr;
    }

    /**
     * 匿名集成是需要调用该方法通过用户名密码在第三方平台进行登录，并返回登录的结果，成功还是失败<BR>
     * 如果系统不支持匿名集成，该方法请返回Either.right(false)<BR>
     *
     * @param sessionId 相关sessionId
     * @param user    登录参数（用户名，密码）
     * @return 登录的结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:10
     */
    @Override
    public Either<String, Boolean> doLoginIn3rdSystemByUserNamePassword(String sessionId, UserDTO user) throws ServiceException {
        org.json.JSONObject loginInfo = userServiceApi.loginByUP(user.getUserName(), user.getPassword(), sessionId, null);
        if (loginInfo.optInt("code", -1) != 200) {
            return Either.left(loginInfo.optString("desc"));
        }
        return Either.right(true);
    }

    /**
     * 匿名集成是需要调用该方法在第三方平台进行注销登录<BR>
     *
     * @param sessionId 相关sessionId
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:48
     */
    @Override
    public void doLogoutIn3rdSystem(String sessionId) throws ServiceException {
        userServiceApi.logout(null, sessionId);
    }

    /**
     * 根据用户名从第三方系统中获取用户信息<BR>
     *
     * @param dto 请求参数
     * @return 用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-16 18:26
     */
    @Override
    public JSONObject getUserInfoFrom3rdSystemByUserName(UserDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        return JSONObject.parseObject(adminServiceApi.userQueryForManage(dto.getUserName(), "").toString());
    }


    /**
     * 根据第三方ID从第三方系统中获取用户信息<BR>
     *
     * @param dto 请求参数
     * @return 用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-16 18:26
     */
    @Override
    public JSONObject getUserInfoFrom3rdSystemBySourceUserId(UserDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        return JSONObject.parseObject(adminServiceApi.userQueryForManage(dto.getSourceUserId(), "").toString());
    }

    /**
     * 相关Key<BR>
     *
     * @return 类的Key
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String key() {
        return IDSConstant.KEY;
    }

    /**
     * 相关描述<BR>
     *
     * @return 类功能的描述
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String desc() {
        return IDSConstant.DESC;
    }
}
