package com.trs.user.mgr;

import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.VO.GroupVO;
import com.trs.user.constant.IDSConstant;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class IDSGroupMgr extends GroupMgr {
    /**
     * 相关Key<BR>
     *
     * @return 类的Key
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String key() {
        return IDSConstant.KEY;
    }

    /**
     * 相关描述<BR>
     *
     * @return 类功能的描述
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String desc() {
        return IDSConstant.DESC;
    }

    /**
     * 根据组织ID查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    @Override
    public Optional<GroupVO> findById(GroupDTO dto) throws ServiceException {
        if (dto == null) {
            return Optional.empty();
        }
        Long id = dto.getId();
        if (id != null && id == 0L) {
            return Optional.ofNullable(IDSConstant.getRootGroupVO());
        }
        return super.findById(dto);
    }

    /**
     * 根据组织Key查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    @Override
    public Optional<GroupVO> findByGroupKey(GroupDTO dto) throws ServiceException {
        if (dto == null) {
            return Optional.empty();
        }
        if (IDSConstant.ROOT_GROUP_KEY.equalsIgnoreCase(dto.getGroupKey())) {
            return Optional.ofNullable(IDSConstant.getRootGroupVO());
        }
        return super.findByGroupKey(dto);
    }

    /**
     * 根据组织名称查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    @Override
    public Optional<GroupVO> findByName(GroupDTO dto) throws ServiceException {
        if (dto.getParentId() == null) {
            // 因为IDS不同父组织下可以有同名的组织，所以查询的时候需要加上父ID作为过滤
            throw new ParamInvalidException("父组织ID不能为空！");
        }
        return super.findByName(dto);
    }
}
