package com.trs.user.mgr;

import com.trs.user.constant.IDSConstant;
import org.springframework.stereotype.Component;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * IDS用户业务类
. *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-17 13:41
 * @version 1.0
 * @since 1.0
 */
@Component
public class IDSUserMgr extends UserMgr {
    /**
     * 相关Key<BR>
     *
     * @return 类的Key
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String key() {
        return IDSConstant.KEY;
    }

    /**
     * 相关描述<BR>
     *
     * @return 类功能的描述
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String desc() {
        return IDSConstant.DESC;
    }
}
