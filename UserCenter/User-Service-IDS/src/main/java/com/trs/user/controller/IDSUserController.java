package com.trs.user.controller;

import com.trs.user.service.AbstractUserService;
import com.trs.user.service.impl.IDSUserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/user")
@Slf4j
public class IDSUserController extends UserController {

    @Autowired
    private IDSUserServiceImpl service;

    /**
     * 获取相关服务类<BR>
     *
     * @return 相关服务类
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 14:47
     */
    @Override
    public AbstractUserService getService() {
        return service;
    }
}
