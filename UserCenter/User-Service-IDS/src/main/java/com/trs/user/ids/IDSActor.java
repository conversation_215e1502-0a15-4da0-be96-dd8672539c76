package com.trs.user.ids;

import com.trs.common.base.Report;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.CoreConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.idm.client.actor.ActorException;
import com.trs.idm.client.actor.SSOGroup;
import com.trs.idm.client.actor.SSOUser;
import com.trs.idm.client.actor.StdHttpSessionBasedActor;
import com.trs.ids.api.AdminServiceApi;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.DTO.GroupUserDTO;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.GroupUserVO;
import com.trs.user.VO.GroupVO;
import com.trs.user.VO.UserVO;
import com.trs.user.constant.IDSConstant;
import com.trs.user.mgr.IDSGroupMgr;
import com.trs.user.mgr.IDSGroupUserMgr;
import com.trs.user.mgr.IDSUserMgr;
import com.trs.user.service.impl.IDSUserServiceImpl;
import com.trs.user.util.IDSUtils;
import com.trs.web.builder.util.BeanFactoryHolder;
import io.vavr.control.Either;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@DependsOn("beanFactoryHolder")
public class IDSActor extends StdHttpSessionBasedActor {

    private IDSUserServiceImpl loginUtil = null;

    private IDSUserMgr userMgr = null;

    private IDSGroupMgr groupMgr = null;

    private IDSGroupUserMgr groupUserMgr = null;

    private AdminServiceApi adminServiceApi;

    private void initParam() {
        synchronized (IDSUserServiceImpl.class) {
            if (loginUtil == null) {
                loginUtil = BeanFactoryHolder.getBean(IDSUserServiceImpl.class).getOrElseThrow(() -> new ActorException("初始化Login失败！"));
            }
        }
        synchronized (IDSUserMgr.class) {
            if (userMgr == null) {
                userMgr = BeanFactoryHolder.getBean(IDSUserMgr.class).getOrElseThrow(() -> new ActorException("初始化User失败！"));
            }
        }

        synchronized (IDSGroupMgr.class) {
            if (groupMgr == null) {
                groupMgr = BeanFactoryHolder.getBean(IDSGroupMgr.class).getOrElseThrow(() -> new ActorException("初始化Group失败"));
            }
        }

        synchronized (IDSGroupUserMgr.class) {
            if (groupUserMgr == null) {
                groupUserMgr = BeanFactoryHolder.getBean(IDSGroupUserMgr.class).getOrElseThrow(() -> new ActorException("初始化GroupUser失败"));
            }
        }

        synchronized (AdminServiceApi.class) {
            if (adminServiceApi == null) {
                adminServiceApi = new AdminServiceApi();
            }
        }
    }

    /**
     * 判断当前 Session 是否登录.
     * <p>
     * TRS 身份服务器系统
     * 14
     * V5.0 协作应用集成手册
     *
     * @see StdHttpSessionBasedActor#checkLocalLogin(HttpSession)
     */
    @Override
    public boolean checkLocalLogin(HttpSession session) throws ActorException {
        initParam();
        if (loginUtil == null) {
            log.error("loginUtil 对象为空！");
            return false;
        }
        if (loginUtil.isAnonymous()) {
            return Try.of(() -> StringUtils.isNullOrEmpty(loginUtil.checkLogin(session.getId()).getToken()) == false).getOrElseThrow(err -> new ActorException(err));
        }
        // 非匿名登录时登录判断交由ids控制
        return false;
    }

    /**
     * 加载登录的统一用户到 Demo 应用的当前会话(Session 对象)中, 完成 Demo 应用自己的登录逻辑 (不需要再次对用户进行认证, 只需要加载).
     *
     * @see StdHttpSessionBasedActor#loadLoginUser(HttpServletRequest,
     * SSOUser)
     */
    @Override
    public void loadLoginUser(HttpServletRequest request, SSOUser loginUser) throws ActorException {
        initParam();
        if (loginUtil == null) {
            log.error("loginUtil 对象为空！");
            return;
        }
        Try.of(() -> {
            UserDTO dto = new UserDTO();
            IDSUtils.copyProperties(loginUser, dto);
            loginUtil.loadLoginUser(request.getSession().getId(), dto);
            return true;
        }).getOrElseThrow(err -> new ActorException(err));
    }

    /**
     * 完成 Demo 应用自己的退出登录的逻辑.
     *
     * @see StdHttpSessionBasedActor#logout(HttpSession)
     */
    @Override
    public void logout(HttpSession session) throws ActorException {
        initParam();
        try {
            loginUtil.logout(session.getId());
            session.invalidate();
        } catch (ServiceException e) {
            log.error("注销登录失败！", e);
        }
    }

    @Override
    public boolean userExist(SSOUser ssoUser) throws ActorException {
        initParam();
        UserDTO dto = new UserDTO();
        IDSUtils.copyProperties(ssoUser, dto);
        return Try.of(() -> userMgr.findUserByName(dto).isPresent()).getOrElse(false);
    }

    @Override
    public boolean removeUser(SSOUser ssoUser, HttpServletRequest httpServletRequest) throws ActorException {
        initParam();
        if (userExist(ssoUser)) {
            UserDTO dto = new UserDTO();
            dto.setUserName(ssoUser.getUserName());
            return Try.of(() -> userMgr.deleteUser(dto)).isSuccess();
        }
        return true;
    }

    @Override
    public boolean updateUser(SSOUser ssoUser, HttpServletRequest httpServletRequest) throws ActorException {
        initParam();
        return Try.of(() -> {
            UserDTO dto = new UserDTO();
            IDSUtils.copyProperties(ssoUser, dto);
            Either<Throwable, UserVO> vo = userMgr.saveUser(dto);
            if (vo.isLeft()) {
                throw vo.getLeft();
            }
            Try.of(() -> {
                // 先清除所有绑定情况
                groupUserMgr.unbundlingByUserName(convertSSOUserToDTO(ssoUser));
                // 然后在依次添加
                ssoUser.getSSOGroups().forEach(item -> moveToGroup(ssoUser, (SSOGroup) item));
                return 0;
            }).onFailure(err -> log.error("更新用户组织异常！", err));
            return vo.get() != null;
        }).getOrElseThrow(err -> {
            log.error("编辑用户出错！", err);
            throw new ActorException(err);
        });
    }

    @Override
    public boolean addUser(SSOUser ssoUser, HttpServletRequest httpServletRequest) throws ActorException {
        return updateUser(ssoUser, httpServletRequest);
    }

    @Override
    public boolean enableUser(SSOUser ssoUser) throws ActorException {
        initParam();
        return Try.of(() -> {
            UserDTO dto = new UserDTO();
            IDSUtils.copyProperties(ssoUser, dto);
            dto.setStatus(CoreConstant.ActiveStatue.ENABLE);
            Either<Throwable, UserVO> vo = userMgr.saveUser(dto);
            if (vo.isLeft()) {
                throw vo.getLeft();
            }
            return vo.get() != null;
        }).getOrElseThrow(err -> {
            log.error("编辑用户出错！", err);
            throw new ActorException(err);
        });
    }

    @Override
    public boolean disableUser(SSOUser ssoUser) throws ActorException {
        initParam();
        return Try.of(() -> {
            UserDTO dto = new UserDTO();
            IDSUtils.copyProperties(ssoUser, dto);
            dto.setStatus(CoreConstant.ActiveStatue.DISABLE);
            Either<Throwable, UserVO> vo = userMgr.saveUser(dto);
            if (vo.isLeft()) {
                throw vo.getLeft();
            }
            return vo.get() != null;
        }).getOrElseThrow(err -> {
            log.error("编辑用户出错！", err);
            throw new ActorException(err);
        });
    }

    @Override
    public String extractUserPwd(HttpServletRequest httpServletRequest) throws ActorException {
        return httpServletRequest.getParameter("userName");
    }

    @Override
    public String extractUserName(HttpServletRequest httpServletRequest) throws ActorException {
        return httpServletRequest.getParameter("password");
    }


    /**
     * Demo 应用同步增加机构的实现. 如果协作应用存在机构存储库, 该方法必
     * 须实现，否则会造成 IDS 和协作应用机构信息不一致.
     * TRS 身份服务器系统 107
     * V5.0 协作应用集成手册
     *
     * @see com.trs.idm.client.actor.IServletAppActor#addGroup(com.trs.idm.client.actor.SSOGroup,
     * javax.servlet.http.HttpServletRequest)
     */
    @Override
    public boolean addGroup(SSOGroup ssoGroup, HttpServletRequest req) {
        initParam();
        return updateGroup(ssoGroup, req);
    }

    /**
     * Demo 应用同步删除机构的实现. 如果协作应用存在机构存储库, 该方法必
     * 须实现，否则会造成 IDS 和协作应用机构信息不一致.
     *
     * @see com.trs.idm.client.actor.IServletAppActor#delGroup(com.trs.idm.client.actor.SSOGroup,
     * javax.servlet.http.HttpServletRequest)
     */
    @Override
    public boolean delGroup(SSOGroup ssoGroup, HttpServletRequest req) {
        initParam();
        return Try.of(() -> {
            GroupDTO groupDTO = convertSSOGroupToDTO(ssoGroup);
            if (groupDTO == null) {
                return true;
            }
            Try.of(() -> groupMgr.deleteAllChildrenGroupKeyMappingByChildrenGroupKey(groupDTO)).onFailure(err -> log.error("删除关联关系失败！", err));
            Report<String> report = groupMgr.deleteByGroupKey(groupDTO);
            if (report.getResult() == Report.RESULT.FAIL) {
                throw new ServiceException(report.getDetail());
            }
            return true;
        }).onFailure(err -> log.error(err.getMessage(), err)).getOrElse(false);
    }

    /**
     * Demo 应用同步更新机构的实现. 如果协作应用存在机构存储库, 该方法必
     * 须实现，否则会造成 IDS 和协作应用机构信息不一致.
     *
     * @see com.trs.idm.client.actor.IServletAppActor#updateGroup(com.trs.idm.client.actor.SSOGroup,
     * javax.servlet.http.HttpServletRequest)
     */
    @Override
    public boolean updateGroup(SSOGroup ssoGroup, HttpServletRequest req) {
        initParam();
        return Try.of(() -> {
            GroupDTO dto = convertSSOGroupToDTO(ssoGroup);
            if (dto == null) {
                return true;
            }
            Optional<GroupVO> vo = groupMgr.findByGroupKey(dto);
            if (vo.isPresent()) {
                dto.setId(vo.get().getId());
            }
            Report<String> report = groupMgr.saveGroup(dto);
            if (report.getResult() == Report.RESULT.FAIL) {
                throw new ServiceException(report.getDetail());
            }
            new Thread(() -> Try.of(() -> {
                saveMapping(ssoGroup);
                return 0;
            })).start();
            return true;
        }).onFailure(err -> log.error(err.getMessage(), err)).getOrElse(false);

    }

    private void saveMapping(SSOGroup ssoGroup) throws ServiceException {
        GroupDTO children = convertSSOGroupToDTO(ssoGroup);
        if (children == null) {
            return;
        }
        BaseUtils.checkDTO(children);
        // 1、删除历史记录
        groupMgr.deleteAllChildrenGroupKeyMappingByChildrenGroupKey(children);
        // 2、开始保存记录
        saveChildrenGroupKeyMapping(ssoGroup.getParentGroupId(), children);
    }

    public void saveChildrenGroupKeyMapping(String parentGroupId, GroupDTO children) throws ServiceException {
        if (children == null) {
            return;
        }
        if (CMyString.isEmpty(parentGroupId)
                || IDSConstant.ROOT_GROUP_KEY.equalsIgnoreCase(parentGroupId)) {
            return;
        }
        groupMgr.saveChildrenMapping(GroupDTO.of(parentGroupId, IDSConstant.KEY), children);
        JSONObject idsGroupInfo = adminServiceApi.groupQueryByGroupId(parentGroupId);
        log.debug("IDS接口返回的数据为:{}", idsGroupInfo);
        JSONArray idsGroups = idsGroupInfo.optJSONArray("groups");
        if (idsGroups != null && idsGroups.length() > 0) {
            JSONObject idsGroup = idsGroups.optJSONObject(0);
            if (idsGroup != null) {
                saveChildrenGroupKeyMapping(idsGroup.optString("parentId"), children);
            }
        }
    }

    @Override
    public boolean removeFromGroup(SSOUser user, SSOGroup group) throws ActorException {
        initParam();
        return Try.of(() -> {
            Optional<GroupVO> groupVO = groupMgr.findByGroupKey(convertSSOGroupToDTO(group));
            Optional<UserVO> userVO = userMgr.findUserByName(convertSSOUserToDTO(user));
            if (groupVO.isPresent() && userVO.isPresent()) {
                Report<String> report = groupUserMgr.unbundling(GroupUserDTO.of(groupVO.get().getId(), userVO.get().getUserName()));
                if (report.getResult() == Report.RESULT.FAIL) {
                    throw new ServiceException(report.getDetail());
                }
                List<GroupUserVO> list = groupUserMgr.findBindingRelationshipByUserName(UserDTO.of(userVO.get().getUserName()));
                // 没有任何分组时默认绑定根组织
                if (list == null || list.size() == 0) {
                    groupUserMgr.binding(GroupUserDTO.of(0L, userVO.get().getUserName()));
                }
            } else {
                if (!groupVO.isPresent()) {
                    log.warn("不存在组织[{}]", group);
                }
                if (!userVO.isPresent()) {
                    log.warn("不存在用户[{}]", user);
                }
            }
            return true;
        }).onFailure(err -> log.error(err.getMessage(), err)).getOrElse(false);
    }

    @Override
    public boolean moveToGroup(SSOUser user, SSOGroup group) throws ActorException {
        initParam();
        return Try.of(() -> {
            Optional<GroupVO> groupVO = groupMgr.findByGroupKey(convertSSOGroupToDTO(group));
            if (!groupVO.isPresent()) {
                log.error("组织[{}]不存在!", group);
                return false;
            }
            Optional<UserVO> userVO = userMgr.findUserByName(convertSSOUserToDTO(user));
            if (!userVO.isPresent()) {
                log.error("用户[{}]不存在!", user);
                return false;
            }
            // 先解绑默认的分组
            groupUserMgr.unbundling(GroupUserDTO.of(0L, userVO.get().getUserName()));
            // 再绑定对应的分组
            Report<String> report = groupUserMgr.binding(GroupUserDTO.of(groupVO.get().getId(), userVO.get().getUserName()));
            if (report.getResult() == Report.RESULT.FAIL) {
                throw new ServiceException(report.getDetail());
            }
            return true;
        }).onFailure(err -> log.error(err.getMessage(), err)).getOrElse(false);
    }

    private GroupDTO convertSSOGroupToDTO(SSOGroup ssoGroup) throws ActorException {
        return Try.of(() -> {
            if (ssoGroup == null) {
                log.warn("组织对象为空了！");
                return null;
            }
            Long parentId = Try.of(() -> {
                String parentGroupId = ssoGroup.getParentGroupId();
                if (IDSConstant.ROOT_GROUP_KEY.equals(parentGroupId)) {
                    return 0L;
                }
                return groupMgr.findByGroupKey(GroupDTO.of(ssoGroup.getParentGroupId(), IDSConstant.KEY)).map(item -> item.getId()).orElse(0L);
            }).getOrElse(0L);
            Integer order = Try.of(() -> Integer.valueOf(ssoGroup.getProperty("displayOrder"))).getOrElse(0);
            GroupDTO dto = GroupDTO.of(null, 1, ssoGroup.getGroupDisplayName(), ssoGroup.getGroupId(), IDSConstant.KEY, ssoGroup.getGroupDesc(), order, parentId);
            return dto;
        }).getOrElseThrow(err -> new ActorException("转换异常！", err));
    }

    private UserDTO convertSSOUserToDTO(SSOUser ssoUser) throws ActorException {
        return Try.of(() -> {
            if (ssoUser == null) {
                throw new ServiceException("用户对象不能为空！");
            }
            UserDTO dto = new UserDTO();
            IDSUtils.copyProperties(ssoUser, dto);
            return dto;
        }).getOrElseThrow(err -> new ActorException("转换异常！", err));
    }
}
