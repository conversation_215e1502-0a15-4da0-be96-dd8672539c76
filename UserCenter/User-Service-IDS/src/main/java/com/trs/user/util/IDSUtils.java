package com.trs.user.util;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.CoreConstant;
import com.trs.gov.core.util.BaseUtils;
import com.trs.idm.client.actor.SSOUser;
import com.trs.user.DTO.UserDTO;
import com.trs.user.constant.IDSConstant;
import org.springframework.util.Assert;

public class IDSUtils {
    public static void copyProperties(SSOUser ssoUser, UserDTO dto) {
        Assert.notNull(ssoUser, "集成登录用户不能为空！");
        Assert.notNull(dto, "UserDTO不能为空！");
        BaseUtils.copyProperties(ssoUser, dto);
        dto.setUserName(ssoUser.getUserName());
        dto.setSourceKey(IDSConstant.KEY);
        dto.setEmail(ssoUser.getMail());
        dto.setPhone(ssoUser.getProperty("mobile"));
        dto.setSourceUserId(ssoUser.getProperty("userid"));
        dto.setTrueName(StringUtils.isNullOrEmpty(ssoUser.getTrueName()) ? ssoUser.getUserName() : ssoUser.getTrueName());
        if ("true".equalsIgnoreCase(ssoUser.getProperty("actived"))) {
            dto.setStatus(CoreConstant.ActiveStatue.ENABLE);
        } else {
            dto.setStatus(CoreConstant.ActiveStatue.DISABLE);
        }
    }
}
