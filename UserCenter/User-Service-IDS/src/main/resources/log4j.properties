log.path=@log.path@
### \u8BBE\u7F6E###
log4j.rootLogger=INFO,stdout,totalFile,warningFile
#Spring
log4j.category.org.springframework=ERROR
org.springframework.*=ERROR
#Quartz
log4j.category.org.quartz=ERROR
org.quartz.*=ERROR
#Apache
log4j.category.org.apache=ERROR
#IDS
log4j.category.com.trs.idm=ERROR
#Alibaba
log4j.category.com.alibaba=WARN
### \u8F93\u51FA\u4FE1\u606F\u5230\u63A7\u5236\u53F0\uFF08\u53EA\u8F93\u51FA\u8B66\u544A\u7EA7\u522B\uFF09 ###
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.Threshold=WARN
log4j.appender.stdout.layout.ConversionPattern=%d - %-5p %x - %m - %c%l -%-4r [%t] %n
### \u8F93\u51FAINFO\u4FE1\u606F ###
log4j.appender.totalFile=org.apache.log4j.DailyRollingFileAppender
log4j.appender.totalFile.File=${log.path}/LOGS/WorkOrder/User-Service-IDS/info.log
log4j.appender.totalFile.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.totalFile.layout=org.apache.log4j.PatternLayout
log4j.appender.totalFile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [ %t:%r ] - [ %p ] %m%n
### \u8F93\u51FA\u8B66\u544A\u4FE1\u606F ###
log4j.appender.warningFile=org.apache.log4j.DailyRollingFileAppender
log4j.appender.warningFile.File=${log.path}/LOGS/WorkOrder/User-Service-IDS/warning.log
log4j.appender.warningFile.DatePattern='_'yyyy-MM-dd'.log'
log4j.appender.warningFile.layout=org.apache.log4j.PatternLayout
log4j.appender.warningFile.Threshold=WARN
log4j.appender.warningFile.layout.ConversionPattern=%-d{yyyy-MM-dd HH:mm:ss} [ %t:%r ] - [ %p ] %m%n
