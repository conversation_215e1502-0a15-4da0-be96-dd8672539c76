##\u9ED8\u8BA4\u4E3Atrue\uFF0C\u5982\u679C\u8BBE\u4E3Afalse\uFF0CAgent\u4E2D\u7684Filter\u5C06\u4F1A\u505C\u6B62\u8FC7\u6EE4\u5E94\u7528\u6240\u6709\u7684\u8BF7\u6C42\u3002
sso.on=true
ssoUrl=@ids.ssoUrl@
#\u662F\u5426\u4F7F\u7528HTTP\u534F\u8BAE\u7684\u65B9\u5F0F\u4E0EIDS\u8FDE\u63A5\u3002\u5982\u679C\u6B64\u5904\u586B\u5199true\uFF0C\u90A3\u4E48Agent\u4F1A\u4F18\u5148\u4F7F\u7528HTTP\u901A\u9053\u7684\u65B9\u5F0F\u4E0EIDS\u8FDB\u884C\u8FDE\u63A5\uFF0Cidm.server.*\u7684\u914D\u7F6E\u9879\u6240\u914D\u7F6E\u7684\u53C2\u6570\u4F1A\u81EA\u52A8\u5931\u6548
protocol.http=@ids.protocol.http@
protocol.http.url=@ids.protocol.http.url@
#\u6240\u8981\u8FDE\u63A5IDS\u7684IP(\u6216\u4E3B\u673A). \u5FC5\u586B.
idm.server.host=@ids.idm.server.host@
#\u6240\u8981\u8FDE\u63A5IDS\u7684\u540E\u53F0SSLServer\u7AEF\u53E3.\u5FC5\u586B.\u5982\u679C\u4E0D\u586B\u9ED8\u8BA4\u4E3A2005.
idm.server.port=@ids.idm.server.port@
#\u521B\u5EFAsocket\u6570
idm.sockets.amount=@ids.idm.sockets.amount@
#\u534F\u4F5C\u5E94\u7528\u540D. \u5FC5\u586B, \u5E76\u4E14\u5FC5\u987B\u548C\u5728\u6240\u8981\u8FDE\u63A5IDS\u4E0A\u6CE8\u518C\u7684\u534F\u4F5C\u5E94\u7528\u540D\u4FDD\u6301\u4E00\u81F4.
agent.name=@ids.agent.name@
#\u534F\u52A9\u5E94\u7528\u52A0\u5BC6key
ids.des.key=@ids.des.key@
###ids\u767B\u5F55\u6210\u529F\u540E\u8DF3\u8F6C\u5730\u5740
afterLoginOk.gotoUrl=@ids.afterLoginOk.gotoUrl@
###ids\u767B\u5F55\u5931\u8D25\u540E\u8DF3\u8F6C\u5730\u5740
afterLoginFail.gotoUrl=@ids.afterLoginFail.gotoUrl@
#\u662F\u5426\u5141\u8BB8\u533F\u540D\u8BBF\u95EE, y\u4E3A\u5141\u8BB8, \u5426\u5219\u4E3A\u4E0D\u5141\u8BB8.
allow.anonymous=n
#\u662F\u5426\u662F\u524D\u540E\u7AEF\u5206\u79BB\u7684\u5E94\u7528
isFrontBack.Fl=y
coAppActor.className=com.trs.user.ids.IDSActor
loginAction.uri=/user/login
loginAction.uri.matchPattern=startWith
logout.uri=/user/logout
sso.ifUserNotExistOnIDS.useLocalLoginLogic=true
sso.ifUserNotExistOnIDS.selfLoginPage.action.url=/user/login
afterLoginOk.needPostBack=false
afterLoginOk.gotoType=redirect
#\u4F7F\u7528IBM JDK\u65F6\u5728JSSE\u53EF\u80FD\u4F1A\u9047\u5230\u95EE\u9898, \u6B64\u65F6\u53EF\u91C7\u7528\u5982\u4E0B\u914D\u7F6E(\u5982\u679C\u4F7F\u7528"socketType=plain"\u5219\u5FC5\u987B\u5728IDS\u7AEF\u4E5F\u914D\u7F6E"socketType=plain")
socketType=plain
#socketType=dummySSL
ignoreUrl.prefix=/user/getLoginUserInfo,/user/getLoginInfo