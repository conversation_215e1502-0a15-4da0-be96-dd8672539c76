package com.trs.user.service.impl;

import com.trs.gov.core.constant.CoreConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.GroupUserSearchDTO;
import com.trs.user.UserServiceIDSApplication;
import com.trs.user.VO.GroupVO;
import com.trs.user.VO.UserVO;
import com.trs.user.constant.IDSConstant;
import com.trs.user.service.IGroupUserService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.fail;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = UserServiceIDSApplication.class)
@Slf4j
public class IDSGroupUserServiceImplTest {

    @Reference
    private IGroupUserService service;

    @Test
    public void getAllUserInGroup() {
        try {
            RestfulResults<List<UserVO>> users = service.getAllUserInGroup(GroupUserSearchDTO.of("17", IDSConstant.KEY, CoreConstant.ActiveStatue.DISABLE));
            System.out.println(users.getDatas().size());
        } catch (ServiceException e) {
            log.error("[getAllUserInGroup]发生异常", e);
            fail("[getAllUserInGroup]发生异常,ERR=" + e.getMessage());
        }
    }


    @Test
    public void getAllGroupInUser() {
        try {
            RestfulResults<List<GroupVO>> groups = service.getAllGroupInUser(GroupUserSearchDTO.of("ccb"));
            System.out.println(groups.getDatas().size());
        } catch (ServiceException e) {
            log.error("[getAllGroupInUser]发生异常", e);
            fail("[getAllGroupInUser]发生异常,ERR=" + e.getMessage());
        }
    }
}
