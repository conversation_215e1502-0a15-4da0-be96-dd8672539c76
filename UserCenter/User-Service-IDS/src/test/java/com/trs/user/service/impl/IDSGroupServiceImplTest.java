package com.trs.user.service.impl;

import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.UserServiceIDSApplication;
import com.trs.user.VO.GroupVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = UserServiceIDSApplication.class)
@Slf4j
public class IDSGroupServiceImplTest {

    @Autowired
    private IDSGroupServiceImpl service;

    @Test
    public void findGroupIn3rdSystem() throws ServiceException {
        Optional<GroupVO> vo =  service.findGroupIn3rdSystem(GroupDTO.of(0L, null, "TRS开发人员123", null, null, null, null, null));
        System.out.println(vo.orElse(null));
    }

    @Test
    public void addGroupTo3rdSystem() throws ServiceException {
        service.addGroupTo3rdSystem(GroupDTO.of(0L, null, "TRS开发人员123", null, null, null, null, null));
    }

    @Test
    public void deleteGroupIn3rdSystem() throws ServiceException {
        service.deleteGroupIn3rdSystem(GroupDTO.of("68"));
    }
}