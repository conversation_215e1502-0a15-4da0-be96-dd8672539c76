package com.trs.user.mgr;

import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.UserServiceIDSApplication;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.Assert.fail;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = UserServiceIDSApplication.class)
@Slf4j
public class UserMgrTest {

    @Resource
    private IDSUserMgr idsUserMgr;

    @Test
    public void getAllUser() {
        try {
            UserSearchDTO dto = new UserSearchDTO();
            RestfulResults<List<UserVO>> list =  idsUserMgr.getAllUser(dto);
            list.getDatas().forEach(item-> System.out.println(item.getUserName()));
        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            fail(e.getMessage());
        }
    }
}
