plugins {
    `java-library`
    id("org.springframework.boot")
}

dependencies {
    api(project(":Configuration"))
    api(project(":User-Service-Impl"))
    implementation("com.trs.ids:ids-sdk:[1.2,)")
    implementation("trs.com.idm:trsids-agent:1.6")
    implementation("trs.com.idm:trsids-filter:1.6")
    implementation("commons-httpclient:commons-httpclient:3.1")
}

description = "User-Service-IDS"
