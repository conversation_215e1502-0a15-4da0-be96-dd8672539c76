package com.trs.user.service;

import com.trs.upms.client.po.Resource;
import com.trs.upms.client.service.impl.UpmsRightServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户模块实现权限服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-13 16:16
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class UserRightServiceImpl extends UpmsRightServiceImpl {
    /**
     * @param resourceId
     * @param userName
     * @return
     * <AUTHOR>
     * @Description 获取资源配置---------需要子系统来完成
     * @Date 10:04 2020/2/20
     * @Param
     */
    @Override
    public List<Resource> queryRightResources(String resourceId, String userName) throws Exception {
        return new ArrayList<>();
    }
}
