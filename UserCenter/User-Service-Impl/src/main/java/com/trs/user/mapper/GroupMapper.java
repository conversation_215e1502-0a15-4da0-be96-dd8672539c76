package com.trs.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.user.DO.GroupDO;
import com.trs.user.DTO.GroupUserSearchDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

@Component
public interface GroupMapper extends BaseMapper<GroupDO> {

    @Select("select * from group_table where id IN ( select group_id from group_user_table where user_name=#{dto.userName})")
    public Page<GroupDO> getAllGroupInUser(Page<GroupDO> page, @Param("dto") GroupUserSearchDTO dto);


}
