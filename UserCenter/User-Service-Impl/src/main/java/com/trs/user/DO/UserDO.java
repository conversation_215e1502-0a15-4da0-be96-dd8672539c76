package com.trs.user.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户实体对象
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-14 19:49
 * @version 1.0
 * @since 1.0
 */
@Entity
@Data
@TableName("user")
public class UserDO extends BaseDO {

    /**
     * 用户名
     */
    @Column(name = "user_name")
    private String userName;

    /**
     * 密码
     */
    @Column(name = "password")
    private String password;

    /**
     * 真实姓名
     */
    @Column(name = "true_name")
    private String trueName;

    /**
     * 邮箱
     */
    @Column(name = "email")
    private String email;

    /**
     * 电话
     */
    @Column(name = "phone")
    private String phone;

    /**
     * 用户数据源的Key，例如IDS
     */
    @Column(name = "source_key")
    private String sourceKey;

    /**
     * 用户数据源的用户ID
     */
    @Column(name = "source_user_id")
    private String sourceUserId;

    /**
     * 头像
     */
    @Column(name = "avatar")
    private String avatar;
}
