package com.trs.user.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Report;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.user.DO.GroupDO;
import com.trs.user.DO.GroupUserDO;
import com.trs.user.DO.UserDO;
import com.trs.user.DTO.GroupUserDTO;
import com.trs.user.DTO.GroupUserSearchDTO;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.GroupUserVO;
import com.trs.user.VO.GroupVO;
import com.trs.user.VO.UserVO;
import com.trs.user.mapper.GroupMapper;
import com.trs.user.mapper.GroupUserMapper;
import com.trs.user.mapper.UserMapper;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class GroupUserMgr implements IGroupUserMgr {

    @Autowired
    private GroupUserMapper groupUserMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private UserMapper userMapper;

    /**
     * 查找绑定关系<BR>
     *
     * @param dto 请求参数
     * @return 绑定关系
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 17:09
     */
    @Override
    public Optional<GroupUserVO> findBindingRelationship(GroupUserDTO dto) throws ServiceException {
        QueryWrapper<GroupUserDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_id", dto.getGroupId()).eq("user_name", dto.getUserName());
        return Optional.ofNullable(groupUserMapper.selectOne(queryWrapper)).map(item -> {
            GroupUserVO vo = new GroupUserVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        });
    }

    /**
     * 绑定<BR>
     *
     * @param dto 请求参数
     * @return 绑定报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 17:09
     */
    @Override
    public Report<String> binding(GroupUserDTO dto) throws ServiceException {
        Optional<GroupUserVO> opt = findBindingRelationship(dto);
        if (opt.isPresent()) {
            return new Report<String>("绑定", "关系已存在，无需再次绑定！");
        } else {
            GroupUserDO groupUserDO = new GroupUserDO();
            BaseUtils.copyProperties(dto, groupUserDO, "id");
            groupUserMapper.insert(groupUserDO);
            return new Report<String>("绑定", "成功绑定！");
        }
    }

    /**
     * 解绑<BR>
     *
     * @param dto 请求参数
     * @return 解绑报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 17:09
     */
    @Override
    public Report<String> unbundling(GroupUserDTO dto) throws ServiceException {
        return findBindingRelationship(dto).map(item -> {
            groupUserMapper.deleteById(item.getId());
            return new Report<String>("解绑", "成功解绑");
        }).orElse(new Report<String>("解绑", "绑定关系可能不存在！", Report.RESULT.UNKNOW));
    }

    /**
     * 取消用户所有绑定记录<BR>
     *
     * @param dto 请求参数(需要包含用户名)
     * @return 取消情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 18:02
     */
    @Override
    public Report<String> unbundlingByUserName(UserDTO dto) throws ServiceException {
        if (CMyString.isEmpty(dto.getUserName())) {
            throw new ParamInvalidException("用户名不能为空！");
        }
        return Try.of(() -> {
            QueryWrapper<GroupUserDO> queryWrapper = new QueryWrapper<>();
            groupUserMapper.delete(queryWrapper.eq("user_name", dto.getUserName()));
            return new Report<String>("解绑", "成功解绑");
        }).getOrElseThrow(err -> new ServiceException(err.getMessage(), err));
    }

    /**
     * 查找用户所有绑定记录<BR>
     *
     * @param dto 请求参数(需要包含用户名)
     * @return 相关列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 18:02
     */
    @Override
    public List<GroupUserVO> findBindingRelationshipByUserName(UserDTO dto) throws ServiceException {
        if (CMyString.isEmpty(dto.getUserName())) {
            throw new ParamInvalidException("用户名不能为空！");
        }
        return Try.of(() -> {
            QueryWrapper<GroupUserDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_name", dto.getUserName());
            return groupUserMapper.selectList(queryWrapper).stream().map(item -> {
                GroupUserVO vo = new GroupUserVO();
                BaseUtils.copyProperties(item, vo);
                return vo;
            }).collect(Collectors.toList());
        }).getOrElseThrow(err -> new ServiceException(err.getMessage(), err));
    }

    /**
     * 查询组织中的所有用户<BR>
     *
     * @param dto 查询参数
     * @return 用户列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-28 10:58
     */
    @Override
    public RestfulResults<List<UserVO>> getAllUserInGroup(GroupUserSearchDTO dto) throws ServiceException {
        Page<UserDO> page = userMapper.getAllUserInGroup(new Page<>(dto.getPageNum(), dto.getPageSize()), dto);
        List<UserDO> list = page.getRecords().stream().map(item -> {
            UserDO vo = new UserDO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
        return RestfulResults.ok(list).addMsg("成功获取数据！").addTotalCount(page.getTotal()).addPageSize(dto.getPageSize()).addPageNum(dto.getPageNum());
    }

    /**
     * 查询用户所属的组织<BR>
     *
     * @param dto 查询参数
     * @return 组织列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-28 10:58
     */
    @Override
    public RestfulResults<List<GroupVO>> getAllGroupInUser(GroupUserSearchDTO dto) throws ServiceException {
        Page<GroupDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        page = groupMapper.getAllGroupInUser(page, dto);
        List<GroupVO> list = page.getRecords().stream().map(item -> {
            GroupVO vo = new GroupVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
        return RestfulResults.ok(list).addMsg("成功获取数据！").addTotalCount(page.getTotal()).addPageSize(dto.getPageSize()).addPageNum(dto.getPageNum());
    }
}
