package com.trs.user.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.Report;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.user.DO.GroupDO;
import com.trs.user.DO.GroupKeyMappingDO;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.VO.GroupVO;
import com.trs.user.mapper.GroupKeyMapper;
import com.trs.user.mapper.GroupMapper;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public abstract class GroupMgr implements IGroupMgr {

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private GroupKeyMapper groupKeyMapper;

    /**
     * 保存组织<BR>
     *
     * @param dto 请求参数
     * @return 保存报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 15:55
     */
    @Override
    public Report<String> saveGroup(GroupDTO dto) throws ServiceException {
        return Try.of(() -> {
            Optional<GroupDO> opt = findByGroupKey(dto).map(item -> groupMapper.selectById(item.getId()));
            if (!opt.isPresent()) {
                opt = findById(dto).map(item -> groupMapper.selectById(item.getId()));
            }
            GroupDO groupDO = opt.orElse(new GroupDO());
            BaseUtils.copyProperties(dto, groupDO, "id");
            if (groupDO.getId() != null) {
                groupMapper.updateById(groupDO);
            } else {
                groupMapper.insert(groupDO);
            }
            return new Report<String>("新建/编辑组织", "成功操作数据！");
        }).getOrElseThrow(err -> new ServiceException("操作失败！", err));
    }

    /**
     * 根据组织ID查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    @Override
    public Optional<GroupVO> findById(GroupDTO dto) throws ServiceException {
        return Optional.ofNullable(groupMapper.selectById(Optional.ofNullable(dto.getId()).orElse(0L))).map(item -> {
            GroupVO vo = new GroupVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        });
    }

    /**
     * 根据组织名称查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    @Override
    public Optional<GroupVO> findByName(GroupDTO dto) throws ServiceException {
        if (CMyString.isEmpty(dto.getGroupName())) {
            throw new ParamInvalidException("组织名称不能为空！");
        }
        QueryWrapper<GroupDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_name", dto.getGroupName())
                .eq("group_source", CMyString.showEmpty(dto.getGroupSource(), key()));
        if (dto.getParentId() != null) {
            queryWrapper.eq("parent_id", dto.getParentId());
        }
        if (dto.getStatus() != null) {
            queryWrapper.eq("status", dto.getStatus());
        }
        return Optional.ofNullable(groupMapper.selectOne(queryWrapper)).map(item -> {
            GroupVO vo = new GroupVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        });
    }

    /**
     * 根据组织Key查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    @Override
    public Optional<GroupVO> findByGroupKey(GroupDTO dto) throws ServiceException {
        QueryWrapper<GroupDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_key", dto.getGroupKey()).eq("group_source", CMyString.showEmpty(dto.getGroupSource(), key()));
        return Optional.ofNullable(groupMapper.selectOne(queryWrapper)).map(item -> {
            GroupVO vo = new GroupVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        });
    }

    /**
     * 根据组织ID删除组织<BR>
     *
     * @param dto 请求参数
     * @return 删除报告
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 15:13
     */
    @Override
    public Report<String> deleteById(GroupDTO dto) throws ServiceException {
        return Try.of(() -> {
            Optional<GroupVO> vo = findById(dto);
            if (vo.isPresent()) {
                groupMapper.deleteById(vo.get().getId());
                log.info("删除组织" + vo.get());
                return new Report<String>("删除组织", "成功删除组织[ID=" + dto.getId() + ",Name=" + vo.get().getGroupName() + "]");
            } else {
                return new Report<String>("删除组织", "不存在ID=" + dto.getId() + "的组织，不用删除！");
            }
        }).getOrElseThrow(err -> new ServiceException(err.getMessage(), err));
    }

    /**
     * 根据组织Key删除组织<BR>
     *
     * @param dto 请求参数
     * @return 删除报告
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 15:13
     */
    @Override
    public Report<String> deleteByGroupKey(GroupDTO dto) throws ServiceException {
        return Try.of(() -> {
            Optional<GroupVO> vo = findByGroupKey(dto);
            if (vo.isPresent()) {
                groupMapper.deleteById(vo.get().getId());
                log.info("删除组织" + vo.get());
                return new Report<String>("删除组织", "成功删除组织[ID=" + dto.getId() + ",Name=" + vo.get().getGroupName() + "]");
            } else {
                return new Report<String>("删除组织", "不存在GroupKey=" + dto.getGroupKey() + "的组织，不用删除！");
            }
        }).getOrElseThrow(err -> new ServiceException(err.getMessage(), err));
    }

    /**
     * 根据GroupKey查询其子组织的GroupKey<BR>
     *
     * @param dto 组织信息（GroupKey不能为空）
     * @return 相关列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-04 17:57
     */
    @Override
    public List<String> findChildrenGroupKeyListByGroupKey(GroupDTO dto) throws ServiceException {
        return groupKeyMapper.getAllChildrenGroupKey(dto)
                .stream()
                .map(item -> item.getChildrenKey())
                .collect(Collectors.toList());
    }

    /**
     * 保存GroupKey的父子关系<BR>
     *
     * @param parent   父组织
     * @param children 子组织
     * @return 保存报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-04 18:07
     */
    @Override
    public Report saveChildrenMapping(GroupDTO parent, GroupDTO children) throws ServiceException {
        BaseUtils.checkDTO(parent, children);
        if (CMyString.isEmpty(parent.getGroupKey())) {
            throw new ParamInvalidException("父组织的GroupKey不能为空！");
        }
        if (CMyString.isEmpty(children.getGroupKey())) {
            throw new ParamInvalidException("子组织的GroupKey不能为空！");
        }
        if (CMyString.isEmpty(parent.getGroupSource())) {
            parent.setGroupSource(key());
        }
        if (CMyString.isEmpty(children.getGroupSource())) {
            children.setGroupSource(key());
        }
        if (!parent.getGroupSource().equals(children.getGroupSource())) {
            throw new ParamInvalidException("父子组织的数据源不一致，父为" + parent.getGroupSource() + ",子为" + children.getGroupSource());
        }
        GroupKeyMappingDO groupKeyMappingDO = new GroupKeyMappingDO(parent.getGroupKey(), children.getGroupKey(), parent.getGroupSource());
        groupKeyMapper.insert(groupKeyMappingDO);
        return new Report("添加记录成功！");
    }

    /**
     * 根据子的GroupKey去删除所有映射记录<BR>
     *
     * @param children 子组织
     * @return 保存报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-04 18:07
     */
    @Override
    public Report deleteAllChildrenGroupKeyMappingByChildrenGroupKey(GroupDTO children) throws ServiceException {
        BaseUtils.checkDTO(children);
        groupKeyMapper.delete(
                new QueryWrapper<GroupKeyMappingDO>()
                        .eq("children_key", children.getGroupKey())
                        .eq("group_source", Optional.ofNullable(children.getGroupSource()).orElse(key())));
        return new Report("删除记录成功！");
    }
}
