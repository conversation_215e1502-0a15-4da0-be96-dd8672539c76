package com.trs.user.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 组织对象
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-24 17:55
 * @version 1.0
 * @since 1.0
 */
@Entity
@Data
@TableName("group_table")
public class GroupDO extends BaseDO {

    /**
     * 组织名
     */
    @Column(name = "group_name")
    @TableField("group_name")
    private String groupName;

    /**
     * 数据来源Key
     */
    @Column(name = "group_key")
    @TableField("group_key")
    private String groupKey;

    /**
     * 数据来源
     */
    @Column(name = "group_source")
    @TableField("group_source")
    private String groupSource;

    /**
     * 组织描述
     */
    @Column(name = "group_desc")
    @TableField("group_desc")
    private String groupDesc;

    /**
     * 排序字段
     */
    @Column(name = "group_order")
    @TableField("group_order")
    private Integer groupOrder;

    /**
     * 父组织id 0-第一级组织
     */
    @Column(name = "parent_id")
    @TableField("parent_id")
    private Long parentId = 0L;
}
