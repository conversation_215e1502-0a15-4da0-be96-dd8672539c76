package com.trs.user.mgr;

import com.trs.common.base.Report;
import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.GroupUserDTO;
import com.trs.user.DTO.GroupUserSearchDTO;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.GroupUserVO;
import com.trs.user.VO.GroupVO;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;
import java.util.Optional;

public interface IGroupUserMgr extends IKey {
    /**
     * 查找绑定关系<BR>
     *
     * @param dto 请求参数
     * @return 绑定关系
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 17:09
     */
    public Optional<GroupUserVO> findBindingRelationship(GroupUserDTO dto) throws ServiceException;

    /**
     * 绑定<BR>
     *
     * @param dto 请求参数
     * @return 绑定报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 17:09
     */
    public Report<String> binding(GroupUserDTO dto) throws ServiceException;

    /**
     * 解绑<BR>
     *
     * @param dto 请求参数
     * @return 解绑报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 17:09
     */
    public Report<String> unbundling(GroupUserDTO dto) throws ServiceException;

    /**
     * 取消用户所有绑定记录<BR>
     *
     * @param dto 请求参数(需要包含用户名)
     * @return 取消情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 18:02
     */
    public Report<String> unbundlingByUserName(UserDTO dto) throws ServiceException;


    /**
     * 查找用户所有绑定记录<BR>
     *
     * @param dto 请求参数(需要包含用户名)
     * @return 相关列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 18:02
     */
    public List<GroupUserVO> findBindingRelationshipByUserName(UserDTO dto) throws ServiceException;

    /**
     * 查询组织中的所有用户<BR>
     *
     * @param dto 查询参数
     * @return 用户列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-28 10:58
     */
    public RestfulResults<List<UserVO>> getAllUserInGroup(GroupUserSearchDTO dto) throws ServiceException;

    /**
     * 查询用户所属的组织<BR>
     *
     * @param dto 查询参数
     * @return 组织列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-28 10:58
     */
    public RestfulResults<List<GroupVO>> getAllGroupInUser(GroupUserSearchDTO dto) throws ServiceException;
}
