package com.trs.user.service;

import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.DTO.GroupUserSearchDTO;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.GroupVO;
import com.trs.user.VO.UserVO;
import com.trs.user.mgr.GroupMgr;
import com.trs.user.mgr.GroupUserMgr;
import com.trs.user.mgr.UserMgr;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

@Slf4j
public abstract class AbstractGroupUserService implements IGroupUserService {

    @Autowired
    private GroupUserMgr groupUserMgr;

    @Autowired
    private GroupMgr groupMgr;

    @Autowired
    private UserMgr userMgr;


    /**
     * 查询组织中的所有用户<BR>
     *
     * @param dto 查询参数
     * @return 用户列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-28 10:58
     */
    @Override
    public RestfulResults<List<UserVO>> getAllUserInGroup(GroupUserSearchDTO dto) throws ServiceException {
        Long groupId = dto.getGroupId();
        Optional<GroupVO> opt = Optional.empty();
        if (groupId != null) {
            opt = groupMgr.findById(GroupDTO.of(groupId));
        }
        if (!opt.isPresent() && !CMyString.isEmpty(dto.getGroupKey())) {
            opt = groupMgr.findByGroupKey(GroupDTO.of(dto.getGroupKey(), CMyString.showEmpty(dto.getGroupSource(), key())));
        }
        if (!opt.isPresent()) {
            log.error("不存在对应的组织[{}]", dto);
            throw new ParamInvalidException("不存在对应的组织");
        }
        dto.setGroupId(opt.get().getId());
        return groupUserMgr.getAllUserInGroup(dto);
    }

    /**
     * 查询用户所属的组织<BR>
     *
     * @param dto 查询参数
     * @return 组织列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-28 10:58
     */
    @Override
    public RestfulResults<List<GroupVO>> getAllGroupInUser(GroupUserSearchDTO dto) throws ServiceException {
        if (CMyString.isEmpty(dto.getUserName())) {
            throw new ParamInvalidException("用户名不能为空！");
        }
        Optional<UserVO> opt = userMgr.findUserByName(UserDTO.of(dto.getUserName()));
        if (!opt.isPresent()) {
            log.error("不存在对应的用户[{}]", dto);
            throw new ParamInvalidException("不存在对应的用户");
        }
        return groupUserMgr.getAllGroupInUser(dto);
    }
}
