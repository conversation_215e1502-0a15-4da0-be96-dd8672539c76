package com.trs.user.mgr;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.Report;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.tcache.util.redis.JedisPoolUtils;
import com.trs.user.DO.UserDO;
import com.trs.user.DTO.UserDTO;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.constant.UserConstant;
import com.trs.user.mapper.UserMapper;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Either;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户相关业务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-17 11:13
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public abstract class UserMgr implements IUserMgr {

    @Autowired
    private UserMapper userMapper;


    @Value("${UserService.Redis.Index}")
    private int redisIndex;
    @Value("${UserService.Redis.KeyExpireSecond}")
    private int redisKeyExpireSecond;


    private String makeRedisKey(String token) {
        return key() + "-" + getClass().getName() + "-" + token;
    }

    private String makeRedisKey(String sessionId, String flag) {
        return key() + "-" + getClass().getName() + "-" + sessionId + "-" + flag;
    }

    /**
     * 保存用户<BR>
     *
     * @param dto
     * @return 保存后的数据
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 10:47
     */
    @Override
    public Either<Throwable, UserVO> saveUser(UserDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        return Try.of(() -> {
            Optional<UserVO> optionalUserVO = findUserByName(dto);
            if (!optionalUserVO.isPresent()) {
                optionalUserVO = findUserBySourceUserId(dto);
            }
            UserDO userDO;
            if (optionalUserVO.isPresent()) {
                userDO = userMapper.selectById(optionalUserVO.get().getId());
            } else {
                userDO = new UserDO();
            }
            BaseUtils.copyProperties(dto, userDO, "id");
            if (userDO.getId() != null) {
                userMapper.updateById(userDO);
            } else {
                userMapper.insert(userDO);
            }
            return Optional.ofNullable(userDO).map(item -> {
                UserVO vo = new UserVO();
                BaseUtils.copyProperties(item, vo);
                return vo;
            }).orElseThrow(() -> new ServiceException("数据异常!"));
        }).toEither();
    }

    @Override
    public void saveLoginInfo(String sessionId, String token, UserDTO user) throws ServiceException {
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        jedisPoolUtils.set(makeRedisKey(sessionId, UserConstant.SESSION_LOGIN_FLAG), token, redisKeyExpireSecond, redisIndex);
        jedisPoolUtils.set(makeRedisKey(token), JSONObject.toJSONString(user), redisKeyExpireSecond, redisIndex);
    }

    @Override
    public void deleteLoginInfo(String sessionId, Optional<String> token) throws ServiceException {
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        jedisPoolUtils.del(makeRedisKey(sessionId, UserConstant.SESSION_LOGIN_FLAG), redisIndex);
        if (token.isPresent()){
            jedisPoolUtils.del(makeRedisKey(token.get()), redisIndex);
        }
    }

    @Override
    public Optional<UserDTO> getLoginInfo(String token) throws ServiceException {
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        return Try.of(() -> Optional.ofNullable(jedisPoolUtils.get(makeRedisKey(token), redisIndex)).map(item -> {
            log.info("根据Token[{}]获取到的用户信息为[{}]", token, item);
            return JSONObject.parseObject(item, UserDTO.class);
        })).getOrElseThrow(err -> new ParamInvalidException("Token【" + token + "】已失效！"));
    }

    @Override
    public Optional<String> getLoginToken(String sessionId) {
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        return Optional.ofNullable(sessionId).map(item -> jedisPoolUtils.get(makeRedisKey(item, UserConstant.SESSION_LOGIN_FLAG), redisIndex));
    }

    /**
     * 删除用户<BR>
     *
     * @param dto
     * @return 保存后的DO
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 10:47
     */
    @Override
    public Report deleteUser(UserDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        Optional<UserVO> optionalUserVO = findUserByName(dto);
        if (!optionalUserVO.isPresent()) {
            optionalUserVO = findUserBySourceUserId(dto);
        }
        if (optionalUserVO.isPresent()) {
            userMapper.deleteById(optionalUserVO.get().getId());
        }
        return new Report("删除用户", "成功删除用户！");
    }

    /**
     * 根据用户名查找用户<BR>
     *
     * @param dto 请求数据
     * @return 用户
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 10:57
     */
    @Override
    public Optional<UserVO> findUserByName(UserDTO dto) throws ServiceException {
        UserDO userDO = userMapper.selectOne(
                new QueryWrapper<UserDO>()
                        .eq("user_name", Optional.ofNullable(dto.getUserName()).orElseThrow(() -> new ParamInvalidException("用户名不能为空！")))
        );
        return Optional.ofNullable(userDO).map(item -> {
            UserVO vo = new UserVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        });
    }

    /**
     * 根据数据源ID查找用户<BR>
     *
     * @param dto 请求数据
     * @return 用户
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 10:57
     */
    @Override
    public Optional<UserVO> findUserBySourceUserId(UserDTO dto) throws ServiceException {
        UserDO userDO = userMapper.selectOne(
                new QueryWrapper<UserDO>()
                        .eq("source_key", Optional.ofNullable(dto.getSourceKey()).orElse(key()))
                        .eq("source_user_id", Optional.ofNullable(dto.getSourceUserId()).orElseThrow(() -> new ParamInvalidException("数据源ID不能为空！")))
        );
        return Optional.ofNullable(userDO).map(item -> {
            UserVO vo = new UserVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        });
    }

    @Override
    public RestfulResults<List<UserVO>> getAllUser(UserSearchDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        QueryWrapper<UserDO> queryWrapper = new QueryWrapper<>();
        if (dto.getStatus() != null) {
            queryWrapper.eq("status", dto.getStatus());
        }
        if (!CMyString.isEmpty(dto.getTrueName())) {
            queryWrapper.eq("true_name", dto.getTrueName());
        }
        if (!CMyString.isEmpty(dto.getUserName())) {
            queryWrapper.in("user_name", Arrays.asList(CMyString.split(dto.getUserName(), ",")).stream().filter(item -> !CMyString.isEmpty(item)).collect(Collectors.toList()));
        }
        Page<UserDO> page = userMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        return RestfulResults.ok(page.getRecords().stream().map(item -> {
            UserVO vo = new UserVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList())).addMsg("成功获取数据").addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(page.getTotal());
    }
}

