package com.trs.user.mgr;

import com.trs.common.base.Report;
import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.UserDTO;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Either;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户业务接口
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-17 10:59
 * @version 1.0
 * @since 1.0
 */
public interface IUserMgr extends IKey {
    /**
     * 保存用户<BR>
     *
     * @param dto
     * @return 保存后的数据
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 10:47
     */
    public Either<Throwable, UserVO> saveUser(UserDTO dto) throws ServiceException;

    public void saveLoginInfo(String sessionId, String token, UserDTO user) throws ServiceException;

    public void deleteLoginInfo(String sessionId, Optional<String> token) throws ServiceException;

    public Optional<String> getLoginToken(String sessionId);

    public Optional<UserDTO> getLoginInfo(String token) throws ServiceException;

    /**
     * 删除用户<BR>
     *
     * @param dto
     * @return 保存后的DO
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 10:47
     */
    public Report deleteUser(UserDTO dto) throws ServiceException;

    /**
     * 根据用户名查找用户<BR>
     *
     * @param dto 请求数据
     * @return 用户
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 10:57
     */
    public Optional<UserVO> findUserByName(UserDTO dto) throws ServiceException;

    /**
     * 根据数据源ID查找用户<BR>
     *
     * @param dto 请求数据
     * @return 用户
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 10:57
     */
    public Optional<UserVO> findUserBySourceUserId(UserDTO dto) throws ServiceException;

    public RestfulResults<List<UserVO>> getAllUser(UserSearchDTO dto) throws ServiceException;

}
