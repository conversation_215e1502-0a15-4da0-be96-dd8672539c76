package com.trs.user.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Data
@TableName("group_user_table")
public class GroupUserDO extends BaseDO {
    /**
     * 组织ID
     */
    @Column(name = "group_id")
    @TableField("group_id")
    private Long groupId;

    /**
     * 用户名
     */
    @Column(name = "user_name")
    @TableField("user_name")
    private String userName;
}
