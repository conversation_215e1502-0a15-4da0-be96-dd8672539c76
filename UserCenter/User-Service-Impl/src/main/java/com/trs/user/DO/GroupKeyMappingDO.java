package com.trs.user.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import lombok.Data;
import lombok.NonNull;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Data
@TableName("group_key_mapping")
public class GroupKeyMappingDO extends BaseDO {

    /**
     * 父Key
     */
    @Column(name = "parent_key")
    @TableField("parent_key")
    @NonNull
    private String parentKey;

    /**
     * 子Key
     */
    @Column(name = "children_key")
    @TableField("children_key")
    @NonNull
    private String childrenKey;

    /**
     * 数据来源
     */
    @Column(name = "group_source")
    @TableField("group_source")
    @NonNull
    private String groupSource;
}
