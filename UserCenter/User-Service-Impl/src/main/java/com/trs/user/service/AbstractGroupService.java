package com.trs.user.service;

import com.trs.common.base.Report;
import com.trs.common.exception.ExceptionNumber;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.upms.client.mgr.UpmsRightMgr;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.VO.GroupVO;
import com.trs.user.mgr.GroupMgr;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 组织服务类
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-16 15:20
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public abstract class AbstractGroupService implements IGroupService {

    @Autowired
    private IUserService userService;

    @Autowired
    private UpmsRightMgr rightMgr;

    @Autowired
    private GroupMgr groupMgr;

    /**
     * 编辑组织<BR>
     *
     * @param dto 组织信息
     * @return 编辑后的组织信息(groupKey为组织在第三方平台的Key)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 15:16
     */
    @Override
    final public RestfulResults<GroupVO> editGroup(GroupDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        if (!Try.of(() -> rightMgr.isAdmin(ContextHelper.getLoginUser().get()))
                .onFailure(err -> log.error("判断用户权限异常", err))
                .getOrElse(false)) {
            throw new ServiceException(ExceptionNumber.ERR_USER_NORIGHT, "只有管理员有权限操作组织信息！");
        }
        Optional<GroupVO> groupVO = findGroupIn3rdSystem(dto);
        if (CMyString.isEmpty(dto.getGroupKey()) && CMyString.isEmpty(dto.getGroupName())) {
            throw new ParamInvalidException("组织名称和组织Key不能同时为空！");
        }
        Optional<GroupVO> result;
        if (groupVO.isPresent()) {
            result = updateGroupTo3rdSystem(dto);
        } else {
            result = addGroupTo3rdSystem(dto);
        }
        return Try.of(() -> {
            GroupVO vo = result.orElseThrow(() -> new ServiceException("数据操作失败！"));
            if (CMyString.isEmpty(vo.getGroupKey())) {
                throw new ParamInvalidException("第三方操作数据后未返回组织Key！");
            }
            return RestfulResults.ok(vo).addMsg("成功操作数据！");
        }).getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    /**
     * 删除组织<BR>
     *
     * @param dto 组织信息（含sourceKey）
     * @return 编辑后的组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 15:16
     */
    @Override
    final public RestfulResults<Report> deleteGroupByGroupKey(GroupDTO dto) throws ServiceException {
        if (CMyString.isEmpty(dto.getGroupKey())) {
            throw new ParamInvalidException("groupKey不能为空！");
        }
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        if (!Try.of(() -> rightMgr.isAdmin(ContextHelper.getLoginUser().get()))
                .onFailure(err -> log.error("判断用户权限异常", err))
                .getOrElse(false)) {
            throw new ServiceException(ExceptionNumber.ERR_USER_NORIGHT, "只有管理员有权限操作组织信息！");
        }
        return deleteGroupIn3rdSystem(dto);
    }

    /**
     * 从第三方平台查询相关组织信息（不存在时返回empty）<BR>
     *
     * @param dto 接收到的数据
     * @return 查询到的组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 17:01
     */
    public abstract Optional<GroupVO> findGroupIn3rdSystem(GroupDTO dto) throws ServiceException;

    /**
     * 从第三方平台中删除相关组织<BR>
     *
     * @param dto 信息（需要包含GroupKey）
     * @return 删除情况
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-19 12:42
     */
    public abstract RestfulResults<Report> deleteGroupIn3rdSystem(GroupDTO dto) throws ServiceException;

    /**
     * 添加组织到第三方平台<BR>
     *
     * @param dto 请求数据
     * @return 第三方保存的结果（groupKey不能为空）
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 17:22
     */
    public abstract Optional<GroupVO> addGroupTo3rdSystem(GroupDTO dto) throws ServiceException;

    /**
     * 更新组织到第三方平台(默认第三方平台不用实现更新操作，防止在工单操作后数据异常的情况)<BR>
     *
     * @param dto 请求数据
     * @return 第三方保存的结果（groupKey不能为空）
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 17:22
     */
    public Optional<GroupVO> updateGroupTo3rdSystem(GroupDTO dto) throws ServiceException {
        return findGroupIn3rdSystem(dto);
    }

    /**
     * 根据GroupKey查询其子组织的GroupKey<BR>
     *
     * @param dto 组织信息（GroupKey不能为空）
     * @return 相关列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-04 17:57
     */
    @Override
    public RestfulResults<List<String>> findChildrenGroupKeyListByGroupKey(GroupDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        if (CMyString.isEmpty(dto.getGroupKey())) {
            throw new ParamInvalidException("GroupKey不能为空！");
        }
        if (CMyString.isEmpty(dto.getGroupSource())) {
            dto.setGroupSource(key());
        }
        return Try.of(() -> RestfulResults.ok(groupMgr.findChildrenGroupKeyListByGroupKey(dto)).addMsg("成功获取数据")).getOrElseGet(err -> {
            log.error("获取子组织的GroupKey失败！", err);
            return RestfulResults.error(err.getMessage());
        });
    }

    /**
     * 编辑组织<BR>
     *
     * @param dto 组织信息
     * @return 编辑后的组织信息(groupKey为组织在第三方平台的Key)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 15:16
     */
    @Override
    final public RestfulResults<GroupVO> editGroupByHy(GroupDTO dto) throws ServiceException {
        Optional<GroupVO> groupVO = findGroupIn3rdSystem(dto);
        if (CMyString.isEmpty(dto.getGroupKey()) && CMyString.isEmpty(dto.getGroupName())) {
            throw new ParamInvalidException("组织名称和组织Key不能同时为空！");
        }
        Optional<GroupVO> result;
        if (groupVO.isPresent()) {
            result = updateGroupTo3rdSystem(dto);
        } else {
            result = addGroupTo3rdSystem(dto);
        }
        return Try.of(() -> {
            GroupVO vo = result.orElseThrow(() -> new ServiceException("数据操作失败！"));
            if (CMyString.isEmpty(vo.getGroupKey())) {
                throw new ParamInvalidException("第三方操作数据后未返回组织Key！");
            }
            return RestfulResults.ok(vo).addMsg("成功操作数据！");
        }).getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }
}
