package com.trs.user.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.LoginInfoVo;
import com.trs.user.service.AbstractUserService;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户相关controller
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-17 14:19
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public abstract class UserController {

    /**
     * 获取相关服务类<BR>
     *
     * @return 相关服务类
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 14:47
     */
    public abstract AbstractUserService getService();

    /**
     * @return
     * <AUTHOR>
     * @Description 用户是否登录判断接口
     * @Date 9:41 2020/02/17
     * @Param []
     */
    @RequestMapping(value = "checkLogin", method = {RequestMethod.GET})
    public RestfulResults<LoginInfoVo> checkLogin(HttpServletRequest req, HttpServletResponse resp) {
        String url = req.getParameter("url");
        if (!CMyString.isEmpty(url)) {
            try {
                resp.sendRedirect(url);
                return null;
            } catch (IOException ex) {
                log.error("重定向失败", ex);
            }
        }
        return Try.of(() -> {
            LoginInfoVo loginInfoVo = getService().checkLogin(req.getSession().getId());
            return RestfulResults.ok(loginInfoVo);
        }).getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 退出登录接口
     * @Date 9:41 2019/10/17
     * @Param []
     */
    @RequestMapping(value = "logout", method = {RequestMethod.GET})
    public void logout(HttpSession session) {
        try {
            getService().logout(session.getId());
            session.invalidate();
        } catch (ServiceException e) {
            log.error("注销登录异常！", e);
        }
    }

    /**
     * @Description  根据用户名和密码 获取登录信息
     * @Param [request, user]
     * @return com.trs.web.builder.base.RestfulResults<com.trs.user.VO.LoginInfoVo>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/31 14:54
     **/
    @RequestMapping(value = "getLoginInfo",method = {RequestMethod.POST})
    public RestfulResults<LoginInfoVo> getLoginInfoByNameAndPwd(HttpServletRequest request, UserDTO user){
        return Try.of(()->{
            return RestfulResults.ok(getService().getLoginInfoByNameAndPwd(request.getSession().getId(), user));
        }).getOrElseGet(e -> RestfulResults.error("根据用户名和密码获取token信息失败！ "+e.getMessage()));
    }

}
