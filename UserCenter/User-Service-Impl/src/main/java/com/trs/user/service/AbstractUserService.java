package com.trs.user.service;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.exception.ExceptionNumber;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.upms.client.mgr.UpmsRightMgr;
import com.trs.user.DTO.UserDTO;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.LoginInfoVo;
import com.trs.user.VO.UserVO;
import com.trs.user.constant.UserConstant;
import com.trs.user.mgr.UserMgr;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Either;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户服务的实现
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-16 13:51
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public abstract class AbstractUserService implements IUserService {

    @Autowired
    private UpmsRightMgr upmsRightMgr;

    /**
     * 获取相关用户的业务类<BR>
     *
     * @return 用户的业务类
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-17 15:21
     */
    public abstract UserMgr getUserMgr();

    /**
     * 集成第三方系统时会通过该方法加载登录用户到会话中<BR>
     *
     * @param sessionId 相关sessionId
     * @param user      用户信息（用户名不能为空）
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:07
     */
    @Override
    public void loadLoginUser(String sessionId, UserDTO user) throws ServiceException {
        checkDTO(user);
        if (StringUtils.isNullOrEmpty(user.getUserName())) {
            throw new ParamInvalidException("用户名不能为空！");
        }
        Optional<String> token = getLoginToken(sessionId);
        if (!token.isPresent()) {
            token = Optional.ofNullable(java.util.UUID.randomUUID().toString());
        }
        Throwable o = saveLoginInfo(sessionId, token.get(), user);
        if (o != null) {
            throw new ServiceException(ExceptionNumber.ERR_USER_NOTLOGIN, "保存登录信息异常", o);
        }
        ContextHelper.initContext(token.get(), user.getUserName());
    }

    @Override
    public String getLoadLoginUserToken(String sessionId, UserDTO user) throws ServiceException {
        checkDTO(user);
        if (StringUtils.isNullOrEmpty(user.getUserName())) {
            throw new ParamInvalidException("用户名不能为空！");
        }
        Optional<String> token = getLoginToken(sessionId);
        if (!token.isPresent()) {
            token = Optional.ofNullable(java.util.UUID.randomUUID().toString());
        }
        Throwable o = saveLoginInfo(sessionId, token.get(), user);
        if (o != null) {
            throw new ServiceException(ExceptionNumber.ERR_USER_NOTLOGIN, "保存登录信息异常", o);
        }
        ContextHelper.initContext(token.get(), user.getUserName());
        return token.get();
    }

    /**
     * 保存登录信息<BR>
     *
     * @param sessionId 相关sessionId
     * @param token     用户Token
     * @param user      用户信息
     * @return 保存结果（如果有异常就返回相关异常）
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-16 16:57
     */
    @Override
    public Throwable saveLoginInfo(String sessionId, String token, UserDTO user) {
        Try t = Try.of(() -> {
            checkDTO(user);
            getUserMgr().saveLoginInfo(sessionId, token, user);
            return 0;
        });
        return t.isSuccess() ? null : t.getCause();
    }


    private Optional<String> getLoginToken(String sessionId) {
        Either<Throwable, String> token = Try.of(() -> getUserMgr().getLoginToken(sessionId).get()).toEither();
        if (token.isLeft() || StringUtils.isNullOrEmpty(token.get())) {
            return Optional.empty();
        }
        return Optional.ofNullable(token.get());
    }

    /**
     * 进行登录检测操作<BR>
     * 捞取session中是否存在登录用户：<BR>
     * 主要是捞取Token，并通过getLoginUserInfoByToken获取用户信息<BR>
     *
     * @param sessionId 相关sessionId
     * @return 登录的用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:08
     */
    @Override
    public LoginInfoVo checkLogin(String sessionId) throws ServiceException {
        Optional<String> token = getLoginToken(sessionId);
        if (!token.isPresent()) {
            throw new ServiceException(ExceptionNumber.ERR_USER_NOTLOGIN, "用户未登录！");
        }
        LoginInfoVo vo = LoginInfoVo.convertUserVOToMe(token.get(), Optional.ofNullable(getLoginUserInfoByToken(token.get()))).orElseThrow(() -> new ServiceException("用户信息异常！"));
        vo.setIsAdmin(userIsAdmin(UserDTO.of(vo.getUserName())));
        return vo;
    }


    /**
     * 判断用户是否是管理员<BR>
     *
     * @param dto 用户信息
     * @return 判断结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-20 14:16
     */
    @Override
    public boolean userIsAdmin(UserDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        if (CMyString.isEmpty(dto.getUserName())) {
            throw new ParamInvalidException("用户名不能为空！");
        }
        return Try.of(() -> upmsRightMgr.isAdmin(dto.getUserName())).onFailure(err -> log.error("检测管理员权限失败！", err)).getOrElse(false);
    }

    /**
     * 进行登录操作(匿名集成时调用)<BR>
     *
     * @param sessionId 相关sessionId
     * @param user      登录参数
     * @return 登录的用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:10
     */
    @Override
    public LoginInfoVo login(String sessionId, UserDTO user) throws ServiceException {
        if (isAnonymous()) {
            Either<String, Boolean> result = doLoginIn3rdSystem(sessionId, user);
            if (result.isLeft()) {
                throw new ServiceException(ExceptionNumber.ERR_USER_NOTLOGIN, result.getLeft());
            } else if (result.get()) {
                loadLoginUser(sessionId, user);
                return checkLogin(sessionId);
            }
        }
        throw new ServiceException("方法调用错误，只有匿名集成时才能调用该方法！");
    }

    /**
     * @Description  根据用户名字、密码、SessionId 获取用户的 token
     * @Param [sessionId,Session Id]
     * @Param [userName ,用户名 ]
     * @Param [password ,密码]
     * @return com.trs.user.VO.LoginInfoVo
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/31 14:55
     **/
    @Override
    public LoginInfoVo getLoginInfoByNameAndPwd(String sessionId, UserDTO user) throws ServiceException {
        Either<String, Boolean> result = doLoginIn3rdSystem(sessionId, user);
        if (result.isLeft()) {
            throw new ServiceException(ExceptionNumber.ERR_USER_NOTLOGIN, result.getLeft());
        } else if (result.get()) {
            loadLoginUser(sessionId, user);
            return checkLogin(sessionId);
        }
        throw new ServiceException("方法调用错误，只有匿名集成时才能调用该方法！");
    }

    /**
     * 匿名集成是需要调用该方法在第三方平台进行登录，并返回登录的结果，成功还是失败<BR>
     * 如果系统不支持匿名集成，该方法请返回Either.right(false)<BR>
     *
     * @param sessionId 相关sessionId
     * @param user      登录参数
     * @return 登录的结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:10
     */
    public Either<String, Boolean> doLoginIn3rdSystem(String sessionId, UserDTO user) throws ServiceException {
        checkDTO(user);
        if (!StringUtils.isNullOrEmpty(user.getUserName()) && !StringUtils.isNullOrEmpty(user.getUserName())) {
            return doLoginIn3rdSystemByUserNamePassword(sessionId, user);
        }
        return Either.right(false);
    }

    /**
     * 匿名集成是需要调用该方法通过用户名密码在第三方平台进行登录，并返回登录的结果，成功还是失败<BR>
     * 如果系统不支持匿名集成，该方法请返回Either.right(false)<BR>
     *
     * @param sessionId 相关sessionId
     * @param user      登录参数（用户名，密码）
     * @return 登录的结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:10
     */
    public abstract Either<String, Boolean> doLoginIn3rdSystemByUserNamePassword(String sessionId, UserDTO user) throws ServiceException;

    /**
     * 注销登录<BR>
     *
     * @param sessionId 相关session
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:12
     */
    @Override
    public void logout(String sessionId) throws ServiceException {
        if (isAnonymous()) {
            doLogoutIn3rdSystem(sessionId);
        }
        getUserMgr().deleteLoginInfo(sessionId, getLoginToken(sessionId));
    }

    /**
     * 匿名集成是需要调用该方法在第三方平台进行注销登录<BR>
     *
     * @param sessionId 相关sessionId
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:48
     */
    public abstract void doLogoutIn3rdSystem(String sessionId) throws ServiceException;

    /**
     * 根据请求参数获取基础用户信息<BR>
     *
     * @param dto 用户信息
     * @return 基础用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:16
     */
    @Override
    public UserVO getBaseUserInfo(UserDTO dto) throws ServiceException {
        checkDTO(dto);
        if (!StringUtils.isNullOrEmpty(dto.getUserName())) {
            return getBaseUserInfoByUserName(dto);
        } else {
            return getBaseUserInfoBySourceUserId(dto);
        }
    }

    /**
     * 根据用户名获取基础用户信息<BR>
     *
     * @param dto 用户信息（包含用户名）
     * @return 基础用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:16
     */
    @Override
    public UserVO getBaseUserInfoByUserName(UserDTO dto) throws ServiceException {
        checkDTO(dto);
        if (UserConstant.SYSTEM_USER.getUserName().equals(dto.getUserName())) {
            return UserConstant.SYSTEM_USER;
        }
        return getUserMgr().findUserByName(dto).orElseThrow(() -> new ServiceException("不存在用户【" + dto.getUserName() + "】"));
    }

    private void checkDTO(UserDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        // 塞入默认的数据源KEY
        if (StringUtils.isNullOrEmpty(dto.getSourceKey())) {
            dto.setSourceKey(desc());
        }
    }

    /**
     * 根据第三方数据源的信息去获取基础用户信息<BR>
     *
     * @param dto 用户信息（第三方数据源的信息）
     * @return 基础用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:16
     */
    @Override
    public UserVO getBaseUserInfoBySourceUserId(UserDTO dto) throws ServiceException {
        checkDTO(dto);
        return getUserMgr().findUserBySourceUserId(dto).orElseThrow(() -> new ServiceException("不存在源ID【" + dto.getSourceUserId() + "】"));
    }

    /**
     * 根据Token获取基础用户信息<BR>
     *
     * @param token 用户token
     * @return 基础用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:16
     */
    @Override
    public UserVO getLoginUserInfoByToken(String token) throws ServiceException {
        if (StringUtils.isNullOrEmpty(token)) {
            throw new ParamInvalidException("token不能为空");
        }
        log.debug("token=[{}]", token);
        Optional<UserDTO> userInfo = getUserMgr().getLoginInfo(token);
        if (userInfo.isPresent()) {
            return getBaseUserInfo(userInfo.get());
        } else {
            throw new ParamInvalidException("Token【" + token + "】已失效！");
        }
    }

    /**
     * 从第三方平台获取用户信息<BR>
     *
     * @param dto 基础用户信息
     * @return 第三方用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:18
     */
    @Override
    public JSONObject getUserInfoFrom3rdSystem(UserDTO dto) throws ServiceException {
        checkDTO(dto);
        if (!StringUtils.isNullOrEmpty(dto.getUserName())) {
            return getUserInfoFrom3rdSystemByUserName(dto);
        } else {
            return getUserInfoFrom3rdSystemBySourceUserId(dto);
        }
    }

    /**
     * 根据用户名从第三方系统中获取用户信息<BR>
     *
     * @param dto 请求参数
     * @return 用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-16 18:26
     */
    public abstract JSONObject getUserInfoFrom3rdSystemByUserName(UserDTO dto) throws ServiceException;

    /**
     * 根据第三方ID从第三方系统中获取用户信息<BR>
     *
     * @param dto 请求参数
     * @return 用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-16 18:26
     */
    public abstract JSONObject getUserInfoFrom3rdSystemBySourceUserId(UserDTO dto) throws ServiceException;

    /**
     * 是否是匿名集成（默认非匿名集成）<BR>
     *
     * @return 相关结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:19
     */
    @Override
    public boolean isAnonymous() {
        return false;
    }

    /**
     * <p>Title:        TRS WCM</p>
     * <p>Copyright:    Copyright (c) 2004-2020</p>
     * <p>Company:      www.trs.com.cn</p>
     * IUserService
     *
     * @param dto
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 15:47
     * @version 1.0
     * @since 1.0
     */
    @Override
    public RestfulResults<List<UserVO>> getAllUser(UserSearchDTO dto) throws ServiceException {
        return getUserMgr().getAllUser(dto);
    }
}
