package com.trs.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.user.DO.GroupKeyMappingDO;
import com.trs.user.DTO.GroupDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface GroupKeyMapper extends BaseMapper<GroupKeyMappingDO> {

    @Select("select * from group_key_mapping where parent_key = #{dto.groupKey} AND group_source = #{dto.groupSource}")
    public List<GroupKeyMappingDO> getAllChildrenGroupKey(@Param("dto") GroupDTO dto);
}
