package com.trs.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.user.DO.UserDO;
import com.trs.user.DTO.GroupUserSearchDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

@Component
public interface UserMapper extends BaseMapper<UserDO> {
    @Select("<script>" +
            " select * from user t " +
            "<where>" +
            "<if test=\"dto.userStatue != null\">" +
            "and t.status = #{dto.userStatue}" +
            "</if>" +
            " and t.user_name IN ( select g.user_name from group_user_table g where g.group_id=#{dto.groupId})" +
            "</where>" +
            "</script>")
    public Page<UserDO> getAllUserInGroup(Page<UserDO> page, @Param("dto") GroupUserSearchDTO dto);
}
