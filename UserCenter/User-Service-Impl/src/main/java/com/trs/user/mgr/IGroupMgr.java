package com.trs.user.mgr;

import com.trs.common.base.Report;
import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.VO.GroupVO;

import java.util.List;
import java.util.Optional;

public interface IGroupMgr extends IKey {

    /**
     * 保存组织<BR>
     *
     * @param dto 请求参数
     * @return 保存报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 15:55
     */
    public Report<String> saveGroup(GroupDTO dto) throws ServiceException;

    /**
     * 根据组织ID查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    public Optional<GroupVO> findById(GroupDTO dto) throws ServiceException;

    /**
     * 根据组织名称查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    public Optional<GroupVO> findByName(GroupDTO dto) throws ServiceException;

    /**
     * 根据组织Key查询组织信息<BR>
     *
     * @param dto 请求参数
     * @return 组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 14:47
     */
    public Optional<GroupVO> findByGroupKey(GroupDTO dto) throws ServiceException;

    /**
     * 根据组织ID删除组织<BR>
     *
     * @param dto 请求参数
     * @return 删除报告
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 15:13
     */
    public Report<String> deleteById(GroupDTO dto) throws ServiceException;

    /**
     * 根据组织Key删除组织<BR>
     *
     * @param dto 请求参数
     * @return 删除报告
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-27 15:13
     */
    public Report<String> deleteByGroupKey(GroupDTO dto) throws ServiceException;

    /**
     * 根据GroupKey查询其子组织的GroupKey<BR>
     *
     * @param dto 组织信息（GroupKey不能为空）
     * @return 相关列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-04 17:57
     */
    public List<String> findChildrenGroupKeyListByGroupKey(GroupDTO dto) throws ServiceException;

    /**
     * 保存GroupKey的父子关系<BR>
     *
     * @param parent   父组织
     * @param children 子组织
     * @return 保存报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-04 18:07
     */
    public Report saveChildrenMapping(GroupDTO parent, GroupDTO children) throws ServiceException;

    /**
     * 根据子的GroupKey去删除所有映射记录<BR>
     *
     * @param children 子组织
     * @return 保存报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-04 18:07
     */
    public Report deleteAllChildrenGroupKeyMappingByChildrenGroupKey(GroupDTO children) throws ServiceException;
}
