package com.trs.user.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.Date;

@Data
public class GroupVO extends BaseVO {

    private Long id;

    private Date crTime;

    private String crUser;

    private Integer status;

    /**
     * 组织名
     */
    private String groupName;

    /**
     * 数据来源Key
     */
    private String groupKey;

    /**
     * 数据来源
     */
    private String groupSource;

    /**
     * 组织描述
     */
    private String groupDesc;

    /**
     * 排序字段
     */
    private Integer groupOrder;

    /**
     * 父组织id 0-第一级组织
     */
    private Long parentId;
}
