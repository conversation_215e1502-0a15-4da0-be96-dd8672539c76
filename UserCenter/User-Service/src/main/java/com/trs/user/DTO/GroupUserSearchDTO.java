package com.trs.user.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.constant.CoreConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import lombok.Data;

@Data
public class GroupUserSearchDTO extends BasePageDTO {

    /**
     * 组织ID
     */
    private Long groupId;

    /**
     * 数据来源Key
     */
    private String groupKey;

    /**
     * 数据来源
     */
    private String groupSource;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 用户状态
     */
    private Integer userStatue;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }

    public static GroupUserSearchDTO of(Long groupId) throws ServiceException {
        return of(groupId, null, null, null, CoreConstant.ActiveStatue.ENABLE);
    }

    public static GroupUserSearchDTO of(String userName) throws ServiceException {
        return of(null, null, null, userName, CoreConstant.ActiveStatue.ENABLE);
    }

    public static GroupUserSearchDTO of(String userName, Integer userStatue) throws ServiceException {
        return of(null, null, null, userName, userStatue);
    }

    public static GroupUserSearchDTO of(String groupKey, String groupSource) throws ServiceException {
        return of(null, groupKey, groupSource, null, CoreConstant.ActiveStatue.ENABLE);
    }


    public static GroupUserSearchDTO of(String groupKey, String groupSource, Integer userStatue) throws ServiceException {
        return of(null, groupKey, groupSource, null, userStatue);
    }


    public static GroupUserSearchDTO of(Long groupId, String groupKey, String groupSource, String userName, Integer userStatue) throws ServiceException {
        GroupUserSearchDTO dto = new GroupUserSearchDTO();
        dto.groupId = groupId;
        dto.groupKey = groupKey;
        dto.groupSource = groupSource;
        dto.userName = userName;
        dto.userStatue = userStatue;
        BaseUtils.checkDTO(dto);
        return dto;
    }
}
