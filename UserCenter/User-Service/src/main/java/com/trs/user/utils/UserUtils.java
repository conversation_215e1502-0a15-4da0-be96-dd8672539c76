package com.trs.user.utils;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.core.util.OpenApiUtil;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;

import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户工具类
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-23 20:11
 * @version 1.0
 * @since 1.0
 */
public class UserUtils {

    /**
     * 检测DTO参数，并加载用户信息到DTO中，方便在各个服务间传递用户信息<BR>
     *
     * @param service 用户服务
     * @param dto     相关DTO
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-23 20:01
     */
    public static void checkDTOAndLoadUserInfoByDTO(IUserService service, BaseDTO dto) throws ServiceException {
        if (service == null) {
            throw new ParamInvalidException("用户服务不能为空！");
        }
        Optional<String> openApi = OpenApiUtil.getIsOpenApi();
        if (openApi.isPresent() && "true".equals(openApi.get())) {
            return;
        }
        BaseUtils.checkDTOIsValidAndHaveLoginInfo(dto);
        String unitId = ContextHelper.getLoginUnitId().orElse("");
        String token = ContextHelper.getToken().orElse("");
        if (!CMyString.isEmpty(dto.getToken())) {
            token = dto.getToken();
        } else {
            dto.setToken(token);
        }
        if (!CMyString.isEmpty(dto.getUnitId())) {
            unitId = dto.getUnitId();
        } else {
            dto.setUnitId(unitId);
        }
        UserVO vo = Optional.ofNullable(service.getLoginUserInfoByToken(token)).orElseThrow(() -> new ParamInvalidException("根据Token[" + dto.getToken() + "]无法查询到相关用户信息！"));
        ContextHelper.initContext(token, vo.getUserName(), unitId);
    }
}
