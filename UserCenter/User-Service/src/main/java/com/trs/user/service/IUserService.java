package com.trs.user.service;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.UserDTO;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.LoginInfoVo;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户服务接口
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-14 14:21
 * @version 1.0
 * @since 1.0
 */
public interface IUserService extends IKey {

    /**
     * 集成第三方系统时会通过该方法加载登录用户到会话中<BR>
     *
     * @param sessionId
     * @param user      用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:07
     */
    public void loadLoginUser(String sessionId, UserDTO user) throws ServiceException;

    /**
     * 集成第三方系统时会通过该方法加载登录用户到会话中<BR>
     *
     * @param sessionId
     * @param user      用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:07
     */
    public String getLoadLoginUserToken(String sessionId, UserDTO user) throws ServiceException;



    /**
     * 保存登录信息<BR>
     *
     * @param sessionId 相关sessionId
     * @param token     用户Token
     * @param user      用户信息
     * @return 保存结果（如果有异常就返回相关异常）
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-16 16:57
     */
    public Throwable saveLoginInfo(String sessionId, String token, UserDTO user);

    /**
     * 进行登录检测操作<BR>
     * 捞取session中是否存在登录用户：<BR>
     * 主要是捞取Token，并通过getLoginUserInfoByToken获取用户信息<BR>
     *
     * @param sessionId 相关sessionId
     * @return 登录的用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:08
     */
    public LoginInfoVo checkLogin(String sessionId) throws ServiceException;


    /**
     * 进行登录操作<BR>
     *
     * @param sessionId 相关sessionId
     * @param user      登录参数
     * @return 登录的用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:10
     */
    public LoginInfoVo login(String sessionId, UserDTO user) throws ServiceException;

    /**
     * @Description  根据用户名字、密码、SessionId 获取用户的 token
     * @Param [sessionId,Session Id]
     * @Param [userName ,用户名 ]
     * @Param [password ,密码]
     * @return com.trs.user.VO.LoginInfoVo
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/31 14:55
     **/
    public LoginInfoVo getLoginInfoByNameAndPwd(String sessionId, UserDTO dto) throws ServiceException;

    /**
     * 注销登录<BR>
     *
     * @param sessionId 相关sessionId
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:12
     */
    public void logout(String sessionId) throws ServiceException;

    /**
     * 根据请求参数获取基础用户信息<BR>
     *
     * @param dto 用户信息
     * @return 基础用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:16
     */
    public UserVO getBaseUserInfo(UserDTO dto) throws ServiceException;

    /**
     * 判断用户是否是管理员<BR>
     *
     * @param dto 用户信息
     * @return 判断结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-20 14:16
     */
    public boolean userIsAdmin(UserDTO dto) throws ServiceException;

    /**
     * 根据用户名获取基础用户信息<BR>
     *
     * @param dto 用户信息（包含用户名）
     * @return 基础用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:16
     */
    public UserVO getBaseUserInfoByUserName(UserDTO dto) throws ServiceException;

    /**
     * 根据第三方数据源的信息去获取基础用户信息<BR>
     *
     * @param dto 用户信息（第三方数据源的信息）
     * @return 基础用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:16
     */
    public UserVO getBaseUserInfoBySourceUserId(UserDTO dto) throws ServiceException;

    /**
     * 根据Token获取基础用户信息<BR>
     *
     * @param token 用户token
     * @return 基础用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:16
     */
    public UserVO getLoginUserInfoByToken(String token) throws ServiceException;

    /**
     * 从第三方平台获取用户信息<BR>
     *
     * @param dto 基础用户信息
     * @return 第三方用户信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:18
     */
    public JSONObject getUserInfoFrom3rdSystem(UserDTO dto) throws ServiceException;

    /**
     * 是否是匿名集成（默认非匿名集成）<BR>
     *
     * @return 相关结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-14 18:19
     */
    public boolean isAnonymous();

    /**
     * <p>Title:        TRS WCM</p>
     * <p>Copyright:    Copyright (c) 2004-2020</p>
     * <p>Company:      www.trs.com.cn</p>
     * IUserService
     *
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-21 15:47
     * @version 1.0
     * @since 1.0
     */
    public RestfulResults<List<UserVO>> getAllUser(UserSearchDTO dto) throws ServiceException;

}
