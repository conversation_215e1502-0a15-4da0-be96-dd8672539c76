package com.trs.user.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

@Data
public class GroupUserDTO extends BaseDTO {

    /**
     * 组织ID
     */
    private Long groupId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (groupId == null || groupId < 0L) {
            throw new ParamInvalidException("组织ID不能为空！");
        }
        if (CMyString.isEmpty(userName)) {
            throw new ParamInvalidException("用户名不能为空！");
        }
        return true;
    }

    public static GroupUserDTO of(Long groupId, String userName) throws ServiceException {
        GroupUserDTO dto = new GroupUserDTO();
        dto.groupId = groupId;
        dto.userName = userName;
        BaseUtils.checkDTO(dto);
        return dto;
    }
}
