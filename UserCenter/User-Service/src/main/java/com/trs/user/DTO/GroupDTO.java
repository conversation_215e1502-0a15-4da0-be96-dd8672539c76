package com.trs.user.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import lombok.Data;

@Data
public class GroupDTO extends BaseDTO {

    private Long id;

    private Integer status;

    /**
     * 组织名
     */
    private String groupName;

    /**
     * 数据来源Key
     */
    private String groupKey;

    /**
     * 数据来源
     */
    private String groupSource;

    /**
     * 组织描述
     */
    private String groupDesc;

    /**
     * 排序字段
     */
    private Integer groupOrder;

    /**
     * 父组织id 0-第一级组织
     */
    private Long parentId;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }

    public static GroupDTO of(Long id) throws ServiceException {
        return of(id, null, null);
    }

    public static GroupDTO of(String groupKey) throws ServiceException {
        return of(0L, groupKey, null);
    }

    public static GroupDTO of(String groupKey, String groupSource) throws ServiceException {
        return of(0L, groupKey, groupSource);
    }

    public static GroupDTO of(Long id, String groupKey, String groupSource) throws ServiceException {
        return of(id, null, null, groupKey, groupSource, null, null, null);
    }

    public static GroupDTO of(Long id, Integer status, String groupName, String groupKey, String groupSource, String groupDesc, Integer groupOrder, Long parentId) throws ServiceException {
        GroupDTO dto = new GroupDTO();
        dto.id = id;
        dto.status = status;
        dto.groupName = groupName;
        dto.groupKey = groupKey;
        dto.groupSource = groupSource;
        dto.groupDesc = groupDesc;
        dto.groupOrder = groupOrder;
        dto.parentId = parentId;
        BaseUtils.checkDTO(dto);
        return dto;
    }
}
