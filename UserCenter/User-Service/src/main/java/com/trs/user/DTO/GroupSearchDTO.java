package com.trs.user.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import lombok.Data;

@Data
public class GroupSearchDTO extends BasePageDTO {

    private Long id;

    private Integer status;

    /**
     * 组织名
     */
    private String groupName;

    /**
     * 数据来源Key
     */
    private String groupKey;

    /**
     * 数据来源
     */
    private String groupSource;


    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }

    public static GroupSearchDTO of(Long id) throws ServiceException {
        return of(id, null, null);
    }

    public static GroupSearchDTO of(String groupKey, String groupSource) throws ServiceException {
        return of(null, groupKey, groupSource);
    }

    public static GroupSearchDTO of(Long id, String groupKey, String groupSource) throws ServiceException {
        return of(id, null, null, groupKey, groupSource);
    }

    public static GroupSearchDTO of(Long id, Integer status, String groupName, String groupKey, String groupSource) throws ServiceException {
        GroupSearchDTO dto = new GroupSearchDTO();
        dto.id = id;
        dto.status = status;
        dto.groupName = groupName;
        dto.groupKey = groupKey;
        dto.groupSource = groupSource;
        BaseUtils.checkDTO(dto);
        return dto;
    }
}
