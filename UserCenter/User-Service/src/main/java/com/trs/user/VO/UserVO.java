package com.trs.user.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

@Data
public class UserVO extends BaseVO {

    /**
     * 用户ID
     * */
    private Long id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 真实姓名
     */
    private String trueName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 用户数据源的Key，例如IDS
     */
    private String sourceKey;

    /**
     * 用户数据源的用户ID
     */
    private String sourceUserId;

    /**
     * 用户状态
     * */
    private Integer status;

    /**
     * 头像
     */
    private String avatar;
}
