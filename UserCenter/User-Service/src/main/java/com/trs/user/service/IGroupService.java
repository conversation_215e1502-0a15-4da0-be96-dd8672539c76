package com.trs.user.service;

import com.trs.common.base.Report;
import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.GroupDTO;
import com.trs.user.VO.GroupVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 组织服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-16 15:15
 * @version 1.0
 * @since 1.0
 */
public interface IGroupService extends IKey {

    /**
     * 编辑组织<BR>
     *
     * @param dto 组织信息
     * @return 编辑后的组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 15:16
     */
    public RestfulResults<GroupVO> editGroup(GroupDTO dto) throws ServiceException;

    /**
     * 删除组织<BR>
     *
     * @param dto 组织信息（含sourceKey）
     * @return 编辑后的组织信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-16 15:16
     */
    public RestfulResults<Report> deleteGroupByGroupKey(GroupDTO dto) throws ServiceException;

    /**
     * 根据GroupKey查询其子组织的GroupKey<BR>
     *
     * @param dto 组织信息（GroupKey不能为空）
     * @return 相关列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-04 17:57
     */
    public RestfulResults<List<String>> findChildrenGroupKeyListByGroupKey(GroupDTO dto) throws ServiceException;

    RestfulResults<GroupVO> editGroupByHy(GroupDTO groupDTO) throws ServiceException;
}
