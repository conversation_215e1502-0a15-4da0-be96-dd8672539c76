package com.trs.user.filter;

import com.trs.common.exception.ExceptionNumber;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.user.constant.UserConstant;
import com.trs.user.service.IUserService;
import com.trs.web.restful.RestfulJsonHelper;
import io.vavr.Function2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

@Slf4j
public class LoginFilter extends OncePerRequestFilter {

    private IUserService service;

    /**
     * 跳过过滤的判断函数，由用户自己传入来实现自定义过滤的范围
     */
    private Function2<HttpServletRequest, HttpServletResponse, Boolean> skipFilter = (a, b) -> false;

    public LoginFilter(IUserService service) {
        this.service = service;
    }

    public void setSkipFilter(Function2<HttpServletRequest, HttpServletResponse, Boolean> skipFilter) {
        this.skipFilter = skipFilter;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        try {
            if (skipFilter.apply(request, response)) {
                filterChain.doFilter(request, response);
                return;
            }
            String token = request.getHeader(UserConstant.HEADER_PARAM_TOKEN);
            if (StringUtils.isNullOrEmpty(token)) {
                throw new ParamInvalidException("token不能为空");
            }
            String unitId;
            if (StringUtils.isNullOrEmpty(request.getHeader(UserConstant.HEADER_PARAM_UNITID))) {
                unitId = "0";
            } else {
                unitId = request.getHeader(UserConstant.HEADER_PARAM_UNITID);
            }
            if (Optional.ofNullable(service.getLoginUserInfoByToken(token)).map(item ->
                    Try.of(() -> {
                        ContextHelper.initContext(token, item.getUserName(), unitId);
                        return true;
                    }).getOrElse(false)
            ).orElse(false)) {
                filterChain.doFilter(request, response);
            } else {
                throw new ServiceException(ExceptionNumber.ERR_USER_NOTLOGIN, "token无效");
            }
        } catch (ServiceException e) {
            log.error("信息异常", e);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            RestfulJsonHelper jsonHelper = RestfulJsonHelper.BaseOn(RestfulJsonHelper.STATUS_CODE.Unauthorized);
            jsonHelper.addMsg(e.getMessage());
            response.getWriter().append(jsonHelper.toJson()).flush();
        }
    }
}
