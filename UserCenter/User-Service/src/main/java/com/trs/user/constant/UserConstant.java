package com.trs.user.constant;

import com.trs.user.VO.UserVO;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户相关常量
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-15 9:57
 * @version 1.0
 * @since 1.0
 */
public class UserConstant {
    /**
     * 通过判断该值是否存在确定用户是否登录（存入session或redis中的键）
     */
    public final static String SESSION_LOGIN_FLAG = "SESSION_LOGIN_FLAG_TOKEN";

    /**
     * header中的参数：token
     */
    public final static String HEADER_PARAM_TOKEN = "token";
    /**
     * header中的参数：unitId
     */
    public final static String HEADER_PARAM_UNITID = "unitId";
    /**
     * header中的参数：username
     */
    public final static String HEADER_PARAM_USERNAME = "username";

    public final static UserVO SYSTEM_USER = new UserVO();

    static {
        SYSTEM_USER.setTrueName("系统内置用户");
        SYSTEM_USER.setUserName("system");
    }
}
