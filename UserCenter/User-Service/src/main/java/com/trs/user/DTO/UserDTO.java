package com.trs.user.DTO;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 用户请求
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-14 14:46
 * @version 1.0
 * @since 1.0
 */
@Data
public class UserDTO extends BaseDTO {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 真实姓名
     */
    private String trueName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String phone;

    /**
     * 用户数据源的Key，例如IDS
     */
    private String sourceKey;

    /**
     * 用户数据源的用户ID
     */
    private String sourceUserId;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 头像
     */
    private String avatar;


    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (StringUtils.isNullOrEmpty(userName) && StringUtils.isNullOrEmpty(sourceUserId)) {
            throw new ParamInvalidException("用户数据源的用户ID和用户名不能同时为空！");
        }
        return true;
    }

    public static UserDTO of(String userName) throws ServiceException {
        UserDTO dto = new UserDTO();
        dto.setUserName(userName);
        BaseUtils.checkDTO(dto);
        return dto;
    }

    public static UserDTO of(String sourceKey, String sourceUserId) throws ServiceException {
        UserDTO dto = new UserDTO();
        dto.setSourceKey(sourceKey);
        dto.setSourceUserId(sourceUserId);
        BaseUtils.checkDTO(dto);
        return dto;
    }
}
