package com.trs.user.VO;

import com.trs.gov.core.util.BaseUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 登录信息
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-14 13:56
 * @version 1.0
 * @since 1.0
 */
@Data
@Slf4j
public class LoginInfoVo extends UserVO {
    /**
     * 用户Token
     */
    private String token;

    /**
     * 是否是管理员
     */
    private boolean IsAdmin = false;

    public static Optional<LoginInfoVo> convertUserVOToMe(String token, Optional<UserVO> vo) {
        if (vo.isPresent()) {
            LoginInfoVo loginInfoVo = new LoginInfoVo();
            BaseUtils.copyProperties(vo.get(), loginInfoVo);
            loginInfoVo.token = token;
            return Optional.ofNullable(loginInfoVo);
        }
        return Optional.empty();
    }
}
