package com.trs.user.service;

import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.user.DTO.GroupUserSearchDTO;
import com.trs.user.VO.GroupVO;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

public interface IGroupUserService extends IKey {
    /**
     * 查询组织中的所有用户<BR>
     *
     * @param dto 查询参数
     * @return 用户列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-28 10:58
     */
    public RestfulResults<List<UserVO>> getAllUserInGroup(GroupUserSearchDTO dto) throws ServiceException;

    /**
     * 查询用户所属的组织<BR>
     *
     * @param dto 查询参数
     * @return 组织列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-28 10:58
     */
    public RestfulResults<List<GroupVO>> getAllGroupInUser(GroupUserSearchDTO dto) throws ServiceException;
}
