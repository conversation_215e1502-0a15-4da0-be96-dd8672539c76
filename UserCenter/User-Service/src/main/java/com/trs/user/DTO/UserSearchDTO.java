package com.trs.user.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import lombok.Data;

@Data
public class UserSearchDTO extends BasePageDTO {

    private Integer status;
    private String userName;
    private String trueName;
    private String  keyWords;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }

    public static UserSearchDTO of(String userName) throws ServiceException {
        return of(userName, null);
    }

    public static UserSearchDTO of(String userName, String trueName) throws ServiceException {
        return of(userName, trueName, null);
    }

    public static UserSearchDTO of(String userName, String trueName, Integer status) throws ServiceException {
        UserSearchDTO dto = new UserSearchDTO();
        dto.setUserName(userName);
        dto.setTrueName(trueName);
        dto.setStatus(status);
        BaseUtils.checkDTO(dto);
        return dto;
    }
}
