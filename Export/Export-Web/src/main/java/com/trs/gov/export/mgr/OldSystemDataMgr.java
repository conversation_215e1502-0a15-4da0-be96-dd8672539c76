package com.trs.gov.export.mgr;

import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.GradeExportDTO;
import com.trs.gov.interaction.DTO.OprRecordExportDTO;
import com.trs.gov.management.DTO.SiteRelationExportDTO;
import com.trs.gov.management.DTO.UnitExportDTO;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.DTO.WorkTimeExportDTO;
import io.vavr.Tuple2;

import java.util.List;

public interface OldSystemDataMgr extends IKey {
    /**
     * 获取旧系统中待迁移的单位<BR>
     *
     * @return <数据表名,单位列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-25 14:43
     */
    public Tuple2<String, List<UnitExportDTO>> getUnitList() throws ServiceException;

    /**
     * 获取旧系统中待迁移的站点<BR>
     *
     * @return <数据表名,站点列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-26 15:28
     */
    public Tuple2<String, List<SiteRelationExportDTO>> getSiteList() throws ServiceException;

    /**
     * 获取旧系统中待迁移的工单数据<BR>
     *
     * @return <数据表名,工单列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-27 18:30
     */
    public Tuple2<String, List<WorkOrderExportDTO>> getWorkOrderList(Long size) throws ServiceException;

    /**
     * 获取旧系统中待迁移的工时数据<BR>
     *
     * @return <数据表名,工时列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-27 18:30
     */
    public Tuple2<String, List<WorkTimeExportDTO>> getWorkTimeList(Long size) throws ServiceException;

    /**
     * 获取旧系统中待迁移的操作记录数据<BR>
     *
     * @param size 数量
     * @return <数据表名,操作记录列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-12-02 10:39
     */
    public Tuple2<String, List<OprRecordExportDTO>> getRecordList(Long size) throws ServiceException;

    /**
     * 获取旧系统中待迁移的评分数据<BR>
     *
     * @param size 数量
     * @return <数据表名,评分列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-12-02 10:39
     */
    public Tuple2<String, List<GradeExportDTO>> getScoreList(Long size) throws ServiceException;

    public void updateUnitCode() throws ServiceException;

}
