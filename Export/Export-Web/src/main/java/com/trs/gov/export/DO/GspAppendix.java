package com.trs.gov.export.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

@Table(name = "gsp_appendix")
@Data
public class GspAppendix implements java.io.Serializable {
    @TableId
    @TableField("appendixid")
    private String appendixId;

    @TableField("filename")
    private String fileName;

    @TableField("fileext")
    private String fileExt;//附件类型

    @TableField("srcfile")
    private String srcFile;//原始名称包含后缀

    @TableField("filesize")
    private Long fileSize;//附件大小

    @TableField("size")
    private String size;//附件大小，中文格式

    @TableField("filepath")
    private String filePath;//附件地址

    @TableField("appendixtype")
    private Integer appendixType;//附件类别

    @TableField("crtime")
    private Date crtime;
}
