package com.trs.gov.export.DO;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

@Data
@Table(name = "workOrderflow")
public class WorkOrderFlowDO implements java.io.Serializable {
    @Id
    @Column(name = "id", nullable = false, unique = true)
    private String id;

    /**
     * 工单ID
     */
    @Column(name = "workerId", length = 100)
    private String workerId;

    /**
     * 操作人-用户ID
     */
    @Column(name = "operUserId", length = 100)
    private String operUserId;

    /**
     * 操作人-用户名
     */
    @Column(name = "operUser", length = 100)
    private String operUser;

    /**
     * 操作人-用户真实名字
     */
    @Column(name = "operUserName", length = 100)
    private String operUserName;

    /**
     * 操作人-所属运维商ID
     */
    @Column(name = "operDeptId", length = 100)
    private String operDeptId;

    /**
     * 操作人-所属运维商名字
     */
    @Column(name = "operDeptName", length = 100)
    private String operDeptName;

    /**
     * 操作人-操作时间
     */
    @Column(name = "operDate", length = 100)
    private Timestamp operDate;

    /**
     * 操作人-操作类型
     */
    @Column(name = "operType", length = 2)
    private String operType;

    /**
     * 操作人-操作类型名称
     */
    @Column(name = "operTypeName", length = 50)
    private String operTypeName;

    /**
     * 操作人-处理意见
     */
    @Column(name = "content", length = 500)
    private String content;
    /**
     * 操作人-补充信息
     */
    @Column(name = "otherInfo", length = 500)
    private String otherInfo;
    /**
     * 操作人-处理意见
     */
    @Column(name = "htmlContent", length = 500)
    private String htmlContent;

    /**
     * 操作人-预计完成时间
     */
    @Column(name = "predictDate", length = 20)
    private String predictDate;

    /**
     * 操作人-实际完成时间
     */
    @Column(name = "finishDate", length = 20)
    private String finishDate;

    /**
     * 操作人-工时
     */
    @Column(name = "manhours", precision = 5, scale = 1, columnDefinition = "double default 0.0")
    private double manhours;

    /**
     * 操作人-服务态度得分
     */
    @Column(name = "serviceAttitude", length = 4)
    private String serviceAttitude;

    /**
     * 操作人-完成时间得分
     */
    @Column(name = "efficiency", length = 4)
    private String efficiency;

    /**
     * 操作人-建设内容得分
     */
    @Column(name = "buildingContent", length = 4)
    private String buildingContent;

    /**
     * 附件名称
     */
    @Column(name = "appendixName")
    private String appendixName;

    /**
     * 附件名称id
     */
    @Column(name = "appendixId")
    private String appendixId;

    @Column(name = "sid")
    private String sid;
}
