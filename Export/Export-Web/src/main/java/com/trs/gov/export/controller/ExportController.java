package com.trs.gov.export.controller;

import com.trs.common.base.Reports;
import com.trs.gov.export.service.ExportService;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("/export")
@Slf4j
public class ExportController {

    @Autowired
    private ExportService service;

    @ResponseBody
    @RequestMapping("import_unit")
    public RestfulResults<Reports> importUnit() {
        return Try.of(() -> RestfulResults.ok(service.importUnit()).addMsg("工单迁移情况如下")).getOrElseGet(err -> {
            log.error("数据迁移异常！", err);
            return RestfulResults.error(err.getMessage());
        });
    }

    @ResponseBody
    @RequestMapping("import_site")
    public RestfulResults<Reports> importSite() {
        return Try.of(() -> RestfulResults.ok(service.importSite()).addMsg("站点关系迁移情况如下")).getOrElseGet(err -> {
            log.error("数据迁移异常！", err);
            return RestfulResults.error(err.getMessage());
        });
    }

    @ResponseBody
    @RequestMapping("import_workorder")
    public RestfulResults<Reports> importWorkOrder(Long size) {
        return Try.of(() -> RestfulResults.ok(service.importWorkOrder(Optional.ofNullable(size).orElse(100L))).addMsg("工单数据迁移情况如下")).getOrElseGet(err -> {
            log.error("数据迁移异常！", err);
            return RestfulResults.error(err.getMessage());
        });
    }

    @ResponseBody
    @RequestMapping("import_worktime")
    public RestfulResults<Reports> importWorkTime(Long size) {
        return Try.of(() -> RestfulResults.ok(service.importWorkTime(Optional.ofNullable(size).orElse(100L))).addMsg("工时数据迁移情况如下")).getOrElseGet(err -> {
            log.error("数据迁移异常！", err);
            return RestfulResults.error(err.getMessage());
        });
    }

    @ResponseBody
    @RequestMapping("import_record")
    public RestfulResults<Reports> importRecord(Long size) {
        return Try.of(() -> RestfulResults.ok(service.importRecord(Optional.ofNullable(size).orElse(100L))).addMsg("操作记录数据迁移情况如下")).getOrElseGet(err -> {
            log.error("数据迁移异常！", err);
            return RestfulResults.error(err.getMessage());
        });
    }

    @ResponseBody
    @RequestMapping("import_score")
    public RestfulResults<Reports> importScore(Long size) {
        return Try.of(() -> RestfulResults.ok(service.importScore(Optional.ofNullable(size).orElse(100L))).addMsg("评分数据迁移情况如下")).getOrElseGet(err -> {
            log.error("数据迁移异常！", err);
            return RestfulResults.error(err.getMessage());
        });
    }
}
