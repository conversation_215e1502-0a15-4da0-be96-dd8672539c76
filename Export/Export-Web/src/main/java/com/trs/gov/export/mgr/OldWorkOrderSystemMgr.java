package com.trs.gov.export.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.export.DO.GspAppendix;
import com.trs.gov.export.DO.MappingDO;
import com.trs.gov.export.mapper.MappingMapper;
import com.trs.gov.export.mapper.OldWorkOrderSystemMapper;
import com.trs.gov.file.DTO.FileExportDTO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.constant.FileConstant;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.interaction.DTO.GradeExportDTO;
import com.trs.gov.interaction.DTO.OprRecordExportDTO;
import com.trs.gov.management.DTO.SiteRelationExportDTO;
import com.trs.gov.management.DTO.UnitExportDTO;
import com.trs.gov.management.base.constant.UnitConstant;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.DTO.WorkTimeExportDTO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OldWorkOrderSystemMgr implements OldSystemDataMgr {

    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileManagerService;

    @Value("${export.downloadUrlPrefix:http://10.11.2.192/appWebSite/yws/appendix.do?method=download&appendixId=}")
    private String downloadUrlPrefix;

    @Value("${export.downloadPathPrefix:/TRS/DATA/WORKORDER/temp/}")
    private String downloadPathPrefix;

    @Autowired
    private OldWorkOrderSystemMapper oldWorkOrderSystemMapper;

    @Autowired
    private MappingMapper mappingMapper;

    private String getUnitType(String type) {
        if (CMyString.isEmpty(type)) {
            return UnitConstant.PARAM_FACTORY_ID;
        }
        switch (type) {
            // 老工单的主办单位
            case "1":
                return UnitConstant.PARAM_HOSTUNIT_ID;
            // 老工单的运维单位
            case "2":
                return UnitConstant.PARAM_OPERATION_ID;
            // 老工单的主管单位
            case "3":
                return UnitConstant.PARAM_MASTERUNIT_ID;
            // 老工单的平台厂商
            case "4":
            default:
                return UnitConstant.PARAM_FACTORY_ID;
        }
    }

    private FileVO convertFileIdToVO(String fileId) throws ServiceException {
        FileExportDTO dto = new FileExportDTO();
        Optional<GspAppendix> opt = Optional.ofNullable(oldWorkOrderSystemMapper.findFileById(fileId));
        if (opt.isPresent()) {

            String localPath = downloadPathPrefix + fileId + "." + opt.get().getFileExt();
            if (new File(localPath).exists()) {
                dto.setPathFlag(FileConstant.PATH_LOCAL);
                dto.setFilePath(localPath);
            } else {
                dto.setPathFlag(FileConstant.PATH_HTTP);
                dto.setFilePath(downloadUrlPrefix + fileId);
            }
            dto.setFiledesc(opt.get().getSrcFile());
            dto.setFileext(opt.get().getFileExt());
            dto.setFilesize(opt.get().getFileSize());
            return fileManagerService.uploadFile(dto);
        } else {
            return null;
        }
    }


    Map<String, String> idMappingMap = new HashMap<>(100);

    private Integer getRealWorkOrderStatus(String status) throws ServiceException {
        return Try.of(() -> {
            if (CMyString.isEmpty(status)) {
                throw new ParamInvalidException("状态不能为空！");
            }
            switch (Integer.valueOf(status)) {
                case 0:
                case 1:
                    return WorkOrderConstant.STATUS_WAIT_ASSIGN;
                case 2:
                    return WorkOrderConstant.STATUS_DEALING;
                case 3:
                    return WorkOrderConstant.STATUS_FINISHED;
                case 4:
                    return WorkOrderConstant.STATUS_REVIEWED;
                default:
                    throw new ParamInvalidException("未知状态status[" + status + "]！");
            }
        }).getOrElseThrow(err -> new ServiceException("status[" + status + "]不存在对应的状态", err));
    }

    public String getUnitKeyMappingIds(String groupKey) throws ServiceException {
        if (CMyString.isEmpty(groupKey)) {
            throw new ParamInvalidException("组织key不能为空！");
        }
        if ("EveryOne".equalsIgnoreCase(groupKey)) {
            throw new ParamInvalidException("根组织不能用！");
        }
        return Try.of(() -> {
            String key = key() + "-ids-" + groupKey;
            String newId;
            if (idMappingMap.containsKey(key)) {
                newId = idMappingMap.get(key);
            } else {
                newId = oldWorkOrderSystemMapper.findNewId(groupKey, "ids", key());
            }
            if (CMyString.isEmpty(newId)) {
                newId = groupKey;
            }
            idMappingMap.put(key, newId);
            return newId;
        }).getOrElseThrow(err -> new ServiceException("groupKey[" + groupKey + "]不存在对应的单位数据", err));
    }

    private Long getRealUnitId(String unitId) throws ServiceException {
        if (CMyString.isEmpty(unitId)) {
            throw new ParamInvalidException("单位ID不能为空！");
        }
        return Try.of(() -> {
            String key = key() + "-manager_dept-" + unitId;
            String newId;
            if (idMappingMap.containsKey(key)) {
                newId = idMappingMap.get(key);
            } else {
                newId = oldWorkOrderSystemMapper.findNewId(getUnitKeyMappingIds(unitId), "manager_dept", key());
            }
            if (CMyString.isEmpty(newId)) {
                throw new Exception("查询不到ID");
            }
            idMappingMap.put(key, newId);
            return Long.valueOf(newId);
        }).getOrElseThrow(err -> new ServiceException("unitId[" + unitId + "]不存在对应的新单位数据", err));
    }

    private Long getRealWorkOrderId(String workOrderId) throws ServiceException {
        if (CMyString.isEmpty(workOrderId)) {
            throw new ParamInvalidException("工单ID不能为空！");
        }
        return Try.of(() -> {
            String key = key() + "-workorderinfos-" + workOrderId;
            String newId;
            if (idMappingMap.containsKey(key)) {
                newId = idMappingMap.get(key);
            } else {
                newId = oldWorkOrderSystemMapper.findNewId(workOrderId, "workorderinfos", key());
            }
            if (CMyString.isEmpty(newId)) {
                throw new Exception("查询不到ID");
            }
            idMappingMap.put(key, newId);
            return Long.valueOf(newId);
        }).getOrElseThrow(err -> new ServiceException("workOrderId[" + workOrderId + "]不存在对应的新工单数据", err));
    }

    private String getMediaTypeName(String mediaType) throws ServiceException {
        if (CMyString.isEmpty(mediaType)) {
            throw new ParamInvalidException("站点类型不能为空！");
        }
        switch (mediaType) {
            case "1":
                return "网站";
            case "2":
                return "APP";
            case "3":
                return "微信";
            case "4":
                return "微博";
            default:
                throw new ParamInvalidException("站点类型不存在！");
        }
    }


    /**
     * 获取旧系统中待迁移的单位<BR>
     *
     * @return <数据表名,单位列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-25 14:43
     */
    @Override
    public Tuple2<String, List<UnitExportDTO>> getUnitList() throws ServiceException {
        return new Tuple2<>("manager_dept", oldWorkOrderSystemMapper.queryUnitList().stream().map(vo -> {
            UnitExportDTO dto = new UnitExportDTO();
            dto.setOldUnitId(vo.getId());
            dto.setUnitType(getUnitType(vo.getDepttype()));
            dto.setUnitName(vo.getDeptname());
            String unitMaster = vo.getUnitMaster();
            String nickName = vo.getNickname();
            if (CMyString.isEmpty(unitMaster)) {
                unitMaster = "工单默认负责人";
                nickName = "工单默认负责人";
            }
            dto.setUnitMaster(unitMaster);
            dto.setUnitMasterTrueName(nickName);
            dto.setPhone(vo.getPhone());
            dto.setEmail(java.util.UUID.randomUUID().toString() + "@export.trs.com");
            dto.setUnitAddr(vo.getCompanyaddres());
            dto.setUnitDesc(vo.getCompanydesc());
            dto.setUnitCode(vo.getUnitcode());
            String logo = vo.getCompanyimg();
            if (!CMyString.isEmpty(logo)) {
                try {
                    dto.setLogo(convertFileIdToVO(logo));
                } catch (Exception e) {
                    log.error("图片{}迁移失败", logo, e);
                }
            }
            dto.setCrTime(vo.getCreatetime());
            return dto;
        }).filter(item -> item != null).collect(Collectors.toList()));
    }

    /**
     * 获取旧系统中待迁移的站点<BR>
     *
     * @return <数据表名,站点列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-26 15:28
     */
    @Override
    public Tuple2<String, List<SiteRelationExportDTO>> getSiteList() throws ServiceException {
        return new Tuple2<>("sitedevops", oldWorkOrderSystemMapper.getSiteList().stream().map(vo -> Try.of(() -> {
            SiteRelationExportDTO dto = new SiteRelationExportDTO();
            dto.setOldSiteRelationId(vo.getSid());
            dto.setSiteId(Long.valueOf(vo.getSiteId()));
            dto.setSiteName(vo.getSiteName());
            dto.setMediaType(Integer.valueOf(vo.getMediaType()));
            dto.setMediaName(getMediaTypeName(vo.getMediaType()));
            dto.setUniqueId(vo.getIdCode());
            dto.setFilingTime(vo.getFilingTime());
            dto.setConstructionTime(vo.getCreateDate());
            dto.setHostUnitId(vo.getHostUnitId());
            dto.setHostUnit(vo.getResUnit());
            dto.setMasterUnitId(vo.getMasterUnitId());
            dto.setMasterUnit(vo.getComUnit());
            dto.setOperationUnitId(vo.getOperationUnitId());
            dto.setOperationUnit(vo.getOpsUnit());
            dto.setOperationHost(vo.getOpsunitusername());
            dto.setPhone(vo.getPhone());
            dto.setOperationStartTime(vo.getAccessTime());
            dto.setOperationEndTime(TimeUtils.befOrAft(new Date(), 1, Calendar.YEAR));
            if (!CMyString.isEmpty(vo.getWmcsSiteId()) && !CMyString.isEmpty(vo.getWmcsSiteName())) {
                dto.setMonitorSiteId(Long.valueOf(vo.getWmcsSiteId()));
                dto.setMonitorSite(vo.getWmcsSiteName());
            }
            dto.setCrTime(vo.getCreateDate());
            dto.setCrUser(vo.getCreateUser());
            return dto;
        }).onFailure(err -> log.error("数据[{}]转换失败！", vo, err)).getOrNull()).filter(item -> item != null).collect(Collectors.toList()));
    }

    /**
     * 获取旧系统中待迁移的工单数据<BR>
     *
     * @return <数据表名,工单列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-11-27 18:30
     */
    @Override
    public Tuple2<String, List<WorkOrderExportDTO>> getWorkOrderList(Long size) throws ServiceException {
        return new Tuple2<>("workorderinfos", oldWorkOrderSystemMapper.getWorkOrderList(size).stream().map(vo -> Try.of(() -> {
            WorkOrderExportDTO dto = new WorkOrderExportDTO();
            dto.setOldWorkOrderId(vo.getId());
            dto.setWorkOrderTopTypeId(1L);
            dto.setWorkOrderTopTypeName("工单");
            dto.setWorkOrderTypeId(vo.getWorkordertypeid());
            dto.setWorkOrderTypeName(vo.getTitletype2name());
            dto.setContent(vo.getRequiredesc());
            dto.setPriority("一般");
            if (!CMyString.isEmpty(vo.getExpfinishdate())) {
                dto.setExpectedEndDate(TimeUtils.stringToDate(vo.getExpfinishdate(), TimeUtils.YYYYMMDD));
            }
            dto.setFinishTime(vo.getFinishtime());

            // 发起单位信息
            dto.setCrUsername(vo.getCrusername());
            dto.setCrTruename(CMyString.showEmpty(vo.getUsername(), vo.getCrusername()));
            dto.setCrUnitId(getRealUnitId(vo.getSid()));
            dto.setCrUnit(vo.getSdeptname());

            // 主办单位信息(为空时使用发起单位作为主办单位)
            dto.setHostUnitId(getRealUnitId(CMyString.showEmpty(vo.getZbunitid(), vo.getSid())));
            dto.setHostUnit(CMyString.showEmpty(vo.getZbunitname(), vo.getSdeptname()));
            dto.setHostAssignType(2);

            // 受理单位信息
            dto.setDealUnitId(getRealUnitId(vo.getDeptid()));
            dto.setDealUnit(vo.getDept());
            dto.setDealUsername(vo.getDealusername());
            dto.setDealTruename(CMyString.showEmpty(vo.getDealtruename(), vo.getDealusername()));
            if (CMyString.isEmpty(vo.getDealusername())) {
                dto.setDealAssignType(2);
            } else {
                dto.setDealAssignType(1);
            }

            dto.setCrTime(vo.getCreateTime());
            dto.setActionTime(vo.getCreateTime());
            dto.setStatus(getRealWorkOrderStatus(vo.getWorkorderstate()));

            // 最后下载文件
            List<FileVO> filelist = new ArrayList<>();
            if (!CMyString.isEmpty(vo.getAttachuuiddoc())) {
                for (String s : vo.getAttachuuiddoc().split(",")) {
                    filelist.add(convertFileIdToVO(s));
                }
            }
            if (!CMyString.isEmpty(vo.getAttachuuidtb())) {
                for (String s : vo.getAttachuuidtb().split(",")) {
                    filelist.add(convertFileIdToVO(s));
                }
            }
            if (!CMyString.isEmpty(vo.getAttachuuidpic())) {
                for (String s : vo.getAttachuuidpic().split(",")) {
                    filelist.add(convertFileIdToVO(s));
                }
            }
            dto.setFilelist(filelist);
            return dto;
        }).onFailure(err -> log.error("数据[{}]转换失败！", vo, err)).getOrNull()).filter(item -> item != null).collect(Collectors.toList()));
    }

    @Override
    public Tuple2<String, List<WorkTimeExportDTO>> getWorkTimeList(Long size) throws ServiceException {
        return new Tuple2<>("workorderinfos-time", oldWorkOrderSystemMapper.getWorkTimeList(size).stream().map(vo -> Try.of(() -> {
            WorkTimeExportDTO dto = new WorkTimeExportDTO();
            dto.setOldWorkTimeId(vo.getId());
            dto.setCrTime(vo.getFinishtime());
            dto.setCrUnitId(getRealUnitId(vo.getDeptid()));
            dto.setCrUsername(CMyString.showEmpty(vo.getCrusername(), vo.getDealusername()));
            dto.setWorkOrderId(getRealWorkOrderId(vo.getId()));
            dto.setWorkDesc("处理工单");
            dto.setWorkUnitId(getRealUnitId(vo.getDeptid()));
            dto.setWorkUnit(vo.getDept());
            dto.setWorkingTime(Double.valueOf(vo.getManhours()));
            return dto;
        }).onFailure(err -> log.error("数据[{}]转换失败！", vo, err)).getOrNull()).filter(item -> item != null).collect(Collectors.toList()));
    }

    /**
     * 获取旧系统中待迁移的操作记录数据<BR>
     *
     * @param size 数量
     * @return <数据表名,操作记录列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-12-02 10:39
     */
    @Override
    public Tuple2<String, List<OprRecordExportDTO>> getRecordList(Long size) throws ServiceException {
        return new Tuple2<>("workorderflow", oldWorkOrderSystemMapper.getRecordList(size).stream().map(vo -> Try.of(() -> {
            OprRecordExportDTO dto = new OprRecordExportDTO();
            dto.setCrUser(vo.getOperUser());
            Try<Long> t = Try.of(() -> getRealUnitId(vo.getOperDeptId()));
            if (t.isSuccess()) {
                dto.setCrUnitId(t.get());
            } else {
                dto.setCrUnitId(getRealUnitId(vo.getSid()));
            }
            dto.setCrTime(vo.getOperDate());
            dto.setOldRecordId(vo.getId());
            dto.setWorkOrderId(getRealWorkOrderId(vo.getWorkerId()));
            dto.setOprKey(OperateNameConstant.REPLY_WORK_ORDER);
            dto.setOption(CMyString.showEmpty(vo.getContent(), "工单迁移默认填充内容"));
            if (!CMyString.isEmpty(vo.getAppendixId())) {
                List<FileVO> fileList = new ArrayList<>();
                fileList.add(convertFileIdToVO(vo.getAppendixId()));
                dto.setFileList(fileList);
            }
            return dto;
        }).onFailure(err -> log.error("数据[{}]转换失败！", vo, err)).getOrNull()).filter(item -> item != null).collect(Collectors.toList()));
    }

    /**
     * 获取旧系统中待迁移的评分数据<BR>
     *
     * @param size 数量
     * @return <数据表名,评分列表>
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-12-02 10:39
     */
    @Override
    public Tuple2<String, List<GradeExportDTO>> getScoreList(Long size) throws ServiceException {
        return new Tuple2<>("workorderinfos-score", oldWorkOrderSystemMapper.getScoreList(size).stream().map(vo -> Try.of(() -> {
            GradeExportDTO dto = new GradeExportDTO();
            dto.setWorkOrderId(getRealWorkOrderId(vo.getWorkerId()));
            dto.setOldGradeId(vo.getId());
            dto.setGradeUnit(vo.getDept());
            dto.setGradeUnitId(getRealUnitId(vo.getDeptid()));
            dto.setContent(CMyString.showEmpty(vo.getContent(), "工单迁移评分默认填充内容"));
            /**
             * 操作人-服务态度得分
             */
            BigDecimal attitudeGrade = new BigDecimal(Optional.ofNullable(vo.getServiceAttitude())
                    .orElseThrow(() -> new ParamInvalidException("服务态度得分不能为空！")))
                    .divide(new BigDecimal(2), 2, BigDecimal.ROUND_HALF_UP);
            /**
             * 完成时间得分
             */
            BigDecimal completeTimeGrade = new BigDecimal(Optional.ofNullable(vo.getEfficiency())
                    .orElseThrow(() -> new ParamInvalidException("完成时间得分不能为空！")))
                    .divide(new BigDecimal(2), 2, BigDecimal.ROUND_HALF_UP);
            /**
             * 完成内容得分
             */
            BigDecimal completeContentGrade = new BigDecimal(Optional.ofNullable(vo.getBuildingContent())
                    .orElseThrow(() -> new ParamInvalidException("完成内容得分不能为空！")))
                    .divide(new BigDecimal(2), 2, BigDecimal.ROUND_HALF_UP);
            dto.setAttitude(attitudeGrade.doubleValue());
            dto.setCompleteTime(completeTimeGrade.doubleValue());
            dto.setCompleteContent(completeContentGrade.doubleValue());
            dto.setCrUser(vo.getOperUser());
            Try<Long> t = Try.of(() -> getRealUnitId(vo.getOperDeptId()));
            if (t.isSuccess()) {
                dto.setCrUnitId(t.get());
            } else {
                dto.setCrUnitId(getRealUnitId(vo.getSid()));
            }
            dto.setCrTime(vo.getOperDate());
            return dto;
        }).onFailure(err -> log.error("数据[{}]转换失败！", vo, err)).getOrNull()).filter(item -> item != null).collect(Collectors.toList()));
    }

    @Override
    public void updateUnitCode() throws ServiceException {
        StringBuilder stringBuilder = new StringBuilder();
        String sql = "update unit set unit_code=%s where id=%s;";
        mappingMapper.selectList(new QueryWrapper<MappingDO>()
                .eq("new_table", "manager_dept")
                .eq("old_table", "ids")
                .eq("system_key", key())
        ).forEach(vo -> Optional
                .ofNullable(Try.of(() -> getRealUnitId(vo.getNewId())).getOrNull())
                .ifPresent(item -> stringBuilder
                        .append(String.format(sql, vo.getOldId(), item)).append("\n")
                )
        );
        System.out.println(stringBuilder.toString());
    }

    /**
     * 相关Key<BR>
     *
     * @return 类的Key
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String key() {
        return "appjlrb";
    }

    /**
     * 相关描述<BR>
     *
     * @return 类功能的描述
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 10:38
     */
    @Override
    public String desc() {
        return "贵州旧工单系统";
    }
}
