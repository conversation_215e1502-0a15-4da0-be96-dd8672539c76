package com.trs.gov.export.service;

import com.trs.common.base.Reports;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.export.DTO.MappingDTO;
import com.trs.gov.export.mgr.ExportMgr;
import com.trs.gov.export.mgr.OldWorkOrderSystemMgr;
import com.trs.gov.interaction.DTO.GradeExportDTO;
import com.trs.gov.interaction.DTO.OprRecordExportDTO;
import com.trs.gov.interaction.service.GradeService;
import com.trs.gov.interaction.service.OprRecordService;
import com.trs.gov.management.DTO.SiteRelationExportDTO;
import com.trs.gov.management.DTO.UnitExportDTO;
import com.trs.gov.management.service.SiteRelationService;
import com.trs.gov.management.service.UnitService;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.DTO.WorkTimeExportDTO;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.gov.workorder.service.IWorkTimeService;
import com.trs.user.DTO.UserDTO;
import com.trs.user.service.IUserService;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class ExportService {

    @Autowired
    private ExportMgr mgr;

    @Autowired
    private OldWorkOrderSystemMgr oldSystemDataMgr;

    @Reference(check = false, timeout = 60000)
    private UnitService unitService;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Reference(check = false, timeout = 60000)
    private SiteRelationService siteRelationService;

    @Reference(check = false, timeout = 60000)
    private IWorkOrderService workOrderService;

    @Reference(check = false, timeout = 60000)
    private IWorkTimeService workTimeService;

    @Reference(check = false, timeout = 60000)
    private OprRecordService oprRecordService;

    @Reference(check = false, timeout = 60000)
    private GradeService gradeService;

    private static final String EXPORT_SESSIONID = "export_sessionId_20201126";

    private void initUser() throws ServiceException {
        userService.saveLoginInfo(EXPORT_SESSIONID, EXPORT_SESSIONID, UserDTO.of("system"));
        ContextHelper.initContext(EXPORT_SESSIONID, "system");
    }

    public Reports importUnit() throws ServiceException {
        initUser();
        Tuple2<String, List<UnitExportDTO>> oldData = oldSystemDataMgr.getUnitList();
        if (CMyString.isEmpty(oldData._1())) {
            throw new ParamInvalidException("数据表不能为空！");
        }
        Reports reports = Reports.createNewReports("工单迁移情况");
        Optional.ofNullable(oldData._2()).ifPresent(datas -> datas.forEach(item -> {
            BaseUtils.setUserInfoToDTO(item);
            Try.of(() -> unitService.exportUnit(item))
                    .onFailure(err -> {
                        log.error("迁移ID={}的单位出现了异常！", item.getOldUnitId(), err);
                        reports.error(String.format("迁移ID=%s的单位出现了异常！ERR=%s", item.getOldUnitId(), err.getMessage()));
                    })
                    .onSuccess(vo -> {
                        try {
                            mgr.saveMapping(MappingDTO.of(Long.toString(vo.getId()), item.getOldUnitId(), "unit", oldData._1(), oldSystemDataMgr.key()));
                            String info = String.format("完成OLDID=%s,NEWID=%s的单位迁移！", item.getOldUnitId(), vo.getId());
                            log.info(info);
                            System.out.println(info);
                        } catch (Exception e) {
                            log.error("保存OLDID={},NEWID={}的单位出现了异常！", item.getOldUnitId(), vo.getId(), e);
                            reports.error(String.format("保存OLDID=%s,NEWID=%s的单位出现了异常！ERR=%s", item.getOldUnitId(), vo.getId(), e.getMessage()));
                        }
                    });
        }));
        return reports;
    }

    public Reports importSite() throws ServiceException {
        initUser();
        Tuple2<String, List<SiteRelationExportDTO>> oldData = oldSystemDataMgr.getSiteList();
        if (CMyString.isEmpty(oldData._1())) {
            throw new ParamInvalidException("数据表不能为空！");
        }
        Reports reports = Reports.createNewReports("站点关系迁移情况");
        Optional.ofNullable(oldData._2()).ifPresent(datas -> datas.forEach(item -> {
            BaseUtils.setUserInfoToDTO(item);
            Try.of(() -> siteRelationService.exportSiteRelation(item))
                    .onFailure(err -> {
                        log.error("迁移ID={}的站点关系出现了异常！", item.getOldSiteRelationId(), err);
                        reports.error(String.format("迁移ID=%s的站点关系出现了异常！ERR=%s", item.getOldSiteRelationId(), err.getMessage()));
                    })
                    .onSuccess(vo -> {
                        try {
                            mgr.saveMapping(MappingDTO.of(Long.toString(vo.getId()), item.getOldSiteRelationId(), "site_relation", oldData._1(), oldSystemDataMgr.key()));
                            String info = String.format("完成OLDID=%s,NEWID=%s的站点关系迁移！", item.getOldSiteRelationId(), vo.getId());
                            log.info(info);
                            System.out.println(info);
                        } catch (Exception e) {
                            log.error("保存OLDID={},NEWID={}的站点关系出现了异常！", item.getOldSiteRelationId(), vo.getId(), e);
                            reports.error(String.format("保存OLDID=%s,NEWID=%s的站点关系出现了异常！ERR=%s", item.getOldSiteRelationId(), vo.getId(), e.getMessage()));
                        }
                    });
        }));
        return reports;
    }

    public Reports importWorkOrder(Long size) throws ServiceException {
        initUser();
        Tuple2<String, List<WorkOrderExportDTO>> oldData = oldSystemDataMgr.getWorkOrderList(size);
        if (CMyString.isEmpty(oldData._1())) {
            throw new ParamInvalidException("数据表不能为空！");
        }
        Reports reports = Reports.createNewReports("工单迁移情况");
        Optional.ofNullable(oldData._2()).ifPresent(datas -> datas.forEach(item -> {
            BaseUtils.setUserInfoToDTO(item);
            Try.of(() -> workOrderService.exportWorkOrder(item))
                    .onFailure(err -> {
                        log.error("迁移ID={}的工单出现了异常！", item.getOldWorkOrderId(), err);
                        reports.error(String.format("迁移ID=%s的工单出现了异常！ERR=%s", item.getOldWorkOrderId(), err.getMessage()));
                    })
                    .onSuccess(vo -> {
                        try {
                            mgr.saveMapping(MappingDTO.of(Long.toString(vo.getId()), item.getOldWorkOrderId(), "work_order", oldData._1(), oldSystemDataMgr.key()));
                            String info = String.format("完成OLDID=%s,NEWID=%s的工单迁移！", item.getOldWorkOrderId(), vo.getId());
                            reports.success(info);
                            log.info(info);
                            System.out.println(info);
                        } catch (Exception e) {
                            log.error("保存OLDID={},NEWID={}的工单出现了异常！", item.getOldWorkOrderId(), vo.getId(), e);
                            reports.error(String.format("保存OLDID=%s,NEWID=%s的工单出现了异常！ERR=%s", item.getOldWorkOrderId(), vo.getId(), e.getMessage()));
                        }
                    });
        }));
        return reports;
    }

    public Reports importWorkTime(Long size) throws ServiceException {
        initUser();
        Tuple2<String, List<WorkTimeExportDTO>> oldData = oldSystemDataMgr.getWorkTimeList(size);
        if (CMyString.isEmpty(oldData._1())) {
            throw new ParamInvalidException("数据表不能为空！");
        }
        Reports reports = Reports.createNewReports("工时迁移情况");
        Optional.ofNullable(oldData._2()).ifPresent(datas -> datas.forEach(item -> {
            BaseUtils.setUserInfoToDTO(item);
            Try.of(() -> workTimeService.exportWorkTime(item))
                    .onFailure(err -> {
                        log.error("迁移ID={}的工时出现了异常！", item.getOldWorkTimeId(), err);
                        reports.error(String.format("迁移ID=%s的工时出现了异常！ERR=%s", item.getOldWorkTimeId(), err.getMessage()));
                    })
                    .onSuccess(vo -> {
                        try {
                            mgr.saveMapping(MappingDTO.of(Long.toString(vo.getWorkTimeId()), item.getOldWorkTimeId(), "work_time", oldData._1(), oldSystemDataMgr.key()));
                            String info = String.format("完成OLDID=%s,NEWID=%s的工时迁移！", item.getOldWorkTimeId(), vo.getWorkTimeId());
                            reports.success(info);
                            log.info(info);
                            System.out.println(info);
                        } catch (Exception e) {
                            log.error("保存OLDID={},NEWID={}的工时出现了异常！", item.getOldWorkTimeId(), vo.getWorkTimeId(), e);
                            reports.error(String.format("保存OLDID=%s,NEWID=%s的工时出现了异常！ERR=%s", item.getOldWorkTimeId(), vo.getWorkTimeId(), e.getMessage()));
                        }
                    });
        }));
        return reports;
    }

    public Reports importRecord(Long size) throws ServiceException {
        initUser();
        Tuple2<String, List<OprRecordExportDTO>> oldData = oldSystemDataMgr.getRecordList(size);
        if (CMyString.isEmpty(oldData._1())) {
            throw new ParamInvalidException("数据表不能为空！");
        }
        Reports reports = Reports.createNewReports("操作记录迁移情况");
        Optional.ofNullable(oldData._2()).ifPresent(datas -> datas.forEach(item -> {
            BaseUtils.setUserInfoToDTO(item);
            Try.of(() -> oprRecordService.exportOperRecord(item))
                    .onFailure(err -> {
                        log.error("迁移ID={}的操作记录出现了异常！", item.getOldRecordId(), err);
                        reports.error(String.format("迁移ID=%s的操作记录出现了异常！ERR=%s", item.getOldRecordId(), err.getMessage()));
                    })
                    .onSuccess(vo -> {
                        try {
                            mgr.saveMapping(MappingDTO.of(Long.toString(vo.getId()), item.getOldRecordId(), "opr_record", oldData._1(), oldSystemDataMgr.key()));
                            String info = String.format("完成OLDID=%s,NEWID=%s的操作记录迁移！", item.getOldRecordId(), vo.getId());
                            reports.success(info);
                            log.info(info);
                            System.out.println(info);
                        } catch (Exception e) {
                            log.error("保存OLDID={},NEWID={}的操作记录出现了异常！", item.getOldRecordId(), vo.getId(), e);
                            reports.error(String.format("保存OLDID=%s,NEWID=%s的操作记录出现了异常！ERR=%s", item.getOldRecordId(), vo.getId(), e.getMessage()));
                        }
                    });
        }));
        return reports;
    }

    public Reports importScore(Long size) throws ServiceException {
        initUser();
        Tuple2<String, List<GradeExportDTO>> oldData = oldSystemDataMgr.getScoreList(size);
        if (CMyString.isEmpty(oldData._1())) {
            throw new ParamInvalidException("数据表不能为空！");
        }
        Reports reports = Reports.createNewReports("评分迁移情况");
        Optional.ofNullable(oldData._2()).ifPresent(datas -> datas.forEach(item -> {
            BaseUtils.setUserInfoToDTO(item);
            Try.of(() -> gradeService.exportGrade(item))
                    .onFailure(err -> {
                        log.error("迁移ID={}的评分出现了异常！", item.getOldGradeId(), err);
                        reports.error(String.format("迁移ID=%s的评分出现了异常！ERR=%s", item.getOldGradeId(), err.getMessage()));
                    })
                    .onSuccess(vo -> {
                        try {
                            mgr.saveMapping(MappingDTO.of(Long.toString(vo.getId()), item.getOldGradeId(), "grade", oldData._1(), oldSystemDataMgr.key()));
                            String info = String.format("完成OLDID=%s,NEWID=%s的评分迁移！", item.getOldGradeId(), vo.getId());
                            reports.success(info);
                            log.info(info);
                            System.out.println(info);
                        } catch (Exception e) {
                            log.error("保存OLDID={},NEWID={}的评分出现了异常！", item.getOldGradeId(), vo.getId(), e);
                            reports.error(String.format("保存OLDID=%s,NEWID=%s的评分出现了异常！ERR=%s", item.getOldGradeId(), vo.getId(), e.getMessage()));
                        }
                    });
        }));
        return reports;
    }

}
