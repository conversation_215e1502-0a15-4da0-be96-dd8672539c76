package com.trs.gov;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Slf4j
@MapperScan("com.trs.gov.export")
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.trs")
//@EnableSwagger2
public class ExportApplication {
    public static void main(String[] args) {
        SpringApplication.run(ExportApplication.class, args);
        log.info("Export-Service模块启动成功!");
    }
}
