package com.trs.gov.export.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("workorderinfos")
public class WorkOrderInfoDO implements java.io.Serializable {
    private String id;//主键id
    private String title;//工单标题
    private String titletype1;//顶级分类id
    private String titletype1name;//顶级分类名称
    private String titletype2;//子级分类id
    private String titletype2name;//子级分类名称
    private Long workordertypeid;// 最后一级类型ID
    private String state;//状态（1正常，9删除）
    private String workorderstate;//工单状态
    private String statename;
    private Timestamp createTime;//创建时间
    private String createuserid;//创建用户id
    /**
     * 创建人
     */
    private String crusername;
    private String dept;//部门（运维服务商）
    private String expfinishdate;//预期完成时间
    private String requiredesc;//工单内容
    private String attahchname1;//需求描述文档名
    private String attachuuiddoc;//需求描述文档系统名
    private String attachname2;//需求描述excel名
    private String attachuuidtb;//需求描述excel系统名
    private String attachname3;//素材名
    private String attachuuidpic;//素材系统名
    private String iseval;//未知
    private String score;//未知
    private String workeval;//评价内容
    private Timestamp lastupdatedate;//最后更新时间
    private String publictime;//未知
    private String attitude;//未知
    private Timestamp finishtime;//实际完成时
    private String arcontent;//未知
    private String flowflag;//工单流程标识
    private String roleid;//工单审核审核员角色
    private String phone;
    private String username;
    private String deptname;
    private String approvalid;
    private String chphone;
    private String chusername;
    private String chdeptname;
    private String accepter; // 工单受理人ID
    /**
     * 受理人用户名
     */
    private String dealusername;
    /**
     * 受理人用户名 的 真实姓名
     */
    private String dealtruename;
    private String twoattahchname1;
    private String twoattachuuiddoc;
    private String twoattachname2;
    private String twoattachuuidtb;
    private String twoattachname3;
    private String twoattachuuidpic;
    private String requriehtml;//工单内容html
    private String sid;//发起工单运维商id
    private String sdeptname;//发起工单运维商名称
    private String fromback; //0:服务商 1：审核员
    private String manhours;//服务商提交工单时使用，工单完成工时
    private String rehtmlp;
    private String rep;
    private String deptid;//受理运维商id
    private String createdate;//工单创建日期
    private Integer fqrpublic;//发起方公开
    private Integer slrpublic;//受理方公开
    private String commentid;//评论id，评论转化的工单有该值
    private String zbunitid;//工单主办单位id
    private String zbunitname;//工单主办单位名称
}
