package com.trs.gov.export.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 迁移结果映射的DTO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-11-25 14:19
 * @version 1.0
 * @since 1.0
 */
@Data
public class MappingDTO extends BaseDTO {

    private String newId;

    private String oldId;

    private String newTable;

    private String oldTable;

    private String systemKey;

    public static MappingDTO of(String newId, String oldId, String newTable, String oldTable, String systemKey) throws ServiceException {
        MappingDTO dto = new MappingDTO();
        dto.setNewId(newId);
        dto.setOldId(oldId);
        dto.setNewTable(newTable);
        dto.setOldTable(oldTable);
        dto.setSystemKey(systemKey);
        dto.isValid();
        return dto;
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (CMyString.isEmpty(newId)) {
            throw new ParamInvalidException("新ID不能为空！");
        }
        if (CMyString.isEmpty(oldId)) {
            throw new ParamInvalidException("旧ID不能为空！");
        }
        if (CMyString.isEmpty(newTable)) {
            throw new ParamInvalidException("新表不能为空！");
        }
        if (CMyString.isEmpty(oldTable)) {
            throw new ParamInvalidException("旧表不能为空！");
        }
        if (CMyString.isEmpty(systemKey)) {
            throw new ParamInvalidException("旧系统标识不能为空！");
        }
        return true;
    }
}
