package com.trs.gov.export.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.export.DO.*;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface OldWorkOrderSystemMapper extends BaseMapper<ManagerDeptDO> {

    public List<ManagerDeptDO> queryUnitList();

    public List<SiteDevopsDO> getSiteList();

    public List<WorkOrderInfoDO> getWorkOrderList(Long size);

    public List<WorkOrderInfoDO> getWorkTimeList(Long size);

    public List<WorkOrderFlowDO> getRecordList(Long size);

    public List<ScoreDO> getScoreList(Long size);

    public GspAppendix findFileById(String fileId);

    public String findNewId(String oldId, String oldTable, String systemKey);
}
