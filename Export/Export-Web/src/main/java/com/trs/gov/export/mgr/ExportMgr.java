package com.trs.gov.export.mgr;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.export.DO.MappingDO;
import com.trs.gov.export.DTO.MappingDTO;
import com.trs.gov.export.mapper.MappingMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ExportMgr {

    @Autowired
    private MappingMapper mappingMapper;

    public void saveMapping(MappingDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        MappingDO mappingDO = new MappingDO();
        mappingDO.setNewId(dto.getNewId());
        mappingDO.setNewTable(dto.getNewTable());
        mappingDO.setOldId(dto.getOldId());
        mappingDO.setOldTable(dto.getOldTable());
        mappingDO.setSystemKey(dto.getSystemKey());
        mappingMapper.insert(mappingDO);
    }

}
