package com.trs.gov.export.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

@Data
@TableName("sitedevops")
public class SiteDevopsDO implements java.io.Serializable {

    @TableId("sid")
    private String Sid;

    /**
     * 站点id
     */
    @TableField("siteid")
    private String SiteId;

    /**
     * 站点名称
     */
    @TableField("sitename")
    private String SiteName;

    /**
     * 主办单位
     */
    @TableField("resunit")
    private String ResUnit;

    /**
     * 主办单位ID
     */
    @TableField("resunitid")
    private String ResUnitId;

    @TableField("hostUnitId")
    private Long hostUnitId;
    /**
     * 主办单位人员id
     */
    @TableField("resunituserid")
    private String ResUnitUserId;
    /**
     * 主办单位人员名称
     */
    @TableField("resunitusername")
    private String resunitusername;

    /**
     * 主管单位名称
     */
    @TableField("comunit")
    private String ComUnit;

    /**
     * 主管单位id
     */
    @TableField("comunitid")
    private String ComUnitId;

    @TableField("masterUnitId")
    private Long masterUnitId;

    /**
     * 主管单位人员id
     */
    @TableField("comunituserid")
    private String ComUnitUserId;

    /**
     * 主管单位人员名称
     */
    @TableField("comunitusernm")
    private String comunitusernm;

    /**
     * 运维单位
     */
    @TableField("opsunit")
    private String OpsUnit;

    /**
     * 运维单位人员
     */
    @TableField("opsunituserid")
    private String OpsUnitUserId;
    /**
     * 运维单位人员名称
     */
    @TableField("opsunitusername")
    private String opsunitusername;
    /**
     * 运维单位ID
     */
    @TableField("opsunitid")
    private String OpsUnitId;

    @TableField("operationUnitId")
    private Long operationUnitId;

    /**
     * 备案时间
     */
    @TableField("filingtime")
    private Timestamp FilingTime;

    /**
     * 接入时间
     */
    @TableField("accesstime")
    private Timestamp AccessTime;

    /**
     * 创建时间
     */
    @TableField("createdate")
    private Timestamp createDate;

    /**
     * 修改时间
     */
    @TableField("operatedate")
    private Timestamp operateDate;

    /**
     * 用户名称
     */
    @TableField("createuser")
    private String createUser;

    /**
     * 用户ID
     */
    @TableField("createuserid")
    private String createUserId;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 联系邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 固定电话
     */
    @TableField("tel")
    private String tel;


    /**
     * 监测云站点ID
     */
    @TableField("wmcssiteid")
    private String wmcsSiteId;

    /**
     * 监测云站点名称
     */
    @TableField("wmcssitename")
    private String wmcsSiteName;

    /**
     * 网站首页地址
     */
    @TableField("homepage")
    private String homePage;

    /**
     * 网站状态  1-正常，0-不正常
     */
    @TableField("status")
    private Integer status = 1;

    /**
     * 网站标识码
     */
    @TableField("idcode")
    private String idCode;

    /**
     * 网站类型 1-网站  2-app 3-微信 4-微博
     */
    @TableField("mediatype")
    private String mediaType;

    /**
     * 网站省地域编码
     */
    @TableField("provincecode")
    private String provinceCode;

    /**
     * 网站省地域名称
     */
    @TableField("provincename")
    private String provinceName;


    /**
     * 网站市地域编码
     */
    @TableField("citycode")
    private String cityCode;

    /**
     * 网站市地域名称
     */
    @TableField("cityname")
    private String cityName;

    /**
     * 网站县/区地域编码
     */
    @TableField("areacode")
    private String areaCode;

    /**
     * 网站县/区地域名称
     */
    @TableField("areaname")
    private String areaName;
}
