package com.trs.gov.export.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Data
@TableName("temp_export_mapping_table")
public class MappingDO extends BaseDO {

    @Column(name = "new_id")
    @TableField("new_id")
    private String newId;

    @Column(name = "old_id")
    @TableField("old_id")
    private String oldId;

    @Column(name = "new_table")
    @TableField("new_table")
    private String newTable;

    @Column(name = "old_table")
    @TableField("old_table")
    private String oldTable;

    @Column(name = "system_key")
    @TableField("system_key")
    private String systemKey;
}
