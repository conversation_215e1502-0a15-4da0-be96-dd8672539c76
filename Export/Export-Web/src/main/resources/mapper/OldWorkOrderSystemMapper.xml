<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.export.mapper.OldWorkOrderSystemMapper">

    <select id="queryUnitList" resultType="com.trs.gov.export.DO.ManagerDeptDO">
        SELECT
        manager_dept.*,
        manager_user.USER_NAME AS unitMaster,
        manager_user.NICKNAME AS NICKNAME,
        manager_user.phone AS phone,
        temp_ids_unit_mapping.ids_group_key AS unitcode
        FROM
        manager_dept
        LEFT JOIN temp_view_unit_user ON manager_dept.id = temp_view_unit_user.unit_id
        LEFT JOIN manager_user ON (temp_view_unit_user.user_id = manager_user.ID OR
        manager_user.ID=manager_dept.chargeUserId)
        LEFT JOIN temp_ids_unit_mapping ON temp_ids_unit_mapping.old_unit_id = manager_dept.id
        WHERE
        manager_dept.id NOT IN ( SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table =
        "manager_dept" )
    </select>
    <select id="findFileById" resultType="com.trs.gov.export.DO.GspAppendix">
        select * from gsp_appendix
        <where>
            appendixid = #{fileId}
        </where>
    </select>
    <select id="getSiteList" resultType="com.trs.gov.export.DO.SiteDevopsDO">
        SELECT
        site.*,
        ( SELECT tmp.new_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table = "manager_dept" AND tmp.old_id =
        site.resunitid LIMIT 1 ) AS hostUnitId,
        ( SELECT tmp.new_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table = "manager_dept" AND tmp.old_id =
        site.comunitid LIMIT 1) AS masterUnitId,
        ( SELECT tmp.new_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table = "manager_dept" AND tmp.old_id =
        site.opsunitid LIMIT 1) AS operationUnitId
        FROM
        `sitedevops` site
        WHERE
        site.sid NOT IN ( SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table = "sitedevops" );
    </select>
    <select id="getWorkOrderList" resultType="com.trs.gov.export.DO.WorkOrderInfoDO">
        SELECT WORK
        .*,
        ( SELECT temp_export_mapping_table.new_id FROM temp_export_mapping_table WHERE temp_export_mapping_table.old_id
        = WORK.titletype2 ) AS workordertypeid,
        u.USER_NAME AS dealUsername,
        u.NICKNAME AS dealTruename,
        (SELECT USER_NAME FROM manager_user WHERE manager_user.ID=WORK.createuserid) AS crUsername
        FROM
        workorderinfos
        WORK LEFT JOIN manager_user u ON WORK.accepter = u.ID
        WHERE
        WORK.state = 1
        AND WORK.workorderstate IN ( 3, 4 )
        AND WORK.sid !='' AND WORK.sid IS NOT NULL
        AND WORK.id NOT IN ( SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table =
        "workorderinfos" )
        <if test="size>0">
            limit #{size};
        </if>
    </select>
    <select id="findNewId" resultType="java.lang.String">
        SELECT tmp.new_id FROM `temp_export_mapping_table` tmp
        <where>
            tmp.old_id = #{oldId}
            AND tmp.old_table = #{oldTable}
            AND tmp.system_key = #{systemKey}
            limit 1;
        </where>
    </select>
    <select id="getWorkTimeList" resultType="com.trs.gov.export.DO.WorkOrderInfoDO">
        SELECT WORK
        .*,
        ( SELECT temp_export_mapping_table.new_id FROM temp_export_mapping_table WHERE temp_export_mapping_table.old_id
        = WORK.titletype2 ) AS workordertypeid,
        u.USER_NAME AS dealUsername,
        u.NICKNAME AS dealTruename,
        (SELECT USER_NAME FROM manager_user WHERE manager_user.ID=WORK.createuserid) AS crUsername
        FROM
        workorderinfos
        WORK LEFT JOIN manager_user u ON WORK.accepter = u.ID
        WHERE
        WORK.manhours != "" AND WORK.manhours IS NOT NULL
        AND WORK.id IN ( SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table =
        "workorderinfos" )
        AND WORK.id NOT IN ( SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table =
        "workorderinfos-time" )
        <if test="size>0">
            limit #{size};
        </if>
    </select>
    <select id="getRecordList" resultType="com.trs.gov.export.DO.WorkOrderFlowDO">
        SELECT
        flow.*,
        info.sid
        FROM
        workorderflow flow
        LEFT JOIN workorderinfos info ON flow.workerId = info.id
        WHERE
        flow.operType != 4
        AND flow.workerId IN (
        SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table = "workorderinfos"
        )
        AND flow.id NOT IN (
        SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table = "workorderflow"
        )
        <if test="size>0">
            limit #{size};
        </if>
    </select>
    <select id="getScoreList" resultType="com.trs.gov.export.DO.ScoreDO">
        SELECT
        f1.*,
        ( SELECT info.sid FROM workorderinfos info WHERE info.id = f1.workerid ) AS sid,
        ( SELECT info.deptid FROM workorderinfos info WHERE info.id = f1.workerid ) AS deptid,
        ( SELECT info.dept FROM workorderinfos info WHERE info.id = f1.workerid ) AS dept
        FROM
        workorderflow f1
        WHERE
        f1.operType = 4
        AND f1.workerId IN (
        SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table = "workorderinfos"
        )
        AND f1.id NOT IN (
        SELECT tmp.old_id FROM `temp_export_mapping_table` tmp WHERE tmp.old_table = "workorderinfos-score"
        )
        <if test="size>0">
            limit #{size};
        </if>
    </select>
</mapper>
