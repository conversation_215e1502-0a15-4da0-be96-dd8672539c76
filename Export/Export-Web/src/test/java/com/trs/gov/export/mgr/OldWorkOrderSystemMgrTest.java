package com.trs.gov.export.mgr;

import com.trs.gov.ExportApplication;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.GradeExportDTO;
import com.trs.gov.interaction.DTO.OprRecordExportDTO;
import com.trs.gov.management.DTO.SiteRelationExportDTO;
import com.trs.gov.management.DTO.UnitExportDTO;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ExportApplication.class)
@Slf4j
public class OldWorkOrderSystemMgrTest {

    @Autowired
    private OldWorkOrderSystemMgr mgr;

    @Test
    public void getUnitList() throws ServiceException {
        Tuple2<String, List<UnitExportDTO>> t = mgr.getUnitList();
        System.out.println(t._1());
        System.out.println(t._2());
    }

    @Test
    public void getSiteList() throws ServiceException {
        Tuple2<String, List<SiteRelationExportDTO>> t = mgr.getSiteList();
        System.out.println(t._1());
        System.out.println(t._2());
    }

    @Test
    public void getWorkOrderList() throws ServiceException {
        Tuple2<String, List<WorkOrderExportDTO>> t = mgr.getWorkOrderList(-1L);
        System.out.println(t._1());
        System.out.println(t._2().size());
    }

    @Test
    public void getRecordList() throws ServiceException {
        Tuple2<String, List<OprRecordExportDTO>> t = mgr.getRecordList(0L);
        System.out.println(t._1());
        System.out.println(t._2().size());
    }

    @Test
    public void getScoreList() throws ServiceException {
        Tuple2<String, List<GradeExportDTO>> t = mgr.getScoreList(0L);
        System.out.println(t._1());
        System.out.println(t._2().size());
    }

    @Test
    public void updateUnitCode() throws ServiceException {
        mgr.updateUnitCode();
    }
}