<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>Export</artifactId>
        <groupId>com.trs.gov</groupId>
        <version>1.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>Export-Web</artifactId>

    <properties>
        <!--suppress UnresolvedMavenProperty -->
        <filter-env>${env}</filter-env>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.trs.gov</groupId>
            <artifactId>FileManager-Service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.trs.gov</groupId>
            <artifactId>SystemManagement-Service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.trs.gov</groupId>
            <artifactId>WorkOrder-Service</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}-1.0</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <filters>
            <filter>../../Configuration/filters/${filter-env}/resources/bootstrap.properties</filter>
        </filters>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
