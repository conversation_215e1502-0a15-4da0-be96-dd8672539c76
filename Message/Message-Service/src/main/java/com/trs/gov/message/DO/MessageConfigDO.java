package com.trs.gov.message.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @ClassName：MessageConfig
 * @Description :  消息配置表
 * <AUTHOR> YangXin
 * @Date 2020/9/15 12:51
 **/
@Data
@TableName("message_config")
public class MessageConfigDO extends BaseDO {

    /**
     * 工单类型ID
     */
    @ApiModelProperty(value = "工单类型ID", required = true)
    @Column(name = "work_order_type_id")
    private Long workOrderTypeId;

    /**
     * 是否跟随父类配置
     */
    @ApiModelProperty(value = "是否跟随父类配置", required = true)
    @Column(name = "is_follow_parent_config")
    private Integer isFollowParentConfig;

    /**
     * 工单通知消息配置，内容如：000011111111
     */
    @ApiModelProperty(value = "工单通知消息配置", required = true)
    @Column(name = "message_config")
    private String messageConfig;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", required = true)
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 修改人用户名
     */
    @ApiModelProperty(value = "修改人用户名", required = true)
    @Column(name = "update_user_name")
    private String updateUserName;

}
