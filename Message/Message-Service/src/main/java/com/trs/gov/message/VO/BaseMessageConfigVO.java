package com.trs.gov.message.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName：MessageConfigVO
 * @Description : 工单消息配置返回体
 * <AUTHOR> YangXin
 * @Date 2020/9/10 14:02
 **/
@Data
public class BaseMessageConfigVO extends BaseVO {
    /**
     * 配置的index编号
     */
    private int id;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 是否被选择,默认没有选中
     */
    private boolean checked = false;

    /**
     * 子节点信息
     */
    private List<BaseMessageConfigVO> children = new ArrayList<>();
}
