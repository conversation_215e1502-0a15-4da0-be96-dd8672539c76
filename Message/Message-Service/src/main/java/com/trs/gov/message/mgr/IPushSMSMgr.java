package com.trs.gov.message.mgr;

import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.SmsMessageVO;

import java.util.List;

/**
 * @ClassName：IPushSMSMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/9 13:40
 **/
public interface IPushSMSMgr extends IKey {

    /**
     * @Description  构造各个渠道自己的消息，然后返回
     * @Param [dto]
     * @return java.util.List<com.trs.gov.message.VO.SmsMessageVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/4/9 13:52
     **/
    public List<SmsMessageVO> pushSMSMessage(CreateNoticeDTO dto) throws ServiceException;

}
