package com.trs.gov.message.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * @ClassName：ListNoticeDTO
 * @Description : 查询我的消息配置
 * <AUTHOR> YangXin
 * @Date 2020/9/10 14:57
 **/
@Data
public class MessageConfigDTO extends BaseDTO {

    /**
     * 工单类型Id
     */
    private Long workOrderTypeId;

    /**
     * 跟随父配置类 1 -是 0-否
     */
     private Integer followParentConfig;

    /**
     * 配置类型
     */
    private String messageConfig;

    /**
     * 级别
     */
    private String key;




    @Override
    public boolean isValid() throws ServiceException {
        if(workOrderTypeId == null){
            throw new ParamInvalidException("工单类型Id不能为空!");
        }
        return true;
    }
}
