package com.trs.gov.message.DTO;

import com.trs.gov.core.constant.CoreConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName：UserDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/19 19:04
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TargetUserDTO implements Serializable {

    public static final long serialVersionUID = CoreConstant.serialVersionUID;
    /**
     * 用户名
     */
    private String userName;

    /**
     * 单位Id
     */
    private Long unitId;

    /**
     * 组织 Id
     */
    private Long groupId;

    /**
     * 指定类型
     */
    private Integer assignType;



    /**
     * 真实姓名
     */
    private String trueName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 电话
     */
    private String phone;


    public TargetUserDTO(String userName, Long unitId) {
        this.userName = userName;
        this.unitId = unitId;
    }

    /**
     * @Description  类型1,指定到人
     * @Param [userName, unitId, assignType]
     * @return
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/11 9:44
     **/
    public TargetUserDTO(String userName,Long unitId,Integer assignType){
        this.userName = userName;
        this.unitId = unitId;
        this.assignType = assignType;
    }

    /**
     * @Description  类型2或3  指定到单位或者组织
     * @Param [unitId, groupId, assignType]
     * @return
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/11 9:43
     **/
    public TargetUserDTO(Long unitId,Long groupId,Integer assignType){
        this.unitId = unitId;
        this.groupId = groupId;
        this.assignType = assignType;
    }

    public TargetUserDTO(String userName, Long unitId, Long groupId, Integer assignType) {
        this.userName = userName;
        this.unitId = unitId;
        this.groupId = groupId;
        this.assignType = assignType;
    }
    /**
     * @Description  全部
     * @Param [assignType]
     * @return
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/11 9:43
     **/
    public TargetUserDTO(Integer assignType){
        this.assignType = assignType;
    }
}
