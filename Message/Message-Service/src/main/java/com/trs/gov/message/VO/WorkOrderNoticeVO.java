package com.trs.gov.message.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：WorkOrderNoticeVO
 * @Description : 获取最新消息和历史消息的返回实体
 * <AUTHOR> YangXin
 * @Date 2020/9/10 15:17
 **/
@Data
public class WorkOrderNoticeVO extends BaseVO {
    /**
     * 工单Id
     */
    private Long workOrderId;
    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知的内容
     */
    private String content;


    /**
     * 通知创建的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date crTime;

}
