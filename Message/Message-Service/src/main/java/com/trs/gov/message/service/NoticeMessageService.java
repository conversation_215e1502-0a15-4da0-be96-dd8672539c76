package com.trs.gov.message.service;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.NoticeMessageDTO;
import com.trs.gov.message.VO.WorkOrderNoticeVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * @ClassName：NoticeMessageService
 * @Description : 通知消息相关配置
 * <AUTHOR> YangXin
 * @Date 2020/9/10 14:25
 **/
public interface NoticeMessageService {

    /**
     * @Description 获取未读的相关统计信息
     * @Param [username]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/21 17:57
     **/
    String getUnReadMessage(String username,String unitId) throws ServiceException;

    /**
     * @Description  获取我信息列表，最近消息和历史消息
     * @Param [userId type]
     * @return java.util.List<com.trs.gov.message.VO.WorkOrderNoticeVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/10 15:21
     **/
    RestfulResults<List<WorkOrderNoticeVO>> getRecentMessage(NoticeMessageDTO noticeMessageDTO);

}
