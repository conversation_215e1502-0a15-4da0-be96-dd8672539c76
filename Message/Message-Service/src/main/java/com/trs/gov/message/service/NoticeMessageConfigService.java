package com.trs.gov.message.service;

import com.trs.gov.message.DTO.MessageConfigDTO;
import com.trs.gov.message.VO.MessageConfigVO;
import com.trs.web.builder.base.RestfulResults;

/**
 * @ClassName：NoticeMessageConfigService
 * @Description : TODO
 * <AUTHOR> 通知配置消息类
 * @Date 2020/9/10 14:00
 **/
public interface NoticeMessageConfigService {
    /**
     * @Description 返回工单消息的配置信息
     * @Param [workOrderTypeId 工单类型Id]
     * @return java.util.List<com.trs.gov.statistics.VO.MessageConfigVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/10 14:17
     **/
    RestfulResults<MessageConfigVO> listMessageConfig(MessageConfigDTO messageConfigDTO);

    /**
     * @Description  保存工单的配置信息
     * @Param [jsonConfig 工单配置信息的json字符串]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/10 14:20
     **/
    RestfulResults saveMessageConfig(MessageConfigDTO messageConfigDTO);
}
