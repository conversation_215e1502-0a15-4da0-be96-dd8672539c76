package com.trs.gov.message.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：MessageConfigVO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/15 11:37
 **/
@Data
public class MessageConfigVO extends BaseVO {


    /**
     * 工单类型Id
     */
    private boolean isRoot = false;

    /**
     * 工单类型Id
     */
    private Long workOrderTypeId;

    /**
     * 是否跟随父类配置 ,true跟随， false 不跟随
     */
    private Boolean isFolowParentConfig = false;

    /**
     * 工单类型
     **/
    private List<BaseMessageConfigVO> messageConfig;
}
