package com.trs.gov.message.mgr;

import com.trs.gov.core.IKey;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;

import java.util.Map;

public interface ISendMessageMgr extends IKey {


    /**
     * @Description  获取各自的模板通知内容
     * @Param [sendNoticeDTO]
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/18 9:51
     **/
     Map<String, BaseMessageVO> getMessageTemple(CreateNoticeDTO createNoticeDTO) throws Exception;

}
