package com.trs.gov.message.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import io.vavr.control.Try;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * @ClassName：CreateNoticeDTO
 * @Description :  实体，由工单那方调用发送消息接口，参数为该实体
 * <AUTHOR> YangXin
 * @Date 2020/9/15 13:36
 **/
@Data
public class CreateNoticeDTO extends BaseDTO {

    /**
     * 工单的ID
     */
    private Long workOrderId;
    /**
     * 工单类型ID
     */
    private Long workOrderTypeId;
    /**
     * 工单类型
     */
    private Long workOrderTopTypeId;
    /**
     * 顶级工单父类名字
     */
    private String rootTypeName;
    /**
     * 消息配置中心类型【由 OperateName 类】
     */
    private String messageConfigType;
    /**
     * 当前操作用户
     */
    private String currentUser;
    /**
     * 当前操作用户名字
     */
    private String currentUserTrueName;
    /**
     * 至哪个用户
     */
    private String toUser;
    /**
     * 至哪个用户的真实姓名
     */
    private String toUserTrueName;
    /**
     * 创建人
     */
    private List<TargetUserDTO> createUser;
    /**
     * 受理人
     */
    private List<TargetUserDTO> handleUser;
    /**
     * 旧的抄送人
     */
    private List<TargetUserDTO> oldCopyUser;
    /**
     * 新增抄送人
     */
    private List<TargetUserDTO> newCopyUser;
    /**
     * 主办人
     */
    private List<TargetUserDTO> hostUser;
    /**
     * 记录工时
     */
    private String dayTime;

    /**
     * 当前登录 的单位
     */
    private UnitVO loginUnitVO;

    /**
     * 标题
     */
    private String title;


    public Optional<String> getWorkOrderName(){
        Try<Optional<String>> tryResult = Try.of(()->Optional.ofNullable(rootTypeName+"#"+workOrderId));
        if(tryResult.isSuccess()){
            return tryResult.get();
        }
        return Optional.empty();
    }
    /**
     * @Description  校验必传参数
     * @Param [createUser, handleUser, copyUser, hostUser]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/28 13:25
     **/
    public boolean mustNotNull(boolean createUser,boolean handleUser,boolean copyUser,boolean hostUser) throws ParamInvalidException {
        if(createUser && this.getCreateUser() == null){
            throw new ParamInvalidException("创建人数据列表不能为空!");
        }
        if(handleUser && this.getHandleUser() == null){
            throw new ParamInvalidException("受理人数据列表不能为空!");
        }
        if(copyUser && this.getNewCopyUser() == null && this.getOldCopyUser() == null){
            throw new ParamInvalidException("抄送人数据列表不能为空!");
        }
        if(hostUser && this.getHostUser() == null){
            throw new ParamInvalidException("主持人数据列表不能为空!");
        }
        return true;
    }

    @Override
    public boolean isValid() throws ServiceException {
        if(workOrderId == null){
            throw  new ParamInvalidException("工单Id不能为空!");
        }
        if(StringUtils.isEmpty(rootTypeName)){
            throw  new ParamInvalidException("工单顶级类型名字不能为空!");
        }
        if(StringUtils.isEmpty(messageConfigType)){
            throw new ParamInvalidException("消息类型不能为空!");
        }
        if(StringUtils.isEmpty(messageConfigType)){
            throw new ParamInvalidException("消息类型不能为空!");
        }
        if(StringUtils.isEmpty(currentUser)){
            throw new ParamInvalidException("当前用户名不能为空!");
        }
        if(StringUtils.isEmpty(currentUserTrueName)){
            throw new ParamInvalidException("当前用户名的真实姓名不能为空!");
        }
        if(StringUtils.isEmpty(getUnitId())){
            throw new ParamInvalidException("当unitId不能为空!");
        }
        if(workOrderTopTypeId == null){
            throw new ParamInvalidException("工单顶级类型不能为空!");
        }
        return true;
    }
}
