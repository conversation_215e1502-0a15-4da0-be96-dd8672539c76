package com.trs.gov.message.DTO;

import lombok.Data;

import java.util.List;

/**
 * @ClassName：UserUnitMappingSearchDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/1/8 16:49
 **/
@Data
public class UserUnitMappingSearchDTO {

    /**
     *  用户 + 单位
     */
    private List<String> userUnit;


    /**
     *  不等于当前登录的的用户名：用户名 + 单位
     */
    private String notUserAndUnit;


    /**
     *  0 - 不是负责人   1-负责人
     */
    private Boolean isMaster;

    private List<Long> targetUnitIds;

    private Integer isAll;

}
