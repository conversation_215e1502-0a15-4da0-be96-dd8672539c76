package com.trs.gov.message.DTO;

import com.trs.gov.message.VO.SmsMessageVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName：BatchSMSContentDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/12 9:31
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchSMSContentDTO {

    /**
     * 用户帐号
     **/
    private String account;

    /**
     * 用户帐号对应的密码
     **/
    private String password;

    /**
     * 最多支持100个内容提交，如果需要开放更多请申请。
     **/
    private List<SmsMessageVO> bReqContents;

    /**
     * 请先询问配置的通道是否支持扩展子号，如果不支持，请填空。子号只能为数字，且最多3位
     **/
    private String extno;

    public BatchSMSContentDTO(String account, String password, List<SmsMessageVO> bReqContents) {
        this.account = account;
        this.password = password;
        this.bReqContents = bReqContents;
    }
}
