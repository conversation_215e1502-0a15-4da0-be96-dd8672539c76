package com.trs.gov.message.mgr;

import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;

import java.io.IOException;
import java.util.Map;

public interface IDifferentChannel extends IKey {

    /**
     * @Description  发送不同渠道的消息
     * @Param [dto, templeMessage]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 10:49
     **/
    void SendMesaageByChannel(CreateNoticeDTO dto, Map<String, BaseMessageVO> templeMessage);
}
