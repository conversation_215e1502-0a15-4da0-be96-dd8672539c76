package com.trs.gov.message.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @ClassName：NoticeMessageDO
 * @Description : 通知内容Id
 * <AUTHOR> YangXin
 * @Date 2020/9/10 13:55
 **/
@Data
@TableName("notice_message")
public class NoticeMessageDO extends BaseDO {

    /**
     * 工单 Id
     */
    @ApiModelProperty(value = "工单ID", required = true)
    @Column(name = "work_order_id")
    private Long workOrderId;

    /**
     * 通知发送的类型
     */
    @ApiModelProperty(value = "通知发送的类型", required = true)
    @Column(name = "message_config_type")
    private String messageConfigType;
    /**
     * 创建人单位
     */
    @ApiModelProperty(value = "创建人单位", required = true)
    @Column(name = "cr_unit_id")
    private Long crUnitId;

    /**
     * 通知的标题
     */
    @ApiModelProperty(value = "通知的标题", required = true)
    @Column(name = "title")
    private String title;

    /**
     * 通知的内容
     */
    @ApiModelProperty(value = "通知的内容", required = true)
    @Column(name = "content")
    private String content;

    /**
     * 接收通知的人
     */
    @ApiModelProperty(value = "接收通知的人", required = true)
    @Column(name = "reveive_user_name")
    private String reveiveUserName;
    /**
     * 接收人单位Id
     */
    @ApiModelProperty(value = "接收人单位Id", required = true)
    @Column(name = "reveive_unit_id")
    private Long reveiveUnitId;
    /**
     * 通知状态 0-未读 1-已读
     */
    @ApiModelProperty(value = "通知状态 0-未读 1-已读", required = true)
    @Column(name = "is_read")
    private Integer isRead = 0;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", required = true)
    @Column(name = "update_time")
    private Date updateTime;
    /**
     * 修改人Id
     */
    @ApiModelProperty(value = "修改人Id", required = true)
    @Column(name = "update_user_name")
    private String updateUserName;
    /**
     * 修改人单位Id
     */
    @ApiModelProperty(value = "修改人单位Id", required = true)
    @Column(name = "update_unit_id")
    private Long updateUnitId;


}
