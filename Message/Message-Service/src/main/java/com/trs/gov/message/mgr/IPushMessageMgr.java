package com.trs.gov.message.mgr;

import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.PushToPlatformDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.BaseMessageVO;

import java.util.List;
import java.util.Optional;


public interface IPushMessageMgr extends IKey {

    /**
     * @Description  构造各自发送的消息
     * @Param [vo, receiveUser, sendUserName]
     * @return java.util.Optional<com.trs.gov.message.DTO.PushToPlatformDTO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 14:52
     **/
    public Optional<PushToPlatformDTO> buildMesssage(BaseMessageVO vo, List<TargetUserDTO> receiveUser, String sendUserName) throws ServiceException;

    /**
     * @Description  获取基础的url  [除了token以外的所有参数]
     * @Param [channel]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 14:58
     **/
    public String buildBaseUrl(String channel);
}
