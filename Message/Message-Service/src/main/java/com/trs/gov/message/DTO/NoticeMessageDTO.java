package com.trs.gov.message.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName：NoticeMessageDTO
 * @Description :  我的消息通知列表
 * <AUTHOR> YangXin
 * @Date 2020/9/15 11:27
 **/
@Data
public class NoticeMessageDTO extends BasePageDTO {

    /**
     * 用户Id
     */
    private String userName;
    /**
     * 是否已读  0-未读 1-已读
     */
    private Integer isRead = 0;

    /**
     * 消息类型 0-最新消息  1-历史消息
     */
    private Integer messageType;

    /**
     * 时间  单位为：h[小时], 默认为48小时
     */
    private Integer hours;

    /**
     * 分割时间 默认为48小时
     */
    private String splitTime;

    @Override
    public boolean isValid() throws ServiceException {
        if(StringUtils.isEmpty(userName)){
            throw new ParamInvalidException("用户Id参数为空!");
        }
        return true;
    }
}
