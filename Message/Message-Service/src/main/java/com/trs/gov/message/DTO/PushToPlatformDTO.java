package com.trs.gov.message.DTO;

import com.trs.gov.message.VO.PushToPlatformResultVO;
import lombok.Data;

/**
 * @ClassName：SendToPlatformDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 11:40
 **/
@Data
public class PushToPlatformDTO {

    /**
     * 保存原有的数据，如果发送失败，方便构造重试机制
     **/
    private PushToPlatformResultVO resultVO;

    /**
     * 将要发送的XML信息体
     **/
    private String xmlMessage;
    /**
     * 请求的Url,除了token以外 ，token每次发送的时候获取
     **/
    private String requestPreUrl;
    /**
     * 是否发送成功
     **/
    private Boolean isSuccess;
    /**
     * 重传次数
     **/
    private Integer retryCount = 0;

    private String jsonMessage;

}
