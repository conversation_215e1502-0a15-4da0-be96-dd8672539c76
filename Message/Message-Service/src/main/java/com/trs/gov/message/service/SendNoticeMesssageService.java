package com.trs.gov.message.service;

import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.web.builder.base.RestfulResults;

/**
 * @ClassName：SendNoticeMesssageService
 * @Description : 消息的发送类
 * <AUTHOR> YangXin
 * @Date 2020/9/10 15:24
 **/
public interface SendNoticeMesssageService {

    /**
     * @return com.trs.web.builder.base.RestfulResults
     * @Description 根据不同的操作实现各自的发送逻辑
     * @Param [workOrderId 工单ID, messageConfigType OperateNameConstant中对应的编号]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/10 15:40
     **/
    RestfulResults sendNoticeMessage(CreateNoticeDTO createNoticeDTO);
}
