package com.trs.gov.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.message.DO.NoticeMessageDO;
import com.trs.gov.message.MessageApplication;
import com.trs.gov.message.mapper.NoticeMessageDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.function.Consumer;

/**
 * @ClassName：MybatisTest
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/1/14 18:39
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
@Slf4j
public class MybatisTest {
    @Autowired
    private NoticeMessageDOMapper mapper;

    @Test
    public void mybatis(){
        QueryWrapper<NoticeMessageDO> queryWrapper = new QueryWrapper<>();
        QueryWrapper<NoticeMessageDO> queryWrapper1 = new QueryWrapper<>();
        queryWrapper.eq("cr_user","寇婷11");
        queryWrapper1.eq("cr_user","寇婷11");

        Consumer<QueryWrapper<NoticeMessageDO>> queryWrapperConsumer = m->m.eq("id","119");
        queryWrapper.or(queryWrapperConsumer);
        Consumer<QueryWrapper<NoticeMessageDO>> queryWrapperConsumer1 = m->m.eq("id","110");
        queryWrapper1.or().and(queryWrapperConsumer1);


        List<NoticeMessageDO> messageDOS = mapper.selectList(queryWrapper);
        List<NoticeMessageDO> messageDOS1 = mapper.selectList(queryWrapper1);


    }

}
