package com.trs.gov.message.service.impl;

import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.MessageApplication;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @ClassName：SendMessageTest
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/28 19:27
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MessageApplication.class)
@Slf4j
public class SendMessageTest {
    @Autowired
    private SendNoticeMesssageServiceImpl sendNoticeMesssageService;



    @Test
    public void sendMessageCreate(){
        CreateNoticeDTO createNoticeDTO = new CreateNoticeDTO();
        createNoticeDTO.setWorkOrderId(694L);
        createNoticeDTO.setRootTypeName("工单");
        createNoticeDTO.setMessageConfigType("workorder.finish.pingjia");
        createNoticeDTO.setCurrentUser("王临江");
        createNoticeDTO.setCurrentUserTrueName("王临江");
//        createNoticeDTO.setCreateUser(Arrays.asList("王临江"));
//        createNoticeDTO.setHandleUser(Arrays.asList("王临江"));
//        createNoticeDTO.setCopyUser(Arrays.asList("王临江"));
//        createNoticeDTO.setHostUser(Arrays.asList("王临江"));
        RestfulResults restfulResults = sendNoticeMesssageService.sendNoticeMessage(createNoticeDTO);
        System.out.println(restfulResults);
    }
    @Test
    public void sendChaosongMesage(){
        CreateNoticeDTO createNoticeDTO = new CreateNoticeDTO();
        createNoticeDTO.setWorkOrderId(706L);
        createNoticeDTO.setWorkOrderTypeId(113L);
        createNoticeDTO.setRootTypeName("工单");
        createNoticeDTO.setMessageConfigType("workorder.finish.pingjia");
        createNoticeDTO.setCurrentUser("王临江");
        createNoticeDTO.setCurrentUserTrueName("王临江");
//        createNoticeDTO.setCreateUser(Arrays.asList("王临江"));
//        createNoticeDTO.setHandleUser(Arrays.asList("王临江"));
//        createNoticeDTO.setCopyUser(Arrays.asList("王临江","yx","ccb"));
//        createNoticeDTO.setHostUser(Arrays.asList("王临江"));
        RestfulResults restfulResults = sendNoticeMesssageService.sendNoticeMessage(createNoticeDTO);
        log.info("信息"+restfulResults.getMsg());
    }
}
