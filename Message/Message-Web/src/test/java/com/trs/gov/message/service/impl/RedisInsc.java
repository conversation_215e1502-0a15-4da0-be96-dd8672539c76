package com.trs.gov.message.service.impl;

import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisShardInfo;
import redis.clients.jedis.ShardedJedis;
import redis.clients.jedis.ShardedJedisPool;

import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * @ClassName：RedisInsc
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/28 16:05
 **/
public class RedisInsc {

    private static ShardedJedisPool pool;
    static {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(100);
        config.setMaxIdle(50);
        config.setMaxWaitMillis(3000);
        config.setTestOnBorrow(true);
        config.setTestOnReturn(true);
        // 集群
        JedisShardInfo jedisShardInfo1 = new JedisShardInfo("localhost", 6379);
        jedisShardInfo1.setPassword("trsAdmin");
        List<JedisShardInfo> list = new LinkedList<JedisShardInfo>();
        list.add(jedisShardInfo1);
        pool = new ShardedJedisPool(config, list);
    }

    public static void main(String[] args) {
        ShardedJedis jedis = pool.getResource();
        jedis.set("test_A","A");
        jedis.set("test_B","B");
        jedis.set("test_C","C");
        Set<String> test_ = jedis.hkeys("test_");
//        Set<String> test_ = jedis.hkeys("test_");
        System.out.println("--------------");
        for (String s : test_) {
            System.out.println(s);
        }
        System.out.println("--------------");

    }

    public static void testA(){
        ShardedJedis jedis = pool.getResource();
//        Long result1 = jedis.zadd("sitelation", 0, "117");
        Long result4 = jedis.zadd("sitelation", 0, "118");
        Long result5 = jedis.zadd("sitelation", 0, "119");
        Long result6 = jedis.zadd("sitelation", 0, "120");
        Long result7 = jedis.zadd("sitelation", 0, "121");
        System.out.println(result4);
        Double result2 = jedis.zincrby("sitelation", 2.1, "117");
        System.out.println(result2);
        Double result8 = jedis.zincrby("sitelation", -0.001, "116");
        System.out.println(result8);
        Double result3 = jedis.zscore("sitelation",  "153");
        System.out.println(result3);
        System.out.println("---------------");
        Set<String> siteRelation1 = jedis.zrangeByScore("sitelation",0.0,2.0);
        for (String s : siteRelation1) {
            System.out.println(s);
        }
        System.out.println("---------------");
        //offset 从 0开始
        Set<String> siteRelation = jedis.zrangeByScore("sitelation",0.0,2.0,1,2);
        for (String s : siteRelation) {
            System.out.println(s);
        }
    }

}
