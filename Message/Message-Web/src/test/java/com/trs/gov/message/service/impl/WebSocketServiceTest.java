package com.trs.gov.message.service.impl;

import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

public class WebSocketServiceTest {
    @Test
    public void testURLDecoder() throws UnsupportedEncodingException {
        String a = "%E5%85%AD%E7%9B%98%E6%B0%B4%E5%B8%82%E6%95%99%E8%82%B2%E5%B1%80001";
        System.out.println(URLDecoder.decode(a,"UTF-8"));
        a = "寇婷";
        System.out.println(URLDecoder.decode(a,"UTF-8"));
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String a = "%E6%9D%A8%E9%91%AB";
        System.out.println(URLDecoder.decode(a,"UTF-8"));
    }
}