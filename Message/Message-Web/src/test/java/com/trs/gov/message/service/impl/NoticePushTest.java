package com.trs.gov.message.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.message.constant.XmlParamConstant;
import com.trs.gov.message.util.BuildTokenUtils;
import com.trs.gov.message.util.JsonXmlUtils;
import com.trs.log.exception.RecordableException;
import com.trs.web.builder.util.DESUtils;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import org.dom4j.DocumentException;
import org.junit.Test;

import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * Description:  <BR>
 * Copyright: Copyright (c) 2004-2016 拓尔思信息技术股份有限公司 <BR>
 * Company: www.trs.com.cn <BR>
 *
 * <AUTHOR>
 * @version 1.0
 * @createTime 2022/3/29 10:29
 */
public class NoticePushTest {

    @Test
    public void pushSMS(){
        Date date = new Date();
        System.out.println(TimeUtils.dateToString(date,TimeUtils.YYYYMMDD_HHMMSS));
        try {
            System.out.println(getKey(date));
        } catch (ServiceException e) {
            e.printStackTrace();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (RecordableException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }

    }


    @Test
    public void buildBody() throws RecordableException, DocumentException, NoSuchAlgorithmException, ServiceException {
        HttpRequest httpRequest = new HttpRequest.Builder(new OkHttpClient.Builder()
                .connectTimeout(4, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
        ).build();
        String bodyJsonStr = "{\"msg\":[{\"noticeOrg\":[],\"noticeDepartment\":[],\"contentUrl\":\"\",\"mobileUrl\":\"\",\"noticeMobile\":[\"%s\"],\"noticeUser\":[12],\"channles\":[{\"channel\":\"gzh\",\"content\":\"%s\"}]}],\"uid\":0,\"departmentId\":0,\"orgId\":0,\"token\":\"%s\"}";
        JSONObject bodyJson = JSONObject.parseObject(String.format(bodyJsonStr, "17349019676", "推送消息内容-01", getKey(new Date())));
        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), bodyJson.toJSONString());
        String response = httpRequest.doPost("http://message.guizhou.gov.cn/api/receive/public/message", body);
        System.out.println("返回响应："+response);
    }

    /**
     * @Description  根据 |当前时间|用户名|授权密码 获取 key
     * @Param []
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/10 17:06
     **/
    private String getKey(Date nowDate) throws ServiceException, NoSuchAlgorithmException, RecordableException, DocumentException {
        //得到时间格式
        String dateFormat = TimeUtils.dateToString(nowDate,TimeUtils.YYYYMMDD_HHMMSS);
        //获取 token 请求的client_time
        String key = getTokenKey(dateFormat);
        System.out.println(key);
        //获取 token 的 xml请求体
        String xml = getXmlBody(dateFormat,key);
        System.out.println(xml);
        //发送请求
        String tokenXml = getTokenXml(xml);
        //处理返回结果得到Token
        return getToken(tokenXml);
    }

    private String getToken(String tokenXml) throws ServiceException, DocumentException {
        JSONObject jsonObject = JsonXmlUtils.xmlToJson(tokenXml,true);
        System.out.println(jsonObject);
        JSONObject json = jsonObject.getJSONObject(XmlParamConstant.RESULT);
        String status = json.getString(XmlParamConstant.STATUS);
        if(status.equalsIgnoreCase("ok")){
            return json.getString(XmlParamConstant.TOKEN);
        }else{
            throw new ServiceException("获取token失败! response:"+tokenXml);
        }
    }

    private String getTokenXml(String xml) throws RecordableException {
        HttpRequest httpRequest = new HttpRequest.Builder(new OkHttpClient.Builder()
                .connectTimeout(4, TimeUnit.SECONDS)
                .readTimeout(3, TimeUnit.SECONDS)
        ).build();
        RequestBody body = RequestBody.create(MediaType.parse("application/xml; charset=utf-8"), xml);
        String response = httpRequest.doPost("http://message.guizhou.gov.cn/api/receive/public/xml/getToken", body);
        return response;
    }

    private String getXmlBody(String dateFormat, String key) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(XmlParamConstant.CLIENT_TIME,dateFormat);
        jsonObject.put(XmlParamConstant.USERNAME,"jhyptgdxt");
        jsonObject.put(XmlParamConstant.KEY,key);
        JSONObject jsonNotice = new JSONObject();
        jsonNotice.put(XmlParamConstant.MEMBER,jsonObject);
        return JsonXmlUtils.jsonToXml(jsonNotice,true);
    }

    private String getTokenKey(String dateFormat) throws ServiceException, NoSuchAlgorithmException {
        if(CMyString.isEmpty("jhyptgdxt")){
            throw new ServiceException("用户名不能为空!");
        }
        if(CMyString.isEmpty("+HGR8k$v")){
            throw new ServiceException("授权密码不能为空!");
        }
        System.out.println("获取加密前的信息："+"client-time:"+dateFormat+" userName:"+"jhyptgdxt"+" password:"+"+HGR8k$v");
        String key =DESUtils.stringToMD5(String.format("|%s|%s|%s", dateFormat, "jhyptgdxt", "+HGR8k$v"));
        System.out.println("加密后的数据："+key);
        return key;
    }

}
