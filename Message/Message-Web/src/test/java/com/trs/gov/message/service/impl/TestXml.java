package com.trs.gov.message.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.message.util.JsonXmlUtils;
import org.dom4j.DocumentException;

import java.io.IOException;

/**
 * @ClassName：TestXml
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 18:39
 **/
public class TestXml {
    public static void main(String[] args) throws DocumentException, IOException {
        testC();
        testB();
    }

    public static void testC() throws IOException {
        String json = "{\"member\":{\"client-time\":\"2020-12-22 18:09:03\",\"key\":\"aeb78680a8b1bc8f0675a46c504e5466\",\"username\":\"szh_tes_user\"}}";
        JSONObject jsonObject = JSONObject.parseObject(json);
        String xml = JsonXmlUtils.jsonToXml(jsonObject,true);
        System.out.println(xml);

    }
    public static void testB() throws DocumentException {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<member><client-time>2020-12-22 18:09:03</client-time><key>aeb78680a8b1bc8f0675a46c504e5466</key><username>szh_tes_user</username></member>";
        JSONObject jsonObject = JsonXmlUtils.xmlToJson(xml,true);
        String s = jsonObject.toJSONString();
        System.out.println(s);
    }


//    public static void main(String[] args) throws IOException {
//        JSONArray jsonArray = new JSONArray();
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put(XmlParamConstant.NOTICE_MOBILE,"13281837003");
//        jsonObject.put(XmlParamConstant.PUBLISH_USER,"杨鑫");
//        jsonObject.put(XmlParamConstant.MESSAGE,"我时发送的内容，请注意查收!");
//        JSONObject jsonNotice = new JSONObject();
//        jsonNotice.put(XmlParamConstant.NOTICE,jsonObject);
//
//        JSONObject jsonObject1 = new JSONObject();
//        jsonObject1.put(XmlParamConstant.NOTICE_MOBILE,"1300000000");
//        jsonObject1.put(XmlParamConstant.PUBLISH_USER,"哈哈哈");
//        jsonObject1.put(XmlParamConstant.MESSAGE,"我时发sdsdfsdfsd!");
//        JSONObject jsonNotice1= new JSONObject();
//        jsonNotice1.put(XmlParamConstant.NOTICE,jsonObject1);
//
//        JSONObject jsonObject2 = new JSONObject();
//        jsonObject2.put(XmlParamConstant.NOTICE_MOBILE,"13281837003");
//        jsonObject2.put(XmlParamConstant.PUBLISH_USER,"哇哇");
//        jsonObject2.put(XmlParamConstant.MESSAGE,"稍等一下!");
//        JSONObject jsonNotice2 = new JSONObject();
//        jsonNotice2.put(XmlParamConstant.NOTICE,jsonObject2);
//
//        JSONObject jsonObject3 = new JSONObject();
//        jsonObject3.put(XmlParamConstant.NOTICE_MOBILE,"13281837003");
//        jsonObject3.put(XmlParamConstant.PUBLISH_USER,"上个");
//        jsonObject3.put(XmlParamConstant.MESSAGE,"我时看一看额收!");
//        JSONObject jsonNotice3 = new JSONObject();
//        jsonNotice3.put(XmlParamConstant.NOTICE,jsonObject3);
//
//        jsonArray.add(jsonNotice);
//        jsonArray.add(jsonNotice1);jsonArray.add(jsonNotice2);jsonArray.add(jsonNotice3);
//
//        String s = jsonArray.toJSONString();
//        System.out.println(s);
//
//        JSONObject resultJson = new JSONObject();
//        resultJson.put(XmlParamConstant.NOTICES,jsonArray);
//        String s1 = resultJson.toJSONString();
//        System.out.println();
//        System.out.println(s1);
//        System.out.println();
//        String xml = JsonXmlUtils.jsonToXml(resultJson,true);
//        System.out.println(xml);
//    }
}
