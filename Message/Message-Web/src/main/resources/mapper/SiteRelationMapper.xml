<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.message.mapper.NoticeMessageDOMapper">

    <select id="listTargetUserAndUnit" resultType="com.trs.gov.management.VO.UserUnitMappingVO">
        SELECT
        uum.id as id,
        uum.user_name as userName,
        uum.true_name as trueName,
        uum.email as email,
        uum.phone as phone,
        uum.unit_id as unitId,
        uum.unit_type as unitType,
        uum.unit_name as unitName,
        uum.is_master as isMaster
        FROM
        user_unit_mapping uum
        WHERE CONCAT(uum.unit_name,uum.unit_id) !=  #{dto.notUserAndUnit}
        <if test="dto.isAll != 1">
            <choose>
                <when test="dto.userUnit != null and dto.userUnit.size() > 0">
                    AND CONCAT(uum.user_name,uum.unit_id) in
                    <foreach collection="dto.userUnit" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    <if test="dto.targetUnitIds != null and dto.targetUnitIds.size() > 0">
                        or ( uum.unit_id in
                        <foreach collection="dto.targetUnitIds" item="id" index="index" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                        <if test="dto.isMaster != null and dto.isMaster != ''">
                            AND uum.is_master = #{dto.isMaster}
                        </if> )
                    </if>
                </when>
                <otherwise>
                    AND uum.unit_id in
                    <foreach collection="dto.targetUnitIds" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    <if test="dto.isMaster != null and dto.isMaster != ''">
                        AND uum.is_master = #{dto.isMaster}
                    </if>
                </otherwise>
            </choose>
        </if>
        <if test="dto.isAll == 1 and dto.isMaster != null and dto.isMaster != ''">
            AND uum.is_master = #{dto.isMaster}
        </if>
    </select>

    <select id="getUserAndUnit" resultType="com.trs.gov.management.VO.UserUnitMappingVO">
        SELECT
        uum.id as id,
        uum.user_name as userName,
        uum.true_name as trueName,
        uum.email as email,
        uum.phone as phone,
        uum.unit_id as unitId,
        uum.unit_type as unitType,
        uum.unit_name as unitName,
        uum.is_master as isMaster
        FROM
        user_unit_mapping uum
        WHERE user_name = #{userName}
        and unit_id = #{unitId}
    </select>

    <select id="getByUnit" resultType="com.trs.gov.management.VO.UserUnitMappingVO">
        SELECT
        uum.id as id,
        uum.user_name as userName,
        uum.true_name as trueName,
        uum.email as email,
        uum.phone as phone,
        uum.unit_id as unitId,
        uum.unit_type as unitType,
        uum.unit_name as unitName,
        uum.is_master as isMaster
        FROM
        user_unit_mapping uum
        WHERE unit_id = #{unitId}
        and is_master = true
    </select>



    <select id="getUnitName" resultType="string" parameterType="string">
        select unit_name from unit where id = #{unitId}
    </select>

    <select id="getDealUser" resultType="string" parameterType="long">
        select wo.deal_username from work_order wo
        where wo.id = #{id}
    </select>


    <insert id="insertBatchDOS" parameterType="list">
        INSERT INTO notice_message
        (
        `id`,
        `cr_time`,
        `cr_user`,
        `work_order_id`,
        `message_config_type`,
        `content`,
        `reveive_user_name`,
        `is_read`,
        `update_time`,
        `update_user_name`,
        `title`,
        `reveive_unit_id`,
        `update_unit_id`,
        `cr_unit_id`
        )
        VALUES
        <foreach collection="dos" item="do" separator=",">
            (
            #{do.id},
            #{do.crTime},
            #{do.crUser},
            #{do.workOrderId},
            #{do.messageConfigType},
            #{do.content},
            #{do.reveiveUserName},
            #{do.isRead},
            #{do.updateTime},
            #{do.updateUserName},
            #{do.title},
            #{do.reveiveUnitId},
            #{do.updateUnitId},
            #{do.crUnitId}
            )
        </foreach>
    </insert>


</mapper>