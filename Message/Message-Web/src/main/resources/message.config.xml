<?xml version="1.0" encoding="UTF-8"?>
<plugin name="工单消息管理配置" version="1.1">

    <operations element-class="com.trs.workorder.client.config.Operation"
                id="Operations">

        <!--工单新增-->
        <operation name="workorder.add.create">
            <disp-name>创建工单</disp-name>
            <desc>在工单消息配置管理中可配置创建工单</desc>
            <index>1</index>
            <default-value>1</default-value>
            <type>workorder.add</type>
        </operation>


        <!--工单受理-->
        <operation name="workorder.handel.xiangying">
            <disp-name>工单响应</disp-name>
            <desc>在工单消息配置管理中可配置工单响应</desc>
            <index>2</index>
            <default-value>1</default-value>
            <type>workorder.handel</type>
        </operation>
        <!--工单受理-->
        <operation name="workorder.handel.xinzengchaosong">
            <disp-name>新增抄送</disp-name>
            <desc>在工单消息配置管理中可配置新增抄送</desc>
            <index>3</index>
            <default-value>1</default-value>
            <type>workorder.handel</type>
        </operation>
        <!--工单受理-->
        <operation name="workorder.handel.jilugongshi">
            <disp-name>记录工时</disp-name>
            <desc>在工单消息配置管理中可配置记录工时</desc>
            <index>4</index>
            <default-value>1</default-value>
            <type>workorder.handel</type>
        </operation>
        <!--工单受理-->
        <operation name="workorder.handel.huifu">
            <disp-name>回复</disp-name>
            <desc>在工单消息配置管理中可配置回复</desc>
            <index>5</index>
            <default-value>1</default-value>
            <type>workorder.handel</type>
        </operation>
        <!--工单受理-->
        <operation name="workorder.handel.huitui">
            <disp-name>回退</disp-name>
            <desc>在工单消息配置管理中可配置回退</desc>
            <index>6</index>
            <default-value>1</default-value>
            <type>workorder.handel</type>
        </operation>
        <!--工单受理-->
        <operation name="workorder.handel.jiaoban">
            <disp-name>交办</disp-name>
            <desc>在工单消息配置管理中可配置交办</desc>
            <index>7</index>
            <default-value>1</default-value>
            <type>workorder.handel</type>
        </operation>
        <!--工单受理-->
        <operation name="workorder.handel.xiugai">
            <disp-name>修改</disp-name>
            <desc>在工单消息配置管理中可配置修改</desc>
            <index>8</index>
            <default-value>1</default-value>
            <type>workorder.handel</type>
        </operation>
        <!--工单受理-->
        <operation name="workorder.handel.wanchenggongdan">
            <disp-name>完成工单</disp-name>
            <desc>在工单消息配置管理中可配置完成工单</desc>
            <index>9</index>
            <default-value>1</default-value>
            <type>workorder.handel</type>
        </operation>


        <!--评价-->
        <operation name="workorder.finish.pingjia">
            <disp-name>评价</disp-name>
            <desc>在工单消息配置管理中可配置评价</desc>
            <index>10</index>
            <default-value>0</default-value>
            <type>workorder.finish</type>
        </operation>
        <!--工单完成-->
        <operation name="workorder.finish.chongxindakai">
            <disp-name>重新打开</disp-name>
            <desc>在工单消息配置管理中可配置重新打开</desc>
            <index>11</index>
            <default-value>0</default-value>
            <type>workorder.finish</type>
        </operation>


        <!--工单已评价-->
        <operation name="workorder.comment.gongkai">
            <disp-name>公开工单</disp-name>
            <desc>在工单消息配置管理中可配置公开工单</desc>
            <index>12</index>
            <default-value>0</default-value>
            <type>workorder.comment</type>
        </operation>
        <!--工单已评价-->
        <operation name="workorder.comment.bugongkai">
            <disp-name>不公开工单</disp-name>
            <desc>在工单消息配置管理中可配置不公开工单</desc>
            <index>13</index>
            <default-value>0</default-value>
            <type>workorder.comment</type>
        </operation>


        <!--通知-->
        <operation name="notice.handle.chuangjian">
            <disp-name>创建通知</disp-name>
            <desc>在通知工单消息配置管理中可配置创建通知消息</desc>
            <index>1</index>
            <default-value>1</default-value>
            <type>notice.add</type>
        </operation>
        <!--通知-->
        <operation name="notice.handle.huifu">
            <disp-name>回复通知</disp-name>
            <desc>在通知工单消息配置管理中可配置回复通知</desc>
            <index>2</index>
            <default-value>1</default-value>
            <type>notice.handle</type>
        </operation>

    </operations>

</plugin>