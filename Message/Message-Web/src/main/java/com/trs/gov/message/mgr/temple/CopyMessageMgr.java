package com.trs.gov.message.mgr.temple;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.UserConstant;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：CopyMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 15:47
 **/
@Component
@Slf4j
public class CopyMessageMgr extends AbstractSendMessageMgr {
    @Override
    public Map<String, BaseMessageVO> getMessageTemple(CreateNoticeDTO createNoticeDTO) throws Exception {
        createNoticeDTO.mustNotNull(false,false,true,false);
        BaseMessageVO copy = new BaseMessageVO();
        copy.setTitle("新协作工单");
        copy.setContent("有工单需要您的协助，请查看");
        //构造返回结果
        Map<String,BaseMessageVO> resultMap = new HashMap<>();
        resultMap.put(UserConstant.NEW_COPY_USER,copy);
        return resultMap;
    }

    @Override
    public String key() {
        return OperateNameConstant.COPY_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }
}
