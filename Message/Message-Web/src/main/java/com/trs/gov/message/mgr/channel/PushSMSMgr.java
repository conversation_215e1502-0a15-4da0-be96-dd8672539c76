package com.trs.gov.message.mgr.channel;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.message.DTO.BatchSMSContentDTO;
import com.trs.gov.message.helper.SendMessageHelper;
import com.trs.log.exception.RecordableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName：PushSMSMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/12 9:59
 **/
@Component
@Slf4j
public class PushSMSMgr {
    @Value("${sms.batch.url:http://************/smsapi/smsapi/batchSend.json}")
    private String smsBatchUrl;

    @Value("${fail.retry.send.count:2}")
    private Integer retryCount;

    private static final ThreadLocal<Integer> MAX_SEND_COUNT = new ThreadLocal<Integer>(){
        @Override
        protected Integer initialValue() {
            return 0;
        }
    };

    public void sendSMSMessage(List<BatchSMSContentDTO> dtos){
        if (CollectionUtils.isEmpty(dtos)){
            log.info("发送完毕,发送到手机短信的的数据为空!");
            return;
        }
        for (BatchSMSContentDTO dto : dtos) {
            sendSMSOfHTTP(dto);
        }
    }


    private void sendSMSOfHTTP(BatchSMSContentDTO dto){
        String jsonStr = JSONObject.toJSONString(dto);
        try {
            final Integer failureTime = MAX_SEND_COUNT.get();
            String response = SendMessageHelper.sendJsonToThirdSystemByPost(smsBatchUrl, jsonStr);
            if (dealResponse(response)){
                log.info(String.format("第[%s]次：消息体[%s]发送成功!返回结果为[%s]",failureTime+1,jsonStr,response));
            }else {
                if (failureTime < retryCount){
                    MAX_SEND_COUNT.set(failureTime + 1);
                    sendSMSOfHTTP(dto);
                }else {
                    log.error(String.format("执行了[%s]次回调推送都未成功!,消息体[%s]发送成功!返回结果为[%s]",failureTime+1,jsonStr,response));
                }
            }
            MAX_SEND_COUNT.remove();
        } catch (RecordableException e) {
            log.error(e.getMessage(),e);
        }
    }


    private boolean dealResponse(String response){
        JSONObject object = JSONObject.parseObject(response);
        String errorCode = object.getString("errorCode");
        if (!StringUtils.isEmpty(errorCode) && errorCode.equals("AllSuccess")){
            return true;
        }
        return false;
    }

}
