package com.trs.gov.message.util;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.PushToPlatformDTO;
import com.trs.gov.message.VO.SmsMessageVO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Description: 工单流转过程中短信提醒的消息同步到公众号平台 <BR>
 * Copyright: Copyright (c) 2004-2016 拓尔思信息技术股份有限公司 <BR>
 * Company: www.trs.com.cn <BR>
 *
 * <AUTHOR>
 * @version 1.0
 * @createTime 2022/4/1 14:36
 */
@Component
@Slf4j
public class PushSmsToGzhHelper {

    @Value("${fail.retry.send.count}")
    private Integer retryCount;

    @Autowired
    private BuildTokenByGzh buildTokenByGzh;

    @Value("${gzh.push.url}")
    private String pushUrl;

    @Value("${gzh.push.open}")
    private boolean pushSmsToGzhOpen;

    private HttpRequest httpRequest = new HttpRequest.Builder(new OkHttpClient.Builder()
            .connectTimeout(4, TimeUnit.SECONDS)
            .readTimeout(3, TimeUnit.SECONDS)
    ).build();

    public void pushSmsMessage(List<SmsMessageVO> result){
        boolean pushOpen = pushSmsToGzhOpen;
        if (pushOpen ==false){
            return;
        }
        try {
            List<PushToPlatformDTO> pushList = new ArrayList<>();
            for (SmsMessageVO vo: result) {
                JSONObject jsonMessage = buildRequestBodyJson(vo);
                PushToPlatformDTO pushToPlatformDTO = new PushToPlatformDTO();
                pushToPlatformDTO.setJsonMessage(jsonMessage.toJSONString());
                pushToPlatformDTO.setRequestPreUrl(pushUrl);
                pushList.add(pushToPlatformDTO);
            }
            pushMessage(pushList,"黔微政务");
        } catch (Throwable throwable) {
            log.info("推送工单流转短信内容异常：{}","推送失败");
        }
    }

    /**
     * 构建推送消息的请求体
     * @param vo
     * @return
     */
    private JSONObject buildRequestBodyJson(SmsMessageVO vo) throws ServiceException {
        String token = buildTokenByGzh.getToken().orElseThrow(()->new ServiceException("获取token失败!"));
        String bodyJsonString = "{\"msg\":[{\"noticeOrg\":[],\"noticeDepartment\":[],\"contentUrl\":\"\",\"mobileUrl\":\"\",\"noticeMobile\":[\"%s\"],\"noticeUser\":[12],\"channles\":[{\"channel\":\"gzh\",\"content\":\"%s\"}]}],\"uid\":0,\"departmentId\":0,\"orgId\":0,\"token\":\"%s\"}";
        JSONObject bodyJosn = JSONObject.parseObject(String.format(bodyJsonString, vo.getMobile(), vo.getContent(), token));
        return bodyJosn;
    }

    /**
     * @Description  重传
     * @Param [retryPush, channel]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 16:58
     **/
    public void RetryPushMessageMgr(List<PushToPlatformDTO> retryPush, String channel) throws ServiceException {
        if(retryPush.size() == 0){
            log.info(String.format("所有消息推送至第三方平台的【%s】渠道成功!",channel));
        }else{
            log.warn("总共有"+retryPush.size() + "条数据推送至"+channel+"失败!");
            int flag = 1;
            while (flag <= retryCount.intValue()){
                for (PushToPlatformDTO push : retryPush) {
                    retryBuildToken(push);
                    if(!push.getIsSuccess()){
                        log.info(String.format("请求地址【%s】,内容【%s】,开始进行第【%s】次重传!",push.getRequestPreUrl(),push.getXmlMessage(),flag));
                        pushMessage(push,channel);
                        push.setRetryCount(flag);
                        log.info("第"+flag+"次重传结束!");
                    }
                }
                flag ++ ;
            }
            //重传了最大次数还是没有发送成功!
            for (PushToPlatformDTO push : retryPush) {
                if(!push.getIsSuccess() && push.getRetryCount().equals(retryCount)){
                    log.warn(String.format("请求Url【%s】,渠道【%s】,内容【%s】,重传了【%s】次还是没有发送成功!",push.getRequestPreUrl(),channel,push.getXmlMessage(),retryCount));
                }else {
                    log.info(String.format("请求Url【%s】,渠道【%s】,内容【%s】,通过重传【%s】次后发送成功!",push.getRequestPreUrl(),channel,push.getXmlMessage(),retryCount));
                }
            }
        }
    }

    private void retryBuildToken(PushToPlatformDTO push) throws ServiceException {
        JSONObject bodyJosn = JSONObject.parseObject(push.getJsonMessage());
        String token = buildTokenByGzh.getToken().orElseThrow(()->new ServiceException("获取token失败!"));
        bodyJosn.put("token",token);
        push.setJsonMessage(bodyJosn.toJSONString());
    }

    /**
     * @Description  第一次发送
     * @Param [pushToPlatformDTOS, channel]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 16:57
     **/
    public void pushMessage(List<PushToPlatformDTO> pushToPlatformDTOS,String channel) throws ServiceException {
        if (CollectionUtils.isEmpty(pushToPlatformDTOS)){
            log.info("没有消息推送，到此结束!");
        }else {
            log.info("准备开始推送消息到渠道【"+channel+"】");
            List<PushToPlatformDTO> retryPush = new ArrayList<>();
            for (PushToPlatformDTO pushToPlatformDTO : pushToPlatformDTOS) {
                pushMessage(pushToPlatformDTO,channel);
                if(!pushToPlatformDTO.getIsSuccess()){
                    pushToPlatformDTO.setIsSuccess(false);
                    retryPush.add(pushToPlatformDTO);
                }
            }
            log.info("准推送消息到渠道【"+channel+"】结束! 并开始检验所有消息有没有推送成功！如果存在没有推送成功的消息，则准备开始重传!");
            RetryPushMessageMgr(retryPush,channel);
        }
    }

    /**
     * @Description  构建发送消息的 Http请求，并处理发送成功的结果
     * @Param [dto, channel]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 16:43
     **/
    private void pushMessage(PushToPlatformDTO dto, String channel){
        try {
            RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), dto.getJsonMessage());
            String response = httpRequest.doPost(dto.getRequestPreUrl(), body);
            log.info(String.format("渠道【%s】,推送至第三方返回结果为【%s】", channel,response));
            if (StringUtils.isNullOrEmpty(response)){
                dto.setIsSuccess(false);
            }else {
                dto.setIsSuccess(response.equals("1")?true:false);
            }
        } catch (Exception e) {
            dto.setIsSuccess(false);
            log.error(e.getMessage(),e);
        }
    }

}
