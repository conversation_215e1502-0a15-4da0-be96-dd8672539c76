package com.trs.gov.message.mgr.temple;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.UserConstant;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：ManHourMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 15:47
 **/
@Component
@Slf4j
public class ManHourMessageMgr extends AbstractSendMessageMgr {
    @Override
    public Map<String, BaseMessageVO> getMessageTemple(CreateNoticeDTO createNoticeDTO) throws Exception {
        String workOrderName = createNoticeDTO.getWorkOrderName().orElseThrow(()->new ServiceException("获取工单别名失败!"));
        createNoticeDTO.mustNotNull(true,true,false,false);
        if(StringUtils.isEmpty(createNoticeDTO.getDayTime())){
            throw new ParamInvalidException("记录的工时时间不能为空!");
        }
        BaseMessageVO create = new BaseMessageVO();
        BaseMessageVO handle = new BaseMessageVO();
        create.setTitle("工时记录");
        create.setContent(createNoticeDTO.getCurrentUserTrueName()+"在 "+workOrderName+" 记录了工时 "+ createNoticeDTO.getDayTime()+"天");
        handle.setTitle("工时记录");
        handle.setContent(createNoticeDTO.getCurrentUserTrueName()+"在 "+workOrderName+" 记录了工时 "+ createNoticeDTO.getDayTime()+"天");
        //构造返回结果
        Map<String,BaseMessageVO> resultMap = new HashMap<>();
        resultMap.put(UserConstant.CREATE_USER,create);
        resultMap.put(UserConstant.HANDLE_USER,handle);
        return resultMap;
    }

    @Override
    public String key() {
        return OperateNameConstant.MAN_HOUR_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }
}
