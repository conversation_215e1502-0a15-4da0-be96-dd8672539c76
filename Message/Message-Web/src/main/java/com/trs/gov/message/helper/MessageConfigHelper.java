package com.trs.gov.message.helper;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.VO.Operation;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import java.io.File;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：MessageconfigHelper
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/16 15:55
 **/
@Component
@Slf4j
public class MessageConfigHelper {

    private static ClassLoader classLoader = MessageTypeHelper.class.getClassLoader();

    //左边存储类别，右边存储该类别下的所有Operate类
    private static Map<String, List<Operation>> mapTypeAndOperateList = null;

    //存储所有的权限操作集合
    private static List<Operation> listOperation = null;

    //存储操作key的index,方便判断权限
    private static Map<String, Integer> operateTypeAndIndex = null;

    private void doStart() throws ServiceException {
        // 获取classpath
        String classpath = getClasspath();
        classpath = String.format("%s%s%s", classpath, File.separator, "message.config.xml");
        if (classpath != null) {
            readXML(classpath, new Operation());
        }
    }

    /**
     * @return void
     * @Description 获取配置文件的配置内容
     * @Param [classPate, operation]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/16 17:40
     **/
    public synchronized void readXML(String classPath, Operation operation) throws ServiceException {
        List<Operation> operations = new ArrayList<>();
        Map<String, List<Operation>> resultOperationList = new HashMap<>();
        try {
            ClassPathResource classPathResource = new ClassPathResource("message.config.xml");
            SAXReader saxReader = new SAXReader();
            Document document = saxReader.read(new InputStreamReader(classPathResource.getInputStream(), "UTF-8"));
            //获取父节点元素
            Element root = document.getRootElement();
            //获取权限配置父节点
            String className = operation.getClass().getSimpleName().toLowerCase();
            Method method;
            List<Element> father = document.selectNodes("//" + root.getName() + "/" + className + "s");
            if (!father.isEmpty() && father.size() > 0) {
                List<Element> operateLists = father.get(0).elements();
                if (operateLists != null) {
                    operateTypeAndIndex = operateTypeAndIndex == null ? new HashMap<>() : operateTypeAndIndex;
                    //遍历每个operate的 各个 属性
                    for (Element e : operateLists) {
                        String pKey = e.attributeValue("name");
                        List<Element> li = e.elements();
                        operation = operation.getClass().newInstance();
                        operation.setKey(pKey);
                        for (Element e2 : li) {
                            String name = e2.getName();
                            String value = e2.getText();
                            if (name.contains("-")) {
                                name = String.format("%s%s%s", name.substring(0, name.indexOf("-")), name.substring(name.indexOf("-") + 1, name.indexOf("-") + 2).toUpperCase(), name.substring(name.indexOf("-") + 2));
                            }
                            Field field = operation.getClass().getDeclaredField(name);
                            //实例的set方法
                            method = operation.getClass().getMethod("set" + name.substring(0, 1).toUpperCase() + name.substring(1), field.getType());
                            //通过反射注入值
                            if (isTargetClassType(field, String.class)) {
                                method.invoke(operation, value);
                            }
                            if (isTargetClassType(field, int.class)) {
                                method.invoke(operation, Integer.valueOf(value));
                            }
                        }
                        operateTypeAndIndex.put(pKey, operation.getIndex());
                        operations.add(operation);
                        initMapTypeAndOperateList(resultOperationList, operation);
                    }

                }
            }
        } catch (Exception e) {
            log.error("读取工单消息配置文件失败!", e);
            throw new ServiceException("读取工单消息配置文件失败!");
        }
        listOperation = operations;
        mapTypeAndOperateList = resultOperationList;

    }

    private boolean isTargetClassType(Field field, Class targetType) {
        return field.getType() == targetType;
    }

    private void initMapTypeAndOperateList(Map<String, List<Operation>> map, Operation operation) {
        String operType = operation.getType();
        if (operType == null && operType.length() == 0) {
            log.error("初始化mapTypeAndOperateList出错!");
            return;
        }
        List<Operation> temp = map.get(operType);
        if (temp == null) {
            temp = new ArrayList<>();
            map.put(operType, temp);
        }
        temp.add(operation);
    }

    /**
     * 获取classpath
     */
    public static String getClasspath() {
        String classpath = null;
        try {
            classpath = classLoader.getResource("/").getPath();
        } catch (Exception e) {
            try {
                classpath = classLoader.getResource("").getPath();
            } catch (Exception ex) {
                log.error(" classpath 初始化失败：", ex);
            }
        }
        return classpath;
    }

    public List<Operation> getListOperation(String type) throws ServiceException {
        if (mapTypeAndOperateList == null) {
            doStart();
        }
        return mapTypeAndOperateList.get(type);
    }

    public List<Operation> listAllOperation() throws ServiceException {
        if (listOperation == null) {
            doStart();
        }
        return listOperation;
    }

    public Integer getNameIndex(String typeName) throws ServiceException {
        if (operateTypeAndIndex == null) {
            doStart();
        }
        return operateTypeAndIndex.get(typeName);
    }

    public List<Operation> getDefaultValue() throws ServiceException {
        if (listOperation == null) {
            doStart();
        }
        return listOperation;
    }

}
