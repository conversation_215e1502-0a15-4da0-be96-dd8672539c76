package com.trs.gov.message.constant;

/**
 * @ClassName：PushChannelConstant
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 10:56
 **/
public class PushChannelConstant {

    public static final String REDIS_KEY_OF_TOKEN= "the_token_of_push_to_other_platforms";
    /**
     * 工单流转短信提醒推送到贵州微信公众号
     */
    public static final String REDIS_KEY_OF_TOKEN_GZH= "the_token_of_push_to_gzh_platforms_gz";

    /**
     * 发送到工单
     **/
    public static final String SEND_TO_WORKORDER = "gz_work_order";
    public static final String SEND_TO_WORKORDER_DESC = "将消息发送到工单";

    /**
     * 推送到第三方接口平台 Third party interface
     **/
    public static final String SEND_TO_OTHER_PLATFORMS = "third_party_interface";
    public static final String SEND_TO_OTHER_PLATFORMS_DESC = "推送到第三方接口平台";

    /**
     * 发送到第三方平台的微信公众号
     **/
    public static final String PUSH_TO_WXGZH = "wxgzh";
    public static final String PUSH_TO_WXGZH_DESC = "发送到第三方平台的微信公众号";

    /**
     * 发送到第三方平台的微信公众号
     **/
    public static final String PUSH_TO_SMS = "sms";
    public static final String PUSH_TO_SMS_DESC = "通过第三方平台发送短信";


    public static final String PUSH_SUCCESS = "ok";
    public static final String PUSH_FAIL = "err";

    /**
     * @Description  如果数据量比较大, INSC_SIZE 个用户为一组进行发送
     * @Param
     * @return
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/11 9:34
     **/
    public static final int INSC_SIZE = 100;

    //测试环境
    public static final String TEST_ENVIRONMENTAL = "test";
    //正式环境
    public static final String PROD_ENVIRONMENTAL = "szh";

    public static final String[] fieldNames = new String[]{TEST_ENVIRONMENTAL,PROD_ENVIRONMENTAL};

    /**
     * @Description  校验字段是否配置正确
     * @Param [fieldName]  com.trs.gov.message.mgr.PushMessageMgr#dealResponse(com.alibaba.fastjson.JSONObject)
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/15 16:58
     **/
    public static boolean checkFieldExist(String fieldName){
        for (String name : fieldNames) {
            if(name.equals(fieldName)){
                return true;
            }
        }
        return false;
    }

    /**
     * @Description  批量发送短信最多为一百个
     * @Param
     * @return
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/4/12 9:41
     **/
    public static final int SMS_BATCH_MAX_NUM = 100;


}
