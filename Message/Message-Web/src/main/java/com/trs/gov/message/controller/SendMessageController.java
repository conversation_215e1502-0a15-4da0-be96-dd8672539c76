package com.trs.gov.message.controller;

import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.service.SendNoticeMesssageService;
import com.trs.gov.message.service.impl.WebSocketService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * @ClassName：WebSocket
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/20 11:19
 **/
@RestController
@RequestMapping("/message/hasUnRead")
@Slf4j
public class SendMessageController {

    @Autowired
    private WebSocketService webSocketService;
    @Reference(check = false, timeout = 60000)
    private SendNoticeMesssageService sendNoticeMesssageService;

    /**
     * @return com.trs.web.builder.base.RestfulResults
     * @Description 获取当前用户webStocke的数据，仅内部测试，
     * @Param [NoticeMessageDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/17 10:40
     **/
    @RequestMapping("show")
    public String sendInfo(@RequestParam String userName,@RequestParam String unitId) {
        try {
            webSocketService.sendInfo(userName,unitId);
        } catch (Exception e) {
            log.error("信息发送异常!", e);
            return "信息发送异常!";
        }
        return "发送成功~";
    }

    /**
     * @Description c测试发送消息
     * @Param [noticeMessageDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/27 16:55
     **/
    @PostMapping("sendMessage")
    public RestfulResults getRecentMessage(CreateNoticeDTO createNoticeDTO) throws ServiceException {
        BaseUtils.checkDTO(createNoticeDTO);
        createNoticeDTO.setCreateUser(Arrays.asList(new TargetUserDTO("dev",132L)));
        createNoticeDTO.setHandleUser(Arrays.asList(new TargetUserDTO("sdfasdfasdfa",132L)));
        createNoticeDTO.setHostUser(Arrays.asList(new TargetUserDTO("sdfasdfasdfa",132L)));
        createNoticeDTO.setNewCopyUser(Arrays.asList(new TargetUserDTO((Long) null,13L,AssignConstant.ASSIGN_TO_GROUP),
                new TargetUserDTO((Long) null,14L,AssignConstant.ASSIGN_TO_GROUP),
                new TargetUserDTO(94L,null,AssignConstant.ASSIGN_TO_UNIT),
                new TargetUserDTO((Long) null,15L,AssignConstant.ASSIGN_TO_GROUP)));
        createNoticeDTO.setOldCopyUser(Arrays.asList(new TargetUserDTO((Long) null,13L,AssignConstant.ASSIGN_TO_GROUP),
                new TargetUserDTO((Long) null,14L,AssignConstant.ASSIGN_TO_GROUP),
                new TargetUserDTO(94L,null,AssignConstant.ASSIGN_TO_UNIT),
                new TargetUserDTO((Long) null,15L,AssignConstant.ASSIGN_TO_GROUP)));
        return sendNoticeMesssageService.sendNoticeMessage(createNoticeDTO);
    }

}
