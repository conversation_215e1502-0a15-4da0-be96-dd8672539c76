package com.trs.gov.message.config;

import com.trs.user.filter.LoginFilter;
import com.trs.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class FilterConfig {

    @Reference(check = false, timeout = 60000)
    private IUserService service;

    @Bean
    public FilterRegistrationBean registFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        LoginFilter filter=new LoginFilter(service);
        filter.setSkipFilter((request,response)->{
            String str =  request.getRequestURI();
            if(str.contains("webSocket")){
                return true;
            }
            return false;
        });
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("LoginFilter");
        registration.setOrder(1);
        return registration;
    }
}
