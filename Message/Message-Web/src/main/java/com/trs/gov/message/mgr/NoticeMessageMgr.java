package com.trs.gov.message.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.message.DO.NoticeMessageDO;
import com.trs.gov.message.DTO.NoticeMessageDTO;
import com.trs.gov.message.VO.IsReadVO;
import com.trs.gov.message.VO.WorkOrderNoticeVO;
import com.trs.gov.message.mapper.NoticeMessageDOMapper;
import com.trs.gov.message.service.impl.WebSocketService;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName：NoticeMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 10:18
 **/
@Component
@Slf4j
public class NoticeMessageMgr {
    @Autowired
    private NoticeMessageDOMapper noticeMessageDOMapper;
    @Autowired
    private WebSocketService webSocketService;

    /**
     * @return com.trs.gov.message.VO.IsReadVO
     * @Description 获取当前用户是否未读的结果
     * @Param [username]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/18 22:40
     **/
    public IsReadVO getUnReadMessage(String username,String unitId) {
        //构造返回结果
        IsReadVO isReadVO = new IsReadVO();
        try {
            //判断是否还有相关数据 noticeMessageDTO中的用户
            QueryWrapper<NoticeMessageDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("reveive_user_name", username);
            queryWrapper.eq("reveive_unit_id", unitId);
            //0-未读
            queryWrapper.eq("is_read", 0);
            int count = noticeMessageDOMapper.selectCount(queryWrapper);
            if (count > 0) {
                isReadVO.setIsExistUnReadMessage(1);
            } else {
                isReadVO.setIsExistUnReadMessage(0);
            }
        } catch (Exception e) {
            log.error("系统异常，获取当前用户有无未读通知消息失败!", e.getMessage(), e);
        }
        return isReadVO;
    }

    /**
     * @return com.trs.gov.message.VO.IsReadVO
     * @Description 更新用户的数据是否成功
     * @Param [username]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/18 20:40
     **/
    public boolean updateMessageState(NoticeMessageDTO noticeMessageDTO) {
        Try tryResult = Try.of(() -> {
            String username = ContextHelper.getLoginUser().orElseThrow(() -> new ServiceException("请登录后重试!"));
            UpdateWrapper<NoticeMessageDO> objectUpdateWrapper = new UpdateWrapper<>();
            objectUpdateWrapper.eq("reveive_user_name", noticeMessageDTO.getUserName())
                    .eq("reveive_unit_id",noticeMessageDTO.getUnitId())
                    .eq("is_read",0)
                    .ge(noticeMessageDTO.getMessageType().intValue() == 0,"cr_time",noticeMessageDTO.getSplitTime())
                    .lt(noticeMessageDTO.getMessageType().intValue() == 1,"cr_time",noticeMessageDTO.getSplitTime())
                    .set("is_read", 1).set("update_unit_id",noticeMessageDTO.getUnitId()).set("update_user_name",username);
            noticeMessageDOMapper.update(null, objectUpdateWrapper);
            webSocketService.isExistUnRead(noticeMessageDTO.getUserName(),noticeMessageDTO.getUnitId());
            return true;
        });
        if (tryResult.isFailure()) {
            log.error("更新数据状态出错",tryResult.getCause());
            return false;
        }
        return true;
    }

    /**
     * @return com.trs.gov.message.VO.IsReadVO
     * @Description h获取用户的消息列表
     * @Param [username]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/18 19:40
     **/
    public RestfulResults<List<WorkOrderNoticeVO>> getRecentMessage(NoticeMessageDTO noticeMessageDTO) {
        Page<NoticeMessageDO> page = new Page<>(noticeMessageDTO.getPageNum(), noticeMessageDTO.getPageSize());
        //构造返回结果
        List<WorkOrderNoticeVO> noticeVOS = new ArrayList<>();
        try {
            String dateTime = datetime(noticeMessageDTO.getHours());
            noticeMessageDTO.setSplitTime(dateTime);
            if (!updateMessageState(noticeMessageDTO)) {
                throw new ServiceException("后台更新消息状态失败!");
            }
            //获取当前用户的通知列表  ,后从 dao 层获取
            QueryWrapper<NoticeMessageDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("reveive_user_name", noticeMessageDTO.getUserName())
                        .eq("reveive_unit_id",noticeMessageDTO.getUnitId());
            queryWrapper.orderByDesc("cr_time");
            if (noticeMessageDTO.getMessageType().intValue() == 0) {
                queryWrapper.ge("cr_time", noticeMessageDTO.getSplitTime());
            } else {
                queryWrapper.lt("cr_time", noticeMessageDTO.getSplitTime());
            }
            Page<NoticeMessageDO> noticeMessageDOPage = noticeMessageDOMapper.selectPage(page, queryWrapper);
            if(noticeMessageDOPage.getRecords() == null || noticeMessageDOPage.getRecords().size() == 0){
                return RestfulResults.ok(noticeVOS).addMsg("数据为空!");
            }
            List<NoticeMessageDO> messageDOS = noticeMessageDOPage.getRecords();
            for (NoticeMessageDO noticeMessageDO : messageDOS) {
                WorkOrderNoticeVO workOrderNoticeVO = new WorkOrderNoticeVO();
                workOrderNoticeVO.setWorkOrderId(noticeMessageDO.getWorkOrderId());
                workOrderNoticeVO.setContent(noticeMessageDO.getContent());
                workOrderNoticeVO.setTitle(noticeMessageDO.getTitle());
                workOrderNoticeVO.setCrTime(noticeMessageDO.getCrTime());
                noticeVOS.add(workOrderNoticeVO);
            }
            return RestfulResults.ok(noticeVOS)
                    .addTotalCount(noticeMessageDOPage.getTotal())
                    .addPageNum(noticeMessageDTO.getPageNum())
                    .addPageSize(noticeMessageDTO.getPageSize())
                    .addMsg("获取数据成功!");
        } catch (Exception e) {
            log.error("系统异常，获取消息列表出错!", e.getMessage(), e);
            return RestfulResults.error("系统异常，获取消息列表出错!");
        }

    }

    public String datetime(Integer hours) {
        Long nowDae = (new Date()).getTime();
        Long timeNum = nowDae - hours * 60 * 60 * 1000L;
        Date date = new Date(timeNum);
        return format(date, "yyyy-MM-dd HH:mm:ss");

    }

    public static String format(Date date, String pattern) {
        Instant instant = date.toInstant();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return localDateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

}
