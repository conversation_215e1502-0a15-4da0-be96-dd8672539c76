package com.trs.gov.message.helper;

import java.util.*;

/**
 * @ClassName：OrderProperties
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/16 12:45
 **/
public class OrderRootProperties extends Properties {

    //有序
    private static LinkedHashSet<Object> keys = new LinkedHashSet<>();

    @Override
    public Set<String> stringPropertyNames() {
        Set<String> set = new LinkedHashSet<>();
        for(Object key : keys){
            set.add((String)key);
        }
        return set;
    }

    @Override
    public synchronized Enumeration<Object> keys() {
        return Collections.enumeration(keys);
    }

    @Override
    public synchronized Object put(Object key, Object value) {
        keys.add(key);
        return super.put(key, value);
    }

    @Override
    public Set<Object> keySet() {
        return keys;
    }
}
