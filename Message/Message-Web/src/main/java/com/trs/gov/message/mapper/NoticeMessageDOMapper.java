package com.trs.gov.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.management.VO.UserUnitMappingVO;
import com.trs.gov.message.DO.NoticeMessageDO;
import com.trs.gov.message.DTO.UserUnitMappingSearchDTO;
import com.trs.user.VO.UserVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName：NoticeMessageDOMapper
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 22:15
 **/
@Component
public interface NoticeMessageDOMapper extends BaseMapper<NoticeMessageDO> {

    List<UserUnitMappingVO> listTargetUserAndUnit(@Param("dto") UserUnitMappingSearchDTO dto);

    UserUnitMappingVO getUserAndUnit(@Param("userName")String userName,@Param("unitId")Long unitId);

    UserUnitMappingVO getByUnit(@Param("unitId")Long unitId);

    int insertBatchDOS(@Param("dos")List<NoticeMessageDO> dos);

    String getUnitName(@Param("unitId")String unitId);

    String getDealUser(@Param("id")Long id);


}
