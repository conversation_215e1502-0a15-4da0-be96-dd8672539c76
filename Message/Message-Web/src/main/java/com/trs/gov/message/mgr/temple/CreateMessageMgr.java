package com.trs.gov.message.mgr.temple;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.UserConstant;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：CreateMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 15:36
 **/
@Component
@Slf4j
public class CreateMessageMgr extends AbstractSendMessageMgr {
    @Override
    public Map<String, BaseMessageVO> getMessageTemple(CreateNoticeDTO createNoticeDTO) throws Exception {
        String workOrderName = createNoticeDTO.getWorkOrderName().orElseThrow(()->new ServiceException("获取工单别名失败!"));
        createNoticeDTO.mustNotNull(true,true,false,true);
        BaseMessageVO create = new BaseMessageVO();
        BaseMessageVO handle = new BaseMessageVO();
        BaseMessageVO newCopyUser = new BaseMessageVO();
        BaseMessageVO host = new BaseMessageVO();
        create.setTitle("工单创建成功");
        create.setContent("您的 "+workOrderName+" 已经创建成功!");
        handle.setTitle("收到新工单");
        handle.setContent("您收到一条新工单，请查收");
        newCopyUser.setTitle("新协作工单");
        newCopyUser.setContent("有工单需要您的协作，请查收");
        host.setTitle("工单创建功能");
        host.setContent("您主办的 "+workOrderName+" 已创建成功");
        //构造返回结果
        Map<String,BaseMessageVO> resultMap = new HashMap<>();
        resultMap.put(UserConstant.CREATE_USER,create);
        resultMap.put(UserConstant.HANDLE_USER,handle);
        resultMap.put(UserConstant.NEW_COPY_USER,newCopyUser);
        resultMap.put(UserConstant.HOST_USER,host);
        return resultMap;
    }

    @Override
    public String key() {
        return OperateNameConstant.CREATE_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }
}
