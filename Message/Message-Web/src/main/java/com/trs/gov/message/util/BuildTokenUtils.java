package com.trs.gov.message.util;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.http2.HttpRequest;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.message.constant.PushChannelConstant;
import com.trs.gov.message.constant.XmlParamConstant;
import com.trs.log.exception.RecordableException;
import com.trs.tcache.util.redis.JedisPoolUtils;
import com.trs.web.builder.util.DESUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import org.dom4j.DocumentException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName：BuildTokenUtils
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 13:21
 **/
@Component
@Slf4j
public class BuildTokenUtils {
    @Value("${push.user-name:szh_tes_user}")
    private String userName;
    @Value("${push.auth-code:WOi3PjVsb8JYFSz7}")
    private String authCode;
    @Value("${token.expiration.time:600}")
    private Integer expireTime;
    @Value("${token.expiration.index:10}")
    private Integer index;
    @Value("${token.url:http://**************:8082/index.php/token}")
    private String tokenUrl;

    private HttpRequest httpRequest = new HttpRequest.Builder(new OkHttpClient.Builder()
            .connectTimeout(4, TimeUnit.SECONDS)
            .readTimeout(3, TimeUnit.SECONDS)
    ).build();

    /**
     * @Description  根据 |当前时间|用户名|授权密码 获取 key
     * @Param []
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/10 17:06
     **/
    private String getKey(Date nowDate) throws ServiceException, NoSuchAlgorithmException, RecordableException, DocumentException {
        //得到时间格式
        String dateFormat = TimeUtils.dateToString(nowDate,TimeUtils.YYYYMMDD_HHMMSS);
        //获取 token 请求的client_time
        String key = getTokenKey(dateFormat);
        //获取 token 的 xml请求体
        String xml = getXmlBody(dateFormat,key);
        //发送请求
        String tokenXml = getTokenXml(xml);
        //处理返回结果得到Token
        return getToken(tokenXml);
    }

    /**
     * @Description  从 Http 请求结果中得到 Token
     * @Param [response]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 17:25
     **/
    private String getToken(String response) throws DocumentException, ServiceException {
        JSONObject jsonObject = JsonXmlUtils.xmlToJson(response,true);
        JSONObject json = jsonObject.getJSONObject(XmlParamConstant.RESULT);
        String status = json.getString(XmlParamConstant.STATUS);
        if(status.equalsIgnoreCase("ok")){
            return json.getString(XmlParamConstant.TOKEN);
        }else{
            throw new ServiceException("获取token失败! response:"+response);
        }
    }

    /**
     * @Description  获取能够得到token的XML请求体
     * @Param [xml]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 17:24
     **/
    private String getTokenXml(String xml) throws RecordableException {
        RequestBody body = RequestBody.create(MediaType.parse("application/xml; charset=utf-8"), xml);
        String response = httpRequest.doPost(tokenUrl, body);
        return response;
    }

    /**
     * @Description  获取XML de client—time
     * @Param [dateFormat]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 17:23
     **/
    private String getTokenKey(String dateFormat) throws NoSuchAlgorithmException, ServiceException {
        if(CMyString.isEmpty(userName)){
            throw new ServiceException("用户名不能为空!");
        }
        if(CMyString.isEmpty(authCode)){
            throw new ServiceException("授权密码不能为空!");
        }
        return DESUtils.stringToMD5(String.format("|%s|%s|%s", dateFormat, userName, authCode));
    }

    /**
     * @Description  获取Token 的  XML的请求体
     * @Param [dateFormat, key]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 17:23
     **/
    private String getXmlBody(String dateFormat,String key){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(XmlParamConstant.CLIENT_TIME,dateFormat);
        jsonObject.put(XmlParamConstant.USERNAME,userName);
        jsonObject.put(XmlParamConstant.KEY,key);
        JSONObject jsonNotice = new JSONObject();
        jsonNotice.put(XmlParamConstant.MEMBER,jsonObject);
        return JsonXmlUtils.jsonToXml(jsonNotice,true);
    }

    /**
     * @Description  构建一个唯一且一直不变的 Key
     * @Param []
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:19
     **/
    private String buildRedisKey(){
        return PushChannelConstant.REDIS_KEY_OF_TOKEN;
    }

    /**
     * @Description  获取 token
     * @Param []
     * @return java.util.Optional<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:26
     **/
    public Optional<String> getToken() throws ServiceException {
        JedisPoolUtils jedisPoolUtils = JedisPoolUtils.newBuilder();
        String token = jedisPoolUtils.get(buildRedisKey(),index);
        if(CMyString.isEmpty(token)){
            try {
                long time1 = System.currentTimeMillis();
                token = getKey(new Date(time1));
                long differ = (System.currentTimeMillis()  - time1) / 1000;
                //多减一秒，尽可能消除redis自身的影响
                Integer expireSecond = expireTime - (int)differ - 1;
                jedisPoolUtils.set(buildRedisKey(),token,expireSecond,index);
            } catch (Exception e) {
                log.error("获取token时出错！",e.getMessage(),e);
                throw new ServiceException("获取token时出错！");
            }
        }
        return Optional.of(token);
    }

//     供接口验证使用
    public static void main(String[] args) throws NoSuchAlgorithmException {
        String s = DESUtils.stringToMD5("Gzszfwzjyh01@");
        System.out.println(s);

    }

}
