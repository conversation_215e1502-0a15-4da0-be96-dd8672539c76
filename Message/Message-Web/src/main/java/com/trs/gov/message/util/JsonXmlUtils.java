package com.trs.gov.message.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.SAXReader;
import org.dom4j.io.XMLWriter;

import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName：XmlUtils
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 11:31
 **/
public class JsonXmlUtils {
    /**
     * utf-8 格式  <?xml version="1.0" encoding="UTF-8"?>
     */
    private static final String XML_ENCODING = "UTF-8";

    /**
     * @Description  JSONObject 转换成 XML
     * @Param [jsonObject]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 14:20
     **/
    public static String jsonToXml(JSONObject json,boolean isNotArray){
        return jsonToDocument(json,isNotArray).asXML();
    }
    /**
     * @Description  是否需要输出格式,主要用于数据写入文件的使用，prettyFormat为true
     * @Param [json, prettyFormat]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:02
     **/
    public static String jsonToXml(JSONObject json,Boolean isNotArray,Boolean prettyFormat) throws IOException {
        Document document = jsonToDocument(json,isNotArray);
        if(!prettyFormat){
            return document.asXML();
        }
        OutputFormat format = OutputFormat.createPrettyPrint();
        // 默认 XML 格式默认缩进两个" "
        StringWriter formatXml = new StringWriter();
        XMLWriter writer = new XMLWriter(formatXml, format);
        writer.write(document);
        return formatXml.toString();
    }
    /**
     * @Description  将 XML 转换成 JSONObject
     * @Param [xml]
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:26
     **/
    public static  JSONObject xmlToJson(String xml,boolean isNotArray) throws DocumentException {
        JSONObject json = new JSONObject();
        Document document = (new SAXReader()).read(new StringReader(xml));
        Element rootElement = document.getRootElement();
        if(isNotArray){
            json.put(rootElement.getName(),elementToJson(rootElement,isNotArray));
        }else {
            json.put(rootElement.getName(),elementToJson(rootElement));
        }

        return json;
    }
    /**
     * @Description  将 XML 转化成 json 字符串
     * @Param [xml]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:29
     **/
    public static String xmlToJsonString(String xml,boolean isNotArray) throws DocumentException {
        return JSONObject.toJSONString(xmlToJson(xml,isNotArray));
    }
    /**
     * @Description  获取 文件 的 内容
     * @Param [filePath]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:42
     **/
    public static String fileToString(String filePath) throws IOException {
        return IOUtils.toString(Paths.get(filePath).toUri(), XML_ENCODING);
    }
    /**
     * @Description  将 内容 写入 文件
     * @Param [content, filePath]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:44
     **/
    public static void stringToFile(String content,String filePath) throws IOException {
        FileUtils.writeStringToFile(new File(filePath),content,XML_ENCODING);
    }

    /**
     * @Description  JSONObject 转换成 Document
     *               XML格式的串最外层root 只有一个,如果出现是丢失时因为传入的格式问题
     * @Param [json]
     * @return org.dom4j.Document
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:04
     **/
    private static Document jsonToDocument(JSONObject json,boolean isNotArray){
        Document document = DocumentHelper.createDocument();
        document.setXMLEncoding(XML_ENCODING);
        //XML格式的串最外层root 只有一个,如果出现是丢失时因为传入的格式问题,直接结束,剩下的递归获取
        for (String rootKey : json.keySet()){
            if(isNotArray){
                Element element = jsonToElement(json.getJSONObject(rootKey), rootKey);
                document.add(element);
            }else{
                Element root=document.addElement(rootKey);
                List<Element> elements = jsonToElement(json.getJSONArray(rootKey), rootKey);
                elements.forEach(element -> root.add(element));
            }
            break;
        }
        return document;
    }
    /**
     * @Description  递归获取子元素信息并返回，添加带父节点上
     * @Param [json, nodeName]
     * @return org.dom4j.Element
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:04
     **/
    private static List<Element> jsonToElement(JSONArray json, String nodeName){
        List<Element> elementList = new ArrayList<>();

        for (int i = 0; i < json.size(); i++) {
            Element node = null;
            JSONObject jsonObject = json.getJSONObject(i);
            for (String key : jsonObject.keySet()) {
                node = DocumentHelper.createElement(key);
                Object object = jsonObject.get(key);
                // 如果还存在，则继续递归下去,否则返回当前节点
                if(object instanceof JSONObject){
                    elementList.add(jsonToElement(jsonObject.getJSONObject(key), key));
                }else{
                    Element element = DocumentHelper.createElement(key);
                    element.setText(jsonObject.getString(key));
                    node.add(element);
                    elementList.add(node);
                }
            }

        }
        return elementList;
    }

    private static Element jsonToElement(JSONObject json,String nodeName){
        Element node = DocumentHelper.createElement(nodeName);
        for (String key : json.keySet()) {
            Object object = json.get(key);
            // 如果还存在，则继续递归下去,否则返回当前节点
            if(object instanceof JSONObject){
                node.add(jsonToElement(json.getJSONObject(key),key));
            }else{
                Element element = DocumentHelper.createElement(key);
                element.setText(json.getString(key));
                node.add(element);
            }
        }
        return node;
    }
    /**
     * @Description  1.传入参数父Element
     *               2.遍历该节点下的节点
     *                   如果为空，则直接返回当前节点
     *                   如果不为空，继续向下添加
     * @Param [element]
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:24
     **/
    private static JSONArray elementToJson(Element element){
        JSONArray jsonArray = new JSONArray();
        for (Object child : element.elements()) {
            JSONObject json = new JSONObject();
            Element e = (Element) child;
            //如果已经为空了，直接返回即可，反之继续向下递归
            if(e.elements().isEmpty()){
                json.put(e.getName(),e.getText());
            }else{
                json.put(e.getName(),elementToJson(e,true));
            }
            jsonArray.add(json);
        }
        return jsonArray;
    }
    private static JSONObject elementToJson(Element element,boolean isNotArray){
        JSONObject json = new JSONObject();
        for (Object child : element.elements()) {
            Element e = (Element) child;
            //如果已经为空了，直接返回即可，反之继续向下递归
            if(e.elements().isEmpty()){
                json.put(e.getName(),e.getText());
            }else{
                json.put(e.getName(),elementToJson(e));
            }
        }
        return json;
    }

//    public static void main(String[] args) throws IOException, DocumentException {
//        String str = "{\"member\":{\"client-time\":\"2014-07-25 12:23:32\",\"key\":{\"value\":\"aabbccddeeff0011223344\"},\"username\":\"yj\"}}";
//        JSONObject json = JSONObject.parseObject(str);
//        String xml = jsonToXml(json);
//        System.out.println("----------------无格式的XML---------------");
//        System.out.println(xml);
//        System.out.println("----------------无格式的XML---------------");
//        System.out.println(jsonToXml(json,false));
//        System.out.println("----------------有格式的XML---------------");
//        System.out.println(jsonToXml(json,true));
//        System.out.println("----------------将xml转化成Json字符串数组---------------");
//        System.out.println(xmlToJson(xml));
//        System.out.println("----------------获取文件内容为---------------");
//        String content = fileToString("D:\\application\\XML\\test.xml");
//        System.out.println(content);
//        System.out.println("----------------将文件内容写入空文件test1.xml---------------");
//        String filePath = "D:\\application\\XML\\test1.xml";
//        stringToFile(content,filePath);
//        System.out.println(fileToString(filePath));
//    }
}
