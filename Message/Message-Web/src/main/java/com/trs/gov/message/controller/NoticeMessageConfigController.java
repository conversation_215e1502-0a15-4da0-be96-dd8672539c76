package com.trs.gov.message.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.message.DTO.MessageConfigDTO;
import com.trs.gov.message.service.impl.NoticeMessageConfigServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：NoticeMessageConfigController
 * @Description :  工单消息配置相关信息
 * <AUTHOR> YangXin
 * @Date 2020/9/17 20:09
 **/
@RestController
@RequestMapping("/message/message")
@Api(value = "工单消息配置类",tags = "NoticeMessageConfigController")
public class NoticeMessageConfigController {
    @Autowired
    private NoticeMessageConfigServiceImpl noticeMessageConfigService;

    /**
     * @Description  获取配置列表
     * @Param [messageConfigDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.message.VO.MessageConfigVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/17 10:40
     **/
    @PostMapping("get")
    @ApiOperation(value = "获取工单配置类")
    @ApiImplicitParam(name = "workOrderTypeId",value = "工单类型Id",required = true)
    public RestfulResults listMessageConfig(MessageConfigDTO messageConfigDTO) throws ServiceException {
        BaseUtils.checkDTO(messageConfigDTO);
        return noticeMessageConfigService.listMessageConfig(messageConfigDTO);
    }

    /**
     * @Description  保存配置列表
     * @Param [messageConfigDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/17 10:40
     **/
    @ApiOperation(value = "保存工单类型配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "workOrderTypeId",value = "工单类型Id",required = true,type = "long"),
            @ApiImplicitParam(name = "followParentConfig",value = "工单类型Id",required = true,type = "integer"),
            @ApiImplicitParam(name = "messageConfig",value = "工单类型Id",required = false,type = "string")
    })
    @PostMapping("save")
    public RestfulResults saveMessageConfig(MessageConfigDTO messageConfigDTO) throws ServiceException {
        BaseUtils.checkDTO(messageConfigDTO);
        return noticeMessageConfigService.saveMessageConfig(messageConfigDTO);
    }

}
