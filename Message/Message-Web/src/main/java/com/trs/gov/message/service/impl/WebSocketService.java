package com.trs.gov.message.service.impl;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.message.service.NoticeMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName：WebSocket
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/18 10:19
 **/
@ServerEndpoint(value = "/webSocket/{unitId}/{username}")
@Component
@Slf4j
public class WebSocketService {

    /**
     * 线程安全
     */
    private static ConcurrentHashMap<String, WebSocketService> concurrentHashMap = new ConcurrentHashMap<>();
    /**
     * 与某个客户端的连接会话
     */
    private Session session;

    /**
     * 相关socket的Key
     */
    private String socketKey;


    private static NoticeMessageService noticeMessageService;

    @Autowired
    public void setNoticeMessageService(NoticeMessageServiceImpl noticeMessageService) {
        WebSocketService.noticeMessageService = noticeMessageService;
    }

    /**
     * 连接成功调用的方法
     */
    @OnOpen
    public void onOpen(@PathParam(value = "unitId") String unitId,@PathParam(value = "username") String username, Session session) throws IOException, ServiceException {
        if (!CMyString.isEmpty(username)) {
            username = URLDecoder.decode(username, "UTF-8").trim();
        }
        if (!CMyString.isEmpty(unitId)) {
            unitId = URLDecoder.decode(unitId, "UTF-8").trim();
        }
        this.session = session;
        this.socketKey = makeSocketKey(username,unitId,session);
        if (concurrentHashMap != null && concurrentHashMap.contains(socketKey)) {
            concurrentHashMap.remove(socketKey);
        }
        concurrentHashMap.put(socketKey, this);
        log.info("有用户名为[" + username + "]单位为["+unitId+"]的新用户加入!");
        isExistUnRead(username,unitId);
    }

    private String makeSocketKey(String username,String unitId, Session session) {
        return String.format("%s:%s:%s", username,unitId, session.getId());
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        concurrentHashMap.remove(socketKey);
    }

    /**
     * 收到客户端消息后调用的方法
     *
     */
    @OnMessage
    public void onMessage(String message, Session session) throws IOException {

        log.info("来自客户端[" + message + "]的消息!");
        try {
            //message:[unitId,username]
            if(!CMyString.isEmpty(message)){
                message = URLDecoder.decode(message, "UTF-8");
                String[] split = message.split(",");
                if(split.length != 2){
                    throw new ServiceException("WebSocket从前端获取来的信息格式不正确");
                }
                isExistUnRead(split[1],split[0]);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 发生错误时调用
     *
     * @OnError
     */
    public void onError(Session session, Throwable error) {
        log.error("WebSocket异常！", error);
    }

    /**
     * 发送消息
     */
    public void sendMessage(String result) throws IOException {
        this.session.getBasicRemote().sendText(result);
    }

    public void isExistUnRead(String username,String unitId) throws IOException, ServiceException {
        //加冒号，防止 杨鑫:12   杨鑫124 的情况
        String key = String.format("%s:%s", username,unitId)+":";
        for (Map.Entry<String, WebSocketService> entry : concurrentHashMap.entrySet()) {
            if (entry.getKey().startsWith(key)) {
                //给在线的人发送，没有在线的通过登录后前端调用获取
                String isexist = noticeMessageService.getUnReadMessage(username,unitId);
                log.info("准备给用户【用户名:["+username+"]单位["+unitId+"]】发送消息[isexist]:"+isexist);
                entry.getValue().sendMessage(isexist);
            }
        }
    }

    /**
     * @Description : 群发给在线所有人消息
     */
    public void sendMessageToAllUser(){
        try {
            for (Map.Entry<String, WebSocketService> entry : concurrentHashMap.entrySet()) {
                String[] split = entry.getKey().split(":");
                String userName = split[0];
                String unitId = split[1];
                String isexist = noticeMessageService.getUnReadMessage(userName,unitId);
                log.info("准备给用户【用户名:["+userName+"]单位["+unitId+"]】发送消息[isexist]:"+isexist);
                entry.getValue().sendMessage(isexist);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * @Description : 群发自定义消息
     */
    public void sendInfo(String userName,String unitId){
        try {
            String key = String.format("%s:%s", userName,unitId)+":";
            for (Map.Entry<String, WebSocketService> entry : concurrentHashMap.entrySet()) {
                if (entry.getKey().startsWith(key)) {
                    String isexist = noticeMessageService.getUnReadMessage(userName,unitId);
                    log.info("准备给用户【用户名:["+userName+"]单位["+unitId+"]】发送消息[isexist]:"+isexist);
                    entry.getValue().sendMessage(isexist);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
