package com.trs.gov.message.mgr.channel;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.BatchSMSContentDTO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.VO.SmsMessageVO;
import com.trs.gov.message.mgr.AbstractDifferentChanelMgr;
import com.trs.gov.message.mgr.AbstractPushMessageMgr;
import com.trs.gov.message.mgr.AbstractSMSMgr;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：PushToSMSMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/9 11:44
 **/
@Component
@Slf4j
public class SendToSMSPlatformMgr extends AbstractDifferentChanelMgr {

    @Autowired
    private List<AbstractSMSMgr> abstractSMSMgr;

    @Autowired
    private PushSMSMgr pushSMSMgr;


    @Override
    public void SendMesaageByChannel(CreateNoticeDTO dto, Map<String, BaseMessageVO> templeMessage) {
        log.info("进入请求体内容为:"+ JSONObject.toJSONString(dto));
        List<BatchSMSContentDTO> batchSMSContentDTOS = null;
        try {
            for (AbstractSMSMgr smsMgr : abstractSMSMgr) {
                if (dto.getMessageConfigType().equals(smsMgr.key())){
                    // 返回要发送短信的用户
                    batchSMSContentDTOS = smsMgr.buildBatchSMS(smsMgr.pushSMSMessage(dto));
                    log.info("准备发送的消息体信息为:"+JSONObject.toJSONString(batchSMSContentDTOS));
                    // 去做具体的发送消息
                    pushSMSMgr.sendSMSMessage(batchSMSContentDTOS);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("第三方平台平台出现错误!  "+e.getMessage(),e);
        }
    }

    @Override
    public String key() {
        return "sms_platform";
    }

    @Override
    public String desc() {
        return "短信平台";
    }
}
