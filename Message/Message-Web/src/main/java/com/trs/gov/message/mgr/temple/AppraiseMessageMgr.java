package com.trs.gov.message.mgr.temple;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.UserConstant;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：AppraiseMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 15:56
 **/
@Component
@Slf4j
public class AppraiseMessageMgr extends AbstractSendMessageMgr {
    @Override
    public Map<String, BaseMessageVO> getMessageTemple(CreateNoticeDTO createNoticeDTO) throws Exception {
        String workOrderName = createNoticeDTO.getWorkOrderName().orElseThrow(()->new ServiceException("获取工单别名失败!"));
        createNoticeDTO.mustNotNull(true,true,false,true);
        BaseMessageVO create = new BaseMessageVO();
        BaseMessageVO handle = new BaseMessageVO();
        BaseMessageVO copy = new BaseMessageVO();
        BaseMessageVO host = new BaseMessageVO();
        create.setTitle("工单已评价");
        create.setContent(workOrderName+" 已完成评价,请查看评论信息");
        handle.setTitle("工单已评价");
        handle.setContent(workOrderName+" 已完成评价,请查看评论信息");
        copy.setTitle("工单已评价");
        copy.setContent(workOrderName+" 已完成评价,请查看评论信息");
        host.setTitle("工单已评价");
        host.setContent(workOrderName+" 已完成评价");
        //构造返回结果
        Map<String,BaseMessageVO> resultMap = new HashMap<>();
        resultMap.put(UserConstant.CREATE_USER,create);
        resultMap.put(UserConstant.HANDLE_USER,handle);
        resultMap.put(UserConstant.OLD_COPY_USER,copy);
        resultMap.put(UserConstant.HOST_USER,host);
        return resultMap;
    }

    @Override
    public String key() {
        return OperateNameConstant.APPRAISE_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }
}
