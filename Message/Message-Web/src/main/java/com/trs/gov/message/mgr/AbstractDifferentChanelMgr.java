package com.trs.gov.message.mgr;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.constant.UserConstant;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName：AbstractDifferentChanelMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 10:46
 **/
@Component
public abstract class AbstractDifferentChanelMgr implements IDifferentChannel{

    /**
     * @Description  虎丘对应类型的用户消息
     * @Param [key, dto]
     * @return java.util.List<com.trs.gov.message.DTO.UserDTO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 14:53
     **/
    public List<TargetUserDTO> listUserDto(String key, CreateNoticeDTO dto) throws ServiceException {
        if (key.equals(UserConstant.CREATE_USER)) {
            return dto.getCreateUser();
        } else if (key.equals(UserConstant.HANDLE_USER)) {
            return dto.getHandleUser();
        } else if (key.equals(UserConstant.OLD_COPY_USER)) {
            return dto.getOldCopyUser();
        } else if (key.equals(UserConstant.HOST_USER)) {
            return dto.getHostUser();
        } else if (key.equals(UserConstant.NEW_COPY_USER)){
            return dto.getNewCopyUser();
        }else {
            throw new ServiceException("未知类型的type[" + key + "]");
        }
    }

}
