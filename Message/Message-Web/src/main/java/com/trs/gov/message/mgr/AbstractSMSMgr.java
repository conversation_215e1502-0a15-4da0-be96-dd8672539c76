package com.trs.gov.message.mgr;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.service.UnitService;
import com.trs.gov.message.DTO.BatchSMSContentDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.SmsMessageVO;
import com.trs.gov.message.constant.PushChannelConstant;
import com.trs.gov.message.helper.SMSMessageHelper;
import com.trs.gov.message.mapper.NoticeMessageDOMapper;
import com.trs.gov.message.util.PushSmsToGzhHelper;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.builder.util.DESUtils;
import com.trs.web.restful.RestfulJsonHelper;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName：AbstractSMSMgr
 * @Description : 处理各个渠道，并在这里提供公共的服务
 * <AUTHOR> YangXin
 * @Date 2021/4/9 11:45
 **/
@Slf4j
@Component
public abstract class AbstractSMSMgr implements IPushSMSMgr{

    @Value("${sms.account:ys20100026}")
    private String account;

    @Value("${sms.password:Gzszfwzjyh01@}")
    private String password;

    @Reference(check = false, timeout = 60000)
    private UnitService unitService;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Autowired
    protected SMSMessageHelper smsMessageHelper;

    @Autowired
    protected NoticeMessageDOMapper noticeMessageDOMapper;

    @Autowired
    public PushSmsToGzhHelper pushSmsToGzhHelper;

    public List<BatchSMSContentDTO> buildBatchSMS(List<SmsMessageVO> messageVOS) throws ServiceException {
        List<BatchSMSContentDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(messageVOS)){
            return result;
        }
        pushSmsToGzhHelper.pushSmsMessage(messageVOS);
        String md5Password = getMD5password(password).orElseThrow(()->new ServiceException("将密码"+password+"通过MD5加密时失败!"));
        if (messageVOS.size() <= PushChannelConstant.SMS_BATCH_MAX_NUM){
            result.add(new BatchSMSContentDTO(account,md5Password,messageVOS));
            return result;
        }
        double count = messageVOS.size()/(PushChannelConstant.SMS_BATCH_MAX_NUM * 1.0);
        for (int i = 0; i < count;  i ++) {
            int k = i*PushChannelConstant.SMS_BATCH_MAX_NUM;
            if( (messageVOS.size() - k) <= PushChannelConstant.SMS_BATCH_MAX_NUM){
                BatchSMSContentDTO batchSMSContentDTO = new BatchSMSContentDTO(account, md5Password, messageVOS.subList(k, messageVOS.size()));
                result.add(batchSMSContentDTO);

            }else {
                int maxIndex = (i+1)*PushChannelConstant.SMS_BATCH_MAX_NUM;
                BatchSMSContentDTO batchSMSContentDTO = new BatchSMSContentDTO(account, md5Password, messageVOS.subList(k, maxIndex));
                result.add(batchSMSContentDTO);
            }
        }
        return result;
    }

    /**
     * @Description 获取MD5加密后的密码
     * @Param [password]
     * @return java.util.Optional<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/4/12 9:48
     **/
    private Optional<String> getMD5password(String password){
        try {
            return Optional.ofNullable(DESUtils.stringToMD5(password));
        } catch (NoSuchAlgorithmException e) {
            log.error("将密码通过MD5加密时失败!");
            return Optional.empty();
        }
    }

    /**
     * @Description  获取当前登录单位的详情
     * @Param []
     * @return com.trs.gov.management.VO.UnitVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/4/9 10:42
     **/
    public String getLoginUnitVO(String unitId) throws ServiceException {
        if (StringUtils.isEmpty(unitId)){
            throw new ServiceException("dto中的unitId为null");
        }
        return noticeMessageDOMapper.getUnitName(unitId);
    }

    /**
     * @return java.util.Optional<com.trs.user.VO.UserVO>
     * @Description 根据用户名获取用户
     * @Param [username]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/25 17:48
     **/
    public Optional<UserVO> getUserVO(String username) throws ServiceException {
        Try<Optional<UserVO>> tyrResult = Try.of(() -> {
            com.trs.user.DTO.UserDTO userDTO = new com.trs.user.DTO.UserDTO();
            userDTO.setUserName(username);
            UserVO userVO = userService.getBaseUserInfoByUserName(userDTO);
            return Optional.ofNullable(userVO);
        });
        if (tyrResult.isFailure()) {
            log.error("获取用户信息失败! ", tyrResult.getCause());
            throw new ServiceException("系统异常,从其它服务查询用户【" + username + "】的数据失败!");
        }
        return tyrResult.get();
    }

    public Optional<UserVO> getUserVO(TargetUserDTO dto) throws ServiceException {
        if (StringUtils.isEmpty(dto.getTrueName()) && StringUtils.isEmpty(dto.getPhone())){
            return getUserVO(dto.getUserName());
        }
        // 如果是已经处理过的数据则不需要再进行处理
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(dto,userVO);
        return Optional.ofNullable(userVO);
    }

}
