package com.trs.gov.message.mgr.channel;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.PushChannelConstant;
import com.trs.gov.message.mgr.AbstractDifferentChanelMgr;
import com.trs.gov.message.mgr.AbstractPushMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：SendToOtherPlatformsMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 10:51
 **/
@Component
@Slf4j
public class SendToOtherPlatformsMgr extends AbstractDifferentChanelMgr {
    @Autowired
    private List<AbstractPushMessageMgr> pushMessageMgrs;
    @Value("${thirdPartyInterface.of.channels}")
    private String otherPlantForms;

    @Override
    public void SendMesaageByChannel(CreateNoticeDTO dto, Map<String, BaseMessageVO> templeMessage) {
        try {
            if(CMyString.isEmpty(otherPlantForms)){
                log.warn("麻烦确认配置 thirdPartyInterface.of.channels !");
            }else {
                String[] channels = otherPlantForms.split(",");
                for (AbstractPushMessageMgr pushMessageMgr : pushMessageMgrs) {
                    if(isExistChannel(pushMessageMgr.key(),channels)){
                        pushMessageMgr.doPushMessage(templeMessage,dto,pushMessageMgr.key());
                    }
                }
            }
        } catch (Exception e) {
            log.error("SMS平台出现错误!  "+e.getMessage(),e);
        }
    }

    /**
     * @Description  判断是否需要将消息发送给你指定渠道
     * @Param [key, channels]
     * @return java.lang.Boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 11:19
     **/
    public Boolean isExistChannel(String key,String[] channels){
        for (String channel : channels) {
            if(key.equals(channel)){
                return true;
            }
        }
        return false;
    }

    @Override
    public String key() {
        return  PushChannelConstant.SEND_TO_OTHER_PLATFORMS;
    }

    @Override
    public String desc() {
        return PushChannelConstant.SEND_TO_OTHER_PLATFORMS_DESC;
    }
}
