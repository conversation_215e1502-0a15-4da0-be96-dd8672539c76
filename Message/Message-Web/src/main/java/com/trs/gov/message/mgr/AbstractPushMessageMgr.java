package com.trs.gov.message.mgr;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.PushToPlatformDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.PushChannelConstant;
import com.trs.gov.message.constant.UserConstant;
import com.trs.gov.message.util.BuildTokenUtils;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName：AbstractPushMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 10:53
 **/
@Component
@Slf4j
public abstract class AbstractPushMessageMgr implements IPushMessageMgr{
    @Reference(check = false, timeout = 60000)
    private IUserService iUserService;
    @Value("${push.user-name:szh_tes_user}")
    private String sendUserName;
    @Value("${token.url:http://**************:8082/index.php/token}")
    private String tokenUrl;
    @Autowired
    private BuildTokenUtils buildTokenUtils;
    @Autowired
    private PushMessageMgr pushMessageMgr;

    /**
     * @Description  将消息推送至指定渠道
     * @Param [tempMessage, dto, channel]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 16:41
     **/
    public void doPushMessage(Map<String, BaseMessageVO> tempMessage, CreateNoticeDTO dto,String channel) throws ServiceException {
        String baseRequestUrl = buildBaseUrl(channel);
        List<PushToPlatformDTO> pushToPlatformDTOList = new ArrayList<>();
        //构建四种消息体,数据为空的跳过
        //每个模板的消息合在一起发送
        for (Map.Entry<String, BaseMessageVO> map : tempMessage.entrySet()){
            String key = map.getKey();
            BaseMessageVO value = map.getValue();
            if(value != null){
                List<TargetUserDTO> targetUserDTOS = listUserDto(key, dto);
                if(!CollectionUtils.isEmpty(targetUserDTOS)){
                    double count = targetUserDTOS.size()/(PushChannelConstant.INSC_SIZE * 1.0);
                    for (int i = 0; i < count;  i ++) {
                        int k = i*PushChannelConstant.INSC_SIZE;
                        if( (targetUserDTOS.size() - k) <= PushChannelConstant.INSC_SIZE){
                            addPushPlatformDTO(value,baseRequestUrl,pushToPlatformDTOList,targetUserDTOS.subList(k,targetUserDTOS.size()));
                        }else {
                            int maxIndex = (i+1)*PushChannelConstant.INSC_SIZE;
                            addPushPlatformDTO(value,baseRequestUrl,pushToPlatformDTOList, targetUserDTOS.subList(k,maxIndex));
                        }
                    }
                }
            }
        }
        //发送Http请求
        pushMessageMgr.pushMessage(pushToPlatformDTOList,channel);
    }

    public void addPushPlatformDTO(BaseMessageVO value,String baseRequestUrl,List<PushToPlatformDTO> pushToPlatformDTOList,List<TargetUserDTO> targetUserDTOS) throws ServiceException {
        Optional<PushToPlatformDTO> pushContentBody = buildMesssage(value, targetUserDTOS,sendUserName);
        if(pushContentBody.isPresent()){
            PushToPlatformDTO pushToPlatformDTO = pushContentBody.get();
            pushToPlatformDTO.setRequestPreUrl(baseRequestUrl);
            pushToPlatformDTOList.add(pushContentBody.get());
        }
    }

    /**
     * @return java.util.Optional<com.trs.user.VO.UserVO>
     * @Description 根据用户名获取用户
     * @Param [username]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/25 17:48
     **/
    public Optional<UserVO> getUserVO(String username) throws ServiceException {
        Try<Optional<UserVO>> tyrResult = Try.of(() -> {
            com.trs.user.DTO.UserDTO userDTO = new com.trs.user.DTO.UserDTO();
            userDTO.setUserName(username);
            UserVO userVO = iUserService.getBaseUserInfoByUserName(userDTO);
            return Optional.ofNullable(userVO);
        });
        if (tyrResult.isFailure()) {
            log.error("获取用户信息失败! ", tyrResult.getCause());
            throw new ServiceException("系统异常,从其它服务查询用户【" + username + "】的数据失败!");
        }
        return tyrResult.get();
    }

    /**
     * @Description  虎丘对应类型的用户消息
     * @Param [key, dto]
     * @return java.util.List<com.trs.gov.message.DTO.UserDTO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 14:53
     **/
    public List<TargetUserDTO> listUserDto(String key, CreateNoticeDTO dto) throws ServiceException {
        if (key.equals(UserConstant.CREATE_USER)) {
            return distinctTargetUser(dto.getCreateUser());
        } else if (key.equals(UserConstant.HANDLE_USER)) {
            return distinctTargetUser(dto.getHandleUser());
        } else if (key.equals(UserConstant.OLD_COPY_USER)) {
            return distinctTargetUser(dto.getOldCopyUser());
        } else if (key.equals(UserConstant.HOST_USER)) {
            return distinctTargetUser(dto.getHostUser());
        } else if (key.equals(UserConstant.NEW_COPY_USER)){
            return distinctTargetUser(dto.getNewCopyUser());
        }else {
            throw new ServiceException("未知类型的type[" + key + "]");
        }
    }

    public List<TargetUserDTO> distinctTargetUser(List<TargetUserDTO> dtos){
        if(CollectionUtils.isEmpty(dtos)){
            return dtos;
        }
        return dtos.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()->new TreeSet<>(Comparator.comparing(TargetUserDTO::getUserName))),ArrayList::new));
    }

}

