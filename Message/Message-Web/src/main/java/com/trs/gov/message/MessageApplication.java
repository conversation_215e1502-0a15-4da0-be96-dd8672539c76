package com.trs.gov.message;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableDiscoveryClient
@MapperScan("com.trs.gov.message")
@SpringBootApplication(scanBasePackages = "com.trs")
@Slf4j
//@EnableSwagger2
public class MessageApplication {
    public static void main(String[] args) {
        SpringApplication.run(MessageApplication.class, args);
        log.info("Message-Service模块启动成功!");
    }
}
