package com.trs.gov.message.service.impl;

import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import com.trs.gov.message.service.SendNoticeMesssageService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @ClassName：SendNoticeMesssageServiceImpl
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 10:19
 **/
@Service
@Slf4j
public class SendNoticeMesssageServiceImpl implements SendNoticeMesssageService {

    @Autowired
    private List<AbstractSendMessageMgr> abstractSendMessageMgrList;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    /**
     * @Description  通过遍历获取各自的实现类
     * @Param [createNoticeDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/19 15:21
     **/
    @Override
    public RestfulResults sendNoticeMessage(CreateNoticeDTO createNoticeDTO) {
        //校验参数
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, createNoticeDTO);
            boolean isVaild = createNoticeDTO.isValid();
            for(AbstractSendMessageMgr abstractSendMessageMgr : abstractSendMessageMgrList){
                if(abstractSendMessageMgr.key().equals(createNoticeDTO.getMessageConfigType())){
                    return abstractSendMessageMgr.doSendMessage(createNoticeDTO);
                }
            }
        } catch (Exception e) {
            log.error("系统错误，发送消息失败!",e.getMessage(),e);
            return RestfulResults.error(e.getMessage());
        }
        return RestfulResults.error("未知工单消息类型!");
    }
}
