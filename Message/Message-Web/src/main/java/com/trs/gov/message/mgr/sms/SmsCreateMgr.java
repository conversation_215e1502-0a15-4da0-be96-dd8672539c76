package com.trs.gov.message.mgr.sms;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.VO.UserUnitMappingVO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.SmsMessageVO;
import com.trs.gov.message.mgr.AbstractSMSMgr;
import com.trs.user.VO.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName：SmsCreateMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/9 11:48
 **/
@Component
@Slf4j
public class SmsCreateMgr extends AbstractSMSMgr {
    @Override
    public String key() {
        return OperateNameConstant.CREATE_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }

    @Override
    public List<SmsMessageVO> pushSMSMessage(CreateNoticeDTO dto) throws ServiceException {
        List<SmsMessageVO> result = new ArrayList<>();
        String loginUnitName = getLoginUnitVO(dto.getUnitId());
        // 通知
        if (dto.getWorkOrderTopTypeId().equals(new Long(3))) {
            if (!CollectionUtils.isEmpty(dto.getNewCopyUser())) {
                //发送通知，一个用户可能属于多个组织，需要进行排重，（避免出现给一个人重新发送短信通知）
                List<TargetUserDTO> targetUserDTOS = dto.getNewCopyUser().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TargetUserDTO::getTrueName))), ArrayList::new));
                for (TargetUserDTO userDTO : targetUserDTOS) {
                    UserUnitMappingVO userAndUnit = noticeMessageDOMapper.getUserAndUnit(userDTO.getUserName(), userDTO.getUnitId());
                    if (Objects.isNull(userAndUnit) || StringUtils.isNullOrEmpty(userAndUnit.getPhone())) {
                        log.warn("用户[" + userDTO.getUserName() + "]的电话号码为空，发送短信失败!");
                        continue;
                    }
                    String content = smsMessageHelper.hasNoticeMessage(userAndUnit.getTrueName(), dto.getTitle(), loginUnitName, dto.getWorkOrderId(), userAndUnit.getUnitName());
                    result.add(new SmsMessageVO(userAndUnit.getPhone(), content));
                }
            }
            // 需求工单
        } else if (dto.getWorkOrderTopTypeId().equals(new Long(1))) {
            if (!CollectionUtils.isEmpty(dto.getHandleUser())) {
                for (TargetUserDTO userDTO : dto.getHandleUser()) {
                    UserUnitMappingVO userAndUnit = null;
                    if (userDTO.getAssignType().equals(1)) {
                        //个人 1
                        userAndUnit = noticeMessageDOMapper.getUserAndUnit(userDTO.getUserName(), userDTO.getUnitId());
                    } else {
                        //企业(单位) 2
                        userAndUnit = noticeMessageDOMapper.getByUnit(userDTO.getUnitId());
                    }
                    if (Objects.isNull(userAndUnit) || StringUtils.isNullOrEmpty(userAndUnit.getPhone())) {
                        log.warn("用户[" + userDTO.getUserName() + "]的电话号码为空，发送短信失败!");
                        continue;
                    }
                    String content = smsMessageHelper.smsWaitDeal(userAndUnit.getTrueName(), dto.getTitle(), loginUnitName, dto.getWorkOrderId(), userAndUnit.getUnitName());
                    result.add(new SmsMessageVO(userAndUnit.getPhone(), content));
                }
            }
            // 协作工单的消息  还存在一种发送通知
            if (!CollectionUtils.isEmpty(dto.getNewCopyUser())) {
                log.info("开始看协作工单的数据了");
                for (TargetUserDTO userDTO : dto.getNewCopyUser()) {
                    UserUnitMappingVO userAndUnit = noticeMessageDOMapper.getUserAndUnit(userDTO.getUserName(), userDTO.getUnitId());
                    if (Objects.isNull(userAndUnit) || StringUtils.isNullOrEmpty(userAndUnit.getPhone())) {
                        log.warn("用户[" + userDTO.getUserName() + "]的电话号码为空，发送短信失败!");
                        continue;
                    }
                    String content = smsMessageHelper.smsAssignOrCopy(userAndUnit.getTrueName(), dto.getTitle(), loginUnitName, dto.getWorkOrderId(), userAndUnit.getUnitName());
                    result.add(new SmsMessageVO(userAndUnit.getPhone(), content));
                }
            }
            // 给主办负责人发送短信
            if (!CollectionUtils.isEmpty(dto.getHostUser())) {
                UserUnitMappingVO userAndUnit = null;
                for (TargetUserDTO userDTO : dto.getHostUser()) {
                    userAndUnit = noticeMessageDOMapper.getByUnit(userDTO.getUnitId());
                    if (Objects.isNull(userAndUnit) || StringUtils.isNullOrEmpty(userAndUnit.getPhone())) {
                        log.warn("用户[" + userDTO.getUserName() + "]的电话号码为空，发送短信失败!");
                        continue;
                    }
                    String content = smsMessageHelper.smsHostMaster(userAndUnit.getTrueName(), dto.getTitle(), loginUnitName, dto.getWorkOrderId(), userAndUnit.getUnitName());
                    result.add(new SmsMessageVO(userAndUnit.getPhone(), content));
                }
            }
        }
        return result;
    }
}