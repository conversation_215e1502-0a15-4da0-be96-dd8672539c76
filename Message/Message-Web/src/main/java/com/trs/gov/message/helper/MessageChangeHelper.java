package com.trs.gov.message.helper;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.VO.BaseMessageConfigVO;
import com.trs.gov.message.VO.Operation;
import com.trs.gov.message.constant.NoticeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @ClassName： 判断权限以及请求内容和实体的相互转换
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/16 18:24
 **/
@Component
@Slf4j
public class MessageChangeHelper {

    /**
     * @return java.lang.String
     * @Description 将数据库中的配置字段转换成前端需要的实体
     * @Param [numConfig]  1110011110111 rootType：顶级类别
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/16 21:08
     **/
    public Optional<List<BaseMessageConfigVO>> jsonToOperationList(String numConfig, String rootType) throws Exception {
        ArrayList<BaseMessageConfigVO> baseMessageConfigVOS = new ArrayList<>();
        try {
            MessageConfigHelper messageConfigHelper = new MessageConfigHelper();
            char[] a = numConfig.toCharArray();
            if (getOperationSize(rootType) != a.length) {
                throw new ServiceException("消息配置数据内容不合法!");
            }
            List<String> typeList = listSecondTypeRoot(rootType);
            //配置外层参数
            for (String type : typeList) {
                BaseMessageConfigVO baseMessageConfigVO = new BaseMessageConfigVO();
                String typeName = MessageTypeHelper.getMessageTypeName(type);
                baseMessageConfigVO.setName(typeName);
                //配置下一级数据
                List<Operation> operations = messageConfigHelper.getListOperation(type);
                ArrayList<BaseMessageConfigVO> configVOS = new ArrayList<>();
                //配置childrenList参数
                for (Operation operation : operations) {
                    BaseMessageConfigVO configVO = new BaseMessageConfigVO();
                    configVO.setName(operation.getDispName());
                    configVO.setId(operation.getIndex());
                    Boolean isCheck = String.valueOf(a[operation.getIndex() - 1]).equals(String.valueOf(1));
                    configVO.setChecked(isCheck);
                    configVOS.add(configVO);
                }
                baseMessageConfigVO.setChildren(configVOS);
                baseMessageConfigVOS.add(baseMessageConfigVO);
            }
        } catch (Exception e) {
            log.error("[operationListToJson]异常！", e);
            throw new ServiceException(e.getMessage());
        }
        return Optional.ofNullable(baseMessageConfigVOS);
    }

    /**
     * @Description  将前端传过来的json配置串转换成数据库中对应的字段
     * @Param [jsonConfig, rootType]
     * @return java.util.Optional<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/11 11:27
     **/
    public Optional<String> jsonToNumConfig(String jsonConfig,String rootType) {
        String numConfig = "";
        try {
            List<BaseMessageConfigVO> result = JSONObject.parseArray(jsonConfig, BaseMessageConfigVO.class);
            if (result.isEmpty()) {
                return Optional.empty();
            }
            List<BaseMessageConfigVO> temp = new ArrayList<>();
            result.forEach(a -> temp.addAll(a.getChildren()));
            //先根据Id排序，排序后再获取isChecked字段
            List<Boolean> checkList = temp.stream()
                    .sorted(Comparator.comparing(BaseMessageConfigVO::getId))
                    .map(BaseMessageConfigVO::isChecked).collect(Collectors.toList());
            //构造返回参数
            AtomicReference<String> resultString = new AtomicReference<>("");
            checkList.forEach(a -> {
                if (a) {
                    resultString.set(resultString + "1");
                } else {
                    resultString.set(resultString + "0");
                }
            });
            numConfig = resultString.get();
            //检验参数是否正确
            if (numConfig.length() != getOperationSize(rootType)) {
                throw new ServiceException("转换出错！未成功获取到正确的配置内容数据!");
            }
        } catch (Exception e) {
            log.error("转换成数据库保存的配置串出错!", e);
            return Optional.empty();
        }
        return Optional.of(numConfig);
    }

    /**
     * @return boolean
     * @Description 判断某个操作是否有权限进行
     * @Param [typeName, numConfig]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/17 9:49
     **/
    public boolean hasAuth(String typeName, String numConfig,String rootType) {
        MessageConfigHelper messageConfigHelper = new MessageConfigHelper();
        boolean result = false;
        try {
            char[] a = numConfig.toCharArray();
            if (getOperationSize(rootType) != a.length) {
                throw new ServiceException("想要被鉴权的配置数据格式不正确!");
            }
            Integer integer = messageConfigHelper.getNameIndex(typeName);
            int temp = Integer.parseInt(String.valueOf(a[integer - 1]));
            if (temp == 1) {
                result = true;
            } else {
                result = false;
            }
        } catch (Exception e) {
            log.error("[hasAuth]异常！", e);
        }
        return result;
    }




    /**
     * @Description  获取该顶级类型下的二级配置列表
     * @Param [rootType]
     * @return java.util.List<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/11 11:07
     **/
    public List<String> listSecondTypeRoot(String rootType) throws Exception {
        List<String> typeList = new ArrayList<>();
        int i = rootType.equals(NoticeConstant.WORKORDER) ? 0 : 1;
        try {
            String messageConfig = MessageTypeHelper.getAllConfigRootNames().get(i);
            typeList = MessageTypeHelper.listChildrenType(messageConfig);
            if (typeList.isEmpty()) {
                throw new ServiceException("获取第二级列表失败!");
            }
        } catch (Exception e) {
            log.error("获取系统分类时出错!", e.getMessage(), e);
        }
        return typeList;
    }

    /**
     * @Description  获取该顶级类型下的配置数量
     * @Param [type]
     * @return int
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/11 11:09
     **/
    public int getOperationSize(String type) throws Exception {
        List<String> typeRoot = listSecondTypeRoot(type);
        int count = 0;
        MessageConfigHelper messageConfigHelper = new MessageConfigHelper();
        for (String s : typeRoot) {
            List<Operation> temp = messageConfigHelper.getListOperation(s);
            count += temp.size();
        }
        return count;
    }
    /**
     * @Description  获取顶级默认全部开启
     * @Param [rootType]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/11 11:51
     **/
    public String getRootDefaultConfig(String rootType) throws Exception {
        int length = getOperationSize(rootType);
        String result = "";
        for (int i = 0; i < length; i++) {
            result = result + "1";
        }
        return result.trim();
    }

    /**
     * @Description  获取指定顶级类型下的所有配置实体
     * @Param [rootType]
     * @return java.util.List<com.trs.gov.message.VO.Operation>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/11 11:18
     **/
    public List<Operation> listThirdOperation(String rootType) throws Exception {
        List<String> typeRoot = listSecondTypeRoot(rootType);
        List<Operation> resultList = new ArrayList<>();
        MessageConfigHelper messageConfigHelper = new MessageConfigHelper();
        for (String s : typeRoot) {
            List<Operation> temp = messageConfigHelper.getListOperation(s);
            resultList.addAll(temp);
        }
        return resultList;
    }

    /**
     * @Description  获取该顶级配置类别的默认配置项
     * @Param [rootType]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/11 11:20
     **/
    public String getDefaultValue(String rootType) {
        String result = "";
        try {
            List<Operation> operationList = listThirdOperation(rootType);
            result = operationList.stream().sorted(Comparator.comparing(Operation::getIndex))
                    .map(Operation::getDefaultValue).collect(Collectors.joining(""));
            if (result.length() != getOperationSize(rootType)) {
                throw new ServiceException("获取默认配置信息数据不对!");
            }
        } catch (Exception e) {
            log.error("[getDefaultValue]异常！", e);
        }
        return result;
    }

    /**
     * @Description  根据操作类型判断 顶级类别
     * @Param [args]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/11 15:27
     **/
    public String getRootType(String messageConfig) throws Exception {
        MessageConfigHelper messageConfigHelper = new MessageConfigHelper();
        List<Operation> list = messageConfigHelper.listAllOperation();
        Operation operation = null;
        for (Operation oper : list) {
            if(messageConfig.equals(oper.getKey())){
                operation = oper;
                break;
            }
        }
        if(operation == null){
            throw new ServiceException("获取该配置【"+messageConfig+"】的详细信息失败!");
        }
        Map<String, List<String>> messageChildren = MessageTypeHelper.getMessageChildren();
        for (Map.Entry<String,List<String>> map : messageChildren.entrySet()){
            String key = map.getKey();
            List<String> value = map.getValue();
            for (String s : value) {
                if(s.equals(operation.getType())){
                    return key;
                }
            }
        }
        throw new ServiceException("未成功根据配置项获取顶级操作的类型");
    }




    public static void main(String[] args) throws Exception {
        MessageChangeHelper messageChangeHelper = new MessageChangeHelper();
        String numConfig = "1111111000011";
        Optional<List<BaseMessageConfigVO>> baseMessageConfigVOS = messageChangeHelper.jsonToOperationList(numConfig, NoticeConstant.WORKORDER);
        String value = JSONObject.toJSONString(baseMessageConfigVOS.get());
//        String value = "[{\"id\":0,\"name\":\"工单新增\",\"children\":[{\"id\":1,\"name\":\"创建工单\",\"children\":\"[]\",\"checked\":true}],\"checked\":false},{\"id\":0,\"name\":\"工单受理\",\"children\":[{\"id\":2,\"name\":\"工单响应\",\"children\":\"[]\",\"checked\":true},{\"id\":3,\"name\":\"新增抄送\",\"children\":\"[]\",\"checked\":true},{\"id\":4,\"name\":\"记录工时\",\"children\":\"[]\",\"checked\":true},{\"id\":5,\"name\":\"回复\",\"children\":\"[]\",\"checked\":true},{\"id\":6,\"name\":\"回退\",\"children\":\"[]\",\"checked\":true},{\"id\":7,\"name\":\"交办\",\"children\":\"[]\",\"checked\":true},{\"id\":8,\"name\":\"修改\",\"children\":\"[]\",\"checked\":true},{\"id\":9,\"name\":\"完成工单\",\"children\":\"[]\",\"checked\":true}],\"checked\":false},{\"id\":0,\"name\":\"工单完成\",\"children\":[{\"id\":10,\"name\":\"评价\",\"children\":\"[]\",\"checked\":true},{\"id\":11,\"name\":\"重新打开\",\"children\":\"[]\",\"checked\":false}],\"checked\":false},{\"id\":0,\"name\":\"工单已评价\",\"children\":[{\"id\":12,\"name\":\"公开工单\",\"children\":\"[]\",\"checked\":false},{\"id\":13,\"name\":\"不公开工单\",\"children\":\"[]\",\"checked\":true}],\"checked\":false}]";
        Optional<String> optionalS = messageChangeHelper.jsonToNumConfig(value, NoticeConstant.WORKORDER);
        System.out.println(optionalS.get());

    }

}
