package com.trs.gov.message.helper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStreamReader;
import java.util.*;

/**
 * @ClassName：初始化配置文件失败
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/16 12:47
 **/
@Component
@Slf4j
public class MessageTypeHelper {

    //存储 properties的内容，存储方式为 键值对
    private static Properties mapProperties = null;

    //存储消息配置类型的层级接口
    private static Map<String, List<String>> messageChildren = null;

    //存储根节点配置名称
    private static List<String> listRootName = null;


    /**
     * @Description  初始化静态属性
     * 1.读取配置文件
     * 2.根据 . 判断是否为根节点
     *   是：存入listRootName
     *   否：存入messageChildren
     * @Param []
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/16 14:03
     **/
    public static void init() throws Exception{
        if(mapProperties != null){
            return;
        }
        //读取配置文件
        OrderRootProperties orderRootProperties = new OrderRootProperties();
        try {
            ClassPathResource cpr = new ClassPathResource("message.classify.properties");
            orderRootProperties.load(new InputStreamReader(cpr.getInputStream(),"utf-8"));
        } catch (Exception e) {
            log.error("初始化获取配置文件失败!",e);
            throw new Exception();
        }
        mapProperties = orderRootProperties;
        //临时存储父子关系
        Map<String,List<String>> hTemp = new HashMap<>();
        //临时存储根节点名字
        List<String> rootNameTemp = new ArrayList<>();
        for(Object key : orderRootProperties.keySet()){
            String sKey = ((String)key).toLowerCase();
            int pos = sKey.indexOf(".");
            //大于0  workorder.handel  为节点
            if(pos > 0){
                String parentName = sKey.substring(0,pos).toLowerCase();
                List<String> childrens = hTemp.get(parentName);
                if(childrens == null){
                    childrens = new ArrayList<>();
                    hTemp.put(parentName,childrens);
                }
                if(!childrens.contains(parentName)){
                    childrens.add(sKey);
                }
            }else{ ////反之为根节点
                if(!rootNameTemp.contains(sKey)){
                    rootNameTemp.add(sKey);
                }//初始化htemp
                if(!hTemp.containsKey(sKey)){
                    List<String> childrenList = new ArrayList<>();
                    hTemp.put(sKey,childrenList);
                }
            }
        }
        listRootName = rootNameTemp;
        messageChildren = hTemp;
    }

    /**
     * @Description  获取所有根节点的英文名
     * @Param []
     * @return java.util.List<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/16 15:06
     **/
    public static List<String> getAllConfigRootNames() throws Exception{
        if(mapProperties == null){
            init();
        }
        return listRootName;
    }

    /**
     * @Description  获取message分类的中文名字，用户返回结果给前端
     * @Param [sType]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/16 15:20
     **/
    public static String getMessageTypeName(String sType) throws Exception{
        if(mapProperties == null){
            init();
        }
        return mapProperties.getProperty(sType);
    }

    /**
     * @Description  返回父节点下的配置类别列表
     * @Param [parentType]
     * @return java.util.List<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/16 15:23
     **/
    public static List<String> listChildrenType(String parentType) throws Exception{
        if(messageChildren == null){
            init();
        }
        return messageChildren.get(parentType);
    }

    public static Map<String, List<String>> getMessageChildren() throws Exception{
        if(messageChildren == null){
            init();
        }
        return messageChildren;
    }


}
