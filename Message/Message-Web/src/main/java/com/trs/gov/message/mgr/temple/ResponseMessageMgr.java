package com.trs.gov.message.mgr.temple;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.UserConstant;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：ResponseMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 15:45
 **/
@Component
@Slf4j
public class ResponseMessageMgr extends AbstractSendMessageMgr {
    @Override
    public Map<String, BaseMessageVO> getMessageTemple(CreateNoticeDTO createNoticeDTO) throws Exception {
        String workOrderName = createNoticeDTO.getWorkOrderName().orElseThrow(()->new ServiceException("获取工单别名失败!"));
        createNoticeDTO.mustNotNull(true,false,false,true);
        BaseMessageVO create = new BaseMessageVO();
        BaseMessageVO copy = new BaseMessageVO();
        BaseMessageVO host = new BaseMessageVO();
        create.setTitle("工单已响应");
        create.setContent(createNoticeDTO.getCurrentUserTrueName()+"已响应 "+workOrderName);
        copy.setTitle("工单已响应");
        copy.setContent(createNoticeDTO.getCurrentUserTrueName()+"已响应 "+workOrderName);
        host.setTitle("工单创建功能");
        host.setContent(createNoticeDTO.getCurrentUserTrueName()+"已响应 "+workOrderName);
        //构造返回结果
        Map<String,BaseMessageVO> resultMap = new HashMap<>();
        resultMap.put(UserConstant.CREATE_USER,create);
        resultMap.put(UserConstant.OLD_COPY_USER,copy);
        resultMap.put(UserConstant.HOST_USER,host);
        return resultMap;
    }

    @Override
    public String key() {
        return OperateNameConstant.RESPONSE_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }
}
