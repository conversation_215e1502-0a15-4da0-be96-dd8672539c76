package com.trs.gov.message.helper;

import com.trs.common.http2.HttpRequest;
import com.trs.log.exception.RecordableException;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName：SendMessageHelper
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/12 10:11
 **/
public class SendMessageHelper {

    private static HttpRequest httpRequest = new HttpRequest.Builder(new OkHttpClient.Builder()
            .connectTimeout(4, TimeUnit.SECONDS)
            .readTimeout(3, TimeUnit.SECONDS)
    ).setUseConnectionClose(true).build();

    private static Headers.Builder makeBaseHeader() {
        return new Headers.Builder().add("Content-Type", "application/json;charset=UTF-8");
    }


    /**
     * @Description  发送JSON消息
     * @Param [url, jsonStr]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/4/12 10:23
     **/
    public static String sendJsonToThirdSystemByPost(String url, String jsonStr) throws RecordableException {
        RequestBody body = RequestBody.create(MediaType.parse("application/json;charset=utf-8"), jsonStr == null ? "{}" : jsonStr);
        Headers.Builder builder = makeBaseHeader();
        return httpRequest.doPost(url, body, builder.build());
    }

}
