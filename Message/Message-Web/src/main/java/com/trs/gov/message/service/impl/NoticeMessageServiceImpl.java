package com.trs.gov.message.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.message.DTO.NoticeMessageDTO;
import com.trs.gov.message.VO.WorkOrderNoticeVO;
import com.trs.gov.message.mgr.NoticeMessageMgr;
import com.trs.gov.message.service.NoticeMessageService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

/**
 * @ClassName：NoticeMessageServiceImpl
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 10:17
 **/
@Service
@Slf4j
public class NoticeMessageServiceImpl implements NoticeMessageService {
    @Autowired
    private NoticeMessageMgr noticeMessageMgr;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    /**
     * @return com.trs.web.builder.base.RestfulResults
     * @Description 根据用户名去获取当前用户存是否存在未读信息
     * @Param [username]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/17 10:40
     **/
    @Override
    public String getUnReadMessage(String username,String unitId) throws ServiceException {
        if (StringUtils.isEmpty(username)) {
            Optional<String> loginUser = ContextHelper.getLoginUser();
            username = loginUser.orElseThrow(() -> new ServiceException("获取当前登录用户失败!"));
        }
        if (StringUtils.isEmpty(unitId)) {
            unitId = ContextHelper.getLoginUnitId().orElseThrow(() -> new ServiceException("获取当前登录单位Id失败!"));
        }
        //获取当前登录用户与传递过来的用户比较
        return String.valueOf(noticeMessageMgr.getUnReadMessage(username,unitId).getIsExistUnReadMessage());
    }

    /**
     * @return com.trs.web.builder.base.RestfulResults
     * @Description 获取最近消息列表
     * @Param [NoticeMessageDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/17 10:40
     **/
    @Override
    public RestfulResults<List<WorkOrderNoticeVO>> getRecentMessage(NoticeMessageDTO noticeMessageDTO) {
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService,noticeMessageDTO);
            boolean isValid = noticeMessageDTO.isValid();
            PreConditionCheck.checkNotNull(noticeMessageDTO.getMessageType(), "消息类型参数不能为空!");
            PreConditionCheck.checkNotNull(noticeMessageDTO.getHours(), "时间参数不能为空!");
            //获取当前登录用户与传递过来的用户比较
            return noticeMessageMgr.getRecentMessage(noticeMessageDTO);
        } catch (ServiceException e) {
            log.error("参数校验失败!ERR={}", e.getMessage(), e);
            return RestfulResults.error(e.getMessage());
        }
    }
}
