package com.trs.gov.message.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.gov.message.DTO.MessageConfigDTO;
import com.trs.gov.message.VO.MessageConfigVO;
import com.trs.gov.message.mgr.NoticeMessageConfigMgr;
import com.trs.gov.message.service.NoticeMessageConfigService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName：NoticeMessageConfigServiceImpl
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 10:13
 **/
@Service
@Slf4j
public class NoticeMessageConfigServiceImpl implements NoticeMessageConfigService {
    @Autowired
    private NoticeMessageConfigMgr noticeMessageConfigMgr;

    /**
     * @return com.trs.web.builder.base.RestfulResults<java.util.List < com.trs.gov.message.VO.MessageConfigVO>>
     * @Description 获取配置列表
     * @Param [messageConfigDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/17 10:40
     **/
    @Override
    public RestfulResults<MessageConfigVO> listMessageConfig(MessageConfigDTO messageConfigDTO) {
        try {
            boolean isValid = messageConfigDTO.isValid();
            return noticeMessageConfigMgr.listMessageConfig(messageConfigDTO);
        } catch (Exception e) {
            log.error("参数校验失败!", e.getMessage(), e);
            return RestfulResults.error(e.getMessage());
        }
    }

    /**
     * @return com.trs.web.builder.base.RestfulResults
     * @Description 保存配置列表
     * @Param [messageConfigDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/17 10:40
     **/
    @Override
    public RestfulResults saveMessageConfig(MessageConfigDTO messageConfigDTO) {
        try {
            boolean isValid = messageConfigDTO.isValid();
            PreConditionCheck.checkNotNull(messageConfigDTO.getFollowParentConfig(), "跟随父类额配置参数不能为空!");
            if (messageConfigDTO.getFollowParentConfig().intValue() == 0) {
                PreConditionCheck.checkNotNull(messageConfigDTO.getMessageConfig(), "配置的详细内容参数不能为空!");
            }
            return noticeMessageConfigMgr.saveMessageConfig(messageConfigDTO);
        } catch (Exception e) {
            log.error("参数校验失败!", e.getMessage(), e);
            return RestfulResults.error(e.getMessage());
        }
    }
}
