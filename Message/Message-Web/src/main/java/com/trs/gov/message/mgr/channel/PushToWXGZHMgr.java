package com.trs.gov.message.mgr.channel;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.message.DTO.PushToPlatformDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.PushChannelConstant;
import com.trs.gov.message.constant.XmlParamConstant;
import com.trs.gov.message.mgr.AbstractPushMessageMgr;
import com.trs.gov.message.util.JsonXmlUtils;
import com.trs.user.VO.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * @ClassName：PushToWWXGZH
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 11:01
 **/
@Component
@Slf4j
public class PushToWXGZHMgr extends AbstractPushMessageMgr {
    @Value("${push.url:http://**************:8082/index.php/notice}")
    private String tokenUrl;


    @Override
    public Optional<PushToPlatformDTO> buildMesssage(BaseMessageVO vo, List<TargetUserDTO> receiveUser, String sendUserName) throws ServiceException {
        PushToPlatformDTO pushToPlatformDTO = new PushToPlatformDTO();
        if(!CollectionUtils.isEmpty(receiveUser)){
            JSONArray jsonArray = new JSONArray();

            for (TargetUserDTO targetUserDTO : receiveUser) {
                UserVO userVO = getUserVO(targetUserDTO.getUserName()).orElseThrow(() -> new ServiceException("数据库中无用户[" + targetUserDTO.getUserName() + "]的相关数据!"));
                if(CMyString.isEmpty(userVO.getPhone())){
                    log.warn("用户[" + targetUserDTO.getUserName() + "]的电话号码为空，推送至其微信公众号失败!");
                }else{
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put(XmlParamConstant.NOTICE_MOBILE,userVO.getPhone());
                    jsonObject.put(XmlParamConstant.PUBLISH_USER,sendUserName);
                    jsonObject.put(XmlParamConstant.MESSAGE,vo.getContent());
                    JSONObject jsonNotice = new JSONObject();
                    jsonNotice.put(XmlParamConstant.NOTICE,jsonObject);
                    jsonArray.add(jsonNotice);
                }
            }
            JSONObject resultJson = new JSONObject();
            if(jsonArray.size() != 0){
                resultJson.put(XmlParamConstant.NOTICES,jsonArray);
                pushToPlatformDTO.setXmlMessage(JsonXmlUtils.jsonToXml(resultJson,false));
                return Optional.of(pushToPlatformDTO);
            }
        }
        return Optional.empty();
    }

    /**
     * @Description 构建url
     * @Param [channel]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 14:59
     **/
    @Override
    public String buildBaseUrl(String channel) {
        StringBuilder builder = new StringBuilder(tokenUrl);
        builder.append("?").append("channels[]="+channel).append("&token=");
        return builder.toString();
    }

    @Override
    public String key() {
        return PushChannelConstant.PUSH_TO_WXGZH;
    }

    @Override
    public String desc() {
        return PushChannelConstant.PUSH_TO_WXGZH_DESC;
    }
}
