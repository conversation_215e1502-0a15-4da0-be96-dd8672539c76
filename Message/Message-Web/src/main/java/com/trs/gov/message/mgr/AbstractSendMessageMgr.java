package com.trs.gov.message.mgr;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.constant.WorkOrderTypeConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.management.DTO.GroupUnitListSearchDTO;
import com.trs.gov.management.DTO.UnitGroupSearchDTO;
import com.trs.gov.management.VO.UnitGroupVO;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.gov.management.VO.WorkOrderTypeForNoticeVO;
import com.trs.gov.management.service.IUserUnitMappingService;
import com.trs.gov.management.service.UnitGroupService;
import com.trs.gov.management.service.WorkOrderTypeService;
import com.trs.gov.message.DO.MessageConfigDO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.DTO.UserUnitMappingSearchDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.helper.MessageChangeHelper;
import com.trs.gov.message.mapper.MessageConfigDOMapper;
import com.trs.gov.message.mapper.NoticeMessageDOMapper;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.trs.web.restful.RestfulJsonHelper.STATUS_CODE.Unauthorized;

/**
 * @ClassName：AbstractSendMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 15:34
 **/
@Component
@Slf4j
public abstract class AbstractSendMessageMgr implements ISendMessageMgr {

    @Autowired
    private MessageConfigDOMapper messageConfigDOMapper;
    @Autowired
    private NoticeMessageDOMapper noticeMessageDOMapper;
    @Autowired
    private NoticeMessageConfigMgr noticeMessageConfigMgr;

    @Reference(check = false, timeout = 60000)
    private WorkOrderTypeService workOrderTypeService;

    @Reference(check = false, timeout = 60000)
    private IUserUnitMappingService iUserUnitMappingService;

    @Reference(check = false, timeout = 60000)
    private UnitGroupService unitGroupService;

    @Autowired
    private List<AbstractDifferentChanelMgr> differentChanelMgrList;

    @Value("${different.channel.list:gz_work_order,sms_platform,third_party_interface}")
    private String channelList;

    /**
     * @Description  1.获取工单权限
     *                    - 根据Id获取工单详细内容【工单类型】
     *                    - 查询该工单类型的配置信息
     *                    - 检验该工单类型是否具有具有通知权限，有则继续，无则返回提示信息
     *               2.工单1中工单信息，再去获取工单相关的人：
     *                    -创建人   List
     *                    -受理人   List
     *                    -查送人   List
     *                    -主办人   List
     *               3.mgr [获取各自类型的模板，并传入操作人和工单ID,  如果返回结果为空 ，在第四步中不做处理]
     *               4.给四种类型的人的发送通知，如果返回模板内容为空，则对相应人不做处理
     *               5.成功则返回成功，失败则返回失败!
     * @Param [createNoticeDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/17 14:44
     **/
    @Transactional(rollbackFor = Exception.class)
    public RestfulResults doSendMessage(CreateNoticeDTO createNoticeDTO) throws Exception {
        log.info("进入消息模块的数据为："+ JSONObject.toJSONString(createNoticeDTO));
        //鉴权
        boolean hasAuth = isAuthToSendMessage(createNoticeDTO);
        if(!hasAuth){
            return RestfulResults.error("发送通知失败，该工单类型暂时没有发送消息的权限!").addCode(Unauthorized);
        }
        //处理抄送人,
        createNoticeDTO.setNewCopyUser(listTarGetUserDTO(createNoticeDTO.getNewCopyUser(),createNoticeDTO));
        createNoticeDTO.setOldCopyUser(listTarGetUserDTO(createNoticeDTO.getOldCopyUser(),createNoticeDTO));
        //获取消息模板
        Map<String, BaseMessageVO> temp = getMessageTemple(createNoticeDTO);
        log.info("进入消息模块的数据为："+ JSONObject.toJSONString(createNoticeDTO));
        //各个渠道发送消息
        pushMessage(createNoticeDTO,temp);
        return RestfulResults.ok("发送通知成功!");
    }

    /**
     * @Description  构造获取 抄送人+单位Id 的返回体
     * @Param [dto]
     * @return java.util.List<com.trs.gov.management.VO.UserUnitMappingVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/8 17:48
     **/
    public List<TargetUserDTO> listTarGetUserDTO(List<TargetUserDTO> dtos,CreateNoticeDTO dto) throws Exception {
        UserUnitMappingSearchDTO queryDto = new UserUnitMappingSearchDTO();
        if(CollectionUtils.isEmpty(dtos)){
            log.info("没有抄送人需要被通知消息!");
            return null;
        }
        List<Long> unitIds = new ArrayList<>();
        List<Long> groupIds = new ArrayList<>();
        List<String> userUnit = new ArrayList<>();
        //处理请求参数的unitIds
        for (TargetUserDTO targetUserDTO : dtos) {
            if (targetUserDTO.getAssignType().equals(AssignConstant.ASSIGN_TO_GROUP)){
                groupIds.add(targetUserDTO.getGroupId());
            }else if (targetUserDTO.getAssignType().equals(AssignConstant.ASSIGN_TO_UNIT)){
                unitIds.add(targetUserDTO.getUnitId());
            }else if (targetUserDTO.getAssignType().equals(AssignConstant.ASSIGN_TO_ALL)){
                groupIds.clear();unitIds.clear();unitIds.clear();
                queryDto.setIsAll(1);
                break;
            }else if (targetUserDTO.getAssignType().equals(AssignConstant.ASSIGN_TO_USER)){
                userUnit.add(targetUserDTO.getUserName()+targetUserDTO.getUnitId());
            }
        }
        if(groupIds.size() != 0){
            List<Long> unitsByGroupIds = getUnitsByGroupIds(groupIds);
            if(!CollectionUtils.isEmpty(unitsByGroupIds)){
                unitIds.addAll(unitsByGroupIds);
            }
        }
        //不等于 包含 当前登录用户
        queryDto.setNotUserAndUnit(dto.getCurrentUser()+dto.getUnitId());
        queryDto.setUserUnit(userUnit);
        queryDto.setTargetUnitIds(unitIds);
        if(!dto.getWorkOrderTopTypeId().equals(WorkOrderTypeConstant.TOP_TYPE_NOTICE)){
            queryDto.setIsMaster(true);
        }
        //构造返回结果
        return noticeMessageDOMapper.listTargetUserAndUnit(queryDto).stream().map(a->{
            TargetUserDTO targetUserDTO = new TargetUserDTO();
            BaseUtils.copyProperties(a,targetUserDTO);
            targetUserDTO.setUnitId(Long.valueOf(a.getUnitId()));
            return targetUserDTO;
        }).collect(Collectors.toList());
    }

    /**
     * @Description  获取用户所有有权限的组织
     * @Param []
     * @return java.util.List<java.lang.Long>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/8 18:00
     **/
    public List<Long> allGroupIds() throws ServiceException {
        UnitGroupSearchDTO unitGroupSearchDTO = new UnitGroupSearchDTO();
        unitGroupSearchDTO.setIsAll("1");
        BaseUtils.setUserInfoToDTO(unitGroupSearchDTO);
        RestfulResults<List<UnitGroupVO>> listRestfulResults = unitGroupService.queryUnitGroupList(unitGroupSearchDTO);
        checkRestfulResults(listRestfulResults);
        return listRestfulResults.getDatas().stream().map(UnitGroupVO::getId).collect(Collectors.toList());
    }

    /**
     * @Description  根据组织 Ids 获取 这些组织下的所有单位
     * @Param [longList]
     * @return java.util.List<java.lang.Long>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/8 18:03
     **/
    public List<Long> getUnitsByGroupIds(List<Long> longList) throws Exception {
        if(CollectionUtils.isEmpty(longList)){
            return new ArrayList<Long>();
        }
        List<Long> result = new ArrayList<>();
        GroupUnitListSearchDTO groupUnitListSearchDTO = new GroupUnitListSearchDTO();
        for (Long aLong : longList) {
            groupUnitListSearchDTO.setGroupId(String.valueOf(aLong));
            BaseUtils.setUserInfoToDTO(groupUnitListSearchDTO);
            RestfulResults<List<UserCountVO>> groupUnitInfoListByGroupId = unitGroupService.getGroupUnitInfoListByGroupIdNotRight(groupUnitListSearchDTO);
            checkRestfulResults(groupUnitInfoListByGroupId);
            result.addAll(groupUnitInfoListByGroupId.getDatas().stream().map(UserCountVO::getUnitId).collect(Collectors.toList()));
        }
        return result;
    }
    /**
     * @Description  发送消息
     * @Param [createNoticeDTO, temp]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/8 14:35
     **/
    public void pushMessage(CreateNoticeDTO createNoticeDTO,Map<String, BaseMessageVO> temp){
        if(CMyString.isEmpty(channelList)){
            log.warn("将要发送给的渠道配置为空,麻烦确定下是忘记了配置，还是不发送给任何渠道!");
        }else {
            String[] channels = channelList.split(",");
            // 上层已经采用了异步, 这里就不在创建一个线程池
            doAbstractDifferentChanelMgr(channels,createNoticeDTO,temp);
            log.info(" 异步发送所有消息完成 !");
        }
    }

    /**
     * @Description  根据channel来找对应的实现类，可以通过配置执行的先后顺序
     * @Param [channels, dto, temp]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/28 14:01
     **/
    public void doAbstractDifferentChanelMgr(String[] channels,CreateNoticeDTO dto,Map<String, BaseMessageVO> temp){
        try {
            for (String channel : channels) {
                Optional<AbstractDifferentChanelMgr> abstractDifferentChanelMgr = getAbstractDifferentChanelMgr(channel);
                if(abstractDifferentChanelMgr.isPresent()){
                    abstractDifferentChanelMgr.get().SendMesaageByChannel(dto,temp);
                }
            }
        } catch (Exception e) {
            log.error("发送消息失败! "+e.getMessage(),e);
        }

    }
    private Optional<AbstractDifferentChanelMgr> getAbstractDifferentChanelMgr(String channel){
        for (AbstractDifferentChanelMgr abstractDifferentChanelMgr : differentChanelMgrList) {
            if (abstractDifferentChanelMgr.key().equals(channel)){
                return Optional.ofNullable(abstractDifferentChanelMgr);
            }
        }
        return Optional.empty();
    }

    /**
     * @Description  判断是否需要将消息发送给你指定渠道
     * @Param [key, channels]
     * @return java.lang.Boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 11:19
     **/
    public Boolean isExistChannel(String key,String[] channels){
        for (String channel : channels) {
            if(key.equals(channel)){
                return true;
            }
        }
        return false;
    }

    /**
     * @Description  判断该工单是否有权限进行发送
     * @Param [createNoticeDTO]
     * 备注：rootType作用为：标识是那一个xml模块，防止后续有多个,
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/24 16:57
     **/
    public boolean isAuthToSendMessage(CreateNoticeDTO createNoticeDTO) throws Exception {
        //鉴权
        MessageChangeHelper messageChangeHelper = new MessageChangeHelper();
        boolean hasAuth = false;
        if(createNoticeDTO.getWorkOrderTypeId() == null){ //为空默认为True
            hasAuth = true;
        }else{
            WorkOrderTypeForNoticeVO workOrderTypeForNoticeVO = null;
            if(createNoticeDTO.getWorkOrderTypeId() != null){
                RestfulResults<WorkOrderTypeForNoticeVO> restfulResults = workOrderTypeService.getTypeInfoForOtherPart(String.valueOf(createNoticeDTO.getWorkOrderTypeId()));
                checkRestfulResults(restfulResults);
                workOrderTypeForNoticeVO = restfulResults.getDatas();
            }
            //顶级默认所有权限checkRestfulResults
            if(workOrderTypeForNoticeVO != null && workOrderTypeForNoticeVO.getParentId().equals(0L)){
                hasAuth = true;
            }else {
                String rootType = messageChangeHelper.getRootType(createNoticeDTO.getMessageConfigType());
                String numConfig = getNumConfig(createNoticeDTO.getWorkOrderTypeId(),rootType).orElseThrow(()->new ServiceException("获取该工单类型的配置文件信息失败!"));
                hasAuth = messageChangeHelper.hasAuth(createNoticeDTO.getMessageConfigType(), numConfig,rootType);
            }
        }
        return hasAuth;
    }
    /**
     * @Description  获取配置信息，如果不存在则返回默认的配置信息
     * @Param [workOrderTypeId]
     * @return java.util.Optional<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/18 22:24
     **/
    public Optional<String> getNumConfig(Long workOrderTypeId,String rootType){
        MessageChangeHelper messageChangeHelper = new MessageChangeHelper();
        Try<Optional<String>> result = Try.of(()->{
            if(workOrderTypeId == null){
                return Optional.ofNullable(messageChangeHelper.getDefaultValue(rootType));
            }
            MessageConfigDO messageConfigDO = messageConfigDOMapper.selectOne(new QueryWrapper<MessageConfigDO>().eq("work_order_type_id", workOrderTypeId));
            if(messageConfigDO == null){
                return Optional.ofNullable(messageChangeHelper.getDefaultValue(rootType));
            }else {
                if(messageConfigDO.getIsFollowParentConfig().intValue() == 1){
                    return Optional.ofNullable(noticeMessageConfigMgr.getParentConfig(workOrderTypeId,rootType).orElseThrow(()->new ServiceException("获取父级配置信息失败!")));
                }else{
                    return Optional.ofNullable(messageConfigDO.getMessageConfig());
                }
            }
        });
        if(result.isSuccess()){
            return result.get();
        }
        return Optional.empty();
    }

    public void checkRestfulResults(RestfulResults restfulResults) throws ServiceException {
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
    }
}
