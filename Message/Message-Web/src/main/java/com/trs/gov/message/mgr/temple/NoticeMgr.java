package com.trs.gov.message.mgr.temple;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.UserConstant;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：NoticeMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/28 10:18
 **/
@Component
@Slf4j
public class NoticeMgr extends AbstractSendMessageMgr {
    @Override
    public Map<String, BaseMessageVO> getMessageTemple(CreateNoticeDTO createNoticeDTO) throws Exception {
        String workOrderName = createNoticeDTO.getWorkOrderName().orElseThrow(() -> new ServiceException("获取工单别名失败!"));
        createNoticeDTO.mustNotNull(true,false,true,false);
        BaseMessageVO create = new BaseMessageVO();
        BaseMessageVO copy = new BaseMessageVO();
        create.setTitle("工单已评价");
        create.setContent("您的 "+workOrderName + " 已创建成功");
        copy.setTitle("工单已评价");
        copy.setContent(workOrderName + " 有工单需要您的协助,请查看");
        //构造返回结果
        Map<String, BaseMessageVO> resultMap = new HashMap<>();
        resultMap.put(UserConstant.CREATE_USER, create);
        resultMap.put(UserConstant.HANDLE_USER, null);
        resultMap.put(UserConstant.NEW_COPY_USER, copy);
        resultMap.put(UserConstant.HOST_USER, null);
        return resultMap;
    }

    @Override
    public String key() {
        return OperateNameConstant.CREATE_NOTICE;
    }

    @Override
    public String desc() {
        return null;
    }
}