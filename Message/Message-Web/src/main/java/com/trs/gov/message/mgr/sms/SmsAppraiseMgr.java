package com.trs.gov.message.mgr.sms;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.SmsMessageVO;
import com.trs.gov.message.mgr.AbstractSMSMgr;
import com.trs.user.VO.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName：SmsAppraiseMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/9 11:51
 **/
@Component
@Slf4j
public class SmsAppraiseMgr extends AbstractSMSMgr {
    @Override
    public String key() {
        return OperateNameConstant.APPRAISE_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }

    @Override
    public List<SmsMessageVO> pushSMSMessage(CreateNoticeDTO dto) throws ServiceException {
        List<SmsMessageVO> result = new ArrayList<>();
        // 完成评价   协作工单 + 受理人
        List<TargetUserDTO> userDTOList = null;
        if (!CollectionUtils.isEmpty(dto.getHandleUser())) {
            userDTOList = dto.getHandleUser();
        }
        if (CollectionUtils.isEmpty(userDTOList)) {
            userDTOList = dto.getOldCopyUser();
        } else {
            if (!CollectionUtils.isEmpty(dto.getOldCopyUser())) {
                userDTOList.addAll(dto.getOldCopyUser());
            }
        }
        if (!CollectionUtils.isEmpty(userDTOList)) {
            for (TargetUserDTO userDTO : userDTOList) {
                UserVO userVO = getUserVO(userDTO).orElseThrow(() -> new ServiceException("数据库中无用户[" + userDTO.getUserName() + "]的相关数据!"));
                if (CMyString.isEmpty(userVO.getPhone())) {
                    log.warn("用户[" + userDTO.getUserName() + "]的电话号码为空，发送短信失败!");
                    continue;
                }
                String content = smsMessageHelper.smsHasAppraise(userVO.getTrueName(), dto.getTitle(), dto.getWorkOrderId());
                result.add(new SmsMessageVO(userVO.getPhone(), content));
            }
        }

        return result;
    }
}
