package com.trs.gov.message.mgr;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.constant.WorkOrderTypeConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.management.VO.WorkOrderTypeForNoticeVO;
import com.trs.gov.management.service.WorkOrderTypeService;
import com.trs.gov.message.DO.MessageConfigDO;
import com.trs.gov.message.DTO.MessageConfigDTO;
import com.trs.gov.message.VO.BaseMessageConfigVO;
import com.trs.gov.message.VO.MessageConfigVO;
import com.trs.gov.message.constant.NoticeConstant;
import com.trs.gov.message.helper.MessageChangeHelper;
import com.trs.gov.message.mapper.MessageConfigDOMapper;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName：NoticeMessageConfigMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 10:17
 **/
@Component
@Slf4j
public class NoticeMessageConfigMgr {

    @Autowired
    private MessageConfigDOMapper messageConfigDOMapper;

    @Reference(check = false, timeout = 60000)
    private WorkOrderTypeService workOrderTypeService;

    /**
     * @return com.trs.web.builder.base.RestfulResults<com.trs.gov.message.VO.MessageConfigVO>
     *     判断是不是顶级类型，顶级类型默认是开启所有
     * @Description 获取指定工单类型的配置信息
     * 2.通过工单类型Id获取配置实体
     * 3.如果不为空，就获取数据
     * 如果是跟随父类，就递归去查询父配置，如果还是跟随父配置，继续递归
     * 如果不是跟随父类，直接获取
     * 4.如果为空，获取默认的配置数据
     * 并存入数据库
     * @Param [messageConfigDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/21 15:26
     **/
    public RestfulResults<MessageConfigVO> listMessageConfig(MessageConfigDTO messageConfigDTO) throws Exception {
        //构造返回结果
        MessageConfigVO configVO = new MessageConfigVO();
        configVO.setWorkOrderTypeId(messageConfigDTO.getWorkOrderTypeId());
        //判断是否是顶级类型，如果是，直接返回默认的配置结果
        MessageChangeHelper messageChangeHelper = new MessageChangeHelper();
        try {
            RestfulResults<WorkOrderTypeForNoticeVO> restfulResults = workOrderTypeService.getTypeInfoForOtherPart(String.valueOf(messageConfigDTO.getWorkOrderTypeId()));
            checkRestfulResults(restfulResults);
            WorkOrderTypeForNoticeVO workOrderTypeForNoticeVO= restfulResults.getDatas();
            if(workOrderTypeForNoticeVO.getParentId() != null && workOrderTypeForNoticeVO.getParentId().equals(0L)){
                List<BaseMessageConfigVO> messageConfig = null;
                if(workOrderTypeForNoticeVO.getRootKey().equals(WorkOrderTypeConstant.ROOT_TONG_ZHI_KEY)){
                    String rootDefaultValue = messageChangeHelper.getRootDefaultConfig(NoticeConstant.NOTICE);
                    messageConfig = messageChangeHelper.jsonToOperationList(rootDefaultValue,NoticeConstant.NOTICE).orElseThrow(()->new ServiceException("将配置内容转换成对应实体时出错!"));
                }else{
                    String rootDefaultValue = messageChangeHelper.getRootDefaultConfig(NoticeConstant.WORKORDER);
                    messageConfig = messageChangeHelper.jsonToOperationList(rootDefaultValue,NoticeConstant.WORKORDER).orElseThrow(()->new ServiceException("将配置内容转换成对应实体时出错!"));
                }
                configVO.setRoot(true);
                configVO.setMessageConfig(messageConfig);
                return RestfulResults.ok(configVO).addMsg("获取数据成功!");
            }
            //如果不是顶级类型再进行对应的处理
            Optional<MessageConfigDO> optionalMessageConfigDO = getMessageConfigDO(messageConfigDTO.getWorkOrderTypeId());
            MessageConfigDO messageConfigDO = null;
            if (optionalMessageConfigDO.isPresent()) {
                messageConfigDO = optionalMessageConfigDO.get();
            }
            String rootType = isNoticeType(messageConfigDTO.getWorkOrderTypeId()) ? NoticeConstant.NOTICE : NoticeConstant.WORKORDER;
            String defaultConfig = messageChangeHelper.getDefaultValue(rootType);
            if (messageConfigDO == null) {
                configVO.setIsFolowParentConfig(false);
                //如果不存在，则获取默认值，并且存入数据库
                configVO.setMessageConfig(messageChangeHelper.jsonToOperationList(defaultConfig,rootType).orElseThrow(() -> new ServiceException("配置信息装化成对用的实体失败!")));
                //将默认结果存入数据库中
                messageConfigDTO.setFollowParentConfig(0);
                saveConfig(messageConfigDTO, defaultConfig);
            } else { //如果存在，又分为是否跟随父类
                configVO.setIsFolowParentConfig(messageConfigDO.getIsFollowParentConfig() == null ? false : messageConfigDO.getIsFollowParentConfig().intValue() == 1);
                //如果是跟随父类需要获取 父配置的Id,以及获取等级，顶级为默认配置全部
                if (configVO.getIsFolowParentConfig()) {
                    messageConfigDTO.setMessageConfig(defaultConfig);
                    String numConfig = getParentConfig(messageConfigDTO.getWorkOrderTypeId(), rootType).orElseThrow(() -> new ServiceException("获取父级配置信息失败!"));
                    List<BaseMessageConfigVO> baseMessageConfigVOS = messageChangeHelper.jsonToOperationList(numConfig, rootType).orElseThrow(() -> new ServiceException("配置信息装化成对用的实体失败!"));
                    configVO.setMessageConfig(baseMessageConfigVOS);
                } else {
                    configVO.setMessageConfig(messageChangeHelper.jsonToOperationList(
                            messageConfigDO.getMessageConfig(),rootType).orElseThrow(() -> new ServiceException("配置信息装化成对用的实体失败!")));
                }
            }
        } catch (Exception e) {
            log.error("系统异常，获取数据失败!", e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
        return RestfulResults.ok(configVO).addMsg("查询成功!");
    }

    /**
     * @return java.util.Optional<java.lang.String>
     * @Description 获取父亲配置
     * @Param [typeId]   递归获取夫配置内容
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/18 11:18
     **/
    public Optional<String> getParentConfig(Long workOrderTypeId,String rootType){
        try {
            //获取父配置
            WorkOrderTypeForNoticeVO workOrderTypeForNoticeVO = workOrderTypeService.getTypeInfoForOtherPart(String.valueOf(workOrderTypeId)).getDatas();
            if (workOrderTypeForNoticeVO == null && workOrderTypeForNoticeVO.getParentId() == null) {
                throw new ServiceException("获取父工单类型信息失败!");
            }else if (workOrderTypeForNoticeVO.getParentId().equals(0L)) {
                return getRootConfig(rootType);
            }
            MessageConfigDO messageConfigDO = getMessageConfigDO(workOrderTypeForNoticeVO.getId())
                    .orElseThrow(() -> new ServiceException("获取工单类型【"+workOrderTypeForNoticeVO.getParentId()+"】的配置信息失败!"));
            if (messageConfigDO.getIsFollowParentConfig().intValue() == 1) {
                return getParentConfig(workOrderTypeForNoticeVO.getParentId(),rootType);
            } else if (messageConfigDO.getIsFollowParentConfig().intValue() != 1) {
                return Optional.of(messageConfigDO.getMessageConfig());
            }
        } catch (Exception e) {
            log.error("获取父配置数据失败!",e.getMessage(),e);
        }
        return Optional.empty();
    }

    /**
     * @Description  获取全部开启的配置
     * @Param [config]
     * @return java.util.Optional<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/29 12:52
     **/
    public Optional<String> getRootConfig(String rootType) throws Exception {
        MessageChangeHelper messageChangeHelper = new MessageChangeHelper();
        String rootDefaultValue = messageChangeHelper.getRootDefaultConfig(rootType);
        return Optional.ofNullable(rootDefaultValue.trim());
    }

    /**
     * @Description  判断是不是通知类型
     * @Param [workOrderType]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/11 14:40
     **/
    public boolean isNoticeType(Long workOrderTypeId) throws ServiceException {
        RestfulResults<WorkOrderTypeForNoticeVO> typeInfoForOtherPart = workOrderTypeService.getTypeInfoForOtherPart(String.valueOf(workOrderTypeId));
        checkRestfulResults(typeInfoForOtherPart);
        WorkOrderTypeForNoticeVO workOrderTypeForNoticeVO = typeInfoForOtherPart.getDatas();
        if(workOrderTypeForNoticeVO == null || workOrderTypeForNoticeVO.getParentId() == null){
            throw new ServiceException("不存在ID为【"+workOrderTypeId+"】的工单类型");
        }
        if(!workOrderTypeForNoticeVO.getParentId().equals(0L)){
            return isNoticeType(workOrderTypeForNoticeVO.getParentId());
        }else if(workOrderTypeForNoticeVO.getParentId().equals(0L) && workOrderTypeForNoticeVO.getRootKey().equals(WorkOrderTypeConstant.ROOT_TONG_ZHI_KEY)) {
            return true;
        }else {
            return false;
        }
    }



    /**
     * @return java.util.Optional<com.trs.gov.message.DO.MessageConfigDO>
     * @Description 根据用户查询信息实体
     * @Param [workOrderTypeId]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/18 11:28
     **/
    public Optional<MessageConfigDO> getMessageConfigDO(Long workOrderTypeId) {
        Try<Optional<MessageConfigDO>> tryResult = Try.of(() -> {
            MessageConfigDO messageConfigDO = messageConfigDOMapper.selectOne(
                    new QueryWrapper<MessageConfigDO>().eq("work_order_type_id", workOrderTypeId));
            return Optional.ofNullable(messageConfigDO);
        });
        if (tryResult.isFailure()) {
            log.error("获取配置内容失败!",tryResult.getCause());
            return Optional.empty();
        }
        return tryResult.get();
    }

    /**
     * @return void
     * @Description 初始化保存
     * @Param [messageConfigDTO, defaultValue]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/18 11:16
     **/
    public void saveConfig(MessageConfigDTO messageConfigDTO, String defaultValue) throws ServiceException {
        MessageConfigDO messageConfigDO = new MessageConfigDO();
        messageConfigDO.setWorkOrderTypeId(messageConfigDTO.getWorkOrderTypeId());
        messageConfigDO.setMessageConfig(defaultValue.trim());
        messageConfigDO.setIsFollowParentConfig(messageConfigDTO.getFollowParentConfig());
        Try result = Try.of(() -> {
            //暂时确存入用户相关操作
            Optional<String> loginUser = ContextHelper.getLoginUser();
            String userName = loginUser.orElseThrow(() -> new ServiceException("请登录后使用!"));
            messageConfigDO.setCrUser(userName);
            return messageConfigDOMapper.insert(messageConfigDO);
        });
        if (result.isFailure()) {
            throw new ServiceException("初始化保存数据失败!");
        }
    }

    /**
     * @return void
     * @Description 初始化保存
     * @Param [messageConfigDTO, defaultValue]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/18 11:16
     **/
    public RestfulResults saveMessageConfig(MessageConfigDTO messageConfigDTO) {
        MessageConfigDO messageConfigDO = new MessageConfigDO();

        try {
            String userName = ContextHelper.getLoginUser().orElseThrow(() -> new ServiceException("请登录后使用!"));
            String rootType = isNoticeType(messageConfigDTO.getWorkOrderTypeId()) ? NoticeConstant.NOTICE : NoticeConstant.WORKORDER;
            MessageConfigDO existConfig = messageConfigDOMapper.selectOne(new QueryWrapper<MessageConfigDO>().eq("work_order_type_id", messageConfigDTO.getWorkOrderTypeId()));
            //取消跟随父类  或者跟随父类配置
            if((existConfig.getIsFollowParentConfig().intValue() == 1 && messageConfigDTO.getFollowParentConfig().intValue() == 0) ||
                    (existConfig.getIsFollowParentConfig().intValue() == 0 && messageConfigDTO.getFollowParentConfig().intValue() == 1)){
                existConfig.setIsFollowParentConfig(messageConfigDTO.getFollowParentConfig());
                existConfig.setUpdateTime(new Date());
                existConfig.setUpdateUserName(userName);
                messageConfigDOMapper.updateById(existConfig);
                return RestfulResults.ok("是否跟随父类保存成功!");
            }
            MessageChangeHelper messageChangeHelper = new MessageChangeHelper();
            Optional<String> optional = messageChangeHelper.jsonToNumConfig(JSON.toJSONString(JSON.parse(messageConfigDTO.getMessageConfig())),rootType);
            if (!optional.isPresent()) {
                throw new ServiceException("系统异常，转换获取数字配置失败!");
            }
            //存或者更新
            if (existConfig != null && existConfig.getId() != null) {
                existConfig.setUpdateTime(new Date());
                existConfig.setUpdateUserName(userName);
                existConfig.setMessageConfig(optional.get());
                messageConfigDOMapper.updateById(existConfig);
            } else {
                messageConfigDO.setIsFollowParentConfig(messageConfigDTO.getFollowParentConfig());
                messageConfigDO.setCrUser(userName);
                messageConfigDO.setCrTime(new Date());
                messageConfigDO.setMessageConfig(optional.get());
                messageConfigDO.setWorkOrderTypeId(messageConfigDTO.getWorkOrderTypeId());
                messageConfigDOMapper.insert(messageConfigDO);
            }
            return RestfulResults.ok("保存配置成功!");
        } catch (Exception e) {
            log.error("系统异常，保存数据失败!", e.getMessage(), e);
            return RestfulResults.error("系统异常，保存数据失败!");
        }
    }

    public void checkRestfulResults(RestfulResults restfulResults) throws ServiceException {
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
    }
}
