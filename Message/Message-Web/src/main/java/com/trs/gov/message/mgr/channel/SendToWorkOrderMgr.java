package com.trs.gov.message.mgr.channel;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DO.NoticeMessageDO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.PushChannelConstant;
import com.trs.gov.message.mapper.NoticeMessageDOMapper;
import com.trs.gov.message.mgr.AbstractDifferentChanelMgr;
import com.trs.gov.message.service.impl.WebSocketService;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：SendToWorkOrderMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 10:50
 **/
@Component
@Slf4j
public class SendToWorkOrderMgr extends AbstractDifferentChanelMgr {
    @Autowired
    private NoticeMessageDOMapper noticeMessageDOMapper;
    @Autowired
    private WebSocketService webSocketService;

    @Override
    public void SendMesaageByChannel(CreateNoticeDTO dto, Map<String, BaseMessageVO> templeMessage)  {
        List<NoticeMessageDO> insertDos = new ArrayList<>();
        try {
            //防止脏数据
            for (Map.Entry<String,BaseMessageVO> map : templeMessage.entrySet()){
                String key = map.getKey();
                BaseMessageVO value = map.getValue();
                if(value != null){
                    List<TargetUserDTO> targetUserDTOS = listUserDto(key, dto);
                    sendNoticeMessage(insertDos, value, dto, targetUserDTOS);
                }
            }
            if(!CollectionUtils.isEmpty(insertDos)){
                insertNoticeMessageDos(insertDos);
            }
        } catch (Exception e) {
            log.error("发送平台自身提示出现错误!  "+e.getMessage(),e);
        }
    }

    /**
     * @Description  批量存入数据  PushChannelConstant.INSC_SIZE  一组
     * @Param [insertDos]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/12 18:39
     **/
    private void insertNoticeMessageDos(List<NoticeMessageDO> insertDos){
        double count = insertDos.size()/(PushChannelConstant.INSC_SIZE * 1.0);
        for (int i = 0; i < count;  i ++) {
            int k = i*PushChannelConstant.INSC_SIZE;
            if( (insertDos.size() - k) <= PushChannelConstant.INSC_SIZE){
                insert(insertDos.subList(k, insertDos.size()));
            }else {
                int maxIndex = (i+1)*PushChannelConstant.INSC_SIZE;
                insert(insertDos.subList(k, maxIndex));
            }
        }
    }

    /**
     * @Description  批量插入数据，以及发送红点消息
     * @Param [insert]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/12 18:43
     **/
    private void insert(List<NoticeMessageDO> insert){
        if(CollectionUtils.isEmpty(insert)){
            return ;
        }
        Try.of(()->
                noticeMessageDOMapper.insertBatchDOS(insert)
        ).onFailure(err->log.error(err.getMessage(),err));
        Try.of(()-> {
            for (NoticeMessageDO messageDO : insert) {
                webSocketService.isExistUnRead(messageDO.getReveiveUserName(),String.valueOf(messageDO.getReveiveUnitId()));
            }
            return null;
        }).onFailure(err->log.error(err.getMessage(),err));
    }

    /**
     * @Description  创建通知是否成功，创建成功返回true，反之false
     * @Param [baseMessageVO, createNoticeDTO, sendNoticeDTO]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/18 22:18
     **/
    public void sendNoticeMessage(List<NoticeMessageDO> insertDos,BaseMessageVO baseMessageVO, CreateNoticeDTO createNoticeDTO, List<TargetUserDTO> targetUserDTOS)
    {
        //该类型通知的人为空
        if(CollectionUtils.isEmpty(targetUserDTOS)){
            return ;
        }
        for (TargetUserDTO targetUserDTO : targetUserDTOS) {
            NoticeMessageDO noticeMessageDO = new NoticeMessageDO();
            noticeMessageDO.setTitle(baseMessageVO.getTitle());
            noticeMessageDO.setContent(baseMessageVO.getContent());
            //创建时候，默认为未读
            noticeMessageDO.setIsRead(0);
            noticeMessageDO.setMessageConfigType(createNoticeDTO.getMessageConfigType());
            noticeMessageDO.setCrUser(createNoticeDTO.getCurrentUser());
            noticeMessageDO.setCrUnitId(Long.valueOf(createNoticeDTO.getUnitId()));
            noticeMessageDO.setWorkOrderId(createNoticeDTO.getWorkOrderId());
            noticeMessageDO.setCrTime(new Date());
            noticeMessageDO.setReveiveUserName(targetUserDTO.getUserName());
            noticeMessageDO.setReveiveUnitId(targetUserDTO.getUnitId());
            insertDos.add(noticeMessageDO);
        }
    }

    @Override
    public String key() {
        return PushChannelConstant.SEND_TO_WORKORDER;
    }

    @Override
    public String desc() {
        return PushChannelConstant.SEND_TO_WORKORDER_DESC;
    }


}
