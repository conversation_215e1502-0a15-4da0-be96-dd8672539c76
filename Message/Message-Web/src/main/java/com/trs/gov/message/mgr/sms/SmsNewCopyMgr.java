package com.trs.gov.message.mgr.sms;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UserUnitMappingVO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.SmsMessageVO;
import com.trs.gov.message.mgr.AbstractSMSMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName：SmsNewCopyMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/9 11:49
 **/
@Component
@Slf4j
public class SmsNewCopyMgr extends AbstractSMSMgr {
    @Override
    public String key() {
        return OperateNameConstant.COPY_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }

    @Override
    public List<SmsMessageVO> pushSMSMessage(CreateNoticeDTO dto) throws ServiceException {
        List<SmsMessageVO> result = new ArrayList<>();
        // 新增抄送时   新增的抄送列表
        String loginUnitName = getLoginUnitVO(dto.getUnitId());
        if (!CollectionUtils.isEmpty(dto.getNewCopyUser())) {
            for (TargetUserDTO userDTO : dto.getNewCopyUser()) {
                UserUnitMappingVO userAndUnit = noticeMessageDOMapper.getUserAndUnit(userDTO.getUserName(), userDTO.getUnitId());
                if (Objects.isNull(userAndUnit) || StringUtils.isNullOrEmpty(userAndUnit.getPhone())) {
                    log.warn("用户[" + userDTO.getUserName() + "]的电话号码为空，发送短信失败!");
                    continue;
                }
                String content = smsMessageHelper.smsAssignOrCopy(userAndUnit.getTrueName(), dto.getTitle(), loginUnitName, dto.getWorkOrderId(), userAndUnit.getUnitName());
                result.add(new SmsMessageVO(userAndUnit.getPhone(), content));
            }
        }
        return result;
    }
}
