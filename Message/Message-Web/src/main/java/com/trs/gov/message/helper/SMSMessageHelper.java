package com.trs.gov.message.helper;

import com.trs.gov.message.constant.SMSConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021年04月29日 13:05
 * @Description :
 */
@Component
public class SMSMessageHelper {
    @Value("${sms.template.hasDealEd:[TRUENAME]：您好，您在集约化平台运维系统提交的工单（[WORKORDERID]）已进行受理，请等待处理。后续有问题可联系0851-86893579集约化平台运维服务热线。}")
    private String hasDealEd;
    @Value("${sms.template.hasFinishEd:[TRUENAME]：您好，您在集约化平台运维系统提交的工单（[WORKORDERID]）已进行完成处理，请登录系统进行查看。后续有问题可联系0851-86893579集约化平台运维服务热线。}")
    private String hasFinishEd;
    @Value("${sms.template.hasAppraise:[TRUENAME]：您好，您在集约化平台运维系统处理的工单（[WORKORDERID]）已完成评价，请登录系统进行查看。后续有问题可联系0851-86893579集约化平台运维服务热线。}")
    private String hasAppraise;
    @Value("${sms.template.waitDeal:[TRUENAME]：您好，[UNITNAME]在集约化平台运维系统将工单（[WORKORDERID]）提交到[DEALUNITNAME]进行处理，请登录系统进行查看处理。后续有问题可联系0851-86893579集约化平台运维服务热线。}")
    private String waitDeal;
    @Value("${sms.template.hasRollback:[TRUENAME]：您好，[UNITNAME]在集约化平台运维系统将工单（[WORKORDERID]）回退给[DEALUNITNAME]进行处理，请登录系统进行查看处理。后续有问题可联系0851-86893579集约化平台运维服务热线。}")
    private String hasRollback;
    @Value("${sms.template.assignOrCopy:[TRUENAME]：您好，[UNITNAME]在集约化平台运维系统将工单（[WORKORDERID]）转办/抄送到[DEALUNITNAME]进行处理，请登录系统进行查看处理。后续有问题可联系0851-86893579集约化平台运维服务热线。}")
    private String assignOrCopy;
    @Value("${sms.template.hasMasterUser:[TRUENAME]：您好，[UNITNAME]在集约化平台运维系统创建工单（[WORKORDERID]），请登录系统进行查看处理。后续有问题可联系0851-86893579集约化平台运维服务热线。}")
    private String hasMasterUser;
    @Value("${sms.template.hasNoticeMessage:[TRUENAME]：您好，集约化平台运维工作组在集约化平台工单系统发布了一则通知，工单编号：[WORKORDERID]，请您及时登录系统进行查看处理。后续有问题可联系集约化平台运维工作组（联系电话：0851-86893579）。}")
    private String hasNoticeMessage;

    /**
     * trueName   unitName
     * <AUTHOR>
     * @date 2021/4/29 13:09
     * @param null
     * @return null
     */
    /**
     * @return java.lang.String
     * @Description 由于模板变动，且每条消息的内容不一样
     * @Param [trueName]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2021/4/9 14:20
     **/
    public String smsHasDealEd(String trueName, String title, Long workOrderId) {
        return hasDealEd.replace(SMSConstant.TRUENAME, trueName)
                .replace(SMSConstant.TITLE, title)
                .replace(SMSConstant.WORKORDERID, workOrderId.toString());
    }

    public String smsHasFinishEd(String trueName, String title, Long workOrderId) {
        return hasFinishEd.replace(SMSConstant.TRUENAME, trueName)
                .replace(SMSConstant.TITLE, title)
                .replace(SMSConstant.WORKORDERID, workOrderId.toString());
    }

    public String smsHasAppraise(String trueName, String title, Long workOrderId) {
        return hasAppraise.replace(SMSConstant.TRUENAME, trueName)
                .replace(SMSConstant.TITLE, title)
                .replace(SMSConstant.WORKORDERID, workOrderId.toString());
    }

    public String smsWaitDeal(String trueName, String title, String unitName,  Long workOrderId, String dealUnitName) {
        return waitDeal.replace(SMSConstant.TRUENAME, trueName)
                .replace(SMSConstant.TITLE, title)
                .replace(SMSConstant.UNITNAME, unitName)
                .replace(SMSConstant.WORKORDERID, workOrderId.toString())
                .replace(SMSConstant.DEAL_UNITNAME, dealUnitName);
    }

    public String smsAssignOrCopy(String trueName, String title, String unitName,  Long workOrderId, String dealUnitName) {
        return assignOrCopy.replace(SMSConstant.TRUENAME, trueName)
                .replace(SMSConstant.TITLE, title)
                .replace(SMSConstant.UNITNAME, unitName)
                .replace(SMSConstant.WORKORDERID, workOrderId.toString())
                .replace(SMSConstant.DEAL_UNITNAME, dealUnitName);
    }

    public String smshasRollback(String trueName, String title, String loginUnitName,  Long workOrderId, String unitName) {
        return hasRollback.replace(SMSConstant.TRUENAME, trueName)
                .replace(SMSConstant.TITLE, title)
                .replace(SMSConstant.UNITNAME, loginUnitName)
                .replace(SMSConstant.WORKORDERID, workOrderId.toString())
                .replace(SMSConstant.DEAL_UNITNAME, unitName);
    }

    public String smsHostMaster(String trueName, String title, String loginUnitName, Long workOrderId, String unitName) {
        return hasMasterUser.replace(SMSConstant.TRUENAME, trueName)
                .replace(SMSConstant.TITLE, title)
                .replace(SMSConstant.UNITNAME, loginUnitName)
                .replace(SMSConstant.WORKORDERID, workOrderId.toString());
    }

    public String hasNoticeMessage(String trueName,String title, String unitName, Long workOrderId, String dealUnitName) {
        return hasNoticeMessage.replace(SMSConstant.TRUENAME, trueName)
                .replace(SMSConstant.TITLE, title)
                .replace(SMSConstant.UNITNAME, unitName)
                .replace(SMSConstant.WORKORDERID, workOrderId.toString())
                .replace(SMSConstant.DEAL_UNITNAME, dealUnitName);
    }
}
