package com.trs.gov.message.mgr.sms;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UserUnitMappingVO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.VO.SmsMessageVO;
import com.trs.gov.message.mgr.AbstractSMSMgr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName：SmsAssignMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2021/4/9 11:50
 **/
@Component
@Slf4j
public class SmsAssignMgr extends AbstractSMSMgr {

    @Override
    public String key() {
        return OperateNameConstant.ASSIGN_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }


    @Override
    public List<SmsMessageVO> pushSMSMessage(CreateNoticeDTO dto) throws ServiceException {
        List<SmsMessageVO> result = new ArrayList<>();
        String loginUnitName = getLoginUnitVO(dto.getUnitId());
        // 交办工单
        // 由于这里只发送给新人，所以先要从工单中获取到新人是谁
        String newUserName = noticeMessageDOMapper.getDealUser(dto.getWorkOrderId());
        if (StringUtils.isEmpty(newUserName)) {
            return result;
        }
        if (!CollectionUtils.isEmpty(dto.getHandleUser())) {
            for (TargetUserDTO targetUserDTO : dto.getHandleUser()) {
                if (newUserName.equals(targetUserDTO.getUserName())) {
                    UserUnitMappingVO userAndUnit = noticeMessageDOMapper.getUserAndUnit(targetUserDTO.getUserName(), targetUserDTO.getUnitId());
                    if (Objects.isNull(userAndUnit) || StringUtils.isEmpty(userAndUnit.getPhone())) {
                        log.warn("用户[" + targetUserDTO.getUserName() + "]的电话号码为空，发送短信失败!");
                        continue;
                    }
                    String content = smsMessageHelper.smsAssignOrCopy(userAndUnit.getTrueName(), dto.getTitle(), loginUnitName,  dto.getWorkOrderId(), userAndUnit.getUnitName());
                    result.add(new SmsMessageVO(userAndUnit.getPhone(), content));
                }
            }
        }
        return result;
    }
}

