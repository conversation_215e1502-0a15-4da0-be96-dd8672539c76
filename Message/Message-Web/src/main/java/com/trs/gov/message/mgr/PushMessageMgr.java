package com.trs.gov.message.mgr;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.trs.common.http2.HttpRequest;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.PushToPlatformDTO;
import com.trs.gov.message.constant.PushChannelConstant;
import com.trs.gov.message.constant.XmlParamConstant;
import com.trs.gov.message.util.BuildTokenUtils;
import com.trs.gov.message.util.JsonXmlUtils;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import org.dom4j.DocumentException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName：RetryPushMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/25 15:55
 **/
@Component
@Slf4j
public class PushMessageMgr {
    @Autowired
    private BuildTokenUtils buildTokenUtils;
    @Value("${fail.retry.send.count:2}")
    private Integer retryCount;
    @Value("${not-send-mobile.field:szh}")
    private String fileName;

    private HttpRequest httpRequest = new HttpRequest.Builder(new OkHttpClient.Builder()
            .connectTimeout(4, TimeUnit.SECONDS)
            .readTimeout(3, TimeUnit.SECONDS)
    ).build();

    /**
     * @Description  重传
     * @Param [retryPush, channel]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 16:58
     **/
    public void RetryPushMessageMgr(List<PushToPlatformDTO> retryPush,String channel) {
        if(retryPush.size() == 0){
            log.info(String.format("所有消息推送至第三方平台的【%s】渠道成功!",channel));
        }else{
            log.warn("总共有"+retryPush.size() + "条数据推送至"+channel+"失败!");
            int flag = 1;
            while (flag <= retryCount.intValue()){
                for (PushToPlatformDTO push : retryPush) {
                    if(!push.getIsSuccess()){
                        log.info(String.format("请求地址【%s】,内容【%s】,开始进行第【%s】次重传!",push.getRequestPreUrl(),push.getXmlMessage(),flag));
                        pushMessage(push,channel);
                        push.setRetryCount(flag);
                        log.info("第"+flag+"次重传结束!");
                    }
                }
                flag ++ ;
            }
            //重传了最大次数还是没有发送成功!
            for (PushToPlatformDTO push : retryPush) {
                if(!push.getIsSuccess() && push.getRetryCount().equals(retryCount)){
                    log.warn(String.format("请求Url【%s】,渠道【%s】,内容【%s】,重传了【%s】次还是没有发送成功!",push.getRequestPreUrl(),channel,push.getXmlMessage(),retryCount));
                }else {
                    log.info(String.format("请求Url【%s】,渠道【%s】,内容【%s】,通过重传【%s】次后发送成功!",push.getRequestPreUrl(),channel,push.getXmlMessage(),retryCount));
                }
            }
        }
    }

    /**
     * @Description  第一次发送
     * @Param [pushToPlatformDTOS, channel]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 16:57
     **/
    public void pushMessage(List<PushToPlatformDTO> pushToPlatformDTOS,String channel) {
        if (CollectionUtils.isEmpty(pushToPlatformDTOS)){
            log.info("没有消息推送，到此结束!");
        }else {
            log.info("准备开始推送消息到渠道【"+channel+"】");
            List<PushToPlatformDTO> retryPush = new ArrayList<>();
            for (PushToPlatformDTO pushToPlatformDTO : pushToPlatformDTOS) {
                pushMessage(pushToPlatformDTO,channel);
                if(!pushToPlatformDTO.getIsSuccess()){
                    pushToPlatformDTO.setIsSuccess(false);
                    retryPush.add(pushToPlatformDTO);
                }
            }
            log.info("准推送消息到渠道【"+channel+"】结束! 并开始检验所有消息有没有推送成功！如果存在没有推送成功的消息，则准备开始重传!");
            RetryPushMessageMgr(retryPush,channel);
        }
    }

    /**
     * @Description  构建发送消息的 Http请求，并处理发送成功的结果
     * @Param [dto, channel]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 16:43
     **/
    private void pushMessage(PushToPlatformDTO dto, String channel){
        try {
            String token = buildTokenUtils.getToken().orElseThrow(()->new ServiceException("获取token失败!"));
            String requestUrl = dto.getRequestPreUrl() + token ;
            log.info(String.format("开始发送消息【%s】，请求Url为【%s】", dto.getXmlMessage(),requestUrl));
            RequestBody body = RequestBody.create(MediaType.parse("application/xml; charset=utf-8"), dto.getXmlMessage());
            String response = httpRequest.doPost(requestUrl, body);
            log.info(String.format("渠道【%s】,推送至第三方返回结果为【%s】", channel,response));
            dto.setIsSuccess(getResultInfo(response,channel));
        } catch (Exception e) {
            dto.setIsSuccess(false);
            log.error(e.getMessage(),e);
        }
    }

    /**
     * @Description  判断成功与否
     * @Param [response]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/25 15:46
     **/
    public boolean getResultInfo(String response,String channel) throws DocumentException, ServiceException {
        JSONObject json = JSONObject.parseObject(JsonXmlUtils.xmlToJsonString(response,true));
        JSONObject jsonObject = json.getJSONObject(XmlParamConstant.RESULT);
        String result = jsonObject.getString(XmlParamConstant.STATUS);
        if(result.equalsIgnoreCase(PushChannelConstant.PUSH_SUCCESS)){
            dealResponse(jsonObject);
            return true;
        }else if (result.equalsIgnoreCase(PushChannelConstant.PUSH_FAIL)){
            log.error(String.format("推送至渠道【%s】失败! 原因【%s】",channel,jsonObject.getString(XmlParamConstant.MESSAGE)));
            return false;
        }else{
            throw new ServiceException("推送至第三方平台，返回结果异常! "+response);
        }
    }

    /**
     * @Description  处理 发送成功消息中  未授权的信息
     * @Param [jsonObject]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/15 17:13
     **/
    public void dealResponse(JSONObject jsonObject){
        Try.of(()->{
            if(jsonObject != null){
                JSONObject data = jsonObject.getJSONObject(XmlParamConstant.DATA);
                if (data != null && data.size() > 0 ){
                    JSONArray notSendMobiles = data.getJSONArray(XmlParamConstant.NOT_SEND_MOBILE);
                    for (int i = 0; i < notSendMobiles.size(); i++) {
                        JSONObject notSendMobile = notSendMobiles.getJSONObject(i);
                        if(notSendMobile != null){
                            String phone = notSendMobile.getString(fileName);
                            if(!phone.equals("[]")){
                                log.warn("推送消息至手机号【"+phone+"】相关的微信公众号信失败! 请确认是否与相关微信公众号绑定！");
                            }
                        }
                    }
                }
            }
            return null;
        }).onFailure(err -> log.error(err.getMessage(),err.getCause()));
    }

}
