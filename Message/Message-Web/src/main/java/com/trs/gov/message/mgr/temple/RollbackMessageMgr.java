package com.trs.gov.message.mgr.temple;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.VO.BaseMessageVO;
import com.trs.gov.message.constant.UserConstant;
import com.trs.gov.message.mgr.AbstractSendMessageMgr;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：RollbackMessageMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/17 15:51
 **/
@Component
@Slf4j
public class RollbackMessageMgr extends AbstractSendMessageMgr {
    @Override
    public Map<String, BaseMessageVO> getMessageTemple(CreateNoticeDTO createNoticeDTO) throws Exception {
        String workOrderName = createNoticeDTO.getWorkOrderName().orElseThrow(()->new ServiceException("获取工单别名失败!"));
        createNoticeDTO.mustNotNull(true,true,false,true);
        if(StringUtils.isEmpty(createNoticeDTO.getToUserTrueName())){
            throw new ParamInvalidException("目标用户真实姓名不能为空!");
        }
        BaseMessageVO create = new BaseMessageVO();
        BaseMessageVO handle = new BaseMessageVO();
        BaseMessageVO copy = new BaseMessageVO();
        BaseMessageVO host = new BaseMessageVO();
        create.setTitle("工单回退");
        create.setContent(workOrderName +" 由"+ createNoticeDTO.getCurrentUserTrueName()+"退回至"+ createNoticeDTO.getToUserTrueName());
        handle.setTitle("工单回退");
        handle.setContent(workOrderName +" 由"+ createNoticeDTO.getCurrentUserTrueName()+"退回至"+ createNoticeDTO.getToUserTrueName());
        copy.setTitle("工单回退");
        copy.setContent(workOrderName +" 由"+ createNoticeDTO.getCurrentUserTrueName()+"退回至"+ createNoticeDTO.getToUserTrueName());
        host.setTitle("工单回退");
        host.setContent(workOrderName +" 由"+ createNoticeDTO.getCurrentUserTrueName()+"退回至"+ createNoticeDTO.getToUserTrueName());
        //构造返回结果
        Map<String,BaseMessageVO> resultMap = new HashMap<>();
        resultMap.put(UserConstant.CREATE_USER,create);
        resultMap.put(UserConstant.HANDLE_USER,handle);
        resultMap.put(UserConstant.OLD_COPY_USER,copy);
        resultMap.put(UserConstant.HOST_USER,host);
        return resultMap;
    }

    @Override
    public String key() {
        return OperateNameConstant.ROLLBACK_WORK_ORDER;
    }

    @Override
    public String desc() {
        return null;
    }
}
