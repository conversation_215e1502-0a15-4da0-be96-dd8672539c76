package com.trs.gov.message.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.message.DTO.NoticeMessageDTO;
import com.trs.gov.message.service.impl.NoticeMessageServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：NoticeMessageController
 * @Description : 我的消息列表相关信息
 * <AUTHOR> YangXin
 * @Date 2020/9/18 13:21
 **/
@RestController
@RequestMapping("/message/notice")
public class NoticeMessageController {
    @Autowired
    private NoticeMessageServiceImpl noticeMessageService;

    /**
     * @Description  获取最近消息列表
     * @Param [NoticeMessageDTO]
     * @return com.trs.web.builder.base.RestfulResults
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/17 10:40
     **/
    @ApiOperation(value = "获取小铃铛消息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName",value = "用户名",required = true,type = "string"),
            @ApiImplicitParam(name = "isRead",value = "是否已读  0-未读 1-已读",required = true,type = "integer"),
            @ApiImplicitParam(name = "messageType",value = "消息类型 0-最新消息  1-历史消息",required = false,type = "integer"),
            @ApiImplicitParam(name = "hours",value = "分割时间，默认为48小时",required = false,type = "integer")
    })
    @PostMapping("listNotice")
    public RestfulResults getRecentMessage(NoticeMessageDTO noticeMessageDTO) throws ServiceException {
        BaseUtils.checkDTO(noticeMessageDTO);
        return noticeMessageService.getRecentMessage(noticeMessageDTO);
    }
}
