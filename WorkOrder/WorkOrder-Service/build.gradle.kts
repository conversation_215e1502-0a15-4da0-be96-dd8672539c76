/*
 * This file was generated by the Gradle 'init' task.
 */

plugins {
    `java-library`
}

dependencies {
    api(project(":Core"))
    api(project(":Configuration"))
    api(project(":User-Service"))
    api(project(":SystemManagement-Service"))
    api(project(":Message-Service"))
    api(project(":FileManager-Service"))
    api(project(":Interaction-Service"))
}
description = "WorkOrder-Service"
