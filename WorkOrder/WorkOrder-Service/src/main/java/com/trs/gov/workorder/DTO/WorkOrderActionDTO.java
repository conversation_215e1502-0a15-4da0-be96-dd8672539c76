package com.trs.gov.workorder.DTO;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.file.VO.FileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class WorkOrderActionDTO extends WorkOrderBaseDTO {
    /**
     * 工单ID（定义为String类型方便后续扩充为多条）
     */
    @ApiModelProperty(value = "工单ID")
    private Long workOrderId;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String oprKey;

    /**
     * 转交或回退的的单位id
     */
    @ApiModelProperty(value = "转交或回退的的单位id")
    private Long targetUnitId;

    /**
     * 转交或回退的人员用户名
     */
    @ApiModelProperty(value = "转交或回退的人员用户名")
    private String targetUsername;

    /**
     * 备注信息
     */
    private String option;

    /**
     * 图片列表
     */
    @ApiModelProperty(value = "图片列表")
    private String picList;

    /**
     * 文件列表
     */
    @ApiModelProperty(value = "文件列表")
    private String fileList;

    /**
     * 目标人真实姓名
     */
    private String targetTruename;

    /**
     * 老的处理人用户名
     */
    private Integer oldDealAssignType;

    /**
     * 老的处理人单位id
     */
    private Long oldDealUnitId;

    /**
     * 老的处理人用户名
     */
    private String oldDealUsername;

    /**
     * 指定类型
     */
    private Integer assginType = AssignConstant.NOT_ASSIGN;

    /**
     * 是否响应
     */
    private boolean isAction;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        PreConditionCheck.checkArgument(workOrderId!=null, "工单id不能为空");
        PreConditionCheck.checkArgument(oprKey!=null, "操作类型不能为空");

        if(!StringUtils.isEmpty(picList)){
            List<FileVO> list = JsonUtils.toObject(picList, ArrayList.class, FileVO.class);
            if(list==null){
                throw new ServiceException("图片列表格式有误");
            } else if(list.isEmpty()){
                picList=null;
            }
        }

        if(!StringUtils.isEmpty(fileList)){
            List<FileVO> list = JsonUtils.toObject(fileList, ArrayList.class, FileVO.class);
            if(list==null){
                throw new ServiceException("文件列表格式有误");
            } else if(list.isEmpty()){
                fileList=null;
            }
        }

        return true;
    }
}
