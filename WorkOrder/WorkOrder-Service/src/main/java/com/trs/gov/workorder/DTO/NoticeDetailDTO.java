package com.trs.gov.workorder.DTO;

import com.trs.common.base.PreConditionCheck;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NoticeDetailDTO extends BaseDTO {

    /**
     * 通知id
     */
    @ApiModelProperty(value = "通知id")
    private Long noticeId;

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;


    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        PreConditionCheck.checkArgument(workOrderId!=null,"工单id不能为空");
        return true;
    }
}
