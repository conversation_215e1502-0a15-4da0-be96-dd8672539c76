package com.trs.gov.workorder.DTO;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WorkTimeDTO extends BaseDTO {
    /**
     * 工时id
     */
    @ApiModelProperty(value = "工时id")
    private Long workTimeId;

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 工作名目
     */
    @ApiModelProperty(value = "工作名目")
    private String workDesc;

    /**
     * 工作单位id
     */
    @ApiModelProperty(value = "工作单位id")
    private Long workUnitId;

    /**
     * 工作量（天）
     */
    @ApiModelProperty(value = "工作量")
    private Double workingTime;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        PreConditionCheck.checkArgument(workOrderId!=null || workTimeId!=null, "工单id或工时id不能为空");
        PreConditionCheck.checkArgument(!StringUtils.isEmpty(workDesc), "工作名目不能为空");
        PreConditionCheck.checkArgument(workUnitId!=null, "单位id不能为空");
        PreConditionCheck.checkArgument(workingTime!=null && workingTime>0, "非法工作量");

        return true;
    }
}
