package com.trs.gov.workorder.VO;

import com.trs.gov.core.VO.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NoticeStatisticsVO extends BaseVO {
    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 已阅读
     */
    @ApiModelProperty(value = "已阅读")
    private Long readCount = 0L;

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Long totalCount;

    /**
     * 阅读状态(0：未读，1：已读)
     */
    @ApiModelProperty(value = "阅读状态(0：未读，1：已读)")
    private Integer status;
}
