package com.trs.gov.workorder.VO;

import com.trs.gov.core.VO.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeByMonthVO extends BaseVO {

    /**
     * 时间（yyyy-MM）
     */
    @ApiModelProperty(value = "时间（yyyy-MM")
    private String timeKey;

    /**
     * 通知列表
     */
    @ApiModelProperty(value = "通知列表")
    private List<NoticeVO> list = new ArrayList<>();
}
