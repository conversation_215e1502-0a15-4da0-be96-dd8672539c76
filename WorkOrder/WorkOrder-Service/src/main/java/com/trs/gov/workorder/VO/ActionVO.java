package com.trs.gov.workorder.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActionVO implements Serializable {
    /**
     * 操作key
     */
    @ApiModelProperty(value = "操作key")
    private String oprkey;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String oprname;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActionVO actionVO = (ActionVO) o;
        return Objects.equals(oprkey, actionVO.oprkey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(oprkey);
    }
}
