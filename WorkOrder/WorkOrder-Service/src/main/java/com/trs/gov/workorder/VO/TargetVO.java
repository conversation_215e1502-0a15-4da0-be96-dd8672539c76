package com.trs.gov.workorder.VO;

import com.trs.gov.core.VO.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 抄送、通知目标信息
 * @Author: zuo.kaiyuan
 * @Date: 2021/1/7
 */
@Data
public class TargetVO extends BaseVO {
    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 接收分组id
     */
    @ApiModelProperty(value = "分组id")
    private Long groupId;

    /**
     * 接收分组名称
     */
    @ApiModelProperty(value = "分组名称")
    private String groupName;

    /**
     * 目标单位id
     */
    @ApiModelProperty(value = "通知的目标单位id")
    private Long targetUnitId;

    /**
     * 目标单位
     */
    @ApiModelProperty(value = "通知的目标单位")
    private String targetUnit;

    /**
     * 目标人员用户名
     */
    @ApiModelProperty(value = "通知的目标人员用户名")
    private String targetUsername;

    /**
     * 目标人员真实姓名
     */
    @ApiModelProperty(value = "通知的目标人员真实姓名")
    private String targetTruename;

    /**
     * 接收类型（1：个人，2：单位，3：分组，4：全部）
     */
    @ApiModelProperty(value = "发通知类型（1：个人，2：单位，3：分组，4：全部）")
    private Integer type;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date crTime;
}
