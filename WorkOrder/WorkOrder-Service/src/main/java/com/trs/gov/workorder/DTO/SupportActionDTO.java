package com.trs.gov.workorder.DTO;

import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 判断操作权限需要的信息
 */
@Data
@AllArgsConstructor
public class SupportActionDTO {
    /**
     * 工单
     */
    private WorkOrderDO workOrderDO;
    /**
     * 登录用户名
     */
    private String loginUsername;
    /**
     * 登录单位
     */
    private UnitVO loginUnit;
    /**
     * 登录单位所在分组
     */
    private List<Long> loginGroupIdList = new ArrayList<>();
    /**
     * 工单抄送列表
     */
    private List<CCDO> ccdoList = new ArrayList<>();

    public SupportActionDTO(WorkOrderDO workOrderDO, String loginUsername, UnitVO loginUnit) {
        this.workOrderDO = workOrderDO;
        this.loginUsername = loginUsername;
        this.loginUnit = loginUnit;
    }
}
