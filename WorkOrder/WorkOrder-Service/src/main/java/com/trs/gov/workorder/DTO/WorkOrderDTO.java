package com.trs.gov.workorder.DTO;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.*;

@Data
public class WorkOrderDTO extends AsyncDTO {
    private Long id;

    /**
     * 工单顶级分类id
     */
    @ApiModelProperty(value = "工单顶级分类id")
    private Long workOrderTopTypeId;

    /**
     * 工单类型id
     */
    @ApiModelProperty(value = "工单类型id")
    private Long workOrderTypeId;

    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id")
    private Long siteId;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容")
    private String content;


    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 图片附件
     */
    @ApiModelProperty(value = "图片附件")
    private String piclist;

    /**
     * 文件附件
     */
    @ApiModelProperty(value = "文件附件")
    private String filelist;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 期望完成时间
     */
    @ApiModelProperty(value = "期望完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedEndDate;

    /**
     * 主办单位id
     */
    @ApiModelProperty(value = "主办单位id")
    private Long hostUnitId;

    /**
     * 主办人指定类型（1：指定到人，2：指定到单位）
     */
    private Integer hostAssignType;

    /**
     * 主办人用户名
     */
    @ApiModelProperty(value = "主办人用户名")
    private String hostUsername;

    /**
     * 受理单位id
     */
    @ApiModelProperty(value = "受理单位id")
    private Long dealUnitId;

    /**
     * 处理人指定类型（1：指定到人，2：指定到单位）
     */
    private Integer dealAssignType;

    /**
     * 受理人用户名
     */
    @ApiModelProperty(value = "受理人用户名")
    private String dealUsername;

    /**
     * 渠道类型（0：全部, 1：APP, 2：网站, 3：微信, 4：微博）
     */
    @ApiModelProperty(value = "渠道类型（0：全部, 1：APP, 2：网站, 3：微信, 4：微博）")
    private Integer mediaType;

    /**
     * 抄送列表
     */
    @ApiModelProperty(value = "抄送列表")
    private String cclist;

    /**
     * 抄送列表集合
     */
    private List<CCDTO> ccDTOs = new ArrayList<>();

    /**
     * 新增抄送列表集合
     */
    private List<CCDTO> newCCDTOs = new ArrayList<>();

    /**
     * 新增抄送列表集合
     */
    private List<CCDO> oldCCDOs = new ArrayList<>();

    /**
     * 通知列表
     */
    @ApiModelProperty(value = "通知列表")
    private String noticelist;

    /**
     * 通知列表
     */
    private List<NoticeDTO> noticeDTOS = new ArrayList<>();

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否创建
     */
    private Boolean isCreate;

    /**
     * 是否修改了受理人
     */
    private Boolean isNewDeal = false;

    private Set<Long> unitIdSet = new HashSet<>();

    private Set<String> usernameSet = new HashSet<>();

    /**
     *  来源
     */
    private String source;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        PreConditionCheck.checkArgument(workOrderTopTypeId!=null,"工单顶级分类id");
        PreConditionCheck.checkArgument(!StringUtils.isEmpty(content) &&
                content.length()>=10 && content.length()<=2000,"内容长度为10到2000");

        if(!StringUtils.isEmpty(piclist)){
            List<FileVO> list = JsonUtils.toObject(piclist, ArrayList.class, FileVO.class);
            if(list==null){
                throw new ServiceException("图片列表格式有误");
            } else if(list.isEmpty()){
                piclist=null;
            }
        }

        if(!StringUtils.isEmpty(filelist)){
            List<FileVO> list = JsonUtils.toObject(filelist, ArrayList.class, FileVO.class);
            if(list==null){
                throw new ServiceException("文件列表格式有误");
            } else if(list.isEmpty()){
                filelist=null;
            }
        }

        return true;
    }
}
