package com.trs.gov.workorder.service;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.*;
import com.trs.gov.workorder.VO.*;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-09 14:12
 * @version 1.0
 * @since 1.0
 * 工单服务接口
 */
public interface IWorkOrderService {
    /**
     * 新建或编辑工单<BR>
     *
     * @param workOrderDTO 请求参数
     * @return 新建或编辑结果
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 18:14
     */
    public RestfulResults saveOrUpdateWorkOrder(WorkOrderDTO workOrderDTO) throws ServiceException;

    /**
     * 根据ID获取相关工单详情<BR>
     *
     * @param workOrderSearchDTO 工单ID
     * @return 相关工单数据
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 18:20
     */
    public RestfulResults<WorkOrderDetailVO> getWorkOrderById(WorkOrderSearchDTO workOrderSearchDTO) throws ServiceException;

    /**
     * 工单相关操作<BR>
     *
     * @param oprKey 操作类型
     * @param dto    请求参数
     * @return 操作结果报告
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:09
     */
    public RestfulResults doWorkOrderAction(String oprKey, WorkOrderBaseDTO dto) throws ServiceException;

    /**
     * 获取工单列表<BR>
     *
     * @param workOrderSearchDTO 查询条件
     * @return 工单列表
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:05
     */
    public RestfulResults<List<WorkOrderVO>> searchWorkOrder(WorkOrderSearchDTO workOrderSearchDTO) throws ServiceException;

    /**
     * 根据范围查找工单
     *
     * @param workOrderSearchByScopeDTO 查询条件
     * @return 工单列表
     * @throws ServiceException 服务异常
     */
    public RestfulResults<List<WorkOrderVO>> searchWorkOrderByScope(WorkOrderSearchByScopeDTO workOrderSearchByScopeDTO) throws ServiceException;

    /**
     * 查找超时工单
     *
     * @param WorkOrderSearchToOverTimeDTO 查询条件
     * @return 工单列表
     * @throws ServiceException 服务异常
     */
    public RestfulResults<List<WorkOrderVO>> searchWorkOrderToOverTime(WorkOrderSearchToOverTimeDTO WorkOrderSearchToOverTimeDTO) throws ServiceException;

    /**
     * 查找所有使用的工单类型id
     *
     * @return 工单列表
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:05
     */
    public RestfulResults<List<Long>> searchUsedWorkOrderTypeId();

    /**
     * 查找通知
     *
     * @param noticeSearchDTO 查询条件
     * @return
     * @throws ServiceException
     */
    public RestfulResults<List<NoticeByMonthVO>> searchNotice(NoticeSearchDTO noticeSearchDTO) throws ServiceException;

    /**
     * 查找正在使用的站点id列表(未评价的工单)
     *
     * @return 工站点id列表
     * @throws ServiceException 服务异常
     */
    public RestfulResults<List<Long>> searchUsedSiteId();

    /**
     * 导出工单数据
     *
     * @return 工站点id列表
     * @throws ServiceException 服务异常
     */
    public List<WorkOrderExcel> exportWorkOrder(WorkOrderSearchDTO dto) throws ServiceException;
    /**
     * @Description  导入老工单数据
     * @Param [dto]
     * @return com.trs.gov.workorder.VO.WorkOrderVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 9:36
     **/
    public WorkOrderVO exportWorkOrder(WorkOrderExportDTO dto) throws ServiceException;

    /**
     * 抄送给我的(我的单位)工单id列表
     * @param ccToMyDTO
     * @return
     * @throws ServiceException
     */
    public RestfulResults<List<Long>> findCCtoMeWorkOrderIds(CCToMyDTO ccToMyDTO) throws ServiceException;

    /**
     * 根据抄送查询工单id列表
     * @param ccSearchDTO
     * @return
     * @throws ServiceException
     */
    public RestfulResults<List<Long>> findWorkOrderIdsByCCSearch(CCSearchDTO ccSearchDTO) throws ServiceException;

    /**
     * 查询通知的接收人数、已读数
     * @param workOrderIds
     * @return
     * @throws ServiceException
     */
    public RestfulResults<List<NoticeStatisticsVO>> findNoticeStatistics(WorkOrderSearchDTO dto) throws Exception;

    /**
     * 判断用户是否有权限创建新的工单<br></>
     * （依据-当前人员有待评价的工单就不能创建新的工单）
     * @param dto
     * @return
     */
    RestfulResults limitCreateWorkOrder(WorkOrderSearchDTO dto) throws ServiceException;

    void saveOrUpdateWorkOrderByOpenApi(OpenApiDTO dto) throws ServiceException;
}
