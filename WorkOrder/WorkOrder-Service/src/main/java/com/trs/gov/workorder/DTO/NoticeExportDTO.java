package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

/**
 * @ClassName：NoticeExportDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/11/26 17:50
 **/
@Data
public class NoticeExportDTO extends BaseDTO {

    /**
     * 通知的目标单位id
     */
    private Long targetUnitId;

    /**
     * 通知的目标单位
     */
    private String targetUnit;

    /**
     * 发通知类型 （1: 发通知给个人， 2：发通知给单位）
     */
    private Integer type;

    /**
     * 通知目标人用户名
     */
    private String targetUsername;

    /**
     * 通知目标人真实姓名
     */
    private String targetTruename;

    @Override
    public boolean isValid() throws ServiceException {
        if(targetUnitId == null){
            throw new ServiceException("目标单位Id不能为空!");
        }
        if(CMyString.isEmpty(targetUnit)){
            throw new ServiceException("目标单位名称不能为空!");
        }
        if(type == null){
            throw new ServiceException("发送通知的类型不能为空!");
        }
        //指定到人   需要单位的信息
        //指定到单位   也需要单位下所有人的信息，所以以下两个参数也必传  一条指定到单位的数据可以对应多条 NoticeExportDTO 数据
        if(CMyString.isEmpty(targetUsername)){
            throw new ServiceException("目标人用户名不能为空!");
        }
        if(CMyString.isEmpty(targetTruename)){
            throw new ServiceException("目标人真实姓名不能为空!");
        }
        return true;
    }
}
