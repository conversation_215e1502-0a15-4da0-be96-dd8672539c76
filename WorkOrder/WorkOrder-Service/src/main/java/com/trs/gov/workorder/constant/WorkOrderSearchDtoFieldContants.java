package com.trs.gov.workorder.constant;

/**
 * @ClassName：WorkOrderFieldContants
 * @D数据库cription : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:00
 **/
public class WorkOrderSearchDtoFieldContants {

    /**
     * 顶级工单的id串
     **/
    public static final String WORK_ORDER_TOP_TYPE_ID_LIST = "workOrderTopTypeIdList";
    /**
     * 数据库 顶级工单类型id
     **/
    public static final String DB_FIELD_WORK_ORDER_TOP_TYPE_ID = "work_order_top_type_id";

    /**
     * 工单类型id
     **/
    public static final String WORK_ORDER_TYPE_ID_LIST = "workOrderTypeIdList";
    /**
     * 数据库 工单类型id
     **/
    public static final String DB_FIELD_WORK_ORDER_TYPE_ID = "work_order_type_id";

    /**
     * 工单id串
     **/
    public static final String WORK_ORDER_ID_LIST = "idList";
    /**
     * 数据库 工单id检索字段
     **/
    public static final String DB_FIELD_WORK_ORDER__ID = "id";

    /**
     * 主办单位id串
     **/
    public static final String HOST_UNIT_ID_LIST = "hostUnitIdList";
    /**
     * 数据库 主办单位检索字段
     **/
    public static final String DB_FIELD_HOST_UNIT_ID = "host_unit_id";
    /**
     * 发起单位id串
     **/
    public static final String CR_UNIT_ID_LIST = "crUnitIdList";
    /**
     * 数据库 发起单位检索字段
     **/
    public static final String DB_FIELD_CR_UNIT_ID = "cr_unit_id";
    /**
     * 受理单位id串
     **/
    public static final String DEAL_UNIT_ID_LIST = "dealUnitIdList";
    /**
     * 数据库 受理单位检索字段
     **/
    public static final String DB_FIELD_DEAL_UNIT_ID = "deal_unit_id";
    /**
     * 抄送单位id串
     **/
    public static final String HOST_CC_ID_LIST = "ccUnitIdList";
    /**
     * 数据库 抄送单位检索字段
     **/
    public static final String DB_FIELD_CC_UNIT_ID = "cc_unit_id";
    /**
     * 主办单位id串
     **/
    public static final String STATUS = "status";
    /**
     * 数据库 主办单位检索字段
     **/
    public static final String DB_FIELD_STATUS = "status";
    /**
     * 主办单位id串
     **/
    public static final String CR_TIME = "crTime";
    /**
     * 数据库 主办单位检索字段
     **/
    public static final String DB_FIELD_CR_TIME = "cr_time";
    /**
     * 主办单位id串
     **/
    public static final String EXPECT_END_TIME = "expectedEndDate";
    /**
     * 数据库 主办单位检索字段
     **/
    public static final String DB_FIELD_EXPECT_END_TIME = "expected_end_date";
    /**
     * 数据库 处理类型
     **/
    public static final String Es_DEAL_ASSIGN_TYPE = "deal_assign_type";
    /**
     * 数据库 受理人
     **/
    public static final String DB_DEAL_USERNAME = "deal_username";
    /**
     * 数据库 创建人用户名
     **/
    public static final String DB_CR_USERNAME = "cr_username";
    /**
     * 站点列表
     **/
    public static final String SITE_ID = "siteIdList";
    /**
     * 数据库 站点
     **/
    public static final String DB_SITE_ID = "site_id";
    /**
     * 检索类型
     **/
    public static final String WORK_ORDER_SEARCH_TYPE = "type";

    /**
     * 检索关键字
     **/
    public static final String WORK_ORDER_KEY_WORDS = "keywords";
    /**
     * 数据库 工单id检索字段
     **/
    public static final String DB_FIELD_WORK_ORDER_SITE_NAME = "sitename";

    /**
     * 数据库 工单内容
     */
    public static final String DB_FIELD_WORK_ORDER_CONTENT = "content";

    /**
     * 数据库 工单类型名称
     */
    public static final String DB_FIELD_WORK_ORDER_TOP_TYPE_NAME = "work_order_type_name";



}
