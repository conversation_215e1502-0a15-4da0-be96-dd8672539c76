package com.trs.gov.workorder.DO;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-09 14:21
 * @version 1.0
 * @since 1.0
 * 工单对象
 */
@Entity
@Data
@TableName("work_order")
public class WorkOrderDO extends BaseDO {
    /**
     * 工单顶级类别id
     */
    @Column(name = "work_order_top_type_id")
    @ApiModelProperty(value = "工单顶级类别id")
    private Long workOrderTopTypeId;

    /**
     * 工单顶级类别id
     */
    @Column(name = "work_order_top_type_name")
    @ApiModelProperty(value = "工单顶级类别名")
    private String workOrderTopTypeName;

    /**
     * 工单类别id
     */
    @Column(name = "work_order_type_id")
    @ApiModelProperty(value = "工单类别id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long workOrderTypeId;

    /**
     * 工单类别名
     */
    @Column(name = "work_order_type_name")
    @ApiModelProperty(value = "工单类别名")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String workOrderTypeName;

    /**
     * 发起单位id
     */
    @Column(name = "cr_unit_id")
    @ApiModelProperty(value = "发起单位id")
    private Long crUnitId;

    /**
     * 发起单位
     */
    @Column(name = "cr_unit")
    @ApiModelProperty(value = "发起单位")
    private String crUnit;

    /**
     * 发起人
     */
    @Column(name = "cr_username")
    @ApiModelProperty(value = "发起人")
    private String crUsername;

    /**
     * 发起人
     */
    @Column(name = "cr_truename")
    @ApiModelProperty(value = "发起人")
    private String crTruename;

    /**
     * 站点id
     */
    @Column(name = "site_id")
    @ApiModelProperty(value = "站点id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long siteId;

    /**
     * 站点名
     */
    @Column(name = "sitename")
    @ApiModelProperty(value = "站点名")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String sitename;

    /**
     * 描述
     */
    @Column(name = "content")
    @ApiModelProperty(value = "描述")
    private String content;

    /**
     * 描述
     */
    @Column(name = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 优先级
     */
    @Column(name = "priority")
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 主办单位id
     */
    @Column(name = "host_unit_id")
    @ApiModelProperty(value = "主办单位id")
    private Long hostUnitId;

    /**
     * 主办单位
     */
    @Column(name = "host_unit")
    @ApiModelProperty(value = "主办单位")
    private String hostUnit;

    /**
     * 主办人指定类型（1：指定到人，2：指定到单位）
     */
    @Column(name = "host_assign_type")
    @ApiModelProperty(value = "主办人指定类型（1：指定到人，2：指定到单位）")
    private Integer hostAssignType;

    /**
     * 主办人用户名
     */
    @Column(name = "host_username")
    @ApiModelProperty(value = "主办人用户名")
    private String hostUsername;

    /**
     * 主办人真实姓名
     */
    @Column(name = "host_truename")
    @ApiModelProperty(value = "主办人真实姓名")
    private String hostTruename;

    /**
     * 受理单位id
     */
    @Column(name = "deal_unit_id")
    @ApiModelProperty(value = "受理单位id")
    private Long dealUnitId;

    /**
     * 受理单位
     */
    @Column(name = "deal_unit")
    @ApiModelProperty(value = "受理单位")
    private String dealUnit;

    /**
     * 处理人指定类型（1：指定到人，2：指定到单位）
     */
    @Column(name = "deal_assign_type")
    @ApiModelProperty(value = "处理人指定类型（1：指定到人，2：指定到单位）")
    private Integer dealAssignType;

    /**
     * 受理人用户名
     */
    @Column(name = "deal_username")
    @ApiModelProperty(value = "受理人用户名")
    private String dealUsername;

    /**
     * 受理人真实姓名
     */
    @Column(name = "deal_truename")
    @ApiModelProperty(value = "受理人真实姓名")
    private String dealTruename;

    /**
     * 渠道类型
     */
    @Column(name = "media_type")
    @ApiModelProperty(value = "渠道类型")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer mediaType;

    /**
     * 期望完成时间
     */
    @Column(name = "expected_end_date")
    @ApiModelProperty(value = "期望完成时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date expectedEndDate;

    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    @ApiModelProperty(value = "完成时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date finishTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 收到工单时间
     */
    @Column(name = "receive_time")
    private Date receiveTime;

    /**
     * 是否删除
     */
    @Column(name = "is_delete")
    private Integer isDelete;

    /**
     * 响应时间
     */
    @Column(name = "action_time")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date actionTime;

    /**
     * 是否是回退
     */
    @Column(name = "is_return")
    private Integer isReturn;


    /**
     * 来源
     */
    @Column(name = "source")
    @TableField(value = "source")
    @ApiModelProperty(value = "来源")
    private String source = WorkOrderConstant.DEFAULT_SOURCE;
}
