package com.trs.gov.workorder.DTO;

import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
public class WorkOrderBaseDTO extends AsyncDTO {
    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
