package com.trs.gov.workorder.VO;

import com.trs.gov.file.VO.FileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class WorkOrderDetailVO extends WorkOrderVO{
    /**
     * 抄送列表
     */
    @ApiModelProperty(value = "抄送列表")
    private List<CCVO> ccList = new ArrayList<>();

    /**
     * 通知列表
     */
    @ApiModelProperty(value = "通知列表")
    private List<NoticeBaseVO> noticeList = new ArrayList<>();

    /**
     * 操作列表
     */
    @ApiModelProperty(value = "操作列表")
    private List<ActionVO> actionList = new ArrayList<>();

    /**
     * 图片列表
     */
    @ApiModelProperty(value = "图片列表")
    private List<FileVO> picList;

    /**
     * 文件列表
     */
    @ApiModelProperty(value = "文件列表")
    private List<FileVO> fileList;
}
