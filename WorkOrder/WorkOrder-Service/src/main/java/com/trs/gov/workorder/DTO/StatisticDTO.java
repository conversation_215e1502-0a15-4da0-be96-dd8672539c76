package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * @ClassName：StatisticDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/29 14:54
 **/
@Data
public class StatisticDTO extends BaseDTO {
    /**
     * 用户名
     **/
    private String userName;
    /**
     * 工单类型
     **/
//    private String userName;


    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
