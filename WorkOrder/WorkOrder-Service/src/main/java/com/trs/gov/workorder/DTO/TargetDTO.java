package com.trs.gov.workorder.DTO;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@Data
public class TargetDTO extends BaseDTO {
    /**
     * 目标分组id
     */
    @ApiModelProperty(value = "分组id")
    private Long groupId;

    /**
     * 目标单位id
     */
    @ApiModelProperty(value = "抄送的接收单位id")
    private Long targetUnitId;

    /**
     * 目标用户名
     */
    @ApiModelProperty(value = "抄送接收者用户名")
    private String targetUsername;

    /**
     * 类型 （1: 个人， 2：单位， 3：分组， 4：全部）
     */
    @ApiModelProperty(value = "类型 （1: 个人， 2：单位， 3：分组， 4：全部）")
    private Integer type;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if(AssignConstant.ASSIGN_TO_UNIT.equals(type) || AssignConstant.ASSIGN_TO_USER.equals(type)){
            PreConditionCheck.checkArgument(targetUnitId!=null,"目标单位不能为空");
            if(AssignConstant.ASSIGN_TO_USER.equals(type)){
                PreConditionCheck.checkArgument(!StringUtils.isEmpty(targetUsername),"目标人不能为空");
            }
        } else if(AssignConstant.ASSIGN_TO_GROUP.equals(type)){
            PreConditionCheck.checkArgument(groupId!=null,"目标分组不能为空");
        } else if(!AssignConstant.ASSIGN_TO_ALL.equals(type)){
            throw new ServiceException("未知类型【"+ type +"】");
        }

        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TargetDTO)) return false;
        TargetDTO targetDTO = (TargetDTO) o;
        return Objects.equals(getGroupId(), targetDTO.getGroupId()) &&
                Objects.equals(getTargetUnitId(), targetDTO.getTargetUnitId()) &&
                Objects.equals(getTargetUsername(), targetDTO.getTargetUsername()) &&
                Objects.equals(getType(), targetDTO.getType());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getGroupId(), getTargetUnitId(), getTargetUsername(), getType());
    }
}
