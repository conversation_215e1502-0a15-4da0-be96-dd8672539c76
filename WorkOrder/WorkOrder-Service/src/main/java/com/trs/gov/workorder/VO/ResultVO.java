package com.trs.gov.workorder.VO;

import com.trs.gov.core.VO.BaseVO;
import com.trs.gov.workorder.constant.ResultConstant;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ResultVO extends BaseVO {
    /**
     * 转态值
     */
    private String result;

    /**
     * 标题
     */
    private String title;

    /**
     * 详细
     */
    private String detail;

    public static ResultVO createSuccess(String title, String detail){
        return new ResultVO(ResultConstant.SUCCESS, title, detail);
    }

    public static ResultVO createFail(String title, String detail){
        return new ResultVO(ResultConstant.FALI, title, detail);
    }
}
