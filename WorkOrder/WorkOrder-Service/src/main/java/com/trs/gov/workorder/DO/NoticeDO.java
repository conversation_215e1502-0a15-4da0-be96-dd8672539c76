package com.trs.gov.workorder.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Data
@TableName("notice")
public class NoticeDO extends BaseDO {
    /**
     * 工单id
     */
    @Column(name = "work_order_id")
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 通知的目标分组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "分组id")
    private Long groupId;

    /**
     * 通知的目标单位id
     */
    @Column(name = "target_unit_id")
    @ApiModelProperty(value = "通知的目标单位id")
    private Long targetUnitId;

    /**
     * 通知的目标单位
     */
    @Column(name = "target_unit")
    @ApiModelProperty(value = "通知的目标单位")
    private String targetUnit;

    /**
     * 通知的目标人员用户名
     */
    @Column(name = "target_username")
    @ApiModelProperty(value = "通知的目标人员用户名")
    private String targetUsername;

    /**
     * 通知的目标人员真实姓名
     */
    @Column(name = "target_truename")
    @ApiModelProperty(value = "通知的目标人员真实姓名")
    private String targetTruename;

    /**
     * 发通知类型 （1：发通知给个人，2：发通知给单位，3：发通知给全部）
     */
    @Column(name = "type")
    @ApiModelProperty(value = "发通知类型 （1：发通知给个人，2：发通知给单位，3：发通知给全部）")
    private Integer type;
}
