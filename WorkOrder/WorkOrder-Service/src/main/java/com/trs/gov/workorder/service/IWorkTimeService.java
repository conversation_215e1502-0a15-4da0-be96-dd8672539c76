package com.trs.gov.workorder.service;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.WorkTimeDTO;
import com.trs.gov.workorder.DTO.WorkTimeExportDTO;
import com.trs.gov.workorder.DTO.WorkTimeQueryDTO;
import com.trs.gov.workorder.VO.WorkTimeVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

public interface IWorkTimeService {
    /**
     * 添加/编辑工时<BR>
     *
     * @param dto 请求参数
     * @return 保存情况
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-11 12:49
     */
    public RestfulResults<Report> saveOrUpdateWorkTime(WorkTimeDTO dto) throws ServiceException;

    /**
     * 删除工时<BR>
     *
     * @param dto 工时id
     * @return 删除情况
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-11 12:49
     */
    public RestfulResults<Report> deleteWorkTime(WorkTimeDTO dto) throws ServiceException;

    /**
     * 查询工时<BR>
     *
     * @param dto 请求参数
     * @return 工时列表
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-11 12:49
     */
    public RestfulResults<List<WorkTimeVO>> queryWorkTime(WorkTimeQueryDTO dto) throws ServiceException;

    /**
     * @Description  工时记录迁移
     * @Param [dto]
     * @return com.trs.gov.workorder.VO.WorkTimeVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/1 14:55
     **/
    public WorkTimeVO exportWorkTime(WorkTimeExportDTO dto)throws ServiceException;
}
