package com.trs.gov.workorder.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Data
@TableName("cc")
public class CCDO extends BaseDO {
    /**
     * 工单id
     */
    @Column(name = "work_order_id")
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 抄送的接收分组id
     */
    @Column(name = "group_id")
    @ApiModelProperty(value = "分组id")
    private Long groupId;

    /**
     * 抄送单位id
     */
    @Column(name = "cr_unit_id")
    @ApiModelProperty(value = "抄送的单位id")
    private Long crUnitId;

    /**
     * 抄送单位
     */
    @Column(name = "cr_unit")
    @ApiModelProperty(value = "抄送的单位")
    private String crUnit;

    /**
     * 抄送人员用户名
     */
    @Column(name = "cr_username")
    @ApiModelProperty(value = "抄送人员用户名")
    private String crUsername;

    /**
     * 抄送人员真实姓名
     */
    @Column(name = "cr_truename")
    @ApiModelProperty(value = "抄送人员真实姓名")
    private String crTruename;

    /**
     * 抄送的目标单位id
     */
    @Column(name = "target_unit_id")
    @ApiModelProperty(value = "抄送的目标单位id")
    private Long targetUnitId;

    /**
     * 抄送的目标单位
     */
    @Column(name = "target_unit")
    @ApiModelProperty(value = "抄送的目标单位")
    private String targetUnit;

    /**
     * 抄送的目标人员用户名
     */
    @Column(name = "target_username")
    @ApiModelProperty(value = "抄送的目标人员用户名")
    private String targetUsername;

    /**
     * 抄送的目标人员真实姓名
     */
    @Column(name = "target_truename")
    @ApiModelProperty(value = "抄送的目标人员真实姓名")
    private String targetTruename;

    /**
     * 抄送类型 （1: 抄送给个人， 2：抄送给单位）
     */
    @Column(name = "type")
    @ApiModelProperty(value = "抄送类型 （1: 抄送给个人， 2：抄送给单位）")
    private Integer type;
}
