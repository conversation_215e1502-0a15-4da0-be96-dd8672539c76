package com.trs.gov.workorder.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Data
@TableName("work_time")
public class WorkTimeDO extends BaseDO {
    /**
     * 创建人单位
     */
    @Column(name = "cr_unit_id")
    @ApiModelProperty(value = "创建人单位")
    private Long crUnitId;

    /**
     * 创建人用户名
     */
    @Column(name = "cr_username")
    @ApiModelProperty(value = "创建人用户名")
    private String crUsername;

    /**
     * 工单id
     */
    @Column(name = "work_order_id")
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 工作名目
     */
    @Column(name = "work_desc")
    @ApiModelProperty(value = "工作名目")
    private String workDesc;

    /**
     * 单位id
     */
    @Column(name = "work_unit_id")
    @ApiModelProperty(value = "单位id")
    private Long workUnitId;

    /**
     * 单位
     */
    @Column(name = "work_unit")
    @ApiModelProperty(value = "单位")
    private String workUnit;

    /**
     * 工作量（天）
     */
    @Column(name = "working_time")
    @ApiModelProperty(value = "工作量")
    private double workingTime;
}
