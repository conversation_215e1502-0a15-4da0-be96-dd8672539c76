package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

/**
 * @ClassName：CCExportDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/11/26 17:50
 **/
@Data
public class CCExportDTO extends BaseDTO {

    /**
     * 抄送的接收单位id
     */
    private Long targetUnitId;
    /**
     * 抄送的接收单位 名称
     */
    private String targetUnit;
    /**
     * 抄送接收者用户名
     */
    private String targetUsername;
    /**
     * 抄送接收者 的真实用户名
     */
    private String targetTruename;

    /**
     * 抄送类型 （1: 抄送给个人， 2：抄送给单位）
     */
    private Integer type;

    @Override
    public boolean isValid() throws ServiceException {
        if(targetUnitId == null){
            throw new ServiceException("目标单位Id不能为空!");
        }
        if(CMyString.isEmpty(targetUnit)){
            throw new ServiceException("目标单位名称不能为空!");
        }
        if(type == null){
            throw new ServiceException("发送通知的类型不能为空!");
        }
        //指定到人   需要单位的信息
        //指定到单位   需要该单位负责人的信息信息   一条指定到单位的数据对应一条CCExportDTO数据
        if(CMyString.isEmpty(targetUsername)){
            throw new ServiceException("目标人用户名不能为空!");
        }
        if(CMyString.isEmpty(targetTruename)){
            throw new ServiceException("目标人真实姓名不能为空!");
        }
        return false;
    }
}
