package com.trs.gov.workorder.constant;

import java.util.HashMap;
import java.util.Map;

public class WorkOrderConstant {
    /**
     * 工单顶级类型-工单
     */
    public static final Long TOP_TYPE_WORK_ORDER = 1L;

    /**
     * 工单顶级类型-投诉
     */
    public static final Long TOP_TYPE_COMPLAINT = 2L;

    /**
     * 工单顶级类型-通知
     */
    public static final Long TOP_TYPE_NOTICE = 3L;

    public static final Map<Long, String> topTypeName = new HashMap<>();
    static {
        topTypeName.put(TOP_TYPE_WORK_ORDER, "工单");
        topTypeName.put(TOP_TYPE_COMPLAINT, "投诉");
        topTypeName.put(TOP_TYPE_NOTICE, "通知");
    }

    /**
     * 待分配
     */
    public static final int STATUS_WAIT_ASSIGN = 0;
    /**
     * 处理中
     */
    public static final int STATUS_DEALING = 1;
    /**
     * 已解决--修改为待评价
     */
    public static final int STATUS_FINISHED = 2;
    /**
     * 已评价
     */
    public static final int STATUS_REVIEWED = 3;
    /**
     * 已公开
     */
    public static final int STATUS_OPENED = 4;

    public static Map<Integer, String> statusName = new HashMap<>();

    static {
        statusName.put(STATUS_WAIT_ASSIGN, "待分配");
        statusName.put(STATUS_DEALING, "处理中");
        statusName.put(STATUS_FINISHED, "待评价");
        statusName.put(STATUS_REVIEWED, "已评价");
        statusName.put(STATUS_OPENED, "已公开");
    }

    /**
     * 已完成的状态列表
     * */
    public final static Integer[] STATUS_LIST_FINISHED = new Integer[]{
            STATUS_OPENED,
            STATUS_REVIEWED,
            STATUS_FINISHED
    };

    /**
     * 为完成的状态列表
     * */
    public final static Integer[] STATUS_LIST_NOT_FINISHED = new Integer[]{
            STATUS_WAIT_ASSIGN,
            STATUS_DEALING
    };


    /**
     * 我处理的-待我处理
     */
    public static final int SREARCH_MY_DEAL_DEALING = 0;

    /**
     * 我处理的-抄送我的
     */
    public static final int SREARCH_MY_DEAL_CC = 1;

    /**
     * 我处理的-已处理
     */
    public static final int SREARCH_MY_DEAL_DEALED = 2;

    /**
     * 我评价的-待我评价
     */
    public static final int SREARCH_MY_REVIEM_REVIEWING = 3;

    /**
     * 我评价的-已评价
     */
    public static final int SREARCH_MY_REVIEM_REVIEWED = 4;

    /**
     * 我创建的-待处理
     */
    public static final int SREARCH_MY_CREATED_DEALING = 5;

    /**
     * 我创建的-已处理
     */
    public static final int SREARCH_MY_CREATED_DEALED = 6;

    /**
     * 全部
     */
    public static final int SREARCH_ALL = 7;

    /**
     * 全部-待处理
     */
    public static final int SREARCH_ALL_DEALING = 8;

    /**
     * 全部-已处理
     */
    public static final int SREARCH_ALL_DEALED = 9;

    /**
     * 问题知识库
     */
    public static final int SREARCH_QUESTION_KNOWLEDGE = 10;

    /**
     * 代办
     */
    public static final int SREARCH_WAIT_DEAL = 11;

    /**
     * 主办
     */
    public static final int SREARCH_MY_HOST = 12;

    /**
     * 下属主办
     */
    public static final int SREARCH_CHILD_HOST = 13;

    /**
     * 超时-所有
     */
    public static final int SREARCH_OVERTIME_ALL = 14;

    /**
     * 超时-自己单位
     */
    public static final int SREARCH_OVERTIME_MY_UNIT = 15;

    /**
     * 超时-自己
     */
    public static final int SREARCH_OVERTIME_MY = 16;

    /**
     * 到处的工单数据表格名
     */
    public static final String EXCEL_NAME = "工单列表";

    public static final String DEFAULT_SOURCE = "人工创建";

    /**
     * 有效期时间限制，5 分钟
     */
    public static final int TIME_LIMIT = 5 * 60 * 1000;
}
