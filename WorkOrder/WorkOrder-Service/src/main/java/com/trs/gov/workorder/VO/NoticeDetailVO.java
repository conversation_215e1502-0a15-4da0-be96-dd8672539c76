package com.trs.gov.workorder.VO;

import com.alibaba.fastjson.JSONArray;
import com.trs.gov.core.VO.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class NoticeDetailVO extends BaseVO {
    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 发起单位id
     */
    @ApiModelProperty(value = "发起单位id")
    private Long crUnitId;

    /**
     * 发起单位
     */
    @ApiModelProperty(value = "发起单位")
    private String crUnit;

    /**
     * 发起人用户名
     */
    @ApiModelProperty(value = "发起人用户名")
    private String crUsername;

    /**
     * 发起人真实姓名
     */
    @ApiModelProperty(value = "发起人真实姓名")
    private String crTruename;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String content;

    /**
     * 通知单位
     */
    @ApiModelProperty(value = "通知单位")
    private JSONArray noticeList = new JSONArray();
}
