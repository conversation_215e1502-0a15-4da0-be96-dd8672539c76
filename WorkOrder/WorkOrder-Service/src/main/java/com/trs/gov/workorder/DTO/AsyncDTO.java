package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AsyncDTO extends BaseDTO {
    /**
     * 请求是否异步（0:否，1:是）
     */
    @ApiModelProperty(value = "请求是否异步（0:否，1:是）")
    private Integer async;

    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
