package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class CCToMyDTO extends BaseDTO {
    /**
     * true：抄送给我的单位，false：抄送给我
     */
    private boolean isUnit;

    /**
     * 工单状态，多个以逗号分隔
     */
    private Integer[] workOrderStatus;


    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
