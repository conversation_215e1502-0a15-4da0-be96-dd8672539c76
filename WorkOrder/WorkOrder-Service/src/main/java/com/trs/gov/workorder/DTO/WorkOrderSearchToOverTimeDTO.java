package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查（即将）超时工单
 */
@Data
public class WorkOrderSearchToOverTimeDTO extends BasePageDTO {
    /**
     * 0:全部, 1:自己单位, 2:自己
     */
    @ApiModelProperty(value = "0:全部, 1: 自己单位, 2:自己 ")
    private Integer type;

    /**
     * 多少天算即将超时，默认为5天
     */
    @ApiModelProperty(value = "多少天算即将超时，默认为5天")
    private Integer toOverTimeDays = 5;

    /**
     * 0:没有超时，1：即将超时，2：已超时，多个以逗号分隔
     */
    @ApiModelProperty(value = "0:没有超时，1：即将超时，2：已超时，多个以逗号分隔")
    private String overTimeType;

    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
