package com.trs.gov.workorder.VO;

import com.trs.gov.core.VO.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class WorkTimeVO extends BaseVO {
    /**
     * 工作量id
     */
    @ApiModelProperty(value = "工作量id")
    private Long workTimeId;

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 工作名目
     */
    @ApiModelProperty(value = "工作名目")
    private String workDesc;

    /**
     * 工作单位id
     */
    @ApiModelProperty(value = "工作单位id")
    private Long workUnitId;

    /**
     * 工作单位
     */
    @ApiModelProperty(value = "工作单位")
    private String workUnit;

    /**
     * 工作量（天）
     */
    @ApiModelProperty(value = "工作量")
    private double workingTime;

    /**
     * 工作量权限列表
     */
    @ApiModelProperty(value = "工作量权限列表")
    private List<ActionVO> actionList;
}
