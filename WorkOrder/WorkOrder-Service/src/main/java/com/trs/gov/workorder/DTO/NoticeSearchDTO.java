package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class NoticeSearchDTO extends BasePageDTO {
    /**
     * 通知id
     */
    @ApiModelProperty(value = "通知id")
    private Long noticeId;

    /**
     * 查找类型(0；我收到的，1：我发出的)
     */
    @ApiModelProperty(value = "查找类型")
    private Integer type;

    private String username;

    /**
     * 发送单位
     */
    @ApiModelProperty(value = "发送单位")
    private String crUnitIdList;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    private String crTime;

    /**
     * 发送时间开始
     */
    private Date crTimeStart;

    /**
     * 发送时间结束
     */
    private Date crTimeEnd;

    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
    private String content;

    @Override
    public boolean isValid() throws ServiceException {
//        PreConditionCheck.checkArgument(type!=null, "类型不能为空！");
        return true;
    }
}
