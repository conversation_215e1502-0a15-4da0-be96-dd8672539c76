package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName：WorkTimeExportDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/1 14:39
 **/
@Data
public class WorkTimeExportDTO extends BaseDTO {
    /**
     * 老工时记录数据 Id
     */
    private String oldWorkTimeId;
    /**
     * 创建时间
     */
    private Date crTime;
    /**
     * 创建单位 ID
     */
    private Long crUnitId;

    /**
     * 创建人用户名字
     */
    private String crUsername;

    /**
     * 工单id
     */
    private Long workOrderId;

    /**
     * 工作名目
     */
    private String workDesc;

    /**
     * 工作单位id
     */
    private Long workUnitId;
    /**
     * 工作单位 名称
     */
    private String workUnit;

    /**
     * 工作量（天）
     */
    private Double workingTime;

    @Override
    public boolean isValid() throws ServiceException {
        if(CMyString.isEmpty(oldWorkTimeId)){
            throw new ParamInvalidException("老工时记录Id不能为空!");
        }
        if(crTime == null){
            throw new ParamInvalidException("老工时创建的时间不能为空!");
        }
        if(crUnitId == null){
            throw new ParamInvalidException("创建工时记录的创建单位不能为空!");
        }
        if(CMyString.isEmpty(crUsername)){
            throw new ParamInvalidException("创建人用户名不能为空!");
        }
        if(CMyString.isEmpty(workDesc)){
            throw new ParamInvalidException("工作名目不能为空!");
        }
        if(workOrderId == null){
            throw new ParamInvalidException("工单Id不能为空！");
        }
        if(workUnitId == null){
            throw new ParamInvalidException("被记录的工时单位不能为空!");
        }
        if(CMyString.isEmpty(workUnit)){
            throw new ParamInvalidException("被记录工时的单位名称不能为空!");
        }
        if(workingTime == null){
            throw new ParamInvalidException("记录的工时不能为空!");
        }
        return true;
    }
}
