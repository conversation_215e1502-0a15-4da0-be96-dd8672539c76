package com.trs.gov.workorder.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.gov.core.VO.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WorkOrderVO extends BaseVO {

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long id;

    /**
     * 工单顶级类别id
     */
    @ApiModelProperty(value = "工单类别id")
    private Long workOrderTopTypeId;

    /**
     * 工单顶级类别
     */
    @ApiModelProperty(value = "工单类别")
    private String workOrderTopTypeName;

    /**
     * 工单类别id
     */
    @ApiModelProperty(value = "工单类别id")
    private Long workOrderTypeId;

    /**
     * 工单类别
     */
    @ApiModelProperty(value = "工单类别")
    private String workOrderTypeName;

    /**
     * 工单父级类别id
     */
    @ApiModelProperty(value = "工单父级类别id")
    private Long workOrderParentTypeId;

    /**
     * 工单父级类别
     */
    @ApiModelProperty(value = "工单父级类别")
    private String workOrderParentTypeName;

    /**
     * 是否是回退
     */
    @ApiModelProperty(value = "是否是回退")
    private boolean isReturn;

    /**
     * 发起单位id
     */
    @ApiModelProperty(value = "发起单位id")
    private Long crUnitId;

    /**
     * 发起单位
     */
    @ApiModelProperty(value = "发起单位")
    private String crUnit;

    /**
     * 发起人用户名
     */
    @ApiModelProperty(value = "发起人用户名")
    private String crUsername;

    /**
     * 发起人真实姓名
     */
    @ApiModelProperty(value = "发起人真实姓名")
    private String crTruename;

    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id")
    private Long siteId;

    /**
     * 站点
     */
    @ApiModelProperty(value = "站点")
    private String sitename;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String content;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;

    /**
     * 主办单位id
     */
    @ApiModelProperty(value = "主办单位id")
    private Long hostUnitId;

    /**
     * 主办单位
     */
    @ApiModelProperty(value = "主办单位")
    private String hostUnit;

    /**
     * 主办人用户名
     */
    @ApiModelProperty(value = "主办人用户名")
    private String hostUsername;

    /**
     * 主办人用户名
     */
    @ApiModelProperty(value = "主办人用户名")
    private String hostTruename;

    /**
     * 受理单位id
     */
    @ApiModelProperty(value = "受理单位id")
    private Long dealUnitId;

    /**
     * 受理单位
     */
    @ApiModelProperty(value = "受理单位")
    private String dealUnit;

    /**
     * 受理人用户名
     */
    @ApiModelProperty(value = "受理人用户名")
    private String dealUsername;

    /**
     * 受理人真实姓名
     */
    @ApiModelProperty(value = "受理人真实姓名")
    private String dealTruename;

    /**
     * 渠道类型id
     */
    @ApiModelProperty(value = "渠道类型id")
    private Integer mediaType;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String mediaName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date crTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 收到工单时间
     */
    @ApiModelProperty(value = "收到工单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /**
     * 响应时间
     */
    @ApiModelProperty(value = "响应时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actionTime;

    /**
     * 是否已响应
     */
    @ApiModelProperty(value = "是否已响应")
    private boolean isAction;

    /**
     * 期望完成时间
     */
    @ApiModelProperty(value = "期望完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedEndDate;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    /**
     * 状态（0:待分配, 1: 处理中, 2:已解决, 3:已评价, 4:已公开）
     */
    @ApiModelProperty(value = "状态（0:待分配, 1: 处理中, 2:已解决, 3:已评价, 4:已公开）")
    private Integer status;

    /**
     * 状态名
     */
    @ApiModelProperty(value = "状态名")
    private String statusName;

    /**
     * 当前系统时间
     */
    @ApiModelProperty(value = "当前系统时间")
    private Date curSysTime = new Date();

    /**
     * 主办人指定类型（1：指定到人，2：指定到单位）
     */
    private Integer hostAssignType;

    /**
     * 主办人指定类型（1：指定到人，2：指定到单位）
     */
    private Integer dealAssignType;

    /**
     *  来源
     */
    private String source;
}
