package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 根据范围查询工单
 */
@Data
public class WorkOrderSearchByScopeDTO extends BasePageDTO {
    /**
     * 状态（0:待分配, 1: 处理中, 2:已解决, 3:已评价, 4:已公开），多个以逗号分隔
     */
    @ApiModelProperty(value = "状态（0:待分配, 1: 处理中, 2:已解决, 3:已评价, 4:已公开），多个以逗号分隔")
    private String status;

    /**
     * 我是受理（单位）人（1:个人，2：单位）
     */
    @ApiModelProperty(value = "我是受理（单位）人（1:个人，2：单位）")
    private Integer dealType;

    /**
     * 我是主办（单位）人（1:个人，2：单位）
     */
    @ApiModelProperty(value = "我是主办（单位）人（1:个人，2：单位）")
    private Integer hostType;

    /**
     * 我是创建（单位）人（1:个人，2：单位）
     */
    @ApiModelProperty(value = "我是创建（单位）人（1:个人，2：单位）")
    private Integer crType;

    /**
     * 抄送给我（单位）人（1:个人，2：单位）
     */
    @ApiModelProperty(value = "抄送给我（单位）人（1:个人，2：单位）")
    private Integer ccType;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    /**
     * 是否倒序，不传默认为倒序
     */
    @ApiModelProperty(value = "是否倒序，不传默认为倒序")
    private boolean isDesc = true;

    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
