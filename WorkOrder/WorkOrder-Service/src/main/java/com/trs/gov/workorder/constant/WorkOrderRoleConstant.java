package com.trs.gov.workorder.constant;

public class WorkOrderRoleConstant {
    /**
     * 受理单位负责人(受理人分配类型为到单位)
     */
    public static final String DEAL_UNIT_MASTER = "DEAL_UINT_MASTER";

    /**
     * 受理单位负责人(跟分配类型无关)
     */
    public static final String DEAL_UNIT_MASTER_ONLY = "DEAL_UNIT_MASTER_ONLY";

    /**
     * 受理单位非负责人
     */
    public static final String DEAL_UNIT_NOT_MASTER = "DEAL_UINT_NOT_MASTER";

    /**
     * 受理人(受理人分配类型为到人)
     */
    public static final String DEAL_USER = "DEAL_USER";

    /**
     * 抄送单位负责人(抄送目标人分配类型为到单位)
     */
    public static final String CC_UNIT_MASTER = "CC_UNIT_MASTER";

    /**
     * 抄送单位非负责人
     */
    public static final String CC_UNIT_NOT_MASTER = "CC_UNIT_NOT_MASTER";

    /**
     * 抄送人(抄送目标人分配类型为到人)
     */
    public static final String CC_USER = "CC_USER";

    /**
     * 发起单位
     */
    public static final String CR_UNIT= "CR_UNIT";

    /**
     * 发起人
     */
    public static final String CR_USER= "CR_USER";

    /**
     * 主办单位负责人(跟主办人分配类型无关)
     */
    public static final String HOST_UNIT_MASTER = "HOST_UNIT_MASTER";

    /**
     * 主办人(主办人分配类型为到人)
     */
    public static final String HOST_USER = "HOST_USER";
}
