package com.trs.gov.workorder.DTO;

import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

@Data
public class CCListDTO extends WorkOrderBaseDTO {

    /**
     * 工单ID
     */
    @ApiModelProperty(value = "工单ID")
    private Long workOrderId;

    /**
     * 抄送列表
     */
    @ApiModelProperty(value = "抄送列表")
    private String cclist;

    /**
     * 抄送列表
     */
    private List<CCDTO> ccDTOs = new ArrayList<>();

    /**
     * 新增抄送列表
     */
    private List<CCDTO> newCCDTOs = new ArrayList<>();

    private Set<Long> unitIdSet = new HashSet<>();

    private Set<String> usernameSet = new HashSet<>();

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (Optional.ofNullable(workOrderId).orElse(0L) <= 0L) {
            throw new ParamInvalidException("工单ID不能为空！");
        }
        return true;
    }
}
