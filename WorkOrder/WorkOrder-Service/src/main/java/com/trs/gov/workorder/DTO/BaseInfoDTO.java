package com.trs.gov.workorder.DTO;

import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BaseInfoDTO extends WorkOrderBaseDTO {
    private List<Long> unitIdList = new ArrayList<>();

    private List<String> usernameList = new ArrayList<>();

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
