package com.trs.gov.workorder.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

/**
 * @ClassName：StatisticVO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/29 14:49
 **/
@Data
public class StatisticVO extends BaseVO {
    /**
     * 单位Id
     **/
    private String unitId;
    /**
     * 用户名
     **/
    private String userName;
    /**
     * 未读通知数量
     **/
    private Integer unReadNoticeNum;
    /**
     * 未处理的工单数量
     **/
    private Integer waitMeHandleNum;
    /**
     * 抄送我的数量
     **/
    private Integer copyToMeNum;
    /**
     * 评论数量
     **/
    private Integer myAppraiseNum;
    /**
     * 评论数量
     **/
    private Integer myHandleNum;

}
