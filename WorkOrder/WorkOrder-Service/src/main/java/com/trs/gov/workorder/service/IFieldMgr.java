package com.trs.gov.workorder.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.IKey;
import com.trs.gov.core.exception.ServiceException;

import java.util.Optional;
import java.util.function.Consumer;

public interface IFieldMgr<K> extends IKey {
    /**
     * @Description  获取 各自属性的  Consumer
     * @Param [keyWords]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 14:38
     **/
    <T extends BaseDTO> Optional<Consumer<QueryWrapper<K>>> buildCondition(Object keyWords, T dto) throws ServiceException;
}
