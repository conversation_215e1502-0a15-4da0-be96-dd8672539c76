package com.trs.gov.workorder.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;

import java.util.function.Consumer;

/**
 * @ClassName：IBaseMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 17:51
 **/
public interface IBaseMgr<K> {

    /**
     * @Description  返回不同 dto 的 Consumer
     * @Param [dto]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/16 17:52
     **/
    public <T extends BaseDTO> Consumer<QueryWrapper<K>> buildConsumer(T dto, Consumer<QueryWrapper<K>>... consumers) throws ServiceException, IllegalAccessException;
}
