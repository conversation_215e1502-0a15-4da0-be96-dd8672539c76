package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WorkOrderSearchDTO extends BasePageDTO {

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long id;

    /**
     * 通知id
     */
    @ApiModelProperty(value = "通知id")
    private Long noticeId;

    /**
     * 查找类型
     */
    @ApiModelProperty(value = "查找类型")
    private Integer type;

    /**
     * 工单id列表
     */
    @ApiModelProperty(value = "工单id列表")
    private String idList;

    /**
     * 是否包含通知
     */
    @ApiModelProperty(value = "是否包含通知")
    private boolean isContainNotice;

    /**
     * 工单顶级分类id
     */
    @ApiModelProperty(value = "工单顶级分类id")
    private String workOrderTopTypeIdList;

    /**
     * 工单种类
     */
    @ApiModelProperty(value = "工单种类")
    private String workOrderTypeIdList;

    /**
     * 主办单位id,多个以逗号分隔
     */
    @ApiModelProperty(value = "主办单位id,多个以逗号分隔")
    private String hostUnitIdList;

    /**
     * 发起单位id,多个以逗号分隔
     */
    @ApiModelProperty(value = "发起单位id,多个以逗号分隔")
    private String crUnitIdList;

    /**
     * 处理单位id,多个以逗号分隔
     */
    @ApiModelProperty(value = "处理单位id,多个以逗号分隔")
    private String dealUnitIdList;

    /**
     * 抄送单位id,多个以逗号分隔
     */
    @ApiModelProperty(value = "抄送单位id,多个以逗号分隔")
    private String ccUnitIdList;

    /**
     * 站点id,多个以逗号分隔
     */
    @ApiModelProperty(value = "站点id,多个以逗号分隔")
    private String siteIdList;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String crTime;

    /**
     * 创建时间开始
     */
    private Date crTimeStart;

    /**
     * 创建时间结束
     */
    private Date crTimeEnd;

    /**
     * 期望结束时间
     */
    @ApiModelProperty(value = "期望结束时间")
    private String expectedEndDate;

    /**
     * 期望时间开始
     */
    private Date expectedEndDateStart;

    /**
     * 期望时间结束
     */
    private Date expectedEndDateEnd;

    /**
     * 搜索关键字，工单id、工单内容
     */
    @ApiModelProperty(value = "工单id、工单内容")
    private String keywords;

    /**
     * 排序方式, 例如:cr_time desc;status asc
     */
    private String orderBy;

    /**
     * 状态（0:待分配, 1: 处理中, 2:已解决, 3:已评价, 4:已公开），多个以逗号分隔
     */
    @ApiModelProperty(value = "状态（0:待分配, 1: 处理中, 2:已解决, 3:已评价, 4:已公开），多个以逗号分隔")
    private String status;

    /**
     * 是否跟登录用户无关查所有工单（0:否，是：1）
     */
    @ApiModelProperty(value = "是否跟登录用户无关查所有工单（0:否，是：1）")
    private Integer isAll;


    /**
     * 描述
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
