package com.trs.gov.workorder.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.gov.core.VO.BaseVO;
import com.trs.gov.file.VO.FileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class NoticeVO extends BaseVO {
    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 发起单位id
     */
    @ApiModelProperty(value = "发起单位id")
    private Long crUnitId;

    /**
     * 发起单位
     */
    @ApiModelProperty(value = "发起单位")
    private String crUnit;

    /**
     * 发起人用户名
     */
    @ApiModelProperty(value = "发起人用户名")
    private String crUserName;

    /**
     * 发起人真实姓名
     */
    @ApiModelProperty(value = "发起人真实姓名")
    private String crTrueName;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String content;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date crTime;

    /**
     * 已阅读
     */
    @ApiModelProperty(value = "已阅读")
    private Long readCount = 0L;

    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Long totalCount;

    /**
     * 回复数
     */
    @ApiModelProperty(value = "回复数")
    private Long replyCount;

    /**
     * 阅读状态(0：未读，1：已读)
     */
    @ApiModelProperty(value = "阅读状态(0：未读，1：已读)")
    private Integer status;

    /**
     * 图片列表
     */
    @ApiModelProperty(value = "图片列表")
    private List<FileVO> picList = new ArrayList<>();

    /**
     * 文件列表
     */
    @ApiModelProperty(value = "文件列表")
    private List<FileVO> fileList = new ArrayList<>();
}
