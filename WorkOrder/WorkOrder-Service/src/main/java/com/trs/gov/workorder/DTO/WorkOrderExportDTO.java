package com.trs.gov.workorder.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.VO.FileVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WorkOrderExportDTO extends BaseDTO {
    /**
     * 老工单 id
     */
    private String oldWorkOrderId;

    /**
     * 新工单 id
     */
    private Long workOrderId;

    /**
     * 工单顶级分类id
     */
    private Long workOrderTopTypeId;
    /**
     * 工单顶级分类 名称
     */
    private String workOrderTopTypeName;

    /**
     * 工单类型id
     */
    private Long workOrderTypeId;
    /**
     * 工单类型id
     */
    private String workOrderTypeName;

    /**
     * 站点ID
     */
    private Long siteId;
    /**
     * 站点 名称
     */
    private String sitename;
    /**
     * 渠道类型（0：全部, 1：APP, 2：网站, 3：微信, 4：微博）
     */
    private Integer mediaType;

    /**
     * 内容
     */
    private String content;
    /**
     * 图片附件
     */
    private List<FileVO> piclist;
    /**
     * 文件附件
     */
    private List<FileVO> filelist;
    /**
     * 优先级
     */
    private String priority;
    /**
     * 期望完成时间
     */
    private Date expectedEndDate;
    /**
     * 完成时间
     */
    private Date finishTime;
    /**
     * 主办单位id
     */
    private Long hostUnitId;
    /**
     * 主办单位名称
     */
    private String hostUnit;

    /**
     * 主办人指定类型（1：指定到人，2：指定到单位）
     */
    private Integer hostAssignType;

    /**
     * 主办人用户名
     */
    private String hostUsername;
    /**
     * 主办人用户名
     */
    private String hostTruename;

    /**
     * 受理单位id
     */
    private Long dealUnitId;
    /**
     * 受理单位 名称
     */
    private String dealUnit;

    /**
     * 处理人指定类型（1：指定到人，2：指定到单位）
     */
    private Integer dealAssignType;
    /**
     * 受理人用户名
     */
    private String dealUsername;
    /**
     * 受理人用户名 的 真实姓名
     */
    private String dealTruename;
    /**
     * 创建人
     */
    private String crUsername;
    /**
     * 创建人真实姓名
     */
    private String crTruename;
    /**
     * 创建人所在单位的ID
     */
    private Long crUnitId;
    /**
     * 创建人所在单位
     */
    private String crUnit;

    /**
     * 创建时间
     */
    private Date crTime;
    /**
     * 响应时间
     */
    private Date actionTime;
    /**
     *  3:已评价, 4:已公开
     **/
    private Integer status = 0;


    /**
     * 抄送列表
     */
    private List<CCExportDTO> ccDTOs;
    /**
     * 通知列表
     */
    private List<NoticeExportDTO> noticeDTOS;

    /**
     * @Description  工单校验模块
     * @Param []
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/26 17:42
     **/
    public boolean noticeValid() throws ServiceException{
        this.commonValid();
        if(noticeDTOS == null || noticeDTOS.size() == 0){
            throw new ServiceException("通知列表不能为空!");
        }
        return true;
    }
    /**
     * @Description  是否指定到 人
     * @Param []
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/26 18:44
     **/
    public boolean ComplaintValid() throws ServiceException{
        this.commonValid();
        if(workOrderTypeId == null){
            throw new ParamInvalidException("工单类型Id不能为空!");
        }
        if(CMyString.isEmpty(workOrderTypeName)){
            throw new ParamInvalidException("工单类型名称不能为空!");
        }
        //受理单位相关信息
        if(dealUnitId == null){
            throw new ParamInvalidException("受理单位ID不能为空!");
        }
        if(CMyString.isEmpty(dealUnit)){
            throw new ParamInvalidException("受理单位名称不能为空!");
        }
        if(CMyString.isEmpty(dealUsername)){
            throw new ParamInvalidException("受理单位负责人不能为空!");
        }
        if(CMyString.isEmpty(dealTruename)){
            throw new ParamInvalidException("受理单位负责人人真实姓名不能为空!");
        }
        return true;
    }
    /**
     * @Description  公共校验模块
     * @Param []
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/26 17:41
     **/
    public boolean commonValid() throws ServiceException{
        if(status == null){
            throw new ServiceException("状态值不能为空!");
        }
        //  工单基础属性字段
        if(CMyString.isEmpty(oldWorkOrderId)){
            throw new ParamInvalidException("老工单oldWorkOrderId不能为空!");
        }
        if(workOrderTopTypeId == null){
            throw new ParamInvalidException("工单顶级类型Id不能为空!");
        }
        if(CMyString.isEmpty(workOrderTopTypeName)){
            throw new ParamInvalidException("工单顶级类型名称不能为空!");
        }
        if(CMyString.isEmpty(content)){
            throw new ParamInvalidException("工单内容不能为空!");
        }

        // 工单创建相关信息
        if(CMyString.isEmpty(crUsername)){
            throw new ParamInvalidException("创建人用户名不能为空!");
        }
        if(CMyString.isEmpty(crTruename)){
            throw new ParamInvalidException("创建人真实姓名不能为空!");
        }
        if(crUnitId == null){
            throw new ParamInvalidException("创建人所在单位的ID不能为空!");
        }
        if(CMyString.isEmpty(crUnit)){
            throw new ParamInvalidException("创建人所在单位的名称不能为空!");
        }
        if(crTime == null){
            throw new ParamInvalidException("工单得创建时间不能为空!");
        }

        return true;
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        //三种工单类型公共校验 方法
        this.commonValid();
        if(actionTime == null){
            throw new ServiceException("响应时间不能为空!");
        }
        if(CMyString.isEmpty(priority)){
            throw new ParamInvalidException("工单优先级不能为空!");
        }
        if((workOrderTypeId == null && !CMyString.isEmpty(workOrderTypeName))||(workOrderTypeId != null && CMyString.isEmpty(workOrderTypeName))){
            throw new ParamInvalidException("工单类型要么都为空要么都不为空!");
        }

        //  主办单位相关信息
        if(hostUnitId == null){
            throw new ParamInvalidException("主办单位Id不能为空!");
        }
        if(CMyString.isEmpty(hostUnit)){
            throw new ParamInvalidException("主办单位名称不能为空!");
        }
        if(!CMyString.isEmpty(hostUsername)){
            if(CMyString.isEmpty(hostTruename)){
                throw new ParamInvalidException("主办单位人真实姓名不能为空!");
            }
        }

        //受理单位相关信息
        if(dealUnitId == null){
            throw new ParamInvalidException("受理单位ID不能为空!");
        }
        if(CMyString.isEmpty(dealUnit)){
            throw new ParamInvalidException("受理单位名称不能为空!");
        }
        if(!CMyString.isEmpty(dealUsername)){
            if(CMyString.isEmpty(dealTruename)){
                throw new ParamInvalidException("受理单位人真实姓名不能为空!");
            }
        }

        // 站点内容 相关 数据校验
        if(siteId != null){
            if(CMyString.isEmpty(sitename)){
                throw new ParamInvalidException("站点名称不能为空!");
            }
            if(mediaType == null){
                throw new ParamInvalidException("站点的媒体类型不能为空!");
            }
        }

        return true;
    }
}
