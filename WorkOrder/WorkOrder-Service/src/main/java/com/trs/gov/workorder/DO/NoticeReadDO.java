package com.trs.gov.workorder.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Data
@TableName("notice_read")
public class NoticeReadDO extends BaseDO {
    /**
     * 工单id
     */
    @Column(name = "work_order_id")
    @ApiModelProperty(value = "工单id")
    private Long workOrderId;

    /**
     * 通知的目标单位id
     */
    @Column(name = "target_unit_id")
    @ApiModelProperty(value = "通知的目标单位id")
    private Long targetUnitId;

    /**
     * 通知的目标人员用户名
     */
    @Column(name = "target_username")
    @ApiModelProperty(value = "通知的目标人员用户名")
    private String targetUsername;

    /**
     * 阅读状态 （0: 未读， 1：已读）
     */
    @Column(name = "status")
    @ApiModelProperty(value = "阅读状态 （0: 未读， 1：已读）")
    private Integer status;
}
