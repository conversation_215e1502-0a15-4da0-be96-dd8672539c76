package com.trs.gov.workorder.VO;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class WorkOrderExcel extends BaseRowModel {

    /**
     * 工单#id 格式
     */
    @ExcelProperty(value = "工单ID")
    private String idStr;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String content;

    /**
     * 描述
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 工单类型
     */
    @ExcelProperty(value = "工单类型")
    private String workOrderType;

    /**
     * 站点
     */
    @ExcelProperty(value = "站点")
    private String sitename;

    /**
     * 总的工作量
     */
    @ExcelProperty(value = "工作量")
    private String allWorkTime;

    /**
     * 工单状态
     */
    @ExcelProperty(value = "工单状态")
    private String statusName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", format = "yyyy-MM-dd")
    private Date crTime;

    /**
     * 期望完成时间
     */
    @ExcelProperty(value = "期望完成时间", format = "yyyy-MM-dd")
    private Date expectedEndDate;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间", format = "yyyy-MM-dd")
    private Date updateTime;

    /**
     * 主办单位
     */
    @ExcelProperty(value = "主办单位")
    private String hostUnit;

    /**
     * 主办人
     */
    @ExcelProperty(value = "主办人")
    private String hostTruename;

    /**
     * 发起单位
     */
    @ExcelProperty(value = "发起单位")
    private String crUnit;

    /**
     * 发起人
     */
    @ExcelProperty(value = "发起人")
    private String crTruename;

    /**
     * 受理单位
     */
    @ExcelProperty(value = "受理单位")
    private String dealUnit;

    /**
     * 受理人
     */
    @ExcelProperty(value = "受理人")
    private String dealTruename;

    /**
     * 抄送单位
     */
    @ExcelProperty(value = "抄送单位")
    private String ccList;
}
