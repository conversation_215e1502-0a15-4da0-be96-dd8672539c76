package com.trs.workOrder;

import com.trs.gov.WorkOrderApplication;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.CCExportDTO;
import com.trs.gov.workorder.DTO.NoticeExportDTO;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.dao.NoticeMapper;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = WorkOrderApplication.class)
@Slf4j
public class WorkOrderTest {
    @Autowired
    private IWorkOrderService workOrderService;

    @Test
    public void searchUsedWorkOrderTypeId(){
        WorkOrderSearchDTO searchDTO = new WorkOrderSearchDTO();
        searchDTO.setWorkOrderTypeIdList("93,94");
        RestfulResults<List<Long>> restfulResults = workOrderService.searchUsedWorkOrderTypeId();
        System.out.println(restfulResults.getDatas());
    }

    @Reference(check = false, timeout = 60000)
    private IWorkOrderService service;
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private NoticeMapper noticeMapper;
    @Autowired
    private CCMapper ccMapper;

    /**
     * @Description  工单 迁移
     * @Param []
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 15:08
     **/
    @Test
    public void saveExportWorkOrder() throws ServiceException {
        WorkOrderExportDTO workOrderExportDTO = new WorkOrderExportDTO();
        workOrderExportDTO.setOldWorkOrderId("我是老工单ID");
        workOrderExportDTO.setWorkOrderTopTypeId(1L);
        workOrderExportDTO.setWorkOrderTopTypeName("测试");
        workOrderExportDTO.setContent("我是投诉得内容！我是投诉得内容！我是投诉得内容！ ");
        workOrderExportDTO.setCrUsername("杨鑫");
        workOrderExportDTO.setCrTruename("杨鑫");
        workOrderExportDTO.setCrUnit("我是单位名称");
        workOrderExportDTO.setCrUnitId(250L);
        workOrderExportDTO.setCrTime(new Date());
        workOrderExportDTO.setWorkOrderTypeId(253L);
        workOrderExportDTO.setWorkOrderTypeName("我是工单类型253");
        workOrderExportDTO.setDealTruename("川宝");
        workOrderExportDTO.setDealUsername("川宝");
        workOrderExportDTO.setDealUnitId(699L);
        workOrderExportDTO.setDealUnit("我是单位699");
        workOrderExportDTO.setHostUnitId(565L);
        workOrderExportDTO.setHostUnit("我是单位565");
        workOrderExportDTO.setHostUsername("哈哈");
        workOrderExportDTO.setHostTruename("啦啦啦");
        workOrderExportDTO.setStatus(3);
        workOrderExportDTO.setActionTime(new Date());
        workOrderExportDTO.setPriority("一般");
        workOrderExportDTO.setSiteId(999L);
        workOrderExportDTO.setSitename("我是站点999");
        workOrderExportDTO.setMediaType(2);

        CCExportDTO ccExportDTO = new CCExportDTO();
        ccExportDTO.setTargetUsername("兰鑫");
        ccExportDTO.setTargetTruename("兰鑫");
        ccExportDTO.setTargetUnitId(251L);
        ccExportDTO.setTargetUnit("我是251单位");
        ccExportDTO.setType(1);
        CCExportDTO ccExportDTO1 = new CCExportDTO();
        ccExportDTO1.setTargetUsername("左开元");
        ccExportDTO1.setTargetTruename("左开元");
        ccExportDTO1.setTargetUnitId(252L);
        ccExportDTO1.setTargetUnit("我是252研究院");
        ccExportDTO1.setType(2);
        workOrderExportDTO.setCcDTOs(Arrays.asList(ccExportDTO,ccExportDTO1));
        WorkOrderVO workOrderVO = workOrderService.exportWorkOrder(workOrderExportDTO);
        System.out.println("##################注意删除测试数据########################");
        System.out.println("delete from work_order where id = "+workOrderVO.getId());
        System.out.println("delete from cc where work_order_id = "+workOrderVO.getId());
    }

    @Test
    public void saveExportComplaint() throws ServiceException{
        WorkOrderExportDTO workOrderExportDTO = new WorkOrderExportDTO();
        workOrderExportDTO.setOldWorkOrderId("我是老工单ID");
        workOrderExportDTO.setWorkOrderTopTypeId(2L);
        workOrderExportDTO.setWorkOrderTopTypeName("测试");
        workOrderExportDTO.setContent("我是投诉得内容！我是投诉得内容！我是投诉得内容！ ");
        workOrderExportDTO.setCrUsername("杨鑫");
        workOrderExportDTO.setCrTruename("杨鑫");
        workOrderExportDTO.setCrUnit("我是单位名称");
        workOrderExportDTO.setCrUnitId(250L);
        workOrderExportDTO.setCrTime(new Date());
        workOrderExportDTO.setWorkOrderTypeId(253L);
        workOrderExportDTO.setWorkOrderTypeName("我是工单类型253");
        workOrderExportDTO.setDealAssignType(1);
        workOrderExportDTO.setDealTruename("川宝");
        workOrderExportDTO.setDealUsername("川宝");
        workOrderExportDTO.setDealUnitId(699L);
        workOrderExportDTO.setDealUnit("我是单位699");
        workOrderExportDTO.setStatus(3);
        workOrderExportDTO.setActionTime(new Date());
        CCExportDTO ccExportDTO = new CCExportDTO();
        ccExportDTO.setTargetUsername("兰鑫");
        ccExportDTO.setTargetTruename("兰鑫");
        ccExportDTO.setTargetUnitId(251L);
        ccExportDTO.setTargetUnit("我是251单位");
        ccExportDTO.setType(1);
        CCExportDTO ccExportDTO1 = new CCExportDTO();
        ccExportDTO1.setTargetUsername("左开元");
        ccExportDTO1.setTargetTruename("左开元");
        ccExportDTO1.setTargetUnitId(252L);
        ccExportDTO1.setTargetUnit("我是252研究院");
        ccExportDTO1.setType(2);
        workOrderExportDTO.setCcDTOs(Arrays.asList(ccExportDTO,ccExportDTO1));
        WorkOrderVO workOrderVO = workOrderService.exportWorkOrder(workOrderExportDTO);
        System.out.println("##################注意删除测试数据########################");
        System.out.println("delete from work_order where id = "+workOrderVO.getId());
    }
    /**
     * @Description  通知 测试
     * @Param []
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 14:22
     **/
    @Test
    public void saveExportNotice() throws ServiceException {
        WorkOrderExportDTO workOrderExportDTO = new WorkOrderExportDTO();
        workOrderExportDTO.setOldWorkOrderId("我是老工单ID");
        workOrderExportDTO.setWorkOrderTopTypeId(3L);
        workOrderExportDTO.setWorkOrderTopTypeName("测试");
        workOrderExportDTO.setContent("我是通知得内容！我是通知得内容！我是通知得内容！ ");
        workOrderExportDTO.setCrUsername("杨鑫");
        workOrderExportDTO.setCrTruename("杨鑫");
        workOrderExportDTO.setCrUnit("我是单位名称");
        workOrderExportDTO.setCrUnitId(250L);
        workOrderExportDTO.setCrTime(new Date());
        NoticeExportDTO noticeExportDTO = new NoticeExportDTO();
        noticeExportDTO.setTargetUsername("兰鑫");
        noticeExportDTO.setTargetTruename("兰鑫");
        noticeExportDTO.setTargetUnitId(251L);
        noticeExportDTO.setTargetUnit("我是251单位");
        noticeExportDTO.setType(1);
        NoticeExportDTO noticeExportDTO1 = new NoticeExportDTO();
        noticeExportDTO1.setTargetUsername("左开元");
        noticeExportDTO1.setTargetTruename("左开元");
        noticeExportDTO1.setTargetUnitId(252L);
        noticeExportDTO1.setTargetUnit("我是252研究院");
        noticeExportDTO1.setType(2);
        workOrderExportDTO.setNoticeDTOS(Arrays.asList(noticeExportDTO,noticeExportDTO1));
        WorkOrderVO workOrderVO = workOrderService.exportWorkOrder(workOrderExportDTO);
        System.out.println("##################注意删除测试数据########################");
        System.out.println("delete from work_order where id = "+workOrderVO.getId());
        System.out.println("delete from notice where work_order_id = "+workOrderVO.getId());

    }






















}
