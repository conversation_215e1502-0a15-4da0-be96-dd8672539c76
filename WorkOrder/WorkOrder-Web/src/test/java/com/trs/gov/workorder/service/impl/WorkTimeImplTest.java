package com.trs.gov.workorder.service.impl;


import com.trs.gov.WorkOrderApplication;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.WorkTimeExportDTO;
import com.trs.gov.workorder.VO.WorkTimeVO;
import com.trs.gov.workorder.dao.WorkTimeMapper;
import com.trs.gov.workorder.service.IWorkTimeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = WorkOrderApplication.class)
@Slf4j
public class WorkTimeImplTest{
    @Reference(check = false, timeout = 60000)
    private IWorkTimeService service;
    @Autowired
    private WorkTimeMapper workTimeMapper;

    @Test
    public void testExportWorkTime() throws ServiceException {
        WorkTimeExportDTO dto = new WorkTimeExportDTO();
        dto.setOldWorkTimeId("我是老ID");
        dto.setCrTime(new Date());
        dto.setCrUnitId(999L);
        dto.setCrUsername("杨鑫");
        dto.setWorkDesc("记录工时");
        dto.setWorkingTime(2.0);
        dto.setWorkOrderId(977L);
        dto.setWorkUnit("我是888danwei 名称");
        dto.setWorkUnitId(888L);
        WorkTimeVO workTimeVO = service.exportWorkTime(dto);
        //删除 测试数据应
        System.out.println(workTimeVO.getWorkTimeId());

//        workTimeMapper.deleteById(workTimeVO.getWorkTimeId());

    }
}