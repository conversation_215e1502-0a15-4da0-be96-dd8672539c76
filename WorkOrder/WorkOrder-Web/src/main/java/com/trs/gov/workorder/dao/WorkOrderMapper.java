package com.trs.gov.workorder.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.NoticeSearchDTO;
import com.trs.gov.workorder.VO.NoticeStatisticsVO;
import com.trs.gov.workorder.VO.NoticeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface WorkOrderMapper extends BaseMapper<WorkOrderDO> {

    /**
     * 查找通知
     * @param noticeSearchDTO
     * @return
     */
    IPage<NoticeVO> queryNotice(IPage<?> page, @Param("dto") NoticeSearchDTO noticeSearchDTO, @Param("groupIdList") List<Long> groupIdList);

    /**
     * 查找已读
     * @param workOrderIds
     * @return
     */
    List<NoticeStatisticsVO> queryNoticeReaded(List<Long> workOrderIds);

    /**
     * 查找所有使用的工单类型id
     * @return
     */
    List<Long> queryAllWorkOrderTypeId();
}
