package com.trs.gov.workorder.mgr;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.impl.ComplaintWorkOrderSaveDecorator;
import com.trs.gov.workorder.mgr.impl.NoticeWorkOrderSaveDecorator;
import com.trs.gov.workorder.mgr.impl.WorkOrderSaveDecorator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class WorkOrderFactory {
    @Autowired
    private WorkOrderSaveDecorator workOrderSaveDecorator;
    @Autowired
    private NoticeWorkOrderSaveDecorator noticeWorkOrderSaveDecorator;
    @Autowired
    private ComplaintWorkOrderSaveDecorator complaintWorkOrderSaveDecorator;

    public IWorkOrderSaveDecorator getWorkOrderSave(Long workOrderTopTypeId) throws ServiceException {
        if(workOrderTopTypeId==null){
            throw new ServiceException("顶级工单类型不能为空：" + workOrderTopTypeId);
        }
        if (workOrderTopTypeId.equals(WorkOrderConstant.TOP_TYPE_WORK_ORDER)) {
            return workOrderSaveDecorator;
        } else if (workOrderTopTypeId.equals(WorkOrderConstant.TOP_TYPE_COMPLAINT)) {
            return complaintWorkOrderSaveDecorator;
        } else if (workOrderTopTypeId.equals(WorkOrderConstant.TOP_TYPE_NOTICE)) {
            return noticeWorkOrderSaveDecorator;
        }
        throw new ServiceException("未知顶级工单类型：" + workOrderTopTypeId);
    }
}
