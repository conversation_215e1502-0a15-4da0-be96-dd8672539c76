package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：QuestionKnowledge
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:52
 **/
@Component
public class QuestionKnowledge extends WorkOrderSearchTypeMgr {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        return Optional.ofNullable(m -> m.in("status", WorkOrderConstant.STATUS_OPENED));
    }

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_QUESTION_KNOWLEDGE);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_QUESTION_KNOWLEDGE);
    }
}
