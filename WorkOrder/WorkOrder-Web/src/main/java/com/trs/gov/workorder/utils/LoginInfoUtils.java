package com.trs.gov.workorder.utils;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;

public class LoginInfoUtils {

    public static Long getLoginUnitId() throws ServiceException {
        return Long.valueOf(ContextHelper.getLoginUnitId().orElseThrow(() -> new ServiceException("没有登录单位id")));
    }

    public static String getLoginUser() throws ServiceException {
        return ContextHelper.getLoginUser().orElseThrow(() -> new ServiceException("没有登录用户"));
    }
}
