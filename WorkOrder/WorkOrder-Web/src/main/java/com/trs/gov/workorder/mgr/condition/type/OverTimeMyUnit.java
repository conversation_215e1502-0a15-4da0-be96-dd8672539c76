package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;
import java.util.function.Consumer;


/**
 * @ClassName：ChildHost
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:36
 **/
@Component
public class OverTimeMyUnit extends WorkOrderSearchTypeMgr {
    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_OVERTIME_MY_UNIT);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_OVERTIME_MY_UNIT);
    }

    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        UnitVO loginUnit = unitUtils.getLoginUnit();
        return Optional.ofNullable(m -> m
                .in("status", WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING)
                .lt("expected_end_date", new Date())
                .and(consumerMyDeal(loginUnit, true)));
    }
}
