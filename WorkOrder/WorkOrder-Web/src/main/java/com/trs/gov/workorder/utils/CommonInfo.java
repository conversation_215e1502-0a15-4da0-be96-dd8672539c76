package com.trs.gov.workorder.utils;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.user.VO.UserVO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Data
public class CommonInfo {
    private Map<Long, UnitVO> unitVOMap;
    private Map<String, UserVO> userVOMap;

    public CommonInfo(Map<Long, UnitVO> unitVOMap, Map<String, UserVO> userVOMap){
        this.unitVOMap = unitVOMap == null ? new HashMap<>() : unitVOMap;
        this.userVOMap = userVOMap == null ? new HashMap<>() : userVOMap;
    }

    public UnitVO getUnitVO(Long unitId) throws ServiceException {
        return Optional.ofNullable(unitVOMap.get(unitId)).orElseThrow(() -> new ServiceException("单位【"+unitId+"】不存在"));
    }

    public UserVO getUserVO(String username) throws ServiceException {
        return Optional.ofNullable(userVOMap.get(username)).orElseThrow(() -> new ServiceException("用户【"+username+"】不存在"));
    }
}
