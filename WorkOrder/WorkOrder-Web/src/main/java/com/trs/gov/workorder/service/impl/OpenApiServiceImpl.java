package com.trs.gov.workorder.service.impl;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.core.util.OpenApiUtil;
import com.trs.gov.workorder.DTO.OpenApiDTO;
import com.trs.gov.workorder.VO.AppInfoVO;
import com.trs.gov.workorder.mgr.OpenApiMgr;
import com.trs.gov.workorder.service.IOpenApiService;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.user.DTO.UserDTO;
import com.trs.user.service.IUserService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;
import java.util.UUID;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2025-08-07 17:46
 */
@Service
@Slf4j
public class OpenApiServiceImpl implements IOpenApiService {

    @Autowired
    private OpenApiMgr openApiMgr;

    @Autowired
    private IWorkOrderService workOrderService;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;


    @Override
    public RestfulResults saveWorkOrder(HttpServletRequest request, OpenApiDTO dto) {
        String sessionId = UUID.randomUUID().toString();
        try {
            if (!openApiMgr.checkAuth(request)) {
                throw new ServiceException("接口签名校验失败，请重试！");
            }
            Optional<AppInfoVO> appInfo = openApiMgr.getAppInfo(request.getHeader("appId"));
            if (!appInfo.isPresent()) {
                throw new ServiceException("根据appId获取配置失败！");
            }
            dto.setSource(appInfo.get().getSource());
            // 设置当前登录用户
            if (StringUtils.isEmpty(dto.getUserName())) {
                throw new ServiceException("第三方系统登录用户为空！");
            }
            if (StringUtils.isEmpty(dto.getThirdUnitId())) {
                throw new ServiceException("第三方系统用户单位为空！");
            }
            UserDTO userDTO = new UserDTO();
            userDTO.setUserName(dto.getUserName());
            String userToken = userService.getLoadLoginUserToken(sessionId, userDTO);
            ContextHelper.setLoginUnitId(dto.getUnitId());
            dto.setUnitId(dto.getThirdUnitId());
            dto.setToken(userToken);

            workOrderService.saveOrUpdateWorkOrder(dto);
            return RestfulResults.ok("添加成功");
        } catch (Exception e) {
            log.error("第三方添加工单失败", e);
            return RestfulResults.error("添加工单失败:"+e.getMessage());
        } finally {
            try {
                userService.logout(sessionId);
            } catch (ServiceException e) {
                log.error("登出用户失败", e);
            }
        }

    }
}
