package com.trs.gov.workorder.utils;

import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.gov.workorder.DTO.TargetDTO;
import com.trs.gov.workorder.utils.external.UnitGroupUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/11
 */
@Component
public class TargetDTOUtilts {
    @Autowired
    private UnitGroupUtils unitGroupUtils;

    public void removeRepeat(List<? extends TargetDTO> targetDTOList, boolean isNotice) throws ServiceException {
        String groupIds = targetDTOList.stream()
                .filter(targetDTO -> AssignConstant.ASSIGN_TO_GROUP.equals(targetDTO.getType()))
                .map(targetDTO -> String.valueOf(targetDTO.getGroupId()))
                .distinct().collect(Collectors.joining(","));
        List<UserCountVO> groupUnitInfoList = unitGroupUtils.getGroupUnitInfoByGroupIds(groupIds);
        List<Long> groupUnitList = groupUnitInfoList.stream()
                .map(UserCountVO::getUnitId)
                .distinct().collect(Collectors.toList());
        Iterator<? extends TargetDTO> iterator = targetDTOList.iterator();
        //去掉重复单位
        while (iterator.hasNext()) {
            TargetDTO targetDTO = iterator.next();
            boolean containUnit = groupUnitList.contains(targetDTO.getTargetUnitId());
            if (containUnit) {
                if (AssignConstant.ASSIGN_TO_UNIT.equals(targetDTO.getType())) {
                    iterator.remove();
                } else if (AssignConstant.ASSIGN_TO_USER.equals(targetDTO.getType()) && isNotice) {
                    iterator.remove();
                }
            }
        }

        List<Long> unitList = targetDTOList.stream()
                .filter(targetDTO -> AssignConstant.ASSIGN_TO_UNIT.equals(targetDTO.getType()))
                .map(TargetDTO::getTargetUnitId)
                .distinct().collect(Collectors.toList());
        unitList.addAll(groupUnitList);
        //去掉重复用户
        if (isNotice) {
            iterator = targetDTOList.iterator();
            while (iterator.hasNext()) {
                TargetDTO targetDTO = iterator.next();
                boolean containUnit = unitList.contains(targetDTO.getTargetUnitId());
                if (AssignConstant.ASSIGN_TO_USER.equals(targetDTO.getType()) && containUnit) {
                    iterator.remove();
                }
            }
        }
    }

}
