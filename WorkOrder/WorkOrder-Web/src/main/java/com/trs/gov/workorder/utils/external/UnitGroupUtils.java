package com.trs.gov.workorder.utils.external;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DTO.GroupUnitListSearchDTO;
import com.trs.gov.management.DTO.UnitGroupSearchDTO;
import com.trs.gov.management.VO.GroupUserCountVO;
import com.trs.gov.management.VO.UnitGroupRelationVO;
import com.trs.gov.management.VO.UnitGroupVO;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.gov.management.service.UnitGroupRelationService;
import com.trs.gov.management.service.UnitGroupService;
import com.trs.gov.workorder.constant.CommonConstant;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/1/11
 */
@Component
@Slf4j
public class UnitGroupUtils extends BaseExternalUtils{
    @Reference(check = false, timeout = 60000)
    private UnitGroupRelationService unitGroupRelationService;
    @Reference(check = false, timeout = 60000)
    private UnitGroupService unitGroupService;

    /**
     * 获取登录单位所在分组id
     * @return
     * @throws ServiceException
     */
    public List<Long> getLoginGroupIds() throws ServiceException {
        List<Long> groupIdList = unitGroupRelationService.getGroupByUnitId(String.valueOf(LoginInfoUtils.getLoginUnitId()));
        return groupIdList !=null ? groupIdList : new ArrayList<>();
    }

    /**
     * 获取单位所在分组id
     * @param unitIds
     * @return: java.util.List<java.lang.Long>
     * @Author: zuo.kaiyuan
     * @Date: 2021/1/20
     */
    public List<Long> getGroupIdsByUnitIds(String unitIds) throws ServiceException {
        List<Long> groupIdList = unitGroupRelationService.getGroupByUnitId(unitIds);
        return groupIdList !=null ? groupIdList : new ArrayList<>();
    }

    /**
     * 根据分组id查询分组信息
     * @param groupIds
     * @return: java.util.Map<java.lang.Long,com.trs.gov.management.VO.UnitGroupVO>
     * @Author: zuo.kaiyuan
     * @Date: 2021/1/7
     */
    public Map<Long, UnitGroupVO> getUnitGroupByIds(String groupIds) throws ServiceException {
        Map<Long, UnitGroupVO> unitGroupVOMap = new HashMap<>();
        if(StringUtils.isEmpty(groupIds)){
            return unitGroupVOMap;
        }
        UnitGroupSearchDTO dto = new UnitGroupSearchDTO();
        dto.setId(groupIds);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<UnitGroupVO>> restfulResults = unitGroupService.queryUnitGroupNotRight(dto);
        checkRestfulResults(restfulResults);
        List<UnitGroupVO> unitGroupVOList = restfulResults.getDatas();
        if(!CollectionUtils.isEmpty(unitGroupVOList)){
            for (UnitGroupVO unitGroupVO : unitGroupVOList) {
                unitGroupVOMap.put(unitGroupVO.getId(), unitGroupVO);
            }
        }

        return unitGroupVOMap;
    }

    public Map<Long, UnitGroupVO> getUnitGroupByIds(Set groupIdSet) throws ServiceException{
        String groupIds = StringUtils.join(groupIdSet.toArray(), ",");
        return getUnitGroupByIds(groupIds);
    }

    /**
     * 根据分组id查询单位信息
     * @param groupIds
     * @return: java.util.List<com.trs.gov.management.VO.UserCountVO>
     * @Author: zuo.kaiyuan
     * @Date: 2021/1/11
     */
    public List<UserCountVO> getGroupUnitInfoByGroupIds(String groupIds) throws ServiceException {
        List<UserCountVO> unitCountVOList = new ArrayList<>();
        if(StringUtils.isEmpty(groupIds)){
            return unitCountVOList;
        }
        GroupUnitListSearchDTO dto = new GroupUnitListSearchDTO();
        dto.setIsAll(String.valueOf(CommonConstant.YES));
        dto.setGroupId(groupIds);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<UserCountVO>> restfulResults = unitGroupService.getGroupUnitInfoListByGroupIdNotRight(dto);
        checkRestfulResults(restfulResults);

        return restfulResults.getDatas();
    }

    /**
     * 根据分组id查询单位信息
     * @param groupIds
     * @return: java.util.List<com.trs.gov.management.VO.UserCountVO>
     * @Author: zuo.kaiyuan
     * @Date: 2021/1/11
     */
    public List<GroupUserCountVO> getUserCountByGroupIds(String groupIds) throws Exception {
        List<GroupUserCountVO> groupCountVOList = new ArrayList<>();
        if(StringUtils.isEmpty(groupIds)){
            return groupCountVOList;
        }
        GroupUnitListSearchDTO dto = new GroupUnitListSearchDTO();
        dto.setGroupId(groupIds);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<GroupUserCountVO>> restfulResults = unitGroupService.getGroupUserCount(dto);

        return restfulResults.getDatas();
    }

    /**
     * 根据分组id查询单位信息
     * @param groupIds
     * @return: java.util.List<com.trs.gov.management.VO.UserCountVO>
     * @Author: zuo.kaiyuan
     * @Date: 2021/1/11
     */
    public List<UnitGroupRelationVO> getUnitIdsByGroupIds(String groupIds) throws Exception {
        List<UnitGroupRelationVO> unitGroupRelationVOList = new ArrayList<>();
        if(StringUtils.isEmpty(groupIds)){
            return unitGroupRelationVOList;
        }
        GroupUnitListSearchDTO dto = new GroupUnitListSearchDTO();
        dto.setGroupId(groupIds);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        unitGroupRelationVOList = unitGroupRelationService.getUnitGroupRelationList(dto);

        return unitGroupRelationVOList;
    }
}
