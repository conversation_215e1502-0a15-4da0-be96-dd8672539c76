package com.trs.gov.workorder.service.impl;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.service.IMessageService;
import com.trs.gov.workorder.utils.external.MessageUtils;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class MessageImpl implements IMessageService {
    @Autowired
    private MessageUtils messageUtils;
    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Override
    public CreateNoticeDTO buildbaseMessage(WorkOrderSearchDTO workOrderSerchDTO) throws Exception {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderSerchDTO);

        Long id = workOrderSerchDTO.getId();
        if(id == null){
            throw new ServiceException("工单id不能为空");
        }
        return messageUtils.buildbaseMessage(workOrderSerchDTO.getId());
    }
}
