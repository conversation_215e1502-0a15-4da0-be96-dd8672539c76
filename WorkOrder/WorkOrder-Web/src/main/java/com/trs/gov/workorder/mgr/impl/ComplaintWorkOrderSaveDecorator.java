package com.trs.gov.workorder.mgr.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.WorkOrderDTO;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.VO.ResultVO;
import com.trs.gov.workorder.mgr.AbstractWorkOrderSaveDecorator;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 投诉工单保存
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-15 12:34
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class ComplaintWorkOrderSaveDecorator extends AbstractWorkOrderSaveDecorator {
    @Override
    public String desc() {
        return "保存投诉";
    }

    public boolean isValid(WorkOrderDTO dto) throws ServiceException {
        if (dto == null) {
            throw new ServiceException("工单不能为空");
        }
        dto.isValid();
        PreConditionCheck.checkArgument(dto.getWorkOrderTypeId() != null, "类型不能为空");
        return true;
    }

    @Override
    public void initParameterParse(WorkOrderDTO dto) throws ServiceException {
        isValid(dto);
    }

    @Override
    public boolean checkParamsValid(WorkOrderExportDTO dto) throws ServiceException {
        //投诉 建议 指定到单位
        dto.setDealAssignType(AssignConstant.ASSIGN_TO_UNIT);
        dto.ComplaintValid();
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveExportWorkOrder(WorkOrderExportDTO dto) throws ServiceException {
        WorkOrderDO workOrderDO = exportWorkOrderConvert(dto);
        BeanUtils.copyProperties(dto, workOrderDO);
        saveWorkOrder(workOrderDO, dto);
        dto.setDealAssignType(AssignConstant.ASSIGN_TO_UNIT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestfulResults startExecuteOneWorkOrder(WorkOrderDTO dto) throws ServiceException {
        WorkOrderDO workOrderDO = commonConvert(dto);
        workOrderDO.setWorkOrderTypeName(workOrderTypeUtils.findWorkOrderTypeById(workOrderDO.getWorkOrderTypeId()).getTypeName());
        if (dto.getId() == null || dto.getId() == 0) {
            //如果是创建，存入默认的投诉受理单位
            Long complaintUnitId = workOrderTypeUtils.getComplainUnitId();
            workOrderDO.setDealUnitId(complaintUnitId);
            UnitVO unitVO = unitUtils.findUnitByUnitId(complaintUnitId);
            workOrderDO.setDealUnit(unitVO.getUnitName());
            workOrderDO.setDealAssignType(AssignConstant.ASSIGN_TO_UNIT);
            workOrderDO.setDealUsername(unitVO.getUnitMaster());
            workOrderDO.setDealTruename(unitVO.getUnitMasterTrueName());
        }

        //dto存入处理单位、处理人、指定类型，以便保存操作记录、发送消息
        dto.setDealUnitId(workOrderDO.getDealUnitId());
        dto.setDealUsername(workOrderDO.getDealUsername());
        dto.setDealAssignType(AssignConstant.ASSIGN_TO_UNIT);

        commonSaveOrUpdate(dto, workOrderDO);
        return RestfulResults.ok(ResultVO.createSuccess("保存投诉", "保存投诉成功"));
    }
}
