package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：MyReviewed
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:48
 **/
@Component
public class MyReviewed extends WorkOrderSearchTypeMgr{
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        //历史处理过的工单
        List<Long> relatedWorkIdList = oprRecordUtils.getRelatedWorkOrderId(true, true,
                OperateNameConstant.APPRAISE_WORK_ORDER);
        return Optional.ofNullable(m -> m
                .in(!CollectionUtils.isEmpty(relatedWorkIdList), "id", relatedWorkIdList)
                //没找到，加个false条件
                .in(CollectionUtils.isEmpty(relatedWorkIdList), "id", -1)
        );
    }

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_REVIEM_REVIEWED);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_REVIEM_REVIEWED);
    }
}
