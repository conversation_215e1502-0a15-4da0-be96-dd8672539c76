package com.trs.gov.workorder.utils;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.WorkOrderActionDTO;
import com.trs.gov.workorder.DTO.WorkOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalTime;

/**
 * Description:  <BR>
 * Copyright: Copyright (c) 2004-2016 拓尔思信息技术股份有限公司 <BR>
 * Company: www.trs.com.cn <BR>
 *
 * <AUTHOR>
 * @version 1.0
 * @createTime 2022/4/2 9:21
 */
@Component
@Slf4j
public class AutoReplyUtils {

    @Value("${workOrder.autoReply.content:【自动回复】依据《贵州省政府网站集约化平台工单办理办法（试行）》，非工作时间提交工单，原则上顺延至工作时间处理，如急需处理，请拨打13027880860、0851-86893579、17152144142，我们将在第一时间处理。谢谢！}")
    private String autoReplyContent;

    @Value("${workOrder.autoReply.content.open:false}")
    private boolean autoReplyOpen;

    @Value("${workOrder.autoResponse.person.name:负责人}")
    private String autoResponsePersonName;

    @Value("${workOrder.autoReply.person.content:您好，您提的问题／需求已收到，正在处理中，请耐心等待，稍后给您回复。}")
    private String autoReplyContentByPerson;

    /**
     * 晚上17：30到第二天早上8：30期间提工单系统自动回复
     * @param workOrderDTO
     */
    public WorkOrderActionDTO autoReplyGetAction(WorkOrderDTO workOrderDTO) throws ServiceException {
        WorkOrderActionDTO action = new WorkOrderActionDTO();
        if (workOrderDTO.getIsCreate() == true){
            //当前时间
            LocalTime now = LocalTime.now();
            //今日早上8点30分
            LocalTime eight = LocalTime.of(8,30);
            //今日晚上17点30分
            LocalTime seventeen = LocalTime.of(17,30);
            if (now.isBefore(eight) || now.isAfter(seventeen)) {
                String reply = OperateNameConstant.REPLY_WORK_ORDER;
                action.setOprKey(reply);
                action.setOption(autoReplyContent);
                action.setWorkOrderId(workOrderDTO.getId());
            }
        }
        return action;
    }

    /**
     * 考核需求，5分钟内响应
     * @param workOrderDTO
     */
    public WorkOrderActionDTO autoReplyByPerson(WorkOrderDTO workOrderDTO) throws ServiceException {
        WorkOrderActionDTO action = new WorkOrderActionDTO();
        if (workOrderDTO.getIsCreate() == true){
                String reply = OperateNameConstant.REPLY_WORK_ORDER;
                action.setOprKey(reply);
                action.setOption(autoReplyContentByPerson);
                action.setWorkOrderId(workOrderDTO.getId());
                action.setTargetUnitId(workOrderDTO.getDealUnitId());
                action.setTargetUsername(workOrderDTO.getDealUsername());
                action.setUnitId(String.valueOf(workOrderDTO.getDealUnitId()));
            }
        return action;
    }

    public boolean getReplyOpen(){
        return autoReplyOpen;
    }

    public String getAutoResponsePersonName(){
        return autoResponsePersonName;
    }

}
