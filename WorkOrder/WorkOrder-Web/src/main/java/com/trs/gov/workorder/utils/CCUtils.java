package com.trs.gov.workorder.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.CCDTO;
import com.trs.gov.workorder.DTO.CCExportDTO;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.utils.external.UnitUtils;
import com.trs.gov.workorder.utils.external.UserExternalUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 抄送工具类
 */
@Component
public class CCUtils {
    @Autowired
    private CCMapper ccMapper;
    @Autowired
    private UserExternalUtils userExternalUtils;
    @Autowired
    private UnitUtils unitUtils;

    public List<CCDTO> cc(WorkOrderDO workOrderDO, List<CCDTO> ccdtoList, List<CCDO> retainCcdoList, CommonInfo info) throws ServiceException {
        List<CCDO> oldCcdoList = ccMapper.selectList(new QueryWrapper<CCDO>().eq("work_order_id", workOrderDO.getId()));
        List<CCDTO> newCcdtoList = new ArrayList<>();

        //抄送给全部
        for (CCDTO ccdto : ccdtoList) {
            if (AssignConstant.ASSIGN_TO_ALL.equals(ccdto.getType())) {
                int count = ccMapper.selectCount(new QueryWrapper<CCDO>()
                        .eq("work_order_id", workOrderDO.getId())
                        .eq("type", AssignConstant.ASSIGN_TO_ALL));
                if(count==0){
                    ccMapper.delete(new QueryWrapper<CCDO>().eq("work_order_id", workOrderDO.getId()));
                    CCDO ccdo = ccdtoToCcdo(ccdto, workOrderDO.getId(), info);
                    ccMapper.insert(ccdo);
                    newCcdtoList.add(ccdto);
                }
                return newCcdtoList;
            }
        }

        List<Long> deleteCCIdList = new ArrayList<>();
        Long loginUnitId = LoginInfoUtils.getLoginUnitId();
        String loginUser = LoginInfoUtils.getLoginUser();
        boolean isDealUser = loginUnitId.equals(workOrderDO.getDealUnitId()) && loginUser.equals(workOrderDO.getDealUsername());
        //删除不用的抄送
        for (CCDO oldCcdo : oldCcdoList) {
            if (!isContain(ccdtoList, oldCcdo)) {
                boolean deleteAccess = !userExternalUtils.isAdminAndHasLoginUnit() && !isDealUser &&
                        !oldCcdo.getType().equals(AssignConstant.ASSIGN_TO_GROUP) && !oldCcdo.getType().equals(AssignConstant.ASSIGN_TO_ALL) &&
                        !((loginUnitId.equals(oldCcdo.getCrUnitId()) && loginUser.equals(oldCcdo.getCrUsername())) ||
                                (loginUnitId.equals(oldCcdo.getTargetUnitId()) && loginUser.equals(oldCcdo.getTargetUsername())));
                if (deleteAccess) {
                    throw new ServiceException("删除抄送" + "【" +oldCcdo.getGroupId()+":"+oldCcdo.getTargetUnitId()+":"+oldCcdo.getTargetUsername() + "】失败,没有权限");
                } else {
                    deleteCCIdList.add(oldCcdo.getId());
                }
            }else if(retainCcdoList!=null){
                retainCcdoList.add(oldCcdo);
            }

        }
        if (!deleteCCIdList.isEmpty()) {
            ccMapper.deleteBatchIds(deleteCCIdList);
        }

        List<CCDO> newCcdoList = new ArrayList<>();
        //添加之前没有的抄送
        for (CCDTO ccdto : ccdtoList) {
            if (!isContain(oldCcdoList, ccdto)) {
                CCDO ccdo = ccdtoToCcdo(ccdto, workOrderDO.getId(), info);
                newCcdoList.add(ccdo);
                //保存新增的抄送，方便发送消息
                newCcdtoList.add(ccdto);
            }
        }
        if (!CollectionUtils.isEmpty(newCcdoList)) {
            ccMapper.insertList(newCcdoList);
        }

        return newCcdtoList;
    }

    /**
     * CCDO列表是否包含CCDTO
     * @param ccdoList
     * @param ccdto
     * @return
     */
    public boolean isContain(List<CCDO> ccdoList, CCDTO ccdto){
        for (CCDO ccdo : ccdoList) {
            if(compareCCDTOWithDO(ccdo, ccdto)){
                return true;
            }
        }

        return false;
    }

    /**
     * CCDTO列表是否包含CCDO
     * @param ccdtoList
     * @param ccdo
     * @return
     */
    public boolean isContain(List<CCDTO> ccdtoList, CCDO ccdo) {
        for (CCDTO ccdto : ccdtoList) {
            if(compareCCDTOWithDO(ccdo, ccdto)){
                return true;
            }
        }

        return false;
    }

    public boolean compareCCDTOWithDO(CCDO ccdo, CCDTO ccdto){
        if (ccdto.getType().equals(ccdo.getType())) {
            if(ccdto.getType().equals(AssignConstant.ASSIGN_TO_ALL)){
                return true;
            }

            if (ccdto.getType().equals(AssignConstant.ASSIGN_TO_GROUP) && ccdto.getGroupId().equals(ccdo.getGroupId())) {
                return true;
            }

            if(ccdto.getType().equals(AssignConstant.ASSIGN_TO_UNIT) && ccdto.getTargetUnitId().equals(ccdo.getTargetUnitId())){
                return true;
            }

            if (ccdto.getType().equals(AssignConstant.ASSIGN_TO_USER) && ccdto.getTargetUnitId().equals(ccdo.getTargetUnitId()) &&
                    ccdto.getTargetUsername().equals(ccdo.getTargetUsername())) {
                return true;
            }
        }

        return false;
    }

    public CCDO ccdtoToCcdo(CCDTO ccdto, Long workOrderId, CommonInfo info) throws ServiceException {
        CCDO ccdo = new CCDO();
        ccdo.setWorkOrderId(workOrderId);
        ccdo.setCrUnitId(LoginInfoUtils.getLoginUnitId());
        ccdo.setCrUnit(info.getUnitVO(ccdo.getCrUnitId()).getUnitName());
        ccdo.setCrUsername(ccdo.getCrUser());
        ccdo.setCrTruename(info.getUserVO(ccdo.getCrUsername()).getTrueName());
        ccdo.setType(ccdto.getType());
        if(AssignConstant.ASSIGN_TO_GROUP.equals(ccdto.getType())){
            ccdo.setGroupId(ccdto.getGroupId());
        }else if(AssignConstant.ASSIGN_TO_UNIT.equals(ccdto.getType()) || AssignConstant.ASSIGN_TO_USER.equals(ccdto.getType())){
            ccdo.setTargetUnitId(ccdto.getTargetUnitId());
            ccdo.setTargetUnit(info.getUnitVO(ccdo.getTargetUnitId()).getUnitName());
            if(AssignConstant.ASSIGN_TO_USER.equals(ccdo.getType())){
                ccdo.setTargetUsername(ccdto.getTargetUsername());
            }else {
                ccdo.setTargetUsername(info.getUnitVO(ccdo.getTargetUnitId()).getUnitMaster());
            }
            ccdo.setTargetTruename(info.getUserVO(ccdo.getTargetUsername()).getTrueName());
        }

        return ccdo;
    }

    public CCDO ccExportDtoToCcdo(CCExportDTO ccdto, WorkOrderExportDTO dto){
        CCDO ccdo = new CCDO();
        BaseUtils.copyProperties(ccdto,ccdo);
        ccdo.setWorkOrderId(dto.getWorkOrderId());
        ccdo.setCrTime(dto.getCrTime());
        ccdo.setCrUser(dto.getCrUsername());
        ccdo.setCrUsername(dto.getCrUsername());
        ccdo.setCrTruename(dto.getCrTruename());
        ccdo.setCrUnitId(dto.getCrUnitId());
        ccdo.setCrUnit(dto.getCrUnit());
        return ccdo;
    }
}
