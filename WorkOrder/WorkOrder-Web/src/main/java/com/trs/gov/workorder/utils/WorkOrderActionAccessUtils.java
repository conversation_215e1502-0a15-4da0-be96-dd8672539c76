package com.trs.gov.workorder.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.VO.ActionVO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.service.ISupportKey;
import com.trs.gov.workorder.utils.external.UnitGroupUtils;
import com.trs.gov.workorder.utils.external.UnitUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class WorkOrderActionAccessUtils {
    @Autowired
    private List<ISupportKey> mgrs;
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private CCMapper ccMapper;
    @Autowired
    private WorkTimeAccessUtils workTimeAccessUtils;
    @Autowired
    private UnitGroupUtils unitGroupUtils;
    @Autowired
    private UnitUtils unitUtils;

    /**
     *
     * @param workOrderId
     * @return
     * @throws ServiceException
     */
    public SupportActionDTO getSupportAction(Long workOrderId) throws ServiceException {
        WorkOrderDO workOrder = workOrderMapper.selectById(workOrderId);

        //通知没有任何操作
        if (workOrder.getWorkOrderTopTypeId().equals(WorkOrderConstant.TOP_TYPE_NOTICE)) {
            return null;
        }

        String loginUser = LoginInfoUtils.getLoginUser();
        UnitVO loginUnit = unitUtils.getLoginUnit();
        //投诉工单的创建人，没有任何操作权限
        if (workOrder.getWorkOrderTopTypeId().equals(WorkOrderConstant.TOP_TYPE_COMPLAINT) &&
                loginUnit.getId() == workOrder.getCrUnitId().longValue() && loginUser.equals(workOrder.getCrUsername())) {
            return null;
        }
        List<CCDO> ccdoList = ccMapper.selectList(new QueryWrapper<CCDO>().eq("work_order_id", workOrder.getId()));
        List<Long> loginGroupIdList = unitGroupUtils.getLoginGroupIds();

        SupportActionDTO supportActionDTO = new SupportActionDTO(workOrder, loginUser, loginUnit, loginGroupIdList, ccdoList);
        return supportActionDTO;
    }

    /**
     * 获取工单的操作列表
     */
    public List<ActionVO> getOprList(Long workOrderId) throws ServiceException {
        List<ActionVO> list = new ArrayList<>();

        SupportActionDTO supportActionDTO = getSupportAction(workOrderId);
        if(supportActionDTO==null){
            return list;
        }

        mgrs.forEach(mgr -> {
            if (mgr.isSupportAction(supportActionDTO)) {
                if (!OperateNameConstant.REPLY_WORK_ORDER.equals(mgr.key())) {
                    ActionVO actionVO = new ActionVO();
                    actionVO.setOprkey(mgr.key());
                    actionVO.setOprname(mgr.desc());
                    if(OperateNameConstant.UPDATE_WORK_ORDER.equals(mgr.key())){
                        actionVO.setOprname(OperateNameConstant.getTypeDesc(OperateNameConstant.UPDATE_WORK_ORDER).get());
                    }
                    if (!list.contains(actionVO)) {
                        list.add(actionVO);
                    }
                }
            }
        });
        //添加工时权限
        list.addAll(workTimeAccessUtils.getWorkTimeAccess(workOrderId, null, OperateNameConstant.MAN_HOUR_WORK_ORDER));

        return list;
    }
}
