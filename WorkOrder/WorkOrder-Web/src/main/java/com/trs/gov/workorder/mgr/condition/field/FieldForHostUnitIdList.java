package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldForId
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 14:48
 **/
@Component
public class FieldForHostUnitIdList extends BaseCommonFieldMgr<WorkOrderDO> {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) {
        String hostUnitIdList = (String) keyWords;
        if(!CMyString.isEmpty(hostUnitIdList)){
            return Optional.ofNullable(m -> m.in(searchField(), hostUnitIdList.split(",")));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.HOST_UNIT_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.HOST_UNIT_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_FIELD_HOST_UNIT_ID;
    }
}
