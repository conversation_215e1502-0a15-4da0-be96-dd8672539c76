package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldForCcUnitIdList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:42
 **/
@Component
public class FieldForSiteIdList extends BaseCommonFieldMgr<WorkOrderDO> {
    @Override
    public <T extends BaseDTO>  Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keywords, T dto) throws ServiceException {
        String siteIdList = (String) keywords;
        if(!CMyString.isEmpty(siteIdList)){
            return Optional.ofNullable(m -> m.in(searchField(), siteIdList.split(",")));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.SITE_ID;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.SITE_ID;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_SITE_ID;
    }

}
