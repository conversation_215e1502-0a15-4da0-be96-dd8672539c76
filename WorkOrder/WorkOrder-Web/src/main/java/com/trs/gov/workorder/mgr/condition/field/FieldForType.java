package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldForType
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:30
 **/
@Component
public class FieldForType extends BaseCommonFieldMgr<WorkOrderDO> {
    @Autowired
    private List<WorkOrderSearchTypeMgr> searchTypes;

    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        Integer type = (Integer) keyWords;
        for (WorkOrderSearchTypeMgr searchType : searchTypes) {
            if(searchType.key().equalsIgnoreCase(String.valueOf(type))){
                return searchType.buildCondition("", dto);
            }
        }
        throw new ServiceException("未知查询分类：" + type);
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_SEARCH_TYPE;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_SEARCH_TYPE;
    }

}
