package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldForWorkOrderTopTypeList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 14:52
 **/
@Component
public class FieldForWorkOrderTopTypeIdList extends BaseCommonFieldMgr<WorkOrderDO> {

    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) {
        String workOrderTypeIdList = (String) keyWords;
        if(CMyString.isEmpty(workOrderTypeIdList)){
            return Optional.of(m -> m.ne(searchField(), WorkOrderConstant.TOP_TYPE_NOTICE));
        }else{
            return Optional.of(m -> m.in(searchField(), workOrderTypeIdList.split(",")));
        }
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_TOP_TYPE_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_TOP_TYPE_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_FIELD_WORK_ORDER_TOP_TYPE_ID;
    }
}
