package com.trs.gov.workorder.mgr.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.DTO.WorkOrderActionDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.constant.CommonConstant;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.mgr.AbstractActionMgr;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 工单-回退
 */
@Component
@Slf4j
public class ReturnActionMgr extends AbstractActionMgr {
    @Override
    public String key() {
        return OperateNameConstant.ROLLBACK_WORK_ORDER;
    }

    @Override
    public String desc() {
        return OperateNameConstant.getTypeDesc(OperateNameConstant.ROLLBACK_WORK_ORDER).get();
    }

    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO) {
        return (super.isSupportAction(supportActionDTO) ||
                workOrderRoleUtils.isWorkOrderRole(supportActionDTO,
                        WorkOrderRoleConstant.DEAL_UNIT_MASTER,
                        WorkOrderRoleConstant.DEAL_USER)) &&
                (supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_WAIT_ASSIGN ||
                        supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_DEALING);
    }

    @Override
    public void initParameterParse(BaseDTO baseDTO) throws ServiceException {
        WorkOrderActionDTO dto = (WorkOrderActionDTO) baseDTO;
        dto.isValid();
        PreConditionCheck.checkArgument(dto.getTargetUnitId()!=null, "回退单位不能为空");
    }

    @Override
    public RestfulResults startExecuteOneWorkOrder(BaseDTO baseDTO) throws ServiceException {
        WorkOrderDO workOrderDO = commonConvert(baseDTO);
        workOrderDO.setIsReturn(CommonConstant.YES);

        return commonDoAction(workOrderDO);
    }

    @Override
    public void endExecuteOneWorkOrder(BaseDTO dto, WorkOrderVO workOrder) throws ServiceException {

    }
}
