package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import com.trs.gov.workorder.mgr.impl.WorkOrderMgr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldForCcUnitIdList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:42
 **/
@Component
public class FieldForCcUnitIdList extends BaseCommonFieldMgr<WorkOrderDO> {
    @Autowired
    private WorkOrderMgr workOrderMgr;

    @Override
    public <T extends BaseDTO>  Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        String ccUnitIdList = (String) keyWords;
        if(!CMyString.isEmpty(ccUnitIdList)){
            List<Long> workOrderIdList = workOrderMgr.queryWorkOrderIdsByCCSearch(ccUnitIdList);
            return Optional.ofNullable(m -> m.in(!CollectionUtils.isEmpty(workOrderIdList), "id", workOrderIdList));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.HOST_CC_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.HOST_CC_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_FIELD_WORK_ORDER__ID;
    }

}
