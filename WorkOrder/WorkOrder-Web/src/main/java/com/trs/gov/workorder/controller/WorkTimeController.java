package com.trs.gov.workorder.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.WorkTimeDTO;
import com.trs.gov.workorder.DTO.WorkTimeQueryDTO;
import com.trs.gov.workorder.service.IWorkTimeService;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController()
@RequestMapping("/workorder/worktime")
public class WorkTimeController {
    @Autowired
    private IWorkTimeService workTimeService;

    @PostMapping("saveWorkTime")
    @ApiOperation(value = "保存或添加工时")
    public RestfulResults saveWorkTime(WorkTimeDTO workTimeDTO) throws ServiceException {
        return workTimeService.saveOrUpdateWorkTime(workTimeDTO);
    }

    @GetMapping("findWorkTime")
    @ApiOperation(value = "查找工时")
    public RestfulResults findWorkTime(WorkTimeQueryDTO workTimeQueryDTO) throws ServiceException {
        return workTimeService.queryWorkTime(workTimeQueryDTO);
    }

    @PostMapping("deleteWorkTime")
    @ApiOperation(value = "删除工时")
    public RestfulResults deleteWorkTime(WorkTimeDTO workTimeDTO) throws ServiceException {
        return workTimeService.deleteWorkTime(workTimeDTO);
    }
}
