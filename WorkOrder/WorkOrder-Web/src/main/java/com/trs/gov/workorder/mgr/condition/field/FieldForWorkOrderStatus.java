package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldForStatus
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:51
 **/
@Component
public class FieldForWorkOrderStatus extends BaseCommonFieldMgr<WorkOrderDO> {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        String status = (String) keyWords;
        if(!CMyString.isEmpty(status)){
            return Optional.ofNullable(m -> m.in(searchField(), status.split(",")));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.STATUS;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.STATUS;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_FIELD_STATUS;
    }
}
