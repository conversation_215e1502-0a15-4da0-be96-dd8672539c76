package com.trs.gov.workorder.utils.external;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.DTO.WorkOrderTypeSearchDTO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.gov.management.service.UnitService;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 调用外部接口工具类
 */
@Component
@Slf4j
public class UnitUtils extends BaseExternalUtils{
    @Reference(check = false, timeout = 60000)
    private UnitService unitService;

    /**
     * 获取登录用户的登录单位
     *
     * @return
     */
    public UnitVO getLoginUnit() throws ServiceException {
        String crUser = LoginInfoUtils.getLoginUser();
        Long unitId = LoginInfoUtils.getLoginUnitId();
        if (unitId.equals(0L)) {
            if (userService.userIsAdmin(UserDTO.of(LoginInfoUtils.getLoginUser()))) {
                //设定管理员临时单位，避免报错，权限之后考虑
                UnitVO unitVO = new UnitVO();
                unitVO.setId(0L);
                return unitVO;
            } else {
                throw new ServiceException("非管理员请选择登录单位！");
            }
        }

        try {
            return findUnitByUnitId(unitId);
        } catch (ServiceException e){
            throw new ServiceException("获取登录单位失败",e);
        }
    }

    /**
     * 根据单位id获取单位
     *
     * @param unitId
     * @return
     * @throws ServiceException
     */
    public UnitVO findUnitByUnitId(Long unitId) throws ServiceException {
        if (unitId == null) {
            throw new ServiceException("单位id不能为空: ");
        }

        UnitSearchDTO dto = new UnitSearchDTO();
        dto.setRpcTag(true);
        dto.setId(String.valueOf(unitId));
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<UnitVO>> restfulResults = unitService.queryUnitList(dto);
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
        if (CollectionUtils.isEmpty(restfulResults.getDatas())) {
            throw new ServiceException("单位【" + unitId + "】未找到");
        }

        return restfulResults.getDatas().get(0);
    }

    public Map<Long, UnitVO> findUnit(Set<Long> unitIdSet) throws ServiceException {
        String unitIds = StringUtils.join(unitIdSet.toArray(), ",");
        return findUnit(unitIds);
    }

    /**
     * 根据单位id列表获取单位列表
     *
     * @param unitIds
     * @return
     * @throws ServiceException
     */
    public Map<Long, UnitVO> findUnit(String unitIds) throws ServiceException {
        Map<Long, UnitVO> unitVOMap = new HashMap<>();
        if (StringUtils.isEmpty(unitIds)) {
            return unitVOMap;
        }

        UnitSearchDTO dto = new UnitSearchDTO();
        dto.setRpcTag(true);
        dto.setId(unitIds);
        dto.setIsAll("1");
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<UnitVO>> restfulResults = unitService.queryBaseUnitList(dto);
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
        List<UnitVO> unitVOList = restfulResults.getDatas();
        for (UnitVO unitVO : unitVOList) {
            unitVOMap.put(unitVO.getId(), unitVO);
        }

        return unitVOMap;
    }

    /**
     * 根据单位id查找单位下的所有用户
     *
     * @param unitId
     * @return
     * @throws ServiceException
     */
    public List<UserVO> findUsersByUnitId(Long unitId) throws ServiceException {
        if (unitId == null) {
            throw new ServiceException("单位id不能为空: ");
        }

        UnitSearchDTO unitSearchDTO = new UnitSearchDTO();
        unitSearchDTO.setRpcTag(true);
        String loginUnitId = ContextHelper.getLoginUnitId().orElse("");
        unitSearchDTO.setId(String.valueOf(unitId));
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, unitSearchDTO);
        ContextHelper.setLoginUnitId(loginUnitId);
        RestfulResults<List<UnitVO>> restfulResults = unitService.queryUnitList(unitSearchDTO);
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }

        if(!CollectionUtils.isEmpty(restfulResults.getDatas())){
            List<UserVO> userVOList = restfulResults.getDatas().get(0).getUserVOS();
            if(!CollectionUtils.isEmpty(userVOList)){
                return userVOList;
            }
        }

        return new ArrayList<>();
    }

    /**
     * 根据单位id获取所有下属单位
     *
     * @param unitId
     * @return
     * @throws ServiceException
     */
    public List<UnitVO> findChildrenUnitByUnitId(Long unitId) throws ServiceException {
        if (unitId == null) {
            throw new ServiceException("单位id不能为空: ");
        }

        UnitSearchDTO dto = new UnitSearchDTO();
        dto.setId(String.valueOf(unitId));
        dto.setIsAll("1");
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<UnitVO>> restfulResults = unitService.queryBaseChildrenUnitList(dto);
        checkRestfulResults(restfulResults);

        return restfulResults.getDatas();
    }

    /**
     * 根据单位id获取所有下属单位id
     *
     * @param unitId
     * @return
     * @throws ServiceException
     */
    public List<Long> findChildrenUnitIdsByUnitId(Long unitId) throws ServiceException {
        List<UnitVO> childrenUnits = findChildrenUnitByUnitId(unitId);
        List<Long> childrenUnitIds = new ArrayList<>();
        if(!CollectionUtils.isEmpty(childrenUnits)){
            childrenUnitIds = childrenUnits.stream().map(UnitVO::getId).collect(Collectors.toList());
        }

        return childrenUnitIds;
    }

    /**
     * 根据单位id获取单位用户数量
     *
     * @param ids
     * @return
     * @throws ServiceException
     */
    public List<UserCountVO> getUnitUserCountByUnitIds(String ids) throws ServiceException {
        WorkOrderTypeSearchDTO workOrderTypeSearchDTO = new WorkOrderTypeSearchDTO();
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderTypeSearchDTO);
        workOrderTypeSearchDTO.setId(ids);
        RestfulResults<List<UserCountVO>> restfulResults = unitService.getUserCountByUnitIds(ids);
        checkRestfulResults(restfulResults);
        return restfulResults.getDatas();
    }

    /**
     * 获取所有用户数量
     *
     * @param
     * @return
     * @throws ServiceException
     */
    public Long getAllUserCount() throws ServiceException {
        List<UserCountVO> unitUserCount = getUnitUserCountByUnitIds(null);
        return unitUserCount.stream().mapToLong(i -> Long.parseLong(String.valueOf(i.getUserCount()))).sum();
    }
}
