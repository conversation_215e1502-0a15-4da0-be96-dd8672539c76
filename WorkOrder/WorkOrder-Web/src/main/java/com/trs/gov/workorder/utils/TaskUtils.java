package com.trs.gov.workorder.utils;

import java.util.concurrent.*;

public class TaskUtils {
    public static ExecutorService threadPool = new ThreadPoolExecutor(20, 20, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());

    public static void newTask(Runnable runnable){
        threadPool.execute(runnable);
    }
}
