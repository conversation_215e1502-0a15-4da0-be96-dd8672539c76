package com.trs.gov.workorder.service.impl;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.StatisticDTO;
import com.trs.gov.workorder.VO.StatisticVO;
import com.trs.gov.workorder.mgr.impl.StatisticNumMgr;
import com.trs.gov.workorder.service.IStatisticNumService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName：StatisticNumServiceImpl
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/29 14:59
 **/
@Service
public class StatisticNumServiceImpl implements IStatisticNumService {
    @Autowired
    private StatisticNumMgr statisticNumMgr;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;
    /**
     * @Description  统计页面数量
     * @Param [statisticDTO]
     * @return com.trs.web.builder.base.RestfulResults<com.trs.gov.workorder.VO.StatisticVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/29 14:59
     **/
    @Override
    public RestfulResults<StatisticVO> countStatistic(StatisticDTO statisticDTO) {
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService,statisticDTO);
            //调用业务方法
            if(StringUtils.isEmpty(statisticDTO.getUserName()))
                throw new ServiceException("用户名不能为空!");
            if(StringUtils.isEmpty(statisticDTO.getUnitId()))
                throw new ServiceException("用户名单位不能为空!");
            return statisticNumMgr.countStatistic(statisticDTO);
        } catch (Exception e) {
            return RestfulResults.error("统计异常: " + e.getMessage());
        }
    }
}
