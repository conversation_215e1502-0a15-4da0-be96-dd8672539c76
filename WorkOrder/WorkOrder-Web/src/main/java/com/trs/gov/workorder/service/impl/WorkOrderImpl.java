package com.trs.gov.workorder.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.core.util.OpenApiUtil;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.*;
import com.trs.gov.workorder.VO.*;
import com.trs.gov.workorder.constant.CommonConstant;
import com.trs.gov.workorder.constant.NoticeConstant;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.mgr.BaseActionFactory;
import com.trs.gov.workorder.mgr.IBaseActionMgr;
import com.trs.gov.workorder.mgr.IWorkOrderSaveDecorator;
import com.trs.gov.workorder.mgr.WorkOrderFactory;
import com.trs.gov.workorder.mgr.impl.WorkOrderMgr;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.gov.workorder.utils.AutoReplyUtils;
import com.trs.gov.workorder.utils.TaskUtils;
import com.trs.gov.workorder.utils.external.UnitUtils;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class WorkOrderImpl implements IWorkOrderService {
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private WorkOrderFactory workOrderFactory;
    @Autowired
    private BaseActionFactory baseActionFactory;
    @Autowired
    private WorkOrderMgr workOrderMgr;
    @Autowired
    private HttpServletRequest request;
    @Reference(check = false, timeout = 60000)
    private IUserService userService;
    @Autowired
    private UnitUtils unitUtils;
    @Autowired
    private AutoReplyUtils autoReplyUtils;


    @Override
    public RestfulResults<WorkOrderDetailVO> getWorkOrderById(WorkOrderSearchDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return workOrderMgr.getWorkOrderById(dto);
    }

    @Override
    public RestfulResults<List<WorkOrderVO>> searchWorkOrder(WorkOrderSearchDTO dto) throws ServiceException{
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return workOrderMgr.searchWorkOrder(dto);
    }

    @Override
    public RestfulResults<List<WorkOrderVO>> searchWorkOrderByScope(WorkOrderSearchByScopeDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return workOrderMgr.searchWorkOrderByScope(dto);
    }

    @Override
    public RestfulResults<List<WorkOrderVO>> searchWorkOrderToOverTime(WorkOrderSearchToOverTimeDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return workOrderMgr.searchWorkOrderToOverTime(dto);
    }

    @Override
    public RestfulResults<List<Long>> searchUsedWorkOrderTypeId() {
        return workOrderMgr.searchUsedWorkOrderTypeId();
    }

    @Override
    public RestfulResults<List<Long>> searchUsedSiteId() {
        return workOrderMgr.searchUsedSiteId();
    }

    @Override
    public List<WorkOrderExcel> exportWorkOrder(WorkOrderSearchDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return workOrderMgr.exportWorkOrder(dto);
    }

    @Override
    public RestfulResults<List<NoticeByMonthVO>> searchNotice(NoticeSearchDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        PreConditionCheck.checkArgument(NoticeConstant.MY_RECEIVED.equals(dto.getType()) || NoticeConstant.MY_CREATED.equals(dto.getType()), "类型错误！");
        if (!StringUtils.isEmpty(dto.getCrTime())) {
            String[] dateList = dto.getCrTime().split(",");
            if (dateList.length != 2) {
                throw new ServiceException("发送时间格式有误！");
            }
            dto.setCrTimeStart(TimeUtils.stringToDate(dateList[0], TimeUtils.YYYYMMDD));
            Date date = TimeUtils.stringToDate(dateList[1], TimeUtils.YYYYMMDD);
            dto.setCrTimeEnd(TimeUtils.befOrAft(date, 1, Calendar.DATE));
        }
        dto.setUsername(new WorkOrderDO().getCrUser());
        return workOrderMgr.searchNotice(dto);
    }

    @Override
    public RestfulResults doWorkOrderAction(String oprKey, WorkOrderBaseDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        //获取相应操作实现
        IBaseActionMgr actionMgr = baseActionFactory.getActionMgr(oprKey);
        //权限校验
        try {
            if(!actionMgr.isSupportAction(dto)){
                return RestfulResults.error("没有操作权限！");
            }
        } catch (Exception e){
            log.error("获取操作权限失败", e);
            return RestfulResults.error("获取操作权限失败:"+e);
        }

        //参数校验
        actionMgr.initParameterParse(dto);

        if(CommonConstant.YES.equals(dto.getAsync())){
            TaskUtils.newTask(() -> {
                try {
                    UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
                    doWorkOrderAction(actionMgr, oprKey, dto);
                } catch (ServiceException e) {
                    e.printStackTrace();
                    log.error("【" + oprKey + "】操作失败", e);
                }
            });
            return RestfulResults.ok();
        }else {
            RestfulResults restfulResults = null;
            try {
                restfulResults = actionMgr.startExecuteOneWorkOrder(dto);
            } catch (Exception e){
                e.printStackTrace();
                log.error(actionMgr.desc()+"失败",e);
                return RestfulResults.error(actionMgr.desc()+"失败："+e.getMessage());
            }
            if (!RestfulJsonHelper.STATUS_CODE.OK.getValue().equals(restfulResults.getCode())) {
                return restfulResults;
            }

            TaskUtils.newTask(() -> {
                try {
                    UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
                } catch (ServiceException e) {
                    log.error("获取用户信息失败", e);
                }
                //保存操作记录
                try {
                    actionMgr.recordLogAfterExecute(dto);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("保存【" + oprKey + "】操作记录失败", e);
                }
                actionMgr.sendMessage(dto);
            });

            return restfulResults;
        }
    }

    public RestfulResults doWorkOrderAction(IBaseActionMgr actionMgr, String oprKey, BaseDTO dto) throws ServiceException {
        RestfulResults restfulResults = actionMgr.startExecuteOneWorkOrder(dto);
        if (!RestfulJsonHelper.STATUS_CODE.OK.getValue().equals(restfulResults.getCode())) {
            return restfulResults;
        }
        //保存操作记录
        try {
            actionMgr.recordLogAfterExecute(dto);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("保存【" + oprKey + "】操作记录失败", e);
        }
        //发送消息
        actionMgr.sendMessage(dto);

        return restfulResults;
    }

    @Override
    public RestfulResults saveOrUpdateWorkOrder(WorkOrderDTO workOrderDTO) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderDTO);
        //获取相应类型工单
        IWorkOrderSaveDecorator workOrderSaveDecorator = workOrderFactory.getWorkOrderSave(workOrderDTO.getWorkOrderTopTypeId());
        //权限校验
//        workOrderSaveDecorator.validateRight(userDTO, workOrderDTO);
        //参数校验
        workOrderSaveDecorator.initParameterParse(workOrderDTO);
        if(CommonConstant.YES.equals(workOrderDTO.getAsync())){
            TaskUtils.newTask(() -> {
                try {
                    UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderDTO);
                    saveOrUpdateWorkOrder(workOrderSaveDecorator, workOrderDTO);
                    autoReply(workOrderDTO);
                } catch (ServiceException e) {
                    e.printStackTrace();
                    log.error(workOrderSaveDecorator.desc()+"失败", e);
                }
            });
            return RestfulResults.ok();
        } else {
            //保存
            RestfulResults restfulResults = null;
            try {
                restfulResults = workOrderSaveDecorator.startExecuteOneWorkOrder(workOrderDTO);
            } catch (Exception e){
                e.printStackTrace();
                log.error(workOrderSaveDecorator.desc()+"失败",e);
                return RestfulResults.error(workOrderSaveDecorator.desc()+"失败："+e.getMessage());
            }

            if (!RestfulJsonHelper.STATUS_CODE.OK.getValue().equals(restfulResults.getCode())) {
                return restfulResults;
            }

            //非工作时间自动回复
            log.error("自动回复：{}",workOrderDTO.toString());
            autoReply(workOrderDTO);
            TaskUtils.newTask(() -> {
                try {
                    UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderDTO);
                } catch (ServiceException e) {
                    log.error("获取用户信息失败", e);
                }
                //保存操作记录
                workOrderSaveDecorator.recordLogAfterExecute(workOrderDTO);
                //发送消息
                workOrderSaveDecorator.sendMessage(workOrderDTO);
            });

            return restfulResults;
        }

    }

    /**
     * 晚上17：30到第二天早上8：30期间提工单系统自动回复
     * @param workOrderDTO
     */
    private void autoReply(WorkOrderDTO workOrderDTO) throws ServiceException {
        boolean autoReplyOpen = autoReplyUtils.getReplyOpen();
        if (workOrderDTO.getIsCreate() == true && autoReplyOpen){
            //当前时间
            LocalTime now = LocalTime.now();
            //今日早上8点30分
            LocalTime eight = LocalTime.of(8,30);
            //今日晚上17点30分
            LocalTime seventeen = LocalTime.of(17,30);
           if (now.isBefore(eight) || now.isAfter(seventeen)) {
                WorkOrderActionDTO action = autoReplyUtils.autoReplyGetAction(workOrderDTO);
                doWorkOrderAction(OperateNameConstant.REPLY_WORK_ORDER, action);
           }else if (StringUtils.isNotEmpty(workOrderDTO.getDealUsername()) && autoReplyUtils.getAutoResponsePersonName().contains(workOrderDTO.getDealUsername())){
                WorkOrderActionDTO action = autoReplyUtils.autoReplyByPerson(workOrderDTO);
                doWorkOrderAction(OperateNameConstant.REPLY_WORK_ORDER, action);
           }
        }
    }

    public RestfulResults saveOrUpdateWorkOrder(IWorkOrderSaveDecorator workOrderSaveDecorator, WorkOrderDTO workOrderDTO){
        //保存
        RestfulResults restfulResults = null;
        try {
            restfulResults = workOrderSaveDecorator.startExecuteOneWorkOrder(workOrderDTO);
        } catch (Exception e){
            e.printStackTrace();
            log.error("保存"+workOrderSaveDecorator.desc()+"失败",e);
            return RestfulResults.error("保存"+workOrderSaveDecorator.desc()+"失败"+e.getMessage());
        }

        if (!RestfulJsonHelper.STATUS_CODE.OK.getValue().equals(restfulResults.getCode())) {
            return restfulResults;
        }

        //保存操作记录
        workOrderSaveDecorator.recordLogAfterExecute(workOrderDTO);
        //发送消息
        workOrderSaveDecorator.sendMessage(workOrderDTO);

        return restfulResults;
    }

    @Override
    public WorkOrderVO exportWorkOrder(WorkOrderExportDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        //获取对应类型工单 的 实现 类
        IWorkOrderSaveDecorator workOrderSaveDecorator = workOrderFactory.getWorkOrderSave(dto.getWorkOrderTopTypeId());
        //老工单传过来的参数
        workOrderSaveDecorator.checkParamsValid(dto);
        //保存各个类型的工单
        workOrderSaveDecorator.saveExportWorkOrder(dto);
        WorkOrderVO workOrderVO = new WorkOrderVO();
        BeanUtils.copyProperties(dto,workOrderVO);
        workOrderVO.setId(dto.getWorkOrderId());
        return workOrderVO;
    }

    @Override
    public RestfulResults<List<Long>> findCCtoMeWorkOrderIds(CCToMyDTO ccToMyDTO) throws ServiceException{
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, ccToMyDTO);
        UnitVO loginUnit = unitUtils.getLoginUnit();
        List<Long> workOrderIds = workOrderMgr.queryCCToMe(loginUnit, ccToMyDTO.isUnit(), ccToMyDTO.getWorkOrderStatus());
        return RestfulResults.ok(workOrderIds);
    }

    @Override
    public RestfulResults<List<Long>> findWorkOrderIdsByCCSearch(CCSearchDTO ccSearchDTO) throws ServiceException{
        ccSearchDTO.isValid();
        List<Long> workOrderIdList = workOrderMgr.queryWorkOrderIdsByCCSearch(ccSearchDTO);
        return RestfulResults.ok(workOrderIdList);
    }

    @Override
    public RestfulResults<List<NoticeStatisticsVO>> findNoticeStatistics(WorkOrderSearchDTO dto) throws Exception{
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return RestfulResults.ok(workOrderMgr.getNoticeStatistics(dto.getIdList()));
    }

    @Override
    public RestfulResults limitCreateWorkOrder(WorkOrderSearchDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return workOrderMgr.limitCreateWorkOrder(dto);
    }

    @Override
    public void saveOrUpdateWorkOrderByOpenApi(OpenApiDTO workOrderDTO) throws ServiceException {
        checkOpenApiDTOAndLoadUserInfoByDTO(userService, workOrderDTO);
        //获取相应类型工单
        IWorkOrderSaveDecorator workOrderSaveDecorator = workOrderFactory.getWorkOrderSave(workOrderDTO.getWorkOrderTopTypeId());
        //权限校验
//        workOrderSaveDecorator.validateRight(userDTO, workOrderDTO);
        //参数校验
        workOrderSaveDecorator.initParameterParse(workOrderDTO);
        if(CommonConstant.YES.equals(workOrderDTO.getAsync())){
            TaskUtils.newTask(() -> {
                try {
                    checkOpenApiDTOAndLoadUserInfoByDTO(userService, workOrderDTO);
                    saveOrUpdateWorkOrder(workOrderSaveDecorator, workOrderDTO);
                    autoReply(workOrderDTO);
                } catch (ServiceException e) {
                    e.printStackTrace();
                    log.error(workOrderSaveDecorator.desc()+"失败", e);
                } finally {
                    OpenApiUtil.remove();
                }
            });
            return;
        } else {
            //保存
            RestfulResults restfulResults = null;
            try {
                restfulResults = workOrderSaveDecorator.startExecuteOneWorkOrder(workOrderDTO);
            } catch (Exception e){
                e.printStackTrace();
                log.error(workOrderSaveDecorator.desc()+"失败",e);
                throw new ServiceException(workOrderSaveDecorator.desc()+"失败："+e.getMessage());
            }

            if (!RestfulJsonHelper.STATUS_CODE.OK.getValue().equals(restfulResults.getCode())) {
                return ;
            }

            //非工作时间自动回复
            log.error("自动回复：{}",workOrderDTO.toString());
            autoReply(workOrderDTO);
            TaskUtils.newTask(() -> {
                try {
                    checkOpenApiDTOAndLoadUserInfoByDTO(userService, workOrderDTO);
                } catch (ServiceException e) {
                    log.error("获取用户信息失败", e);
                }
                try {
                    //保存操作记录
                    workOrderSaveDecorator.recordLogAfterExecute(workOrderDTO);
                    //发送消息
                    workOrderSaveDecorator.sendMessage(workOrderDTO);
                } catch (Exception e) {
                    log.error("保存操作记录或发送消息失败", e);
                } finally {
                    OpenApiUtil.remove();
                }
            });

            return ;
        }
    }

    public static void checkOpenApiDTOAndLoadUserInfoByDTO(IUserService service, OpenApiDTO dto) throws ServiceException {
        if (service == null) {
            throw new ParamInvalidException("用户服务不能为空！");
        }

//        String token = ContextHelper.getToken().orElse("");
//
//        ContextHelper.initContext(token, dto.getUserName(), dto.getUnitId());
        OpenApiUtil.addIsOpenApi("true");
    }
}
