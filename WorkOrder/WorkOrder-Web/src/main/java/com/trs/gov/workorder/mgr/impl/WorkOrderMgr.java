package com.trs.gov.workorder.mgr.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.VO.BaseVO;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.management.VO.*;
import com.trs.gov.management.base.enums.MediaType;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.search.service.ISearch;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.NoticeDO;
import com.trs.gov.workorder.DO.NoticeReadDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.*;
import com.trs.gov.workorder.VO.*;
import com.trs.gov.workorder.constant.*;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.dao.NoticeMapper;
import com.trs.gov.workorder.dao.NoticeReadMapper;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.mgr.condition.BaseSearchMgr;
import com.trs.gov.workorder.service.IBaseMgr;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.gov.workorder.utils.WorkOrderActionAccessUtils;
import com.trs.gov.workorder.utils.external.*;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 工单搜索查询等接口
 */
@Component
@Slf4j
public class WorkOrderMgr extends BaseSearchMgr {
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private CCMapper ccMapper;
    @Autowired
    private MessageUtils messageUtils;
    @Autowired
    private OprRecordUtils oprRecordUtils;
    @Autowired
    private WorkTimeMgr workTimeMgr;
    @Autowired
    private NoticeReadMapper noticeReadMapper;
    @Autowired
    private NoticeMapper noticeMapper;
    @Autowired
    private IBaseMgr<WorkOrderDO> baseMgr;
    @Autowired
    private WorkOrderActionAccessUtils workOrderActionAccessUtils;
    @Autowired
    private UnitGroupUtils unitGroupUtils;
    @Autowired
    private WorkOrderTypeUtils workOrderTypeUtils;
    @Autowired
    private SiteRelationUtils siteRelationUtils;
    @Autowired
    private FileManagerUtils fileManagerUtils;

    @Reference(check = false, timeout = 60000)
    private ISearch search;

    /**
     * 构建工单查询条件
     * @param dto
     * @return
     * @throws ServiceException
     */
    public QueryWrapper<WorkOrderDO> buildWorkOrderSearchQueryWrapper(WorkOrderSearchDTO dto) throws ServiceException {
        QueryWrapper<WorkOrderDO> queryWrapper = new QueryWrapper<>();
        //默认排序规则
        queryWrapper.last("ORDER BY " +
                "action_time IS NULL DESC," +
                "IF(expected_end_date is not null AND now()>expected_end_date AND status in(" + WorkOrderConstant.STATUS_WAIT_ASSIGN + "," + WorkOrderConstant.STATUS_DEALING + "), expected_end_date, 1) DESC," +
                "cr_time DESC");

        Consumer<QueryWrapper<WorkOrderDO>> consumer = null;
        try {
            consumer = baseMgr.buildConsumer(dto);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
            log.error("系统错误，查询工单失败", e);
            throw new ServiceException("系统错误，查询工单失败" + e);
        }
        queryWrapper.and(consumer);
        if(dto.getType()==WorkOrderConstant.SREARCH_MY_HOST || dto.getType()==WorkOrderConstant.SREARCH_CHILD_HOST){
            queryWrapper.last("ORDER BY cr_time DESC");
        }

        return queryWrapper;
    }

    /**
     * 查找工单
     *
     * @param dto
     * @return
     * @throws ServiceException
     */
    public RestfulResults<List<WorkOrderVO>> searchWorkOrder(WorkOrderSearchDTO dto) throws ServiceException {
        QueryWrapper<WorkOrderDO> queryWrapper = buildWorkOrderSearchQueryWrapper(dto);
        try {
            Page<WorkOrderDO> page = workOrderMapper.selectPage(new Page(dto.getPageNum(), dto.getPageSize()), queryWrapper);
            List<WorkOrderDO> workOrderDOS = page.getRecords();
            List<WorkOrderVO> workOrderVOS = buildWorkOrderVO(workOrderDOS);
            return RestfulResults.ok(workOrderVOS)
                    .addTotalCount(page.getTotal())
                    .addPageNum((int) page.getCurrent())
                    .addPageSize((int) page.getSize());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询工单失败", e);
            return RestfulResults.error("查询工单失败" + e);
        }
    }

    /**
     * 根据范围查找工单
     *
     * @param dto
     * @return
     * @throws ServiceException
     */
    public RestfulResults<List<WorkOrderVO>> searchWorkOrderByScope(WorkOrderSearchByScopeDTO dto) throws ServiceException {
        String loginUsername = new WorkOrderDO().getCrUser();
        UnitVO loginUnit = unitUtils.getLoginUnit();
        QueryWrapper<WorkOrderDO> queryWrapper = new QueryWrapper<>();
        if (!StringUtils.isEmpty(dto.getStatus())) {
            queryWrapper.in("status", dto.getStatus().split(","));
        }
        List<Long> ccToMeByUnit = queryCCToMe(loginUnit, true);
        List<Long> ccToMeByUser = queryCCToMe(loginUnit, false);
        queryWrapper.and(
                dto.getDealType() != null ||
                        dto.getHostType() != null ||
                        dto.getCrType() != null ||
                        dto.getCcType() != null, m -> {
                    if (dto.getDealType() != null) {
                        m.or(i -> i
                                .and(consumerMyDeal(loginUnit, true))
                                .and(dto.getDealType().equals(AssignConstant.ASSIGN_TO_USER), consumerMyDeal(loginUnit, false)));
                    }
                    if (dto.getHostType() != null) {
                        m.or(i -> i
                                .and(consumerMyHost(loginUnit, true))
                                .and(dto.getHostType().equals(AssignConstant.ASSIGN_TO_USER), consumerMyHost(loginUnit, false)));
                    }
                    if (dto.getCrType() != null) {
                        m.or(i -> i
                                .eq("cr_unit_id", loginUnit.getId())
                                .eq(dto.getCrType().equals(AssignConstant.ASSIGN_TO_USER), "cr_username", loginUsername));
                    }
                    if (dto.getCcType() != null) {
                        m.or(i -> i
                                .in("id", ccToMeByUnit)
                                .in(dto.getCcType().equals(AssignConstant.ASSIGN_TO_USER), "id", ccToMeByUser));
                    }
                });
        queryWrapper.orderBy("expectedEndDate".equals(dto.getOrderBy()), !dto.isDesc(), "expected_end_date");
        queryWrapper.orderByDesc("cr_time");

        try {
            Page<WorkOrderDO> page = workOrderMapper.selectPage(new Page(dto.getPageNum(), dto.getPageSize()), queryWrapper);
            List<WorkOrderDO> workOrderDOS = page.getRecords();
            List<WorkOrderVO> workOrderVOS = buildWorkOrderVO(workOrderDOS);
            return RestfulResults.ok(workOrderVOS)
                    .addTotalCount(page.getTotal())
                    .addPageNum((int) page.getCurrent())
                    .addPageSize((int) page.getSize());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("根据范围查询工单失败", e);
            return RestfulResults.error("查询工单失败" + e);
        }
    }

    /**
     * 根据超时状态查找工单
     *
     * @param dto
     * @return
     * @throws ServiceException
     */
    public RestfulResults<List<WorkOrderVO>> searchWorkOrderToOverTime(WorkOrderSearchToOverTimeDTO dto) throws ServiceException {
        UnitVO loginUnit = unitUtils.getLoginUnit();
        QueryWrapper<WorkOrderDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING);

        if (WorkOrderSearchConstant.SEARCH_DELA_UNIT.equals(dto.getType())) {
            queryWrapper.and(consumerMyDeal(loginUnit, true));
        } else if (WorkOrderSearchConstant.SEARCH_DELA_UNIT_USER.equals(dto.getType())) {
            queryWrapper.and(consumerMyDeal(loginUnit, false));
        }

        queryWrapper.and(dto.getOverTimeType() != null, i -> {
            String[] overTimeTypes = dto.getOverTimeType().split(",");
            for (String overTimeType : overTimeTypes) {
                if (OverTimeConstant.TO_OVERTIME.equals(Integer.parseInt(overTimeType))) {
                    Date toOvertimeDate = TimeUtils.befOrAft(new Date(), dto.getToOverTimeDays(), Calendar.DATE);
                    i.or().and(j -> j
                            .lt("expected_end_date", toOvertimeDate)
                            .gt("expected_end_date", new Date()));
                } else if (OverTimeConstant.ALREADY_OVERTIME.equals(Integer.parseInt(overTimeType))) {
                    i.or().lt("expected_end_date", new Date());
                } else if (OverTimeConstant.NOT_OVERTIME.equals(Integer.parseInt(overTimeType))) {
                    i.or().gt("expected_end_date", new Date());
                }
            }
        });

        try {
            Page<WorkOrderDO> page = workOrderMapper.selectPage(new Page(dto.getPageNum(), dto.getPageSize()), queryWrapper);
            List<WorkOrderDO> workOrderDOS = page.getRecords();
            List<WorkOrderVO> workOrderVOS = buildWorkOrderVO(workOrderDOS);
            return RestfulResults.ok(workOrderVOS)
                    .addTotalCount(page.getTotal())
                    .addPageNum((int) page.getCurrent())
                    .addPageSize((int) page.getSize());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("根据超时状态查找工单失败", e);
            return RestfulResults.error("根据超时状态查找工单失败" + e);
        }
    }

    /**
     * 构建WorkOrderVO
     *
     * @param workOrderDOS
     */
    private List<WorkOrderVO> buildWorkOrderVO(List<WorkOrderDO> workOrderDOS) throws ServiceException {
        List<WorkOrderVO> workOrderVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(workOrderDOS)) {
            return workOrderVOS;
        }
        //单位id列表
        List<Long> unitIds = new ArrayList<>();
        //工单类型列表
        List<Long> workOrderTypeIds = new ArrayList<>();
        //站点id列表
        List<Long> siteIds = new ArrayList<>();
        for (WorkOrderDO workOrderDO : workOrderDOS) {
            workOrderVOS.add(workOrderDOToVO(workOrderDO));
            if (workOrderDO.getWorkOrderTypeId() != null) {
                workOrderTypeIds.add(workOrderDO.getWorkOrderTypeId());
            }
            if (workOrderDO.getCrUnitId() != null) {
                unitIds.add(workOrderDO.getCrUnitId());
            }
            if (workOrderDO.getHostUnitId() != null) {
                unitIds.add(workOrderDO.getHostUnitId());
            }
            if (workOrderDO.getDealUnitId() != null) {
                unitIds.add(workOrderDO.getDealUnitId());
            }
            if (workOrderDO.getSiteId() != null) {
                siteIds.add(workOrderDO.getSiteId());
            }
        }

        Map<Long, UnitVO> unitVOS = unitUtils.findUnit(StringUtils.join(unitIds.toArray(), ","));
        Map<Long, ParentWorkOrderTypeVO> parentTypes = workOrderTypeUtils.findParentWorkOrderTypeById(StringUtils.join(workOrderTypeIds.toArray(), ","));
        Map<Long, SiteRelationVO> sites = siteRelationUtils.findSiteBySiteIdList(StringUtils.join(siteIds.toArray(), ","));
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            Long workOrderTypeId = workOrderVO.getWorkOrderTypeId();
            if (workOrderTypeId != null) {
                ParentWorkOrderTypeVO parent = parentTypes.get(workOrderTypeId);
                if (parent == null) {
                    log.warn("查询父级工单类型列表错误，工单类型【" + workOrderTypeId + "】的父级未找到");
                } else {
                    workOrderVO.setWorkOrderTypeName(parent.getChildName());
                    workOrderVO.setWorkOrderParentTypeId(parent.getId());
                    workOrderVO.setWorkOrderParentTypeName(parent.getTypeName());
                }
            }
            if (workOrderVO.getCrUnitId() != null) {
                UnitVO unitVO = unitVOS.get(workOrderVO.getCrUnitId());
                if (unitVO == null) {
                    log.warn("查询单位列表错误，单位【" + workOrderVO.getCrUnitId() + "】未找到");
                } else {
                    workOrderVO.setCrUnit(unitVO.getUnitName());
                }
            }
            if (workOrderVO.getHostUnitId() != null) {
                UnitVO unitVO = unitVOS.get(workOrderVO.getHostUnitId());
                if (unitVO == null) {
                    log.warn("查询单位列表错误，单位【" + workOrderVO.getHostUnitId() + "】未找到");
                } else {
                    workOrderVO.setHostUnit(unitVO.getUnitName());
                }
            }
            if (workOrderVO.getDealUnitId() != null) {
                UnitVO unitVO = unitVOS.get(workOrderVO.getDealUnitId());
                if (unitVO == null) {
                    log.warn("查询单位列表错误，单位【" + workOrderVO.getDealUnitId() + "】未找到");
                } else {
                    workOrderVO.setDealUnit(unitVO.getUnitName());
                }
            }
            if (workOrderVO.getSiteId() != null) {
                SiteRelationVO siteRelationVO = sites.get(workOrderVO.getSiteId());
                if (siteRelationVO == null) {
                    //站点可能被删除，显示工单保存的站点信息
                    if (workOrderVO.getMediaType() != null) {
                        workOrderVO.setMediaName(MediaType.getMediaType().get(workOrderVO.getMediaType()));
                    }
                } else {
                    workOrderVO.setSitename(siteRelationVO.getSiteName());
                    workOrderVO.setMediaType(siteRelationVO.getMediaType());
                    workOrderVO.setMediaName(siteRelationVO.getMediaName());
                }
            }
        }

        return workOrderVOS;
    }

    /**
     * 将workOrderDO转换成workOrderVO
     *
     * @param dO
     * @return
     * @throws ServiceException
     */
    private WorkOrderVO workOrderDOToVO(WorkOrderDO dO) throws ServiceException {
        WorkOrderVO vo = new WorkOrderVO();
        BeanUtils.copyProperties(dO, vo);
        vo.setSource(dO.getSource());
        try {
            //主办、受理指定到单位的不返回用户名和真实姓名
            if (dO.getHostAssignType().equals(AssignConstant.ASSIGN_TO_UNIT)) {
                vo.setHostUsername(null);
                vo.setHostTruename(null);
            }
            if (dO.getDealAssignType().equals(AssignConstant.ASSIGN_TO_UNIT)) {
                vo.setDealUsername(null);
                vo.setDealTruename(null);
            }
            vo.setStatusName(WorkOrderConstant.statusName.get(vo.getStatus()));
            vo.setReturn(dO.getIsReturn() != null && CommonConstant.YES.equals(dO.getIsReturn()));
            vo.setAction(vo.getActionTime() != null);
            if (vo.getMediaType() != null) {
                vo.setMediaName(MediaType.getMediaType().get(vo.getMediaType()));
            }
        } catch (Exception e) {
            log.error("工单【" + dO.getId() + "】转换异常", e);
        }

        return vo;
    }

    /**
     * 查询所有使用的工单类型
     */
    public RestfulResults<List<Long>> searchUsedWorkOrderTypeId() {
        try {
            List<Long> workOrderTypeIdList = workOrderMapper.queryAllWorkOrderTypeId();
            return RestfulResults.ok(workOrderTypeIdList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询所有使用的工单类型失败", e);
            return RestfulResults.error("查询所有使用的工单类型失败" + e);
        }
    }

    /**
     * 查找正在使用的站点id列表(未评价的工单)
     */
    public RestfulResults<List<Long>> searchUsedSiteId() {
        try {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.in("status", WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING, WorkOrderConstant.STATUS_FINISHED);
            List<WorkOrderDO> workOrderDOS = workOrderMapper.selectList(queryWrapper);
            return RestfulResults.ok(workOrderDOS.stream().filter(item -> item.getSiteId() != null).map(WorkOrderDO::getSiteId).distinct().collect(Collectors.toList()));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查找正在使用的站点id列表失败", e);
            return RestfulResults.error("查找正在使用的站点id列表失败" + e);
        }
    }

    /**
     * 导出工单数据
     *
     * @param dto
     * @throws Exception
     */
    public List<WorkOrderExcel> exportWorkOrder(WorkOrderSearchDTO dto) throws ServiceException {
        QueryWrapper<WorkOrderDO> queryWrapper = buildWorkOrderSearchQueryWrapper(dto);
        List<WorkOrderDO> workOrderDOS = workOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(workOrderDOS)) {
            return new ArrayList<>();
        }
        List<Long> workOrderIdList = new ArrayList<>();
        for (WorkOrderDO workOrderDO : workOrderDOS) {
            workOrderIdList.add(workOrderDO.getId());
        }
        String workOrderIds = StringUtils.join(workOrderIdList.toArray(), ",");
        List<CCVO> ccList = getCcList(workOrderIds);
        WorkTimeQueryDTO workTimeQueryDTO = new WorkTimeQueryDTO();
        workTimeQueryDTO.setWorkOrderId(workOrderIds);
        List<WorkTimeVO> workTimeVOS = workTimeMgr.findWorkTime(workTimeQueryDTO).getDatas();
        Map<Long, Double> workTimeMap = new HashMap<>();
        for (WorkTimeVO workTimeVO : workTimeVOS) {
            Double time = workTimeMap.get(workTimeVO.getWorkOrderId());
            if (time == null) {
                workTimeMap.put(workTimeVO.getWorkOrderId(), workTimeVO.getWorkingTime());
            } else {
                workTimeMap.put(workTimeVO.getWorkOrderId(), time + workTimeVO.getWorkingTime());
            }
        }
        List<WorkOrderVO> workOrderVOS = new ArrayList<>();
        if (StringUtils.isNullOrEmpty(dto.getKeywords())){
             workOrderVOS = buildWorkOrderVO(workOrderDOS);
        }else {
            dto.setPageSize(10000);
            workOrderVOS = (List<WorkOrderVO>) search.listSearchWorkOrder(dto).getDatas();
        }

        List<WorkOrderExcel> workOrderExcels = new ArrayList<>();
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            WorkOrderExcel workOrderExcel = new WorkOrderExcel();
            BeanUtils.copyProperties(workOrderVO, workOrderExcel);
            workOrderExcel.setIdStr(WorkOrderConstant.topTypeName.get(workOrderVO.getWorkOrderTopTypeId()) + "#" + workOrderVO.getId());
            if (!StringUtils.isEmpty(workOrderVO.getWorkOrderTypeName())) {
                workOrderExcel.setWorkOrderType(workOrderVO.getWorkOrderParentTypeName() + "-" + workOrderVO.getWorkOrderTypeName());
            }

            StringBuilder ccStr = new StringBuilder();
            for (CCVO ccvo : ccList) {
                if (ccvo.getWorkOrderId().equals(workOrderVO.getId())) {
                    if(AssignConstant.ASSIGN_TO_ALL.equals(ccvo.getType())){
                        ccStr.append("全部单位" + "、");
                    }else if(AssignConstant.ASSIGN_TO_GROUP.equals(ccvo.getType())){
                        ccStr.append(ccvo.getGroupName() + "、");
                    }else if(AssignConstant.ASSIGN_TO_UNIT.equals(ccvo.getType())){
                        ccStr.append(ccvo.getTargetUnit() + "、");
                    }else if(AssignConstant.ASSIGN_TO_USER.equals(ccvo.getType())){
                        ccStr.append(ccvo.getTargetUnit() + "-" + ccvo.getTargetTruename() + "、");
                    }
                }
            }
            if (ccStr.length() != 0) {
                workOrderExcel.setCcList(ccStr.substring(0, ccStr.length() - 1));
            }

            Double time = workTimeMap.get(workOrderVO.getId());
            if (time != null) {
                //工时为整数时去掉小数点
                if ((int) (time * 10) == time.intValue() * 10) {
                    workOrderExcel.setAllWorkTime(time.intValue() + "人天");
                } else {
                    workOrderExcel.setAllWorkTime(time + "人天");
                }
            }

            workOrderExcels.add(workOrderExcel);
        }
        return workOrderExcels;
    }

    /**
     * 查找工单详细
     *
     * @param dto
     * @return
     * @throws ServiceException
     */
    public RestfulResults<WorkOrderDetailVO> getWorkOrderById(WorkOrderSearchDTO dto) throws ServiceException {
        Long id = dto.getId();
        if (id == null) {
            throw new ServiceException("工单id不能为空");
        }
        WorkOrderDO workOrderDO = workOrderMapper.selectById(id);

        if (workOrderDO == null) {
            return RestfulResults.ok(null).addMsg("工单【" + id + "】不存在");
        }

        WorkOrderDetailVO workOrderDetailVO = new WorkOrderDetailVO();
        try {
            BeanUtils.copyProperties(buildWorkOrderVO(Arrays.asList(workOrderDO)).get(0), workOrderDetailVO);
            workOrderDetailVO.setPicList(fileManagerUtils.getFile(id, FileConstant.WORK_ORDER_FILE_TYPE, FileConstant.PICLIST_FILE_NAME));
            workOrderDetailVO.setFileList(fileManagerUtils.getFile(id, FileConstant.WORK_ORDER_FILE_TYPE, FileConstant.FILELIST_FILE_NAME));
            workOrderDetailVO.setCcList(getCcList(String.valueOf(id)));
            workOrderDetailVO.setNoticeList(getNoticeList(String.valueOf(id)));
            workOrderDetailVO.setActionList(workOrderActionAccessUtils.getOprList(workOrderDO.getId()));

            if (workOrderDO.getWorkOrderTopTypeId().equals(WorkOrderConstant.TOP_TYPE_NOTICE)) {
                //更新阅读状态
                QueryWrapper<NoticeReadDO> queryWrapper = new QueryWrapper<NoticeReadDO>()
                        .eq("work_order_id", workOrderDO.getId())
                        .eq("target_unit_id", dto.getUnitId())
                        .eq("target_username", new WorkOrderDO().getCrUser());
                NoticeReadDO noticeReadDO = noticeReadMapper.selectOne(queryWrapper);
                if(noticeReadDO!=null && noticeReadDO.getStatus().equals(CommonConstant.NO)){
                    noticeReadDO.setStatus(CommonConstant.YES);
                    noticeReadMapper.update(noticeReadDO, queryWrapper);
                }
            } else {
                if (workOrderDO.getStatus().equals(WorkOrderConstant.STATUS_DEALING) &&
                        workOrderDO.getActionTime() == null &&
                        LoginInfoUtils.getLoginUnitId().equals(workOrderDO.getDealUnitId()) &&
                        new WorkOrderDO().getCrUser().equals(workOrderDO.getDealUsername())) {
                    //响应工单
                    workOrderDO.setActionTime(new Date());
                    workOrderDO.setUpdateTime(new Date());
                    workOrderMapper.updateById(workOrderDO);
                    //发送响应消息
                    CreateNoticeDTO createNoticeDTO = messageUtils.buildbaseMessage(workOrderDetailVO.getId());
                    createNoticeDTO.setMessageConfigType(OperateNameConstant.RESPONSE_WORK_ORDER);
                    messageUtils.sendNoticeMessage(log, createNoticeDTO);
                }
            }
            //构建用户信息（ 用户名+联系方式）
            if (StringUtils.isNullOrEmpty(workOrderDetailVO.getCrUsername()) == false){
                workOrderDetailVO.setCrTruename(messageUtils.buildUserInfo(workOrderDetailVO.getCrUsername()));
            }
            if (StringUtils.isNullOrEmpty(workOrderDetailVO.getDealUsername()) == false){
                workOrderDetailVO.setDealTruename(messageUtils.buildUserInfo(workOrderDetailVO.getDealUsername()));
            }
            if (StringUtils.isNullOrEmpty(workOrderDetailVO.getHostUsername()) == false){
                workOrderDetailVO.setHostTruename(messageUtils.buildUserInfo(workOrderDetailVO.getHostUsername()));
            }
            //抄送人 真实姓名后 + 联系方式
            List<CCVO> ccList = workOrderDetailVO.getCcList();
            for (CCVO ccvo: ccList) {
                if (StringUtils.isNullOrEmpty(ccvo.getTargetTruename()) == false){
                    ccvo.setTargetTruename(messageUtils.buildUserInfo(ccvo.getTargetUsername()));
                }
            }
            workOrderDetailVO.setCcList(ccList);
        } catch (Exception e) {
            log.error("工单【" + dto.getId() + "】详细转换错误", e);
        }

        return RestfulResults.ok(workOrderDetailVO);
    }

    /**
     * 获取工单抄送列表
     *
     * @param workOrderIds
     * @return
     */
    private List<CCVO> getCcList(String workOrderIds) throws ServiceException {
        List<CCVO> ccvoList = new ArrayList<>();
        List<CCDO> ccdoList = ccMapper.selectList(new QueryWrapper<CCDO>().in("work_order_id", workOrderIds.split(",")));
        if (!CollectionUtils.isEmpty(ccdoList)) {
            for (CCDO ccdo : ccdoList) {
                CCVO ccvo = new CCVO();
                BeanUtils.copyProperties(ccdo, ccvo);
                //到单位的抄送只保留一条记录
                if (AssignConstant.ASSIGN_TO_UNIT.equals(ccvo.getType())) {
                    if (isContainTargetUnit(ccvoList, ccvo.getWorkOrderId(), ccvo.getTargetUnitId(), ccvo.getType())) {
                        continue;
                    } else {
                        ccvo.setTargetUsername(null);
                        ccvo.setTargetTruename(null);
                    }
                }
                ccvoList.add(ccvo);
            }
        }
        convertTargetVO(ccvoList);

        return ccvoList;
    }

    /**
     * 获取工单通知列表
     *
     * @param workOrderIds
     * @return
     */
    private List<NoticeBaseVO> getNoticeList(String workOrderIds) throws ServiceException {
        List<NoticeBaseVO> noticeBaseVOList = new ArrayList<>();
        if(StringUtils.isEmpty(workOrderIds)){
            return noticeBaseVOList;
        }

        List<NoticeDO> noticeDOList = noticeMapper.selectList(new QueryWrapper<NoticeDO>().in("work_order_id", workOrderIds.split(",")));
        if (!CollectionUtils.isEmpty(noticeDOList)) {
            for (NoticeDO noticeDO : noticeDOList) {
                NoticeBaseVO noticeBaseVO = new NoticeBaseVO();
                BeanUtils.copyProperties(noticeDO, noticeBaseVO);
                //到单位的通知只保留一条记录(兼容老的表结构)
                if (AssignConstant.ASSIGN_TO_UNIT.equals(noticeBaseVO.getType())) {
                    if (isContainTargetUnit(noticeBaseVOList, noticeBaseVO.getWorkOrderId(), noticeBaseVO.getTargetUnitId(), noticeBaseVO.getType())) {
                        continue;
                    } else {
                        noticeBaseVO.setTargetUsername(null);
                        noticeBaseVO.setTargetTruename(null);
                    }
                }
                noticeBaseVOList.add(noticeBaseVO);
            }
        }
        convertTargetVO(noticeBaseVOList);

        return noticeBaseVOList;
    }

    /**
     * 补充分组名称等信息
     * @param targetVOS
     * @return: void
     * @Author: zuo.kaiyuan
     * @Date: 2021/1/7
     */
    public void convertTargetVO(List<? extends TargetVO> targetVOS) throws ServiceException {
        Set<Long> groupIdSet = new HashSet<>();
        targetVOS.forEach(targetVO -> {
            if(targetVO.getGroupId()!=null){
                groupIdSet.add(targetVO.getGroupId());
            }
        });
        Map<Long, UnitGroupVO> unitGroupVOMap= unitGroupUtils.getUnitGroupByIds(groupIdSet);

        targetVOS.forEach(targetVO -> {
            Long groupId = targetVO.getGroupId();
            if(groupId!=null){
                UnitGroupVO unitGroupVO = unitGroupVOMap.get(groupId);
                if(unitGroupVO==null){
                    log.warn("查询分组信息失败，【"+groupId+"】分组未找到");
                }else{
                    targetVO.setGroupName(unitGroupVO.getGroupName());
                }
            }
        });
    }

    /**
     * 通知列表或抄送列表是否包含了目标单位
     *
     * @param baseVOList
     * @param unitId
     * @return
     */
    private boolean isContainTargetUnit(List<? extends BaseVO> baseVOList, Long workOrderId, Long unitId, Integer type) {
        for (BaseVO baseVO : baseVOList) {
            if (baseVO instanceof CCVO) {
                CCVO ccvo = (CCVO) baseVO;
                if (ccvo.getWorkOrderId().equals(workOrderId) && ObjectUtils.nullSafeEquals(ccvo.getTargetUnitId(), unitId) && ccvo.getType().equals(type)) {
                    return true;
                }
            } else if (baseVO instanceof NoticeBaseVO) {
                NoticeBaseVO noticeBaseVO = (NoticeBaseVO) baseVO;
                if (noticeBaseVO.getWorkOrderId().equals(workOrderId) && ObjectUtils.nullSafeEquals(noticeBaseVO.getTargetUnitId(), unitId)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 查找通知
     *
     * @param dto
     * @return
     * @throws ServiceException
     */
    public RestfulResults<List<NoticeByMonthVO>> searchNotice(NoticeSearchDTO dto) throws ServiceException {
        try {
            IPage<NoticeVO> page = workOrderMapper.queryNotice(new Page(dto.getPageNum(), dto.getPageSize()), dto, unitGroupUtils.getLoginGroupIds());
            List<NoticeVO> noticeVOList = page.getRecords();
            if (CollectionUtils.isEmpty(noticeVOList)) {
                return RestfulResults.ok(null)
                        .addPageNum((int) page.getCurrent())
                        .addPageSize((int) page.getSize())
                        .addTotalCount(page.getTotal());
            }

            //保存通知阅读记录
            List<NoticeReadDO> noticeReadDOList = new ArrayList<>();
            for (NoticeVO noticeVO : noticeVOList) {
                if (noticeVO.getStatus() == null && NoticeConstant.MY_RECEIVED.equals(dto.getType())) {
                    noticeVO.setStatus(CommonConstant.NO);
                    NoticeReadDO noticeReadDO = new NoticeReadDO();
                    noticeReadDO.setWorkOrderId(noticeVO.getWorkOrderId());
                    noticeReadDO.setTargetUnitId(Long.valueOf(dto.getUnitId()));
                    noticeReadDO.setTargetUsername(noticeReadDO.getCrUser());
                    noticeReadDO.setStatus(CommonConstant.NO);
                    noticeReadDO.setExportFromOther(CommonConstant.NO);
                    noticeReadDOList.add(noticeReadDO);
                }
            }
            if (!CollectionUtils.isEmpty(noticeReadDOList)) {
                noticeReadMapper.insertList(noticeReadDOList);
            }

            List<Long> workOrderIdList = noticeVOList.stream().map(NoticeVO::getWorkOrderId).collect(Collectors.toList());
            List<NoticeStatisticsVO> noticeReadedList = workOrderMapper.queryNoticeReaded(workOrderIdList);
            Map<Long, Long> noticeUserCountMap = getNoticeUserCount(workOrderIdList);
            for (NoticeVO noticeVO : noticeVOList) {
                noticeVO.setPicList(fileManagerUtils.getFile(noticeVO.getWorkOrderId(), FileConstant.WORK_ORDER_FILE_TYPE, FileConstant.PICLIST_FILE_NAME));
                noticeVO.setFileList(fileManagerUtils.getFile(noticeVO.getWorkOrderId(), FileConstant.WORK_ORDER_FILE_TYPE, FileConstant.FILELIST_FILE_NAME));
                for (NoticeStatisticsVO noticeStatisticsVO : noticeReadedList) {
                    if (noticeStatisticsVO.getWorkOrderId().equals(noticeVO.getWorkOrderId())) {
                        noticeVO.setReadCount(noticeStatisticsVO.getReadCount());
                    }
                }
                try {
                    noticeVO.setReplyCount(oprRecordUtils.getCommentNum(noticeVO.getWorkOrderId(), OperateNameConstant.REPLY_WORK_ORDER));
                } catch (Exception e) {
                    log.error("查找通知回复数量失败", e);
                }
                noticeVO.setTotalCount(noticeUserCountMap.get(noticeVO.getWorkOrderId()));
            }

            List<NoticeByMonthVO> noticeByMonthVOList = noticeVoToMonth(noticeVOList);

            return RestfulResults.ok(noticeByMonthVOList)
                    .addPageNum((int) page.getCurrent())
                    .addPageSize((int) page.getSize())
                    .addTotalCount(page.getTotal());
        } catch (Exception e) {
            log.error("查找通知失败", e);
            return RestfulResults.error("查找通知失败: " + e);
        }
    }

    /**
     * 获取通知的接收人数
     * @param workOrderIdList
     * @return
     * @throws ServiceException
     */
    public Map<Long,Long> getNoticeUserCount(List<Long> workOrderIdList) throws Exception {
        Map<Long,Long> noticeUserCountMap = new HashMap<>();
        if(CollectionUtils.isEmpty(workOrderIdList)){
            return noticeUserCountMap;
        }

        List<NoticeBaseVO> noticeBaseVOList = getNoticeList(StringUtils.join(workOrderIdList.toArray(), ","));
        List<Long> allUnitIdList = noticeBaseVOList.stream()
                .filter(i -> i.getType().equals(AssignConstant.ASSIGN_TO_UNIT))
                .map(NoticeBaseVO::getTargetUnitId)
                .distinct().collect(Collectors.toList());
        String allGroupIds = noticeBaseVOList.stream()
                .filter(i -> i.getType().equals(AssignConstant.ASSIGN_TO_GROUP))
                .map(i -> String.valueOf(i.getGroupId()))
                .distinct().collect(Collectors.joining(","));
        List<UnitGroupRelationVO> unitGroupRelationVOList = unitGroupUtils.getUnitIdsByGroupIds(allGroupIds);
        List<Long> allGroupUnitIdList = new ArrayList<>();
        unitGroupRelationVOList.stream().forEach(i-> allGroupUnitIdList.addAll(i.getUnitIds()));
        allUnitIdList.addAll(allGroupUnitIdList);
        List<UserCountVO> allUnitUserCountList = unitUtils.getUnitUserCountByUnitIds(StringUtils.join(allUnitIdList.toArray(), ","));

        Long allCount = null;
        for (Long workOrderId : workOrderIdList) {
            boolean isAll = noticeBaseVOList.stream().anyMatch(i -> workOrderId.equals(i.getWorkOrderId()) && AssignConstant.ASSIGN_TO_ALL.equals(i.getType()));
            if(isAll){
                allCount = allCount != null ? allCount : unitUtils.getAllUserCount();
                noticeUserCountMap.put(Long.valueOf(workOrderId), allCount);
            } else{
                List<Long> groupIds = noticeBaseVOList.stream()
                        .filter(i -> i.getWorkOrderId().equals(workOrderId) && i.getType().equals(AssignConstant.ASSIGN_TO_GROUP))
                        .map(NoticeBaseVO::getGroupId)
                        .distinct().collect(Collectors.toList());
                List<Long> groupUnitIdList = new ArrayList<>();
                unitGroupRelationVOList.stream()
                        .filter(i -> groupIds.contains(i.getGroupId()))
                        .forEach(i-> groupUnitIdList.addAll(i.getUnitIds()));
                List<Long> unitIdList = noticeBaseVOList.stream()
                        .filter(i -> i.getWorkOrderId().equals(workOrderId) && i.getType().equals(AssignConstant.ASSIGN_TO_UNIT))
                        .map(NoticeBaseVO::getTargetUnitId)
                        .distinct().collect(Collectors.toList());
                unitIdList.addAll(groupUnitIdList);
                unitIdList = unitIdList.stream().distinct().collect(Collectors.toList());
                List<Long> finalUnitIdList = unitIdList;
                long toUnitCount = allUnitUserCountList.stream()
                        .filter(i -> finalUnitIdList.contains(i.getUnitId()))
                        .mapToLong(i-> Long.parseLong(String.valueOf(i.getUserCount())))
                        .sum();
                long toUserCount = noticeBaseVOList.stream()
                        .filter(i -> i.getWorkOrderId().equals(workOrderId) && i.getType().equals(AssignConstant.ASSIGN_TO_USER))
                        .count();
                noticeUserCountMap.put(Long.valueOf(workOrderId), toUnitCount + toUserCount);
            }
        }

        return noticeUserCountMap;
    }

    /**
     * 将通知列表根据月份分组
     *
     * @param noticeVOList
     * @return
     */
    private List<NoticeByMonthVO> noticeVoToMonth(List<NoticeVO> noticeVOList) {
        List<NoticeByMonthVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(noticeVOList)) {
            int i = -1;
            for (NoticeVO noticeVO : noticeVOList) {
                String timeKey = TimeUtils.dateToString(noticeVO.getCrTime(), "yyyy-MM");
                if (list.isEmpty() || !list.get(i).getTimeKey().equals(timeKey)) {
                    NoticeByMonthVO noticeByMonthVO = new NoticeByMonthVO();
                    noticeByMonthVO.setTimeKey(timeKey);
                    noticeByMonthVO.getList().add(noticeVO);
                    list.add(noticeByMonthVO);
                    i++;
                } else {
                    list.get(i).getList().add(noticeVO);
                }
            }
        }

        return list;
    }

    /**
     * 获取通知的接收人数,阅读数
     * @param workOrderIds
     * @return
     * @throws ServiceException
     */
    public List<NoticeStatisticsVO> getNoticeStatistics(String workOrderIds) throws Exception{
        List<NoticeStatisticsVO> noticeStatisticsVOList = new ArrayList<>();
        if(StringUtils.isEmpty(workOrderIds)){
            return noticeStatisticsVOList;
        }

        List<Long> workOrderIdList = Arrays.stream(workOrderIds.split(",")).map(i -> Long.parseLong(i)).collect(Collectors.toList());
        String loginUnitId = ContextHelper.getLoginUnitId().orElse("");
        String loginUsername = ContextHelper.getLoginUser().orElse("");
        QueryWrapper<NoticeReadDO> queryWrapper = new QueryWrapper<NoticeReadDO>()
                .in("work_order_id", workOrderIdList)
                .eq("target_unit_id", loginUnitId)
                .eq("target_username", loginUsername);
        List<NoticeReadDO> myNoticeReadList = noticeReadMapper.selectList(queryWrapper);
        List<NoticeStatisticsVO> noticeReadedList = workOrderMapper.queryNoticeReaded(workOrderIdList);
        Map<Long, Long> noticeUserCountMap = getNoticeUserCount(workOrderIdList);
        for (Long workOrderId : workOrderIdList) {
            NoticeStatisticsVO noticeStatisticsVO = new NoticeStatisticsVO();
            noticeStatisticsVO.setWorkOrderId(workOrderId);
            for (NoticeStatisticsVO noticeRead : noticeReadedList) {
                if(noticeRead.getWorkOrderId().equals(workOrderId)){
                    noticeStatisticsVO.setReadCount(noticeRead.getReadCount());
                }
            }
            noticeStatisticsVO.setTotalCount(noticeUserCountMap.get(noticeStatisticsVO.getWorkOrderId()));

            for (NoticeReadDO myNoticeRead : myNoticeReadList) {
                if(myNoticeRead.getWorkOrderId().equals(workOrderId)){
                    noticeStatisticsVO.setStatus(myNoticeRead.getStatus());
                }
            }

            //保存通知阅读记录
            List<NoticeReadDO> noticeReadDOList = new ArrayList<>();
            if(noticeStatisticsVO.getStatus()==null){
                noticeStatisticsVO.setStatus(CommonConstant.NO);
                NoticeReadDO noticeReadDO = new NoticeReadDO();
                noticeReadDO.setWorkOrderId(workOrderId);
                noticeReadDO.setTargetUnitId(Long.valueOf(loginUnitId));
                noticeReadDO.setTargetUsername(loginUsername);
                noticeReadDO.setStatus(CommonConstant.NO);
                noticeReadDO.setExportFromOther(CommonConstant.NO);
                noticeReadDOList.add(noticeReadDO);
            }
            if (!CollectionUtils.isEmpty(noticeReadDOList)) {
                noticeReadMapper.insertList(noticeReadDOList);
            }

            noticeStatisticsVOList.add(noticeStatisticsVO);
        }
        return noticeStatisticsVOList;
    }

    public RestfulResults limitCreateWorkOrder(WorkOrderSearchDTO dto) throws ServiceException {
        dto.setType(3);
        QueryWrapper<WorkOrderDO> queryWrapper = buildWorkOrderSearchQueryWrapper(dto);
        try {
            Page<WorkOrderDO> page = workOrderMapper.selectPage(new Page(dto.getPageNum(), dto.getPageSize()), queryWrapper);
            List<WorkOrderDO> workOrderDOS = page.getRecords();
            int workOrderNumber = 0;
            workOrderNumber=workOrderDOS.size();
            Map resultMap = new HashMap();

            StringBuilder workOrderIds= new StringBuilder();
            if (workOrderNumber >0){
                for (WorkOrderDO vo: workOrderDOS) {
                    workOrderIds.append(vo.getId()).append(",");
                }
                resultMap.put("limit",false);
                resultMap.put("detail","工单："+workOrderIds.toString()+"已完成，您还未评价，请您先评价");
            }else {
                resultMap.put("limit",true);
                resultMap.put("detail","没有未评价工单，可以创建工单");
            }
            return RestfulResults.ok(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询工单失败", e);
            return RestfulResults.error("查询工单失败" + e);
        }
    }
}
