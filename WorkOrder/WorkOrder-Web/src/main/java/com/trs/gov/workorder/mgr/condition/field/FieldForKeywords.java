package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.TimeUtils;
import com.trs.common.utils.expression.Expression;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.WorkOrderTypeConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import io.vavr.control.Try;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.trs.common.utils.expression.ExpressionBuilder.Condition;
import static com.trs.common.utils.expression.ExpressionBuilder.Or;

/**
 * @ClassName：FieldIdList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 14:51
 **/
@Component
public class FieldForKeywords extends BaseCommonFieldMgr<WorkOrderDO> {

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_KEY_WORDS;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_KEY_WORDS;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_FIELD_WORK_ORDER_SITE_NAME;
    }

    /**
     * @Description  获取数据中的数字
     * @Param [content]
     * @return java.util.List<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/28 17:14
     **/
    public List<String> listNumber(String content){
        Pattern p = Pattern.compile("-?\\d+");
        Matcher m = p.matcher(content);
        List<String> numbers = new ArrayList<>();
        while (m.find()) {
            numbers.add(m.group());
        }
        return numbers;
    }

    /**
     * @Description  检验是要查id还是内容中包含的数字
     * @Param [keyWord]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/28 17:33
     **/
    public boolean checkKeyWords(String keyWord){
        Try tryResult = Try.of(()->{return Long.valueOf(keyWord);});
        if(keyWord.contains("#") || tryResult.isSuccess()){
            return true;
        }else {
            return false;
        }
    }

    /**
     * @Description  检查keyWord中是否包含顶级类型
     * @Param [keyword]
     * @return java.util.List<java.lang.String>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 16:21
     **/
    public List<String> listKeyWord(String keyword){
        List<String> keywordList = new ArrayList<>();
        if(keyword.contains(WorkOrderTypeConstant.ROOT_GONG_DAN_KEY_DESC)){
            keywordList.add((WorkOrderTypeConstant.ROOT_GONG_DAN_KEY_DESC));
        }
        if(keyword.contains(WorkOrderTypeConstant.ROOT_TOU_SU_KEY_DESC)){
            keywordList.add(WorkOrderTypeConstant.ROOT_TOU_SU_KEY_DESC);
        }
        if(keyword.contains(WorkOrderTypeConstant.ROOT_TONG_ZHI_KEY_DESC)){
            keywordList.add(WorkOrderTypeConstant.ROOT_TONG_ZHI_KEY_DESC);
        }
        return keywordList;
    }

    /*@Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        String zone = (String) keyWords;
        if(!CMyString.isEmpty(zone)){
            return Optional.ofNullable(m -> m.like(searchField(),zone));
        }
        return Optional.empty();
    }*/

    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        String zone = (String) keyWords;
        if(!CMyString.isEmpty(zone)){
            String keyWord = zone.trim();
            List<String> strings = listNumber(keyWord);
            if(strings != null && strings.size() > 0 && checkKeyWords(keyWord)){
                //工单ID
                return Optional.ofNullable(s ->s.in(WorkOrderSearchDtoFieldContants.DB_FIELD_WORK_ORDER__ID,strings.toArray()));
            }else {
                List<String> listKeyWord = listKeyWord(key());
                if(listKeyWord!=null && listKeyWord.size() > 0){
                    //工单类型名称
                    return Optional.ofNullable(s ->s.in(WorkOrderSearchDtoFieldContants.DB_FIELD_WORK_ORDER_TOP_TYPE_NAME,listKeyWord.toArray()));
                }
                //工单内容
                return  Optional.ofNullable(s ->s.like(WorkOrderSearchDtoFieldContants.DB_FIELD_WORK_ORDER_CONTENT,keyWord));
            }
        }
        return Optional.empty();
    }
}
