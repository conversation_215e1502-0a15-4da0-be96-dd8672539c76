package com.trs.gov.workorder.mgr.impl;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.mgr.AbstractActionMgr;
import com.trs.gov.workorder.utils.WorkOrderRoleUtils;
import com.trs.web.builder.base.RestfulResults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 工单-评价完所有单位后，更新工单状态为已评价
 */
@Component
public class AppraiseActionMgr extends AbstractActionMgr {
    @Autowired
    private WorkOrderRoleUtils workOrderRoleUtils;

    @Override
    public String key() {
        return OperateNameConstant.APPRAISE_WORK_ORDER;
    }

    @Override
    public String desc() {
        return OperateNameConstant.getTypeDesc(OperateNameConstant.APPRAISE_WORK_ORDER).get();
    }

    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO) {
        if (supportActionDTO.getWorkOrderDO().getWorkOrderTopTypeId().equals(WorkOrderConstant.TOP_TYPE_COMPLAINT)) {
            return false;
        }
        return (super.isSupportAction(supportActionDTO) ||
                workOrderRoleUtils.isWorkOrderRole(supportActionDTO,
                        WorkOrderRoleConstant.CR_UNIT,
                        WorkOrderRoleConstant.CR_USER,
                        WorkOrderRoleConstant.HOST_UNIT_MASTER,
                        WorkOrderRoleConstant.HOST_USER)) &&
                supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_FINISHED;
    }

    @Override
    public void initParameterParse(BaseDTO dto) throws ServiceException {
        dto.isValid();
    }

    @Override
    public RestfulResults startExecuteOneWorkOrder(BaseDTO baseDTO) throws ServiceException {
        WorkOrderDO workOrderDO = commonConvert(baseDTO);
        workOrderDO.setStatus(WorkOrderConstant.STATUS_REVIEWED);

        return commonDoAction(workOrderDO);
    }

    @Override
    public void recordLogAfterExecute(BaseDTO dto) {
    }

    @Override
    public void sendMessage(BaseDTO baseDTO) {
    }
}
