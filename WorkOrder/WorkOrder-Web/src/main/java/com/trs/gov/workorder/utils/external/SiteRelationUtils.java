package com.trs.gov.workorder.utils.external;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DTO.SiteRelationSearchDTO;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.management.service.SiteRelationService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/7
 */
@Component
@Slf4j
public class SiteRelationUtils extends BaseExternalUtils{
    @Reference(check = false, timeout = 60000)
    private SiteRelationService siteRelationService;

    /**
     * 根据站点id获取站点
     *
     * @param siteId
     * @return
     * @throws ServiceException
     */
    public SiteRelationVO findSiteBySiteId(Long siteId) throws ServiceException {
        if (siteId == null) {
            throw new ServiceException("站点id不能为空: ");
        }

        SiteRelationSearchDTO dto = new SiteRelationSearchDTO();
        dto.setRpcTag(true);
        dto.setSiteId(String.valueOf(siteId));
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<SiteRelationVO>> restfulResults = siteRelationService.querySiteRelationList(dto);
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
        if (CollectionUtils.isEmpty(restfulResults.getDatas())) {
            throw new ServiceException("站点【" + siteId + "】未找到");
        }

        return restfulResults.getDatas().get(0);
    }

    /**
     * 根据站点id获取站点
     *
     * @param siteIdList
     * @return
     * @throws ServiceException
     */
    public Map<Long, SiteRelationVO> findSiteBySiteIdList(String siteIdList) throws ServiceException {
        Map<Long, SiteRelationVO> siteRelationVOS= new HashMap<>();
        if (StringUtils.isEmpty(siteIdList)) {
            return siteRelationVOS;
        }

        SiteRelationSearchDTO dto = new SiteRelationSearchDTO();
        dto.setRpcTag(true);
        dto.setSiteId(siteIdList);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<SiteRelationVO>> restfulResults = siteRelationService.querySiteRelationList(dto);
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }

        if(!CollectionUtils.isEmpty(restfulResults.getDatas())){
            for (SiteRelationVO siteRelationVO : restfulResults.getDatas()) {
                siteRelationVOS.put(siteRelationVO.getSiteId(),siteRelationVO);
            }
        }

        return siteRelationVOS;
    }
}
