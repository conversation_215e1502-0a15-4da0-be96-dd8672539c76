package com.trs.gov.workorder.mgr;

import com.alibaba.fastjson.JSON;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.SignUtil;
import com.trs.gov.workorder.DTO.OpenApiDTO;
import com.trs.gov.workorder.VO.AppInfoVO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2025-08-07 17:50
 */
@Component
public class OpenApiMgr {

    private Map<String, AppInfoVO> appInfoMap;

    @Value("${workOrder.openapi.appinfos:[]}")
    private String appInfos;

    public boolean checkAuth(HttpServletRequest request) throws ServiceException {
        String appId = request.getHeader("appId");
        String sign = request.getHeader("sign");
        String timestamp = request.getHeader("timestamp");
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(sign) || StringUtils.isEmpty(timestamp)) {
            throw new ServiceException("必传参数为空！");
        }
        // 传入的时间戳为秒
        long time = System.currentTimeMillis() - Long.parseLong(timestamp);
        if (time > WorkOrderConstant.TIME_LIMIT) {
            throw new ServiceException("此次请求超过接口有效期！");
        }
        Optional<AppInfoVO> vo = getAppInfo(appId);
        if (!vo.isPresent()) {
            throw new ServiceException("根据appId获取配置失败！");
        }
        return SignUtil.check(appId, vo.get().getAppKey(), timestamp, sign);
    }

    public Optional<AppInfoVO> getAppInfo(String appId) {
        if (CollectionUtils.isEmpty(appInfoMap)) {
            List<AppInfoVO> list = JSON.parseArray(appInfos, AppInfoVO.class);
            appInfoMap = list.stream().collect(Collectors.toMap(AppInfoVO::getAppId, app -> app));
        }
        return Optional.ofNullable(appInfoMap.get(appId));
    }


}
