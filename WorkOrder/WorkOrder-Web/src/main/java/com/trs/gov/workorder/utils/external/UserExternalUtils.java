package com.trs.gov.workorder.utils.external;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.user.DTO.UserDTO;
import com.trs.user.DTO.UserSearchDTO;
import com.trs.user.VO.UserVO;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/1/7
 */
@Component
@Slf4j
public class UserExternalUtils extends BaseExternalUtils {
    /**
     * 是否是管理员
     * @return
     * @throws ServiceException
     */
    public boolean isAdmin() {
        return Try.of(() -> userService.userIsAdmin(UserDTO.of(LoginInfoUtils.getLoginUser()))).getOrElse(false);
    }

    /**
     * 是管理员且选择了登录单位
     * @return
     */
    public boolean isAdminAndHasLoginUnit() {
        String loginUnitIdstr = ContextHelper.getLoginUnitId().orElse("0");
        return isAdmin() && !loginUnitIdstr.equals("0");
    }

    /**
     * 根据用户名获取真实姓名
     *
     * @param userName
     * @return
     * @throws ServiceException
     */
    public String getTrueNameByUserName(String userName) throws ServiceException {
        if (userName == null) {
            return null;
        }
        return findUserByUserName(userName).getTrueName();
    }

    /**
     * 根据用户名获取用户
     *
     * @param userName
     * @return
     * @throws ServiceException
     */
    public UserVO findUserByUserName(String userName) throws ServiceException {
        if (StringUtils.isEmpty(userName)) {
            throw new ServiceException("用户名不能为空：" + userName);
        }
        UserDTO userDTO = new UserDTO();
        userDTO.setUserName(userName);
        UserVO userVO = userService.getBaseUserInfoByUserName(userDTO);
        if (userVO == null) {
            throw new ServiceException("用户名未找到：" + userName);
        }
        return userVO;
    }

    public Map<String, UserVO> findUserListByUserName(Set<String> userNameSet) throws ServiceException{
        String userNames = StringUtils.join(userNameSet.toArray(), ",");
        return findUserListByUserName(userNames);
    }

    /**
     * 根据用户名列表获取用户列表
     *
     * @param userNames
     * @return
     * @throws ServiceException
     */
    public Map<String, UserVO> findUserListByUserName(String userNames) throws ServiceException {
        Map<String, UserVO> userVOMap = new HashMap<>();
        if (StringUtils.isEmpty(userNames)) {
            return userVOMap;
        }
        UserSearchDTO userSearchDTO = new UserSearchDTO();
        int pageSize = 1000;
        userSearchDTO.setPageSize(pageSize);
        String[] userNameArray = userNames.split(",");
        int length = userNameArray.length;
        for (int i = 0; i < length; i+=pageSize) {
            int endIndex = i+pageSize > length ? length : i+pageSize;
            String userNameStr = org.apache.commons.lang3.StringUtils.join(userNameArray, ",", i, endIndex);
            userSearchDTO.setUserName(userNameStr);
            RestfulResults<List<UserVO>> restfulResults = userService.getAllUser(userSearchDTO);
            checkRestfulResults(restfulResults);
            for (UserVO userVO : restfulResults.getDatas()) {
                userVOMap.put(userVO.getUserName(), userVO);
            }
        }

        return userVOMap;
    }
}
