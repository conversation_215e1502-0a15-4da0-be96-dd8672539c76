package com.trs.gov.workorder.mgr.impl;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.mgr.AbstractActionMgr;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 工单-回复
 */
@Component
@Slf4j
public class ReplyActionMgr extends AbstractActionMgr {
    @Override
    public String key() {
        return OperateNameConstant.REPLY_WORK_ORDER;
    }

    @Override
    public String desc() {
        return OperateNameConstant.getTypeDesc(OperateNameConstant.REPLY_WORK_ORDER).get();
    }

    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO) {
        return true;
    }

    @Override
    public void initParameterParse(BaseDTO baseDTO) throws ServiceException {
        baseDTO.isValid();
    }

    @Override
    public RestfulResults startExecuteOneWorkOrder(BaseDTO baseDTO) throws ServiceException {
        try {
            super.recordLogAfterExecute(baseDTO);
        } catch (Exception e){
            e.printStackTrace();
            return RestfulResults.error("回复失败"+e.getMessage());
        }

        return RestfulResults.ok().addMsg("回复成功");
    }

    @Override
    public void endExecuteOneWorkOrder(BaseDTO dto, WorkOrderVO workOrder) throws ServiceException {

    }

    @Override
    public void recordLogAfterExecute(BaseDTO baseDTO) throws ServiceException{

    }
}
