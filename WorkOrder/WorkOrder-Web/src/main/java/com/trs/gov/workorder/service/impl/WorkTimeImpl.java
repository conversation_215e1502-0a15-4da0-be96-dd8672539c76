package com.trs.gov.workorder.service.impl;

import com.trs.common.base.Report;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.interaction.DTO.OprRecordDTO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.workorder.DTO.WorkTimeDTO;
import com.trs.gov.workorder.DTO.WorkTimeExportDTO;
import com.trs.gov.workorder.DTO.WorkTimeQueryDTO;
import com.trs.gov.workorder.VO.WorkTimeVO;
import com.trs.gov.workorder.mgr.impl.WorkTimeMgr;
import com.trs.gov.workorder.service.IWorkTimeService;
import com.trs.gov.workorder.utils.WorkTimeAccessUtils;
import com.trs.gov.workorder.utils.external.MessageUtils;
import com.trs.gov.workorder.utils.external.OprRecordUtils;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
@Slf4j
public class WorkTimeImpl implements IWorkTimeService {
    @Autowired
    private WorkTimeMgr workTimeMgr;
    @Reference(check = false, timeout = 60000)
    private IUserService userService;
    @Autowired
    private OprRecordUtils oprRecordUtils;
    @Autowired
    private MessageUtils messageUtils;
    @Autowired
    private WorkTimeAccessUtils workTimeAccessUtils;

    @Override
    public RestfulResults<Report> saveOrUpdateWorkTime(WorkTimeDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);

        boolean access =false;
        if (dto.getWorkTimeId() == null) {
            access = workTimeAccessUtils.workTimeAccess(OperateNameConstant.MAN_HOUR_WORK_ORDER, dto.getWorkOrderId(), null);
        } else {
            access = workTimeAccessUtils.workTimeAccess(OperateNameConstant.UPDATE_MANHOUR, null, dto.getWorkTimeId());
        }
        if(!access){
            return RestfulResults.error("没有权限");
        }

        RestfulResults restfulResults = workTimeMgr.saveOrUpdateWorkTime(dto);

        if (RestfulJsonHelper.STATUS_CODE.OK.getValue().equals(restfulResults.getCode())) {
            if (dto.getWorkTimeId() == null) {
                saveOprRecord(OperateNameConstant.MAN_HOUR_WORK_ORDER, dto.getWorkOrderId());
                sendMessage(OperateNameConstant.MAN_HOUR_WORK_ORDER, dto.getWorkOrderId(), String.valueOf(dto.getWorkingTime()));
            }
            else {
                saveOprRecord(OperateNameConstant.UPDATE_MANHOUR, dto.getWorkOrderId());
            }
        }

        return restfulResults;
    }

    @Override
    public RestfulResults<Report> deleteWorkTime(WorkTimeDTO dto) throws ServiceException {
        if(dto.getWorkTimeId() == null){
            return RestfulResults.error("工时id不能为空");
        }
        if(!workTimeAccessUtils.workTimeAccess(OperateNameConstant.DELETE_MANHOUR, null, dto.getWorkTimeId())){
            return RestfulResults.error("没有权限");
        }

        RestfulResults restfulResults = workTimeMgr.deleteWorkTime(dto);

        if (RestfulJsonHelper.STATUS_CODE.OK.getValue().equals(restfulResults.getCode())) {
            saveOprRecord(OperateNameConstant.DELETE_MANHOUR, dto.getWorkOrderId());
        }

        return restfulResults;
    }

    @Override
    public RestfulResults<List<WorkTimeVO>> queryWorkTime(WorkTimeQueryDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);

        return workTimeMgr.findWorkTime(dto);
    }

    /**
     * 保存操作记录
     * @param oprKey
     * @param workOrderId
     */
    private void saveOprRecord(String oprKey, Long workOrderId){
        try {
            OprRecordDTO oprRecordDTO = oprRecordUtils.buildBaseOprRecord();
            oprRecordDTO.setOprKey(oprKey);
            oprRecordDTO.setWorkOrderId(workOrderId);
            oprRecordUtils.saveOprRecord(log, oprRecordDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("保存操作记录【"+oprKey+"】失败",e);
        }
    }

    /**
     * 发送消息
     * @param oprKey
     * @param workOrderId
     * @param workTime
     */
    private void sendMessage(String oprKey, Long workOrderId, String workTime){
        try {
            CreateNoticeDTO createNoticeDTO = messageUtils.buildbaseMessage(workOrderId);
            createNoticeDTO.setMessageConfigType(oprKey);
            if(workTime!=null){
                createNoticeDTO.setDayTime(workTime);
            }
            messageUtils.sendNoticeMessage(log, createNoticeDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送【"+oprKey+"】消息失败",e);
        }
    }

    @Override
    public WorkTimeVO exportWorkTime(WorkTimeExportDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        dto.isValid();
        return workTimeMgr.exportWorkTime(dto).orElseThrow(()->new ServiceException("同步工时记录【"+dto.getOldWorkTimeId()+"】失败!"));
    }
}
