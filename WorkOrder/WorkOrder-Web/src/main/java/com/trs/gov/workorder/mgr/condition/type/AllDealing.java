package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：AllDealing
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:50
 **/
@Component
public class AllDealing extends WorkOrderSearchTypeMgr {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        UnitVO loginUnit = unitUtils.getLoginUnit();

        List<Long> ccToMeWorkOrderIdList = queryCCToMe(loginUnit, true);
        Consumer<QueryWrapper<WorkOrderDO>> consumer2 = m -> m.and(i -> i
                        .and(consumerMyDeal(loginUnit, true))
                        .or().in("id", ccToMeWorkOrderIdList));

        Consumer<QueryWrapper<WorkOrderDO>> consumer3 = queryByMyUnit();
        Consumer<QueryWrapper<WorkOrderDO>> consumer4 = m -> m.in("status", WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING);

        Consumer<QueryWrapper<WorkOrderDO>> consumer = m -> m
                .and(i -> i
                        .and(consumer2)
                        .or(consumer3))
                .and(consumer4);

        return Optional.ofNullable(consumer);
    }

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL_DEALING);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL_DEALING);
    }
}
