package com.trs.gov.workorder.controller;

import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ExcelUtil;
import com.trs.gov.workorder.DTO.*;
import com.trs.gov.workorder.VO.WorkOrderExcel;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController()
@RequestMapping("/workorder/workorder")
public class WorkOrderController {
    @Autowired
    private IWorkOrderService workOrderService;

    @PostMapping("saveWorkOrder")
    @ApiOperation(value = "保存工单")
    public RestfulResults saveWorkOrder(WorkOrderDTO workOrderDTO) throws ServiceException {
        return workOrderService.saveOrUpdateWorkOrder(workOrderDTO);
    }

    @PostMapping("cc")
    @ApiOperation(value = "抄送工单")
    public RestfulResults saveWorkOrder(CCListDTO ccListDTO) throws ServiceException {
        return workOrderService.doWorkOrderAction(OperateNameConstant.COPY_WORK_ORDER, ccListDTO);
    }

    @GetMapping("findWorkOrder")
    @ApiOperation(value = "查找工单")
    public RestfulResults findWorkOrder(WorkOrderSearchDTO dto) throws ServiceException {
        return workOrderService.searchWorkOrder(dto);
    }

    @GetMapping("findWorkOrderByScope")
    @ApiOperation(value = "我的工作信息")
    public RestfulResults findWorkOrderByScope(WorkOrderSearchByScopeDTO dto) throws ServiceException {
        return workOrderService.searchWorkOrderByScope(dto);
    }

    @GetMapping("findWorkOrderToOverTime")
    @ApiOperation(value = "查看超时工单")
    public RestfulResults findWorkOrderByScope(WorkOrderSearchToOverTimeDTO dto) throws ServiceException {
        return workOrderService.searchWorkOrderToOverTime(dto);
    }

    @GetMapping("findWorkOrderDetail")
    @ApiOperation(value = "查看工单详情")
    public RestfulResults findWorkOrderDetail(WorkOrderSearchDTO dto) throws ServiceException {
        return workOrderService.getWorkOrderById(dto);
    }

    @GetMapping("findNotice")
    @ApiOperation(value = "查找通知")
    public RestfulResults findWorkOrder(NoticeSearchDTO dto) throws ServiceException {
        return workOrderService.searchNotice(dto);
    }

    @PostMapping("doAction")
    @ApiOperation(value = "工单操作(交办、回退、解决、重新打开、公开、取消公开)")
    public RestfulResults doAction(WorkOrderActionDTO dto) throws ServiceException {
        return workOrderService.doWorkOrderAction(dto.getOprKey(), dto);
    }

    @GetMapping("export")
    @ApiOperation(value = "导出工单数据")
    public void export(HttpServletResponse response, WorkOrderSearchDTO dto) throws ServiceException {
        List<WorkOrderExcel> workOrderExcels = workOrderService.exportWorkOrder(dto);
        ExcelUtil.exportExcel(response, workOrderExcels, WorkOrderConstant.EXCEL_NAME, WorkOrderConstant.EXCEL_NAME);
    }

    @GetMapping("limitCreateWorkOrder")
    @ApiOperation(value = "是否有权限创建新的工单（如果当前人员有待评价的工单就不能创建新的工单）")
    public RestfulResults limitCreateWorkOrder(WorkOrderSearchDTO dto) throws ServiceException {
        return workOrderService.limitCreateWorkOrder(dto);
    }
}
