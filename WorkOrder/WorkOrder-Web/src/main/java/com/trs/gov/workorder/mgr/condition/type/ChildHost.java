package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;


/**
 * @ClassName：ChildHost
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:36
 **/
@Component
public class ChildHost extends WorkOrderSearchTypeMgr {
    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_CHILD_HOST);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_CHILD_HOST);
    }

    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object type, T dto) throws ServiceException {
        UnitVO loginUnit = unitUtils.getLoginUnit();
        List<Long> childrenUnitIds = unitUtils.findChildrenUnitIdsByUnitId(loginUnit.getId());
        if (CollectionUtils.isEmpty(childrenUnitIds)) {
            return Optional.ofNullable(m -> m.eq("id", -1));
        }
        return Optional.ofNullable(m -> m
                .in("host_unit_id", childrenUnitIds)
                .in("status", WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING, WorkOrderConstant.STATUS_FINISHED));
    }
}
