package com.trs.gov.workorder.mgr;

import com.trs.common.base.Report;
import com.trs.common.base.Reports;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.interaction.DTO.OprRecordDTO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.DTO.WorkOrderActionDTO;
import com.trs.gov.workorder.DTO.WorkOrderBaseDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.gov.workorder.utils.WorkOrderActionAccessUtils;
import com.trs.gov.workorder.utils.WorkOrderRoleUtils;
import com.trs.gov.workorder.utils.external.MessageUtils;
import com.trs.gov.workorder.utils.external.OprRecordUtils;
import com.trs.gov.workorder.utils.external.UnitUtils;
import com.trs.gov.workorder.utils.external.UserExternalUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import java.util.Date;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 抽象业务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-12 13:17
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public abstract class AbstractActionMgr implements IBaseActionMgr {
    @Autowired
    protected WorkOrderMapper workOrderMapper;
    @Autowired
    protected MessageUtils messageUtils;
    @Autowired
    protected WorkOrderRoleUtils workOrderRoleUtils;
    @Autowired
    protected CCMapper ccMapper;
    @Autowired
    protected WorkOrderActionAccessUtils workOrderActionAccessUtils;
    @Autowired
    protected UserExternalUtils userExternalUtils;
    @Autowired
    protected UnitUtils unitUtils;
    @Autowired
    protected OprRecordUtils oprRecordUtils;

    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO) {
        return userExternalUtils.isAdminAndHasLoginUnit();
    }

    @Override
    public boolean isSupportAction(WorkOrderBaseDTO dto) throws ServiceException {
        SupportActionDTO supportAction = workOrderActionAccessUtils.getSupportAction(Long.valueOf(dto.getWorkOrderId()));
        if(supportAction == null && !key().equals(OperateNameConstant.REPLY_WORK_ORDER)){
            return false;
        }
        return isSupportAction(supportAction);
    }

    @Override
    final public Reports executeByIds(BaseDTO dto, String _sOprName, String _sOprDesc) throws ServiceException {
        return null;
    }

    @Override
    public void validateRight(com.trs.user.DTO.UserDTO user, WorkOrderVO workOrder, String _sOprName, String _sOprDesc) throws ServiceException {

    }

    @Override
    public Object getExecuteResult() throws ServiceException {
        return null;
    }

    @Override
    public Object getExecuteResult(Reports reports) throws ServiceException {
        return null;
    }

    @Override
    public void endExecuteOneWorkOrder(BaseDTO dto, WorkOrderVO workOrder) throws ServiceException {
    }

    protected WorkOrderDO commonConvert(BaseDTO baseDTO) throws ServiceException {
        WorkOrderActionDTO dto = (WorkOrderActionDTO) baseDTO;
        WorkOrderDO workOrderDO = workOrderMapper.selectById(Long.valueOf(dto.getWorkOrderId()));
        if(workOrderDO == null){
            throw new ServiceException("工单【"+workOrderDO.getId()+"】不存在");
        }

        if(dto.getTargetUnitId()!=null){
            if(workOrderDO.getStatus().equals(WorkOrderConstant.STATUS_WAIT_ASSIGN) && workOrderRoleUtils.isWorkOrderRole(workOrderDO, WorkOrderRoleConstant.DEAL_UNIT_MASTER)){
                //指定到单位时，负责人分配了任务
                if(workOrderDO.getActionTime()==null){
                    //存入响应状态，以便发送响应消息
                    dto.setAction(true);
                    workOrderDO.setActionTime(new Date());
                }
            }
            Long oldDealUnitId = workOrderDO.getDealUnitId();
            String oldDealUsername = workOrderDO.getDealUsername();
            Integer assignType = AssignConstant.ASSIGN_TO_UNIT;
            if(!StringUtils.isEmpty(dto.getTargetUsername())){
                assignType = AssignConstant.ASSIGN_TO_USER;
            }
            if(assignType.equals(workOrderDO.getDealAssignType()) && dto.getTargetUnitId().equals(oldDealUnitId) &&
                    (assignType.equals(AssignConstant.ASSIGN_TO_UNIT) ||
                    (assignType.equals(AssignConstant.ASSIGN_TO_USER) && oldDealUsername.equals(dto.getTargetUsername())))){
                throw new ServiceException("不能"+desc()+"给同一受理人");
            }
            //投诉工单不能回退或转交给创建人
            if(workOrderDO.getWorkOrderTopTypeId().equals(WorkOrderConstant.TOP_TYPE_COMPLAINT) &&
                    workOrderDO.getCrUnitId().equals(dto.getTargetUnitId()) && workOrderDO.getCrUser().equals(dto.getTargetUsername())){
                throw new ServiceException("投诉工单不能"+desc()+"给创建人");
            }
            //存入指定类型，以便保存操作记录
            dto.setAssginType(assignType);
            //存入老处理人单位id、用户名，方便发送消息
            dto.setOldDealAssignType(workOrderDO.getDealAssignType());
            dto.setOldDealUnitId(oldDealUnitId);
            dto.setOldDealUsername(oldDealUsername);

            workOrderDO.setDealUnitId(dto.getTargetUnitId());
            workOrderDO.setDealUnit(unitUtils.findUnitByUnitId(dto.getTargetUnitId()).getUnitName());
            if(StringUtils.isEmpty(dto.getTargetUsername())){
                String newDealUsername = unitUtils.findUnitByUnitId(dto.getTargetUnitId()).getUnitMaster();
                workOrderDO.setDealUsername(newDealUsername);
                workOrderDO.setStatus(WorkOrderConstant.STATUS_WAIT_ASSIGN);
                workOrderDO.setDealAssignType(AssignConstant.ASSIGN_TO_UNIT);
                //存入单位负责人、指定类型，方便保存操作记录
                dto.setTargetUsername(newDealUsername);
            } else{
                workOrderDO.setDealUsername(dto.getTargetUsername());
                workOrderDO.setStatus(WorkOrderConstant.STATUS_DEALING);
                workOrderDO.setDealAssignType(AssignConstant.ASSIGN_TO_USER);
                //存入指定类型，方便保存操作记录
            }
            workOrderDO.setDealTruename(userExternalUtils.getTrueNameByUserName(workOrderDO.getDealUsername()));
            //存入新处理人真实姓名，方便发送消息
            dto.setTargetTruename(workOrderDO.getDealTruename());

            workOrderDO.setReceiveTime(new Date());
        }
        workOrderDO.setUpdateTime(new Date());

        return workOrderDO;
    }

    protected RestfulResults commonDoAction(WorkOrderDO workOrderDO){
        try{
            workOrderMapper.updateById(workOrderDO);
            return RestfulResults.ok(new Report(desc(), desc()+"成功", Report.RESULT.SUCCESS));
        } catch (Exception e){
            e.printStackTrace();
            log.error("系统错误，"+desc()+"【"+ workOrderDO.getId()+"失败",e);
            return RestfulResults.error("系统错误，"+desc()+"【"+ workOrderDO.getId()+"失败");
        }
    }

    @Override
    public void recordLogAfterExecute(BaseDTO dto) throws ServiceException {
        WorkOrderActionDTO workOrderAction = (WorkOrderActionDTO) dto;
        OprRecordDTO oprRecordDTO = new OprRecordDTO();
        BaseUtils.setUserInfoToDTO(oprRecordDTO);
        oprRecordDTO.setWorkOrderId(Long.valueOf(workOrderAction.getWorkOrderId()));
        oprRecordDTO.setOprKey(workOrderAction.getOprKey());
        oprRecordDTO.setOption(workOrderAction.getOption());
        oprRecordDTO.setPicList(workOrderAction.getPicList());
        oprRecordDTO.setFileList(workOrderAction.getFileList());
        //重新打开需要保存目标单位和用户
        if(workOrderAction.getOprKey().equals(OperateNameConstant.REOPEN_WORK_ORDER)){
            WorkOrderDO workOrderDO = workOrderMapper.selectById(workOrderAction.getWorkOrderId());
            if(workOrderDO==null){
                throw new ServiceException("工单【"+workOrderAction.getWorkOrderId()+"】未找到");
            }
            oprRecordDTO.setTargetUnitId(workOrderDO.getDealUnitId());
            oprRecordDTO.setAssignType(workOrderDO.getDealAssignType());
            oprRecordDTO.setTargetUser(workOrderDO.getDealUsername());
        }else {
            oprRecordDTO.setTargetUnitId(workOrderAction.getTargetUnitId());
            oprRecordDTO.setAssignType(workOrderAction.getAssginType());
            oprRecordDTO.setTargetUser(workOrderAction.getTargetUsername());
        }
        oprRecordDTO.setCrUser(new WorkOrderDO().getCrUser());
        oprRecordDTO.setCrUnitId(LoginInfoUtils.getLoginUnitId());
        oprRecordDTO.setPicList(workOrderAction.getPicList());
        oprRecordDTO.setFileList(workOrderAction.getFileList());
        oprRecordUtils.saveOprRecord(log, oprRecordDTO);
    }

    @Override
    public void sendMessage(BaseDTO baseDTO){
        WorkOrderActionDTO dto = (WorkOrderActionDTO) baseDTO;
        CreateNoticeDTO createNoticeDTO;
        if (dto.isAction()){
            //发送响应消息
            try {
                createNoticeDTO = messageUtils.buildbaseMessage(Long.valueOf(dto.getWorkOrderId()));
            } catch (Exception e){
                e.printStackTrace();
                log.error("创建【"+desc()+"】响应消息失败："+e);
                return;
            }
            createNoticeDTO.setMessageConfigType(OperateNameConstant.RESPONSE_WORK_ORDER);
            messageUtils.sendNoticeMessage(log, createNoticeDTO);
        }

        try {
            createNoticeDTO = messageUtils.buildbaseMessage(Long.valueOf(dto.getWorkOrderId()));
        } catch (Exception e){
            e.printStackTrace();
            log.error("创建【"+desc()+"】消息失败："+e);
            return;
        }
        createNoticeDTO.setUnitId(dto.getUnitId());
        createNoticeDTO.setToUser(dto.getTargetUsername());
        createNoticeDTO.setToUserTrueName(dto.getTargetTruename());
        createNoticeDTO.setMessageConfigType(key());
        if(dto.getOldDealUsername()!=null){
            createNoticeDTO.getHandleUser().add(new TargetUserDTO(dto.getOldDealUsername(), dto.getOldDealUnitId(), null ,dto.getOldDealAssignType()));
        }

        messageUtils.sendNoticeMessage(log, createNoticeDTO);
    }
}
