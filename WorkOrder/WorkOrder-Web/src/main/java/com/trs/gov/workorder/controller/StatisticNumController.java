package com.trs.gov.workorder.controller;

import com.trs.gov.workorder.DTO.StatisticDTO;
import com.trs.gov.workorder.VO.StatisticVO;
import com.trs.gov.workorder.service.impl.StatisticNumServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：StatisticNumController
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/29 15:10
 **/
@RestController
@RequestMapping("/workorder/statistic")
public class StatisticNumController {
    @Autowired
    private StatisticNumServiceImpl statisticNumService;

    /**
     * @Description  首页数据相关统计
     * @Param [statisticDTO]
     * @return com.trs.web.builder.base.RestfulResults<com.trs.gov.workorder.VO.StatisticVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/30 10:58
     **/
    @PostMapping("num")
    public RestfulResults<StatisticVO> countStatistic(StatisticDTO statisticDTO) {
        return statisticNumService.countStatistic(statisticDTO);
    }
}
