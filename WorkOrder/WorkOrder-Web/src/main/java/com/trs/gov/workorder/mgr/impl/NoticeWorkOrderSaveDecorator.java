package com.trs.gov.workorder.mgr.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.NoticeDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.NoticeDTO;
import com.trs.gov.workorder.DTO.NoticeExportDTO;
import com.trs.gov.workorder.DTO.WorkOrderDTO;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.VO.ResultVO;
import com.trs.gov.workorder.constant.CommonConstant;
import com.trs.gov.workorder.constant.NoticeConstant;
import com.trs.gov.workorder.dao.NoticeMapper;
import com.trs.gov.workorder.mgr.AbstractWorkOrderSaveDecorator;
import com.trs.gov.workorder.utils.CommonInfo;
import com.trs.gov.workorder.utils.TargetDTOUtilts;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 通知工单的修饰器
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-15 12:32
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class NoticeWorkOrderSaveDecorator extends AbstractWorkOrderSaveDecorator {
    @Autowired
    private NoticeMapper noticeMapper;
    @Autowired
    private TargetDTOUtilts targetDTOUtilts;

    @Override
    public String desc() {
        return "保存通知";
    }

    @Override
    public void initParameterParse(WorkOrderDTO dto) throws ServiceException {
        if (dto == null) {
            throw new ServiceException("工单不能为空");
        }
        dto.isValid();
        PreConditionCheck.checkArgument(!StringUtils.isEmpty(dto.getNoticelist()), "通知列表不能为空");
        List<NoticeDTO> noticeDTOS = JsonUtils.toObject(dto.getNoticelist(), ArrayList.class, NoticeDTO.class);
        PreConditionCheck.checkArgument(noticeDTOS != null, "通知列表格式有误");
        for (NoticeDTO noticeDTO : noticeDTOS) {
            noticeDTO.isValid();
            if (noticeDTO.getTargetUnitId()!=null){
                dto.getUnitIdSet().add(noticeDTO.getTargetUnitId());
            }
            if (!StringUtils.isEmpty(noticeDTO.getTargetUsername())) {
                dto.getUsernameSet().add(noticeDTO.getTargetUsername());
            }
        }
        noticeDTOS = noticeDTOS.stream().distinct().collect(Collectors.toList());
        targetDTOUtilts.removeRepeat(noticeDTOS, true);
        dto.setNoticeDTOS(noticeDTOS);
    }

    @Override
    public boolean checkParamsValid(WorkOrderExportDTO dto) throws ServiceException {
        dto.noticeValid();
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveExportWorkOrder(WorkOrderExportDTO dto) throws ServiceException {
        WorkOrderDO workOrderDO = exportWorkOrderConvert(dto);
        saveWorkOrder(workOrderDO, dto);
        noticeMapper.delete(new QueryWrapper<NoticeDO>().eq("work_order_id", workOrderDO.getId()));
        if (CollectionUtils.isEmpty(dto.getNoticeDTOS())) {
            throw new ServiceException("通知列表数据为空!");
        }
        for (NoticeExportDTO noticeDTO : dto.getNoticeDTOS()) {
            NoticeDO noticeDO = new NoticeDO();
            BeanUtils.copyProperties(noticeDTO, noticeDO);
            noticeDO.setCrTime(noticeDO.getCrTime());
            noticeDO.setWorkOrderId(workOrderDO.getId());
            noticeDO.setStatus(NoticeConstant.STATUS_NOT_READ);
            noticeDO.setExportFromOther(1);
            noticeMapper.insert(noticeDO);
        }
        dto.setWorkOrderId(workOrderDO.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestfulResults startExecuteOneWorkOrder(WorkOrderDTO dto) throws ServiceException {
        WorkOrderDO workOrderDO = commonConvert(dto);

        commonSaveOrUpdate(dto, workOrderDO);

        CommonInfo info = new CommonInfo(unitUtils.findUnit(dto.getUnitIdSet()), userExternalUtils.findUserListByUserName(dto.getUsernameSet()));
        List<NoticeDTO> noticeDTOS = dto.getNoticeDTOS();
        List<NoticeDO> noticeDOS = new ArrayList<>();
        for (NoticeDTO noticeDTO : noticeDTOS) {
            NoticeDO noticeDO = new NoticeDO();
            noticeDO.setExportFromOther(CommonConstant.NO);
            noticeDO.setWorkOrderId(workOrderDO.getId());
            Integer type = noticeDTO.getType();
            noticeDO.setType(type);
            //发通知给所有人只需保存一条记录
            if (AssignConstant.ASSIGN_TO_ALL.equals(type)) {
                noticeDOS.clear();
                noticeDOS.add(noticeDO);
                break;
            }
            if(AssignConstant.ASSIGN_TO_GROUP.equals(type)){
                noticeDO.setGroupId(noticeDTO.getGroupId());
            }
            if (AssignConstant.ASSIGN_TO_UNIT.equals(type) || AssignConstant.ASSIGN_TO_USER.equals(type)) {
                noticeDO.setTargetUnitId(noticeDTO.getTargetUnitId());
                noticeDO.setTargetUnit(info.getUnitVO(noticeDO.getTargetUnitId()).getUnitName());
                if (AssignConstant.ASSIGN_TO_USER.equals(type)) {
                    noticeDO.setTargetUsername(noticeDTO.getTargetUsername());
                    noticeDO.setTargetTruename(info.getUserVO(noticeDO.getTargetUsername()).getTrueName());
                }
                noticeDO.setExportFromOther(CommonConstant.NO);
            }
            noticeDOS.add(noticeDO);
        }
        noticeMapper.insertList(noticeDOS);

        return RestfulResults.ok(ResultVO.createSuccess("保存通知", "保存通知成功"));
    }
}
