package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldForDealUnitIdList
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 15:37
 **/
@Component
public class FieldForDealUnitIdList extends BaseCommonFieldMgr<WorkOrderDO> {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        String dealUnitIdList = (String) keyWords;
        if(!CMyString.isEmpty(dealUnitIdList)){
            return Optional.ofNullable(m -> m.in(searchField(), dealUnitIdList.split(","))) ;
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.DEAL_UNIT_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.DEAL_UNIT_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_FIELD_DEAL_UNIT_ID;
    }
}
