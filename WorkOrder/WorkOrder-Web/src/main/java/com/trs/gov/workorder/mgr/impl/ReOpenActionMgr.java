package com.trs.gov.workorder.mgr.impl;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.DTO.WorkOrderActionDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.constant.CommonConstant;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.mgr.AbstractActionMgr;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 工单-重新打开
 */
@Component
@Slf4j
public class ReOpenActionMgr extends AbstractActionMgr {
    @Override
    public String key() {
        return OperateNameConstant.REOPEN_WORK_ORDER;
    }

    @Override
    public String desc() {
        return OperateNameConstant.getTypeDesc(OperateNameConstant.REOPEN_WORK_ORDER).get();
    }

    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO) {
        return (super.isSupportAction(supportActionDTO) ||
                workOrderRoleUtils.isWorkOrderRole(supportActionDTO,
                        WorkOrderRoleConstant.DEAL_UNIT_MASTER,
                        WorkOrderRoleConstant.DEAL_USER,
                        WorkOrderRoleConstant.CR_USER,
                        WorkOrderRoleConstant.HOST_UNIT_MASTER,
                        WorkOrderRoleConstant.HOST_USER)) &&
                supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_FINISHED;
    }

    @Override
    public void initParameterParse(BaseDTO baseDTO) throws ServiceException {
        baseDTO.isValid();
    }

    @Override
    public RestfulResults startExecuteOneWorkOrder(BaseDTO baseDTO) throws ServiceException {
        WorkOrderActionDTO dto = (WorkOrderActionDTO) baseDTO;
        WorkOrderDO workOrderDO = commonConvert(baseDTO);
        workOrderDO.setIsReturn(CommonConstant.NO);
        if(dto.getTargetUnitId()==null){
            workOrderDO.setStatus(WorkOrderConstant.STATUS_DEALING);
        }
        workOrderDO.setReceiveTime(new Date());
        workOrderDO.setFinishTime(null);

        return commonDoAction(workOrderDO);
    }

    @Override
    public void endExecuteOneWorkOrder(BaseDTO dto, WorkOrderVO workOrder) throws ServiceException {

    }
}
