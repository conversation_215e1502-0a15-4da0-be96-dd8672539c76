package com.trs.gov.workorder.utils;

import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.utils.external.UnitUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 工单角色判断
 */
@Component
@Slf4j
public class WorkOrderRoleUtils {
    @Autowired
    private UnitUtils unitUtils;

    /**
     * 是否是工单角色列表中的一种
     *
     * @param supportActionDTO
     * @param workOrderRoleList
     * @return
     * @throws ServiceException
     */
    public boolean isWorkOrderRole(SupportActionDTO supportActionDTO, String... workOrderRoleList) {
        if (workOrderRoleList != null && workOrderRoleList.length != 0) {
            for (String role : workOrderRoleList) {
                boolean workOrderRole = isWorkOrderRole(supportActionDTO, role);
                if (workOrderRole) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 是否是工单角色列表中的一种
     *
     * @param workOrderDO
     * @param workOrderRoleList
     * @return
     * @throws ServiceException
     */
    public boolean isWorkOrderRole(WorkOrderDO workOrderDO, String... workOrderRoleList) throws ServiceException {
        SupportActionDTO supportActionDTO = new SupportActionDTO(workOrderDO, ContextHelper.getLoginUser().get(), unitUtils.getLoginUnit());
        return isWorkOrderRole(supportActionDTO, workOrderRoleList);
    }

    /**
     * 是否是某工单角色
     *
     * @param supportActionDTO
     * @param workOrderRole
     * @return
     * @throws ServiceException
     */
    public boolean isWorkOrderRole(SupportActionDTO supportActionDTO, String workOrderRole) {
        WorkOrderDO workOrderDO = supportActionDTO.getWorkOrderDO();
        UnitVO loginUnit = supportActionDTO.getLoginUnit();
        String LoginUsername = supportActionDTO.getLoginUsername();
        List<CCDO> ccdoList = supportActionDTO.getCcdoList();
        List<Long> loginGroupIdList = supportActionDTO.getLoginGroupIdList();

        switch (workOrderRole) {
            case WorkOrderRoleConstant.DEAL_UNIT_MASTER:
                return loginUnit.getId().equals(workOrderDO.getDealUnitId()) &&
                        LoginUsername.equals(loginUnit.getUnitMaster()) &&
                        AssignConstant.ASSIGN_TO_UNIT.equals(workOrderDO.getDealAssignType());
            case WorkOrderRoleConstant.DEAL_UNIT_MASTER_ONLY:
                return loginUnit.getId().equals(workOrderDO.getDealUnitId()) &&
                        LoginUsername.equals(loginUnit.getUnitMaster());
            case WorkOrderRoleConstant.DEAL_UNIT_NOT_MASTER:
                return loginUnit.getId().equals(workOrderDO.getDealUnitId());
            case WorkOrderRoleConstant.DEAL_USER:
                return LoginUsername.equals(workOrderDO.getDealUsername()) &&
                        loginUnit.getId().equals(workOrderDO.getDealUnitId()) &&
                        AssignConstant.ASSIGN_TO_USER.equals(workOrderDO.getDealAssignType());
            case WorkOrderRoleConstant.CC_UNIT_MASTER:
                return loginUnit.getUnitMaster().equals(LoginUsername) &&
                        ccdoList.stream().anyMatch(ccdo ->
                                (AssignConstant.ASSIGN_TO_UNIT.equals(ccdo.getType()) &&
                                        ccdo.getTargetUnitId().equals(loginUnit.getId())) ||
                                (AssignConstant.ASSIGN_TO_GROUP.equals(ccdo.getType()) &&
                                        loginGroupIdList.stream().anyMatch(groupId -> groupId.equals(ccdo.getGroupId()))));
            case WorkOrderRoleConstant.CC_UNIT_NOT_MASTER:
                return ccdoList.stream().anyMatch(ccdo -> loginUnit.getId().equals(ccdo.getTargetUnitId()));
            case WorkOrderRoleConstant.CC_USER:
                return ccdoList.stream().anyMatch(ccdo ->  (
                        (AssignConstant.ASSIGN_TO_USER.equals(ccdo.getType() ) &&
                                loginUnit.getId().equals(ccdo.getTargetUnitId()) &&
                                LoginUsername.equals(ccdo.getTargetUsername())) ||
                         AssignConstant.ASSIGN_TO_ALL.equals(ccdo.getType())));
            case WorkOrderRoleConstant.CR_UNIT:
                return loginUnit.getId().equals(workOrderDO.getCrUnitId());
            case WorkOrderRoleConstant.CR_USER:
                return LoginUsername.equals(workOrderDO.getCrUsername()) &&
                        loginUnit.getId().equals(workOrderDO.getCrUnitId());
            case WorkOrderRoleConstant.HOST_UNIT_MASTER:
                return loginUnit.getId().equals(workOrderDO.getHostUnitId()) &&
                        LoginUsername.equals(loginUnit.getUnitMaster());
            case WorkOrderRoleConstant.HOST_USER:
                return loginUnit.getId().equals(workOrderDO.getHostUnitId()) &&
                        workOrderDO.getHostAssignType().equals(AssignConstant.ASSIGN_TO_USER) &&
                        LoginUsername.equals(workOrderDO.getHostUsername());
        }

        return false;
    }
}
