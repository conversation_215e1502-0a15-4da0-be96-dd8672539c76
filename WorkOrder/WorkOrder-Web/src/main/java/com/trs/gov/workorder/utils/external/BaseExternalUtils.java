package com.trs.gov.workorder.utils.external;

import com.trs.gov.core.exception.ServiceException;
import com.trs.user.service.IUserService;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import org.apache.dubbo.config.annotation.Reference;

/**
 * <AUTHOR>
 * @date 2021/1/11
 */
public class BaseExternalUtils {
    @Reference(check = false, timeout = 60000)
    protected IUserService userService;

    public void checkRestfulResults(RestfulResults restfulResults) throws ServiceException {
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException("访问外部服务异常："+restfulResults.getMsg());
        }
    }
}
