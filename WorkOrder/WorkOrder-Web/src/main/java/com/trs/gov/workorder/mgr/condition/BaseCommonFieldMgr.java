package com.trs.gov.workorder.mgr.condition;

import com.trs.gov.workorder.service.IFieldMgr;

/**
 * @ClassName：BaseCommonFieldMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 11:11
 **/
public abstract class BaseCommonFieldMgr<T> implements IFieldMgr<T> {

    /**
     * @Description  获取查询字段
     * @Param []
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 14:58
     **/
    public String searchField(){
        return key();
    }
}
