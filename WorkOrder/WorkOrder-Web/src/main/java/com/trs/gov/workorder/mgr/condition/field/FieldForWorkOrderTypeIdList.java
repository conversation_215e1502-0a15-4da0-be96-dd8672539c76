package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import com.trs.gov.workorder.utils.external.WorkOrderTypeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldWorkOrderTopTypeName
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 14:49
 **/
@Component
public class FieldForWorkOrderTypeIdList extends BaseCommonFieldMgr<WorkOrderDO> {
    @Autowired
    private WorkOrderTypeUtils workOrderTypeUtils;

    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        String workOrderTypeIdList = (String) keyWords;
        if(!CMyString.isEmpty(workOrderTypeIdList)){
            List<Long> workTypeChildList = workOrderTypeUtils.getWorkTypeChildList(workOrderTypeIdList);
            return Optional.ofNullable(m -> m.in(searchField(), workTypeChildList));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_TYPE_ID_LIST;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.WORK_ORDER_TYPE_ID_LIST;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_FIELD_WORK_ORDER_TYPE_ID;
    }
}
