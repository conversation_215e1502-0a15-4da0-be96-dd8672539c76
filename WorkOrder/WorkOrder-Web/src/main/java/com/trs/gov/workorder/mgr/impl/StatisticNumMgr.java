package com.trs.gov.workorder.mgr.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.constant.WorkOrderTypeConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.management.DTO.WorkOrderTypeSearchDTO;
import com.trs.gov.management.service.WorkOrderTypeService;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.StatisticDTO;
import com.trs.gov.workorder.VO.StatisticVO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * @ClassName：StatisticNumMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/29 15:07
 **/
@Component
@Slf4j
public class StatisticNumMgr {

    @Autowired
    private WorkOrderMapper workOrderMapper;

    @Reference(check = false,timeout = 60000)
    private WorkOrderTypeService workOrderTypeService;

    public RestfulResults<StatisticVO> countStatistic(StatisticDTO statisticDTO) throws ServiceException {
        StatisticVO statisticVO = new StatisticVO();
        statisticVO.setUserName(statisticDTO.getUserName());
        statisticVO.setUnitId(statisticDTO.getUnitId());
        statisticVO.setWaitMeHandleNum(getWaitMeHandleNum(statisticDTO).orElseThrow(()->new ServiceException("获取待我处理数量失败!")));
        statisticVO.setCopyToMeNum(getCopyToMeNum(statisticDTO).orElseThrow(()->new ServiceException("获取抄送给我的数量失败!")));
        statisticVO.setMyAppraiseNum(getMyAppraiseNum(statisticDTO).orElseThrow(()->new ServiceException("获取我评价的数量失败!")));
        statisticVO.setUnReadNoticeNum(getUnReadNoticeNum(statisticDTO).orElseThrow(()->new ServiceException("获取我未读的通知数量失败!")));
        statisticVO.setMyHandleNum(statisticVO.getWaitMeHandleNum()+statisticVO.getCopyToMeNum());
        return RestfulResults.ok(statisticVO).addMsg("获取数据成功!");
    }

    /**
     * @Description  获取待我处理数量
     * @Param [statisticDTO]
     * @return java.util.Optional<java.lang.Integer>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/30 13:07
     **/
    public Optional<Integer> getWaitMeHandleNum(StatisticDTO statisticDTO){
        Integer count = 0;
        try {
            count = workOrderMapper.selectCount(new QueryWrapper<WorkOrderDO>()
                    .inSql("id", "select work_order_id from cc where target_unit_id = '"+statisticDTO.getUnitId()+"' and target_username = '"+statisticDTO.getUserName()+"'"));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return Optional.of(count);
    }

    /**
     * @Description 获取抄送给我的数量
     * @Param [statisticDTO]
     * @return java.util.Optional<java.lang.Integer>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/30 13:21
     **/
    public Optional<Integer> getCopyToMeNum(StatisticDTO statisticDTO){
        Integer count = 0;
        try {
            count = workOrderMapper.selectCount(new QueryWrapper<WorkOrderDO>()
                    .eq("deal_unit",statisticDTO.getUnitId())
                    .eq("deal_username",statisticDTO.getUserName())
                    .in("status", WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return Optional.of(count);
    }

    /**
     * @Description  获取待我评价数量
     * @Param [statisticDTO]
     * @return java.util.Optional<java.lang.Integer>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/30 11:39
     **/
    public Optional<Integer> getMyAppraiseNum(StatisticDTO statisticDTO){
        Integer count = 0;
        try {
            count = workOrderMapper.selectCount(new QueryWrapper<WorkOrderDO>()
                    .eq("host_unit",statisticDTO.getUnitId())
                    .eq("host_username",statisticDTO.getUserName())
                    .eq("status",WorkOrderConstant.STATUS_FINISHED));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return Optional.of(count);
    }

    /**
     * @Description  获取未读通知数量
     * @Param [statisticDTO]
     * @return java.util.Optional<java.lang.Integer>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/30 12:59
     **/
    public Optional<Integer> getUnReadNoticeNum(StatisticDTO statisticDTO){
        Integer count = 0;
        try {
            WorkOrderTypeSearchDTO workOrderTypeSearchDTO = new WorkOrderTypeSearchDTO();
            workOrderTypeSearchDTO.setRootKey(WorkOrderTypeConstant.ROOT_TONG_ZHI_KEY);
            BaseUtils.setUserInfoToDTO(workOrderTypeSearchDTO);
            RestfulResults<List<Long>> listRestfulResults = workOrderTypeService.listAllRelateTypeByRootType(workOrderTypeSearchDTO);
            if(listRestfulResults.getCode().equals("500")){
                throw new ServiceException("从其他服务获取【"+WorkOrderTypeConstant.ROOT_TONG_ZHI_KEY+"】类型的id列表失败!");
            }
            List<Long> datas = listRestfulResults.getDatas();
            count = workOrderMapper.selectCount(new QueryWrapper<WorkOrderDO>()
                    .in(datas != null&&datas.size() > 0,"work_order_type_id",datas)
                    .inSql("id", "select work_order_id from notice where status = '0' and target_unit_id = '"+statisticDTO.getUnitId()+"' and target_username = '"+statisticDTO.getUserName()+"'"));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return Optional.of(count);
    }

}
