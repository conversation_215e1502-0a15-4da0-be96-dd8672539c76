package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：AllDealed
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:51
 **/
@Component
public class AllDealed extends WorkOrderSearchTypeMgr {

    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        UnitVO loginUnit = unitUtils.getLoginUnit();
        List<Long> relatedWorkOrderIdList = oprRecordUtils.getRelatedWorkOrderId(false, false,
                OperateNameConstant.CREATE_WORK_ORDER,
                OperateNameConstant.ASSIGN_WORK_ORDER,
                OperateNameConstant.ROLLBACK_WORK_ORDER,
                OperateNameConstant.UPDATE_WORK_ORDER_DEAL,
                OperateNameConstant.COPY_WORK_ORDER);
        Consumer<QueryWrapper<WorkOrderDO>> consumer1 = m -> m.in ("id", relatedWorkOrderIdList);

        List<Long> ccToMeWorkOrderIdList = queryCCToMe(loginUnit, true, WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING);
        Consumer<QueryWrapper<WorkOrderDO>> consumer2 = m -> m
                        .not(consumerMyDeal(loginUnit, true, WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING))
                        .notIn("id", ccToMeWorkOrderIdList);

        Consumer<QueryWrapper<WorkOrderDO>> consumer3 = m -> m.in("status", WorkOrderConstant.STATUS_FINISHED, WorkOrderConstant.STATUS_REVIEWED, WorkOrderConstant.STATUS_OPENED);
        Consumer<QueryWrapper<WorkOrderDO>> consumer4 = queryByMyUnit();

        Consumer<QueryWrapper<WorkOrderDO>> consumer = m -> m.and(i -> i
                .and(!CollectionUtils.isEmpty(relatedWorkOrderIdList),j -> j
                        .and(consumer1)
                        .and(consumer2))
                .or(j -> j
                        .and(consumer3)
                        .and(consumer4)));

        return Optional.ofNullable(consumer);
    }

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL_DEALED);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL_DEALED);
    }
}
