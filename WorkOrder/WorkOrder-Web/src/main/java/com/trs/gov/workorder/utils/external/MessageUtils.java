package com.trs.gov.workorder.utils.external;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.message.service.SendNoticeMesssageService;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.user.VO.UserVO;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class MessageUtils extends BaseExternalUtils {
    @Autowired
    protected WorkOrderMapper workOrderMapper;
    @Autowired
    protected CCMapper ccMapper;
    @Autowired
    private UserExternalUtils userExternalUtils;
    @Reference(check = false, timeout = 60000)
    private SendNoticeMesssageService sendNoticeMesssageService;

    /**
     * 构建基础消息
     * @param workOrderId
     * @return
     * @throws Exception
     */
    public CreateNoticeDTO buildbaseMessage(Long workOrderId) throws Exception {
        WorkOrderDO workOrderDO = workOrderMapper.selectById(workOrderId);
        if(workOrderDO==null){
            throw new ServiceException("工单【"+workOrderId+"】未找到");
        }

        CreateNoticeDTO createNoticeDTO = new CreateNoticeDTO();
        createNoticeDTO.setWorkOrderId(workOrderDO.getId());
        createNoticeDTO.setRootTypeName(workOrderDO.getWorkOrderTopTypeName());
        createNoticeDTO.setWorkOrderTopTypeId(workOrderDO.getWorkOrderTopTypeId());
        createNoticeDTO.setWorkOrderTypeId(workOrderDO.getWorkOrderTypeId());
        createNoticeDTO.setCurrentUser(new WorkOrderDO().getCrUser());
        createNoticeDTO.setCurrentUserTrueName(userExternalUtils.getTrueNameByUserName(createNoticeDTO.getCurrentUser()));

        TargetUserDTO targetUserDTO = new TargetUserDTO(workOrderDO.getCrUsername(), workOrderDO.getCrUnitId(), null, AssignConstant.ASSIGN_TO_USER);
        createNoticeDTO.setCreateUser(Arrays.asList(targetUserDTO));

        List<TargetUserDTO> handleList = new ArrayList<>();
        if(workOrderDO.getDealUsername()!=null){
            handleList.add(new TargetUserDTO(workOrderDO.getDealUsername(), workOrderDO.getDealUnitId(), null, workOrderDO.getDealAssignType()));
        }
        createNoticeDTO.setHandleUser(handleList);

        List<TargetUserDTO> hostList = new ArrayList<>();
        if(workOrderDO.getHostUsername()!=null){
            hostList.add(new TargetUserDTO(workOrderDO.getHostUsername(), workOrderDO.getHostUnitId(), null, workOrderDO.getHostAssignType()));
        }
        if(workOrderDO.getTitle()!=null){
            createNoticeDTO.setTitle(workOrderDO.getTitle());
        }
        createNoticeDTO.setHostUser(hostList);

        List<CCDO> ccDOList = ccMapper.selectList(new QueryWrapper<CCDO>().eq("work_order_id", workOrderId));
        if(!CollectionUtils.isEmpty(ccDOList)){
            List<TargetUserDTO> ccList = new ArrayList<>();
            for (CCDO ccDO : ccDOList) {
                ccList.add(new TargetUserDTO(ccDO.getTargetUsername(), ccDO.getTargetUnitId(), ccDO.getGroupId(), ccDO.getType()));
            }
            createNoticeDTO.setOldCopyUser(ccList);
        }

        return createNoticeDTO;
    }

    /**
     * 发送消息
     * @param log
     * @param createNoticeDTO
     */
    public void sendNoticeMessage(Logger log, CreateNoticeDTO createNoticeDTO){
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, createNoticeDTO);
            RestfulResults restfulResults = sendNoticeMesssageService.sendNoticeMessage(createNoticeDTO);
            if(!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.Unauthorized.getValue())){
                checkRestfulResults(restfulResults);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送"+createNoticeDTO.getMessageConfigType()+"消息失败， "+e.getMessage());
        }
    }

    /**
     * 通过用户名查询用户信息，并构建 用户名+联系方式
     * @param userName
     * @return
     */
    public String buildUserInfo(String userName) throws ServiceException {
        UserVO userVO = userExternalUtils.findUserByUserName(userName);
        return userVO.getTrueName()+" "+userVO.getPhone();
    }
}
