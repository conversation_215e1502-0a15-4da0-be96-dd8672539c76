package com.trs.gov.workorder.mgr;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import com.trs.gov.workorder.service.IBaseMgr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：BaseMgr
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/16 17:31
 **/
@Component
public class BaseMgr<K> implements IBaseMgr<K> {
    @Autowired
    private List<BaseCommonFieldMgr<K>> fieldMgrs;

    /**
     * 获取consumer表达式
     * @param dto
     * @param consumers
     * @return: java.util.function.Consumer<com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<K>>
     * @Author: zuo.kaiyuan
     * @Date: 2020/12/16 16:25
     */
    @Override
    public <T extends BaseDTO> Consumer<QueryWrapper<K>> buildConsumer(T dto, Consumer<QueryWrapper<K>>... consumers) throws ServiceException, IllegalAccessException {
        // 构造查询语句
        Consumer<QueryWrapper<K>> c = m -> {};
        Field[] declaredFields = dto.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            Object fieldValue = declaredField.get(dto);
            Optional<BaseCommonFieldMgr> fieldMgrs = getFieldMgrs(declaredField.getName());
            if (fieldMgrs.isPresent()){
                Optional<Consumer<QueryWrapper<K>>> optional = fieldMgrs.get().buildCondition(fieldValue, dto);
                if(optional.isPresent()){
                    c = c.andThen(optional.get());
                }
            }
        }
        //添加额外条件
        for (Consumer<QueryWrapper<K>> consumer : consumers) {
            c = c.andThen(consumer);
        }
        return c;
    }

    /**
     * @Description  获取对应的BaseWorkOrderFieldMgr实现类
     * @Param [field]
     * @return java.util.Optional<com.trs.gov.search.mgr.BaseWorkOrderFieldMgr>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 22:23
     **/
    private Optional<BaseCommonFieldMgr> getFieldMgrs(String field) {
        for (BaseCommonFieldMgr fieldmgr : fieldMgrs) {
            if (fieldmgr.key().equalsIgnoreCase(field)) {
                return Optional.ofNullable(fieldmgr);
            }
        }
        return Optional.empty();
    }
}
