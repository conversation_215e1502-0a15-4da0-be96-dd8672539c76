package com.trs.gov.workorder.utils.external;

import com.trs.common.utils.StringUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.DTO.WorkOrderTypeSearchDTO;
import com.trs.gov.management.VO.ParentWorkOrderTypeVO;
import com.trs.gov.management.VO.WorkOrderTypeForNoticeVO;
import com.trs.gov.management.VO.WorkOrderTypeVO;
import com.trs.gov.management.service.WorkOrderTypeService;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/7
 */
@Component
@Slf4j
public class WorkOrderTypeUtils extends BaseExternalUtils {
    @Reference(check = false, timeout = 60000)
    private WorkOrderTypeService workOrderTypeService;

    /**
     * 获取默认投诉单位
     * @return
     * @throws ServiceException
     */
    public Long getComplainUnitId() throws ServiceException {
        WorkOrderTypeVO workOrderTypeVO = findWorkOrderTypeById(WorkOrderConstant.TOP_TYPE_COMPLAINT);
        if(workOrderTypeVO.getAcceptUnitId()==null){
            throw new ServiceException("未指定默认投诉单位，请管理员设置");
        }
        return Long.valueOf(workOrderTypeVO.getAcceptUnitId());
    }

    /**
     * 获取工单类型
     *
     * @param workOrderTypeId
     * @return
     * @throws ServiceException
     */
    public WorkOrderTypeVO findWorkOrderTypeById(Long workOrderTypeId) throws ServiceException {
        if (workOrderTypeId == null) {
            throw new ServiceException("工单类型id不能为空: ");
        }

        WorkOrderTypeSearchDTO dto = new WorkOrderTypeSearchDTO();
        dto.setId(String.valueOf(workOrderTypeId));
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<WorkOrderTypeVO>> restfulResults = workOrderTypeService.getWorkOrderTypes(dto);
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
        if (CollectionUtils.isEmpty(restfulResults.getDatas())) {
            throw new ServiceException("工单类型【" + workOrderTypeId + "】未找到");
        }

        return restfulResults.getDatas().get(0);
    }

    /**
     * 获取父级工单类型
     *
     * @param ids
     * @return
     * @throws ServiceException
     */
    public Map<Long, ParentWorkOrderTypeVO> findParentWorkOrderTypeById(String ids) throws ServiceException {
        Map<Long, ParentWorkOrderTypeVO> parentTypes = new HashMap<>();
        if (StringUtils.isEmpty(ids)) {
            return parentTypes;
        }

        WorkOrderTypeSearchDTO dto = new WorkOrderTypeSearchDTO();
        dto.setId(ids);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        RestfulResults<List<ParentWorkOrderTypeVO>> restfulResults = workOrderTypeService.getParentWorkOrderTypes(dto);
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
        for (ParentWorkOrderTypeVO vo : restfulResults.getDatas()) {
            parentTypes.put(vo.getChildId(), vo);
        }

        return parentTypes;
    }

    /**
     * 获取父级工单类型名称
     *
     * @param workOrderTypeId
     * @return
     * @throws ServiceException
     */
    public WorkOrderTypeVO findParentWorkOrderTypeById(Long workOrderTypeId) throws ServiceException {
        if (workOrderTypeId == null) {
            throw new ServiceException("工单类型id不能为空: ");
        }

        RestfulResults<WorkOrderTypeForNoticeVO> restfulResults = workOrderTypeService.getTypeInfoForOtherPart(String.valueOf(workOrderTypeId));
        checkRestfulResults(restfulResults);
        if (restfulResults.getDatas() == null) {
            throw new ServiceException("工单类型【" + workOrderTypeId + "】父级类型未找到");
        }
        WorkOrderTypeVO parent = findWorkOrderTypeById(restfulResults.getDatas().getParentId());

        return parent;
    }

    /**
     * 根据工单类型id获取子级类型id
     *
     * @param ids
     * @return
     * @throws ServiceException
     */
    public List<Long> getWorkTypeChildList(String ids) throws ServiceException {
        WorkOrderTypeSearchDTO workOrderTypeSearchDTO = new WorkOrderTypeSearchDTO();
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, workOrderTypeSearchDTO);
        workOrderTypeSearchDTO.setId(ids);
        RestfulResults<List<Long>> restfulResults = workOrderTypeService.listAllRelateTypeById(workOrderTypeSearchDTO);
        checkRestfulResults(restfulResults);
        return restfulResults.getDatas();
    }
}
