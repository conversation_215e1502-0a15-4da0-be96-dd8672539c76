package com.trs.gov.workorder.utils.external;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.file.util.FileUtils;
import com.trs.user.utils.UserUtils;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/7
 */
@Component
@Slf4j
public class FileManagerUtils extends BaseExternalUtils{
    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileService;

    /**
     * 保存文件
     *
     * @param id
     * @param objType  保存类型
     * @param fileList 文件列表
     * @param fileName 文件名
     * @throws ServiceException
     */
    public void saveFile(Long id, String objType, String fileList, String fileName) throws ServiceException {
        try {
            if (!CMyString.isEmpty(fileList)) {
                FileUtils.saveFile(fileService, fileList, objType, String.valueOf(id), fileName);
            }
        } catch (Exception e) {
            if (id != null) {
                Try.of(() -> {
                    FileUtils.deleteFile(fileService, objType, String.valueOf(id), fileName);
                    return 0;
                }).onFailure(err -> log.warn("清除文件出错！", err));
            }
            throw new ServiceException("保存" + fileName + "失败!", e);
        }
    }

    /**
     * 获取文件列表
     *
     * @param id
     * @param objType
     * @param fileName
     * @return
     * @throws ServiceException
     */
    public List<FileVO> getFile(Long id, String objType, String fileName) throws ServiceException {
        ObjDTO objDTO = ObjDTO.of(objType, String.valueOf(id), fileName);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, objDTO);
        return fileService.getFileListOfObj(objDTO);
    }
}
