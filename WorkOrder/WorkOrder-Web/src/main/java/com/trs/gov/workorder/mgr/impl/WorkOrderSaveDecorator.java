package com.trs.gov.workorder.mgr.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.management.VO.SiteRelationVO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.base.enums.MediaType;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.CCDTO;
import com.trs.gov.workorder.DTO.CCExportDTO;
import com.trs.gov.workorder.DTO.WorkOrderDTO;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.VO.ResultVO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.mgr.AbstractWorkOrderSaveDecorator;
import com.trs.gov.workorder.utils.*;
import com.trs.gov.workorder.utils.external.SiteRelationUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 工单保存（需求、反馈）
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-15 12:32
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
public class WorkOrderSaveDecorator extends AbstractWorkOrderSaveDecorator {
    @Autowired
    private CCMapper ccMapper;
    @Autowired
    private CCUtils ccUtils;
    @Autowired
    private WorkOrderRoleUtils workOrderRoleUtils;
    @Autowired
    private TargetDTOUtilts targetDTOUtilts;
    @Autowired
    private SiteRelationUtils siteRelationUtils;

    @Override
    public String desc() {
        return "保存工单";
    }

    public boolean isValid(WorkOrderDTO dto) throws ServiceException {
        if (dto == null) {
            throw new ServiceException("工单不能为空");
        }
        dto.isValid();
        PreConditionCheck.checkArgument(dto.getPriority() != null, "优先级不能为空");
        PreConditionCheck.checkArgument(dto.getHostUnitId() != null, "主办单位不能为空");
        PreConditionCheck.checkArgument(dto.getDealUnitId() != null, "受理单位不能为空");
        if (dto.getMediaType() != null) {
            String mediaTypeName = MediaType.getMediaType().get(dto.getMediaType());
            PreConditionCheck.checkArgument(mediaTypeName != null, "未知媒体类型【" + dto.getMediaType() + "】");
        }

        return true;
    }

    @Override
    public void initParameterParse(WorkOrderDTO dto) throws ServiceException {
        isValid(dto);

        dto.getUnitIdSet().add(dto.getHostUnitId());
        if (StringUtils.isEmpty(dto.getHostUsername())) {
            dto.setHostAssignType(AssignConstant.ASSIGN_TO_UNIT);
        } else {
            dto.setHostAssignType(AssignConstant.ASSIGN_TO_USER);
            dto.getUsernameSet().add(dto.getHostUsername());
        }

        dto.getUnitIdSet().add(dto.getDealUnitId());
        if (StringUtils.isEmpty(dto.getDealUsername())) {
            dto.setStatus(WorkOrderConstant.STATUS_WAIT_ASSIGN);
            dto.setDealAssignType(AssignConstant.ASSIGN_TO_UNIT);
        } else {
            dto.setStatus(WorkOrderConstant.STATUS_DEALING);
            dto.setDealAssignType(AssignConstant.ASSIGN_TO_USER);
            dto.getUsernameSet().add(dto.getDealUsername());
        }

        if (!StringUtils.isEmpty(dto.getCclist())) {
            List<CCDTO> ccDTOs = JsonUtils.toObject(dto.getCclist(), ArrayList.class, CCDTO.class);
            PreConditionCheck.checkArgument(ccDTOs != null, "抄送列表格式有误");
            for (CCDTO ccdto : ccDTOs) {
                ccdto.isValid();
                if (ccdto.getTargetUnitId() != null) {
                    dto.getUnitIdSet().add(ccdto.getTargetUnitId());
                }
                if (!StringUtils.isEmpty(ccdto.getTargetUsername())) {
                    dto.getUsernameSet().add(ccdto.getTargetUsername());
                }
            }
            ccDTOs = ccDTOs.stream().distinct().collect(Collectors.toList());
            targetDTOUtilts.removeRepeat(ccDTOs, false);
            dto.setCcDTOs(ccDTOs);
        }

        dto.getUnitIdSet().add(LoginInfoUtils.getLoginUnitId());
        dto.getUsernameSet().add(LoginInfoUtils.getLoginUser());
    }

    @Override
    public boolean checkParamsValid(WorkOrderExportDTO dto) throws ServiceException {
        if (!CMyString.isEmpty(dto.getDealUsername())) {
            dto.setDealAssignType(AssignConstant.ASSIGN_TO_USER);
        } else {
            dto.setDealAssignType(AssignConstant.ASSIGN_TO_UNIT);
        }
        if (!CMyString.isEmpty(dto.getHostUsername())) {
            dto.setHostAssignType(AssignConstant.ASSIGN_TO_USER);
        } else {
            dto.setHostAssignType(AssignConstant.ASSIGN_TO_UNIT);
        }
        dto.isValid();
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveExportWorkOrder(WorkOrderExportDTO dto) throws ServiceException {
        WorkOrderDO workOrderDO = exportWorkOrderConvert(dto);
        BaseUtils.copyProperties(dto, workOrderDO);
        saveWorkOrder(workOrderDO, dto);
        ccMapper.delete(new QueryWrapper<CCDO>().eq("work_order_id", workOrderDO.getId()));
        List<CCExportDTO> ccDTOs = dto.getCcDTOs();
        if (!CollectionUtils.isEmpty(ccDTOs)) {
            for (CCExportDTO ccDTO : ccDTOs) {
                CCDO ccdo = ccUtils.ccExportDtoToCcdo(ccDTO, dto);
                ccdo.setExportFromOther(1);
                ccMapper.insert(ccdo);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestfulResults startExecuteOneWorkOrder(WorkOrderDTO dto) throws ServiceException {
        WorkOrderDO workOrderDO = commonConvert(dto);

        Map<Long, UnitVO> unitVOMap = unitUtils.findUnit(dto.getUnitIdSet());
        for (UnitVO unitVO : unitVOMap.values()) {
            dto.getUsernameSet().add(unitVO.getUnitMaster());
        }
        CommonInfo info = new CommonInfo(unitVOMap, userExternalUtils.findUserListByUserName(dto.getUsernameSet()));

        if (workOrderDO.getWorkOrderTypeId() != null) {
            workOrderDO.setWorkOrderTypeName(workOrderTypeUtils.findWorkOrderTypeById(workOrderDO.getWorkOrderTypeId()).getTypeName());
        } else {
            workOrderDO.setWorkOrderTypeName(null);
        }
        if(AssignConstant.ASSIGN_TO_UNIT.equals(workOrderDO.getHostAssignType())){
            workOrderDO.setHostUsername(info.getUnitVO(workOrderDO.getHostUnitId()).getUnitMaster());
        }
        workOrderDO.setHostTruename(info.getUserVO(workOrderDO.getHostUsername()).getTrueName());
        workOrderDO.setHostUnit(info.getUnitVO(dto.getHostUnitId()).getUnitName());
        if(AssignConstant.ASSIGN_TO_UNIT.equals(workOrderDO.getDealAssignType())){
            workOrderDO.setDealUsername(info.getUnitVO(workOrderDO.getDealUnitId()).getUnitMaster());
        }
        workOrderDO.setDealTruename(info.getUserVO(workOrderDO.getDealUsername()).getTrueName());
        workOrderDO.setDealUnit(info.getUnitVO(workOrderDO.getDealUnitId()).getUnitName());
        if (workOrderDO.getSiteId() != null) {
            SiteRelationVO siteRelationVO = siteRelationUtils.findSiteBySiteId(workOrderDO.getSiteId());
            workOrderDO.setSitename(siteRelationVO.getSiteName());
            workOrderDO.setMediaType(siteRelationVO.getMediaType());
        } else {
            workOrderDO.setSitename(null);
        }

        commonSaveOrUpdate(dto, workOrderDO);
        dto.setNewCCDTOs(ccUtils.cc(workOrderDO, dto.getCcDTOs(), dto.getOldCCDOs(), info));

        return RestfulResults.ok(ResultVO.createSuccess("保存工单", "保存工单成功"));
    }

    @Override
    public void endExecuteOneWorkOrder(BaseDTO dto) throws ServiceException {

    }
}
