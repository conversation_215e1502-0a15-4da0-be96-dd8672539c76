package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：CopyMy
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:36
 **/
@Component
public class MyDealCC extends WorkOrderSearchTypeMgr {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        UnitVO loginUnit = unitUtils.getLoginUnit();
        List<Long> workOrderIdList = queryCCToMe(loginUnit, false, WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING);
        return Optional.ofNullable(m -> m.in("id", workOrderIdList));
    }

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_CC);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_CC);
    }
}
