package com.trs.gov.workorder.mgr.condition.field;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderSearchDtoFieldContants;
import com.trs.gov.workorder.mgr.condition.BaseCommonFieldMgr;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：FieldExpectedEndDate
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:08
 **/
@Component
public class FieldForExpectedEndDate extends BaseCommonFieldMgr<WorkOrderDO> {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        String expectEndDate = (String) keyWords;
        if(!CMyString.isEmpty(expectEndDate)){
            String[] dateList = expectEndDate.split(",");
            if (dateList.length != 2) {
                throw new ServiceException("期望完成时间格式有误！");
            }
            Date expectedEndDateStart = TimeUtils.stringToDate(dateList[0]+" 00:00:00", TimeUtils.YYYYMMDD_HHMMSS);
            Date date = TimeUtils.stringToDate(dateList[1], TimeUtils.YYYYMMDD);
            Date expectedEndDateEnd = TimeUtils.stringToDate(dateList[1]+" 23:59:59", TimeUtils.YYYYMMDD_HHMMSS);
            return Optional.ofNullable(m -> m.between(searchField(), expectedEndDateStart, expectedEndDateEnd));
        }
        return Optional.empty();
    }

    @Override
    public String key() {
        return WorkOrderSearchDtoFieldContants.EXPECT_END_TIME;
    }

    @Override
    public String desc() {
        return WorkOrderSearchDtoFieldContants.EXPECT_END_TIME;
    }

    @Override
    public String searchField() {
        return WorkOrderSearchDtoFieldContants.DB_FIELD_EXPECT_END_TIME;
    }
}
