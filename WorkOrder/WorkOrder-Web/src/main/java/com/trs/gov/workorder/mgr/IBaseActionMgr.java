package com.trs.gov.workorder.mgr;

import com.trs.common.base.Reports;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.DTO.WorkOrderBaseDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.service.ISupportKey;
import com.trs.user.DTO.UserDTO;
import com.trs.web.builder.base.RestfulResults;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 基础的动作业务接口
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-12 12:51
 * @version 1.0
 * @since 1.0
 */
public interface IBaseActionMgr extends ISupportKey {

    /**
     * 操作是否支持
     * @param supportActionDTO
     * @return
     */
    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO);

    /**
     * 操作是否支持
     * @param workOrderBaseDTO
     * @return
     */
    public boolean isSupportAction(WorkOrderBaseDTO workOrderBaseDTO) throws ServiceException;

    /**
     * 相关业务的主入口<BR>
     *
     * @param dto       传入的请求参数
     * @param _sOprName 对应的操作（用于权限校验）
     * @param _sOprDesc 操作描述
     * @return Reports 操作报告
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:04
     */
    public Reports executeByIds(BaseDTO dto, String _sOprName, String _sOprDesc) throws ServiceException;

    /**
     * 初始化相关参数<BR>
     *
     * @param dto 请求参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:06
     */
    public void initParameterParse(BaseDTO dto) throws ServiceException;



    /**
     * 进行权限校验<BR>
     *
     * @param user      用户
     * @param workOrder 工单
     * @param _sOprName 操作名
     * @param _sOprDesc 操作描述
     * @throws ServiceException 无权限的异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:07
     */
    public void validateRight(UserDTO user, WorkOrderVO workOrder, String _sOprName, String _sOprDesc) throws ServiceException;

    /**
     * 开始进行相关操作<BR>
     *
     * @param dto       请求参数
     * @return 操作报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:11
     */
    public RestfulResults startExecuteOneWorkOrder(BaseDTO dto) throws ServiceException;

    /**
     * 结束进行相关操作<BR>
     *
     * @param dto       请求参数
     * @param workOrder 工单
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:11
     */
    public void endExecuteOneWorkOrder(BaseDTO dto, WorkOrderVO workOrder) throws ServiceException;

    /**
     * 操作结束后记录日志等信息<BR>
     *
     * @param dto 工单操作
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:15
     */
    public void recordLogAfterExecute(BaseDTO dto) throws ServiceException;

    /**
     * 返回相关结果，防止需要返回非Reports的结果<BR>
     *
     * @return 相关结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:11
     */
    public Object getExecuteResult() throws ServiceException;

    /**
     * 返回相关结果，防止需要返回非Reports的结果<BR>
     *
     * @param reports 相关运行保存
     * @return 相关结果
     * @throws ServiceException
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:14
     */
    public Object getExecuteResult(Reports reports) throws ServiceException;


    /**
     * 发送消息
     * @param baseDTO
     */
    public void sendMessage(BaseDTO baseDTO);

}
