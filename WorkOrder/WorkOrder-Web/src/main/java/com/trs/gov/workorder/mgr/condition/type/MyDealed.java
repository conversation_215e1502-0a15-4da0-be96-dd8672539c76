package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：MyDealed
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:46
 **/
@Component
public class MyDealed extends WorkOrderSearchTypeMgr {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        UnitVO loginUnit = unitUtils.getLoginUnit();
        //历史是处理人、抄送人的工单
        List<Long> relatedWorkOrderIdList = oprRecordUtils.getRelatedWorkOrderId(false, true,
                OperateNameConstant.CREATE_WORK_ORDER,
                OperateNameConstant.ASSIGN_WORK_ORDER,
                OperateNameConstant.ROLLBACK_WORK_ORDER,
                OperateNameConstant.UPDATE_WORK_ORDER_DEAL,
                OperateNameConstant.COPY_WORK_ORDER);
        //创建人交办或修改了受理人
        relatedWorkOrderIdList.addAll(oprRecordUtils.getRelatedWorkOrderId(true, true,
                OperateNameConstant.ASSIGN_WORK_ORDER,
                OperateNameConstant.UPDATE_WORK_ORDER_DEAL));
        if (relatedWorkOrderIdList.isEmpty()) {
            relatedWorkOrderIdList.add(0L);
        }
        Consumer<QueryWrapper<WorkOrderDO>> consumer1 = m -> m.in("id", relatedWorkOrderIdList);

        Consumer<QueryWrapper<WorkOrderDO>> consumerMyDeal = consumerMyDeal(loginUnit, false, WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING);
        List<Long> ccToMe = queryCCToMe(loginUnit, false, WorkOrderConstant.STATUS_WAIT_ASSIGN, WorkOrderConstant.STATUS_DEALING);
        Consumer<QueryWrapper<WorkOrderDO>> consumerMyDealFinished = consumerMyDeal(loginUnit, false, WorkOrderConstant.STATUS_FINISHED, WorkOrderConstant.STATUS_REVIEWED, WorkOrderConstant.STATUS_OPENED);
        List<Long> ccToMeFinished = queryCCToMe(loginUnit, false, WorkOrderConstant.STATUS_FINISHED, WorkOrderConstant.STATUS_REVIEWED, WorkOrderConstant.STATUS_OPENED);

        Consumer<QueryWrapper<WorkOrderDO>> consumer = m -> m
                .and(i -> i
                        .or(consumer1)
                        //没有操作记录也能查出当前已处理的工单
                        .or(consumerMyDealFinished)
                        .or().in("id", ccToMeFinished))
                //去掉是当前受理人、抄送给我，未处理的工单
                .and(i -> i
                        .notIn("id", ccToMe)
                        .not(consumerMyDeal));

        return Optional.ofNullable(consumer);
    }

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_DEALED);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_DEAL_DEALED);
    }
}
