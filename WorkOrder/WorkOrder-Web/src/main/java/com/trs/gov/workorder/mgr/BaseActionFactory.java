package com.trs.gov.workorder.mgr;

import com.trs.gov.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class BaseActionFactory {
    @Autowired
    private List<IBaseActionMgr> mgrs;

    public IBaseActionMgr getActionMgr(String key) throws ServiceException {
        Optional<IBaseActionMgr> optional = mgrs.stream().filter(mgr -> mgr.key().equals(key)).findFirst();
        if(!optional.isPresent()){
            throw new ServiceException("未知操作【"+key+"】");
        }
        return optional.get();
    }
}
