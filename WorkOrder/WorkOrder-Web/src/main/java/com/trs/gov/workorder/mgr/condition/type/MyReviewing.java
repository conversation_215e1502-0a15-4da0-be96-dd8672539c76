package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：WaitMyReview
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:47
 **/
@Component
public class MyReviewing extends WorkOrderSearchTypeMgr {
    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        UnitVO loginUnit = unitUtils.getLoginUnit();
        return Optional.ofNullable(m -> m.and(consumerMyHostAndCrHost(loginUnit, true, WorkOrderConstant.STATUS_FINISHED)));
    }

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_REVIEM_REVIEWING);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_MY_REVIEM_REVIEWING);
    }
}
