package com.trs.gov;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@MapperScan("com.trs.gov.workorder.dao")
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.trs")
@Slf4j
@EnableTransactionManagement
//@EnableSwagger2
public class WorkOrderApplication {
    public static void main(String[] args) {
        SpringApplication.run(WorkOrderApplication.class, args);
        log.info("WorkOrder-Service模块启动成功!");
    }
}
