package com.trs.gov.workorder.utils.external;

import com.trs.common.base.Report;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.OprRecordDTO;
import com.trs.gov.interaction.DTO.QueryWorkOrderIdsDTO;
import com.trs.gov.interaction.DTO.StatisticsCountDTO;
import com.trs.gov.interaction.service.OprRecordService;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 操作记录工具类
 */
@Component
public class OprRecordUtils extends BaseExternalUtils {
    @Reference(check = false, timeout = 60000)
    protected OprRecordService oprRecordService;

    /**
     * 根据操作记录获取跟当前用户有关系的工单
     * @param oprRecordTypes 操作列表
     * @return
     * @throws ServiceException
     */
    public List<Long> getRelatedWorkOrderId(boolean myCreated, boolean toUser, String... oprRecordTypes) throws ServiceException {
        QueryWorkOrderIdsDTO queryWorkOrderIdsDTO = new QueryWorkOrderIdsDTO();
        List<String> oprRecordTypeList = new ArrayList<>();
        for (String oprRecordType : oprRecordTypes) {
            oprRecordTypeList.add(oprRecordType);
        }
        queryWorkOrderIdsDTO.setOprRecordType(oprRecordTypeList);

        if(toUser){
            queryWorkOrderIdsDTO.setAssignType(AssignConstant.ASSIGN_TO_USER);
        }else{
            queryWorkOrderIdsDTO.setAssignType(AssignConstant.ASSIGN_TO_UNIT);
        }

        if(myCreated){
            queryWorkOrderIdsDTO.setCrUnit(LoginInfoUtils.getLoginUnitId());
            queryWorkOrderIdsDTO.setCrUser(LoginInfoUtils.getLoginUser());
        }else {
            queryWorkOrderIdsDTO.setTargetUnit(LoginInfoUtils.getLoginUnitId());
            queryWorkOrderIdsDTO.setTargetUser(LoginInfoUtils.getLoginUser());
        }

        RestfulResults<List<Long>> restfulResults = oprRecordService.listWorkOrderIdByUserAndUnitAndType(queryWorkOrderIdsDTO);
        checkRestfulResults(restfulResults);
        if(restfulResults.getDatas()==null){
            return new ArrayList<Long>();
        }
        return restfulResults.getDatas();
    }

    /**
     * 构建基础操作记录
     * @return
     */
    public OprRecordDTO buildBaseOprRecord() throws ServiceException {
        OprRecordDTO oprRecordDTO = new OprRecordDTO();
        oprRecordDTO.setCrUser(new WorkOrderDO().getCrUser());
        oprRecordDTO.setCrUnitId(LoginInfoUtils.getLoginUnitId());
        return oprRecordDTO;
    }

    /**
     * 保存操作记录
     * @param oprRecordDTO
     */
    public void saveOprRecord(Logger log, OprRecordDTO oprRecordDTO) throws ServiceException {
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, oprRecordDTO);
            RestfulResults<Report> restfulResults = oprRecordService.saveOprRecord(oprRecordDTO);
            checkRestfulResults(restfulResults);
        } catch (Exception e) {
            log.error("保存"+oprRecordDTO.getOprKey()+"操作记录失败， ", e);
            throw new ServiceException("保存"+oprRecordDTO.getOprKey()+"操作记录失败， ", e);
        }
    }

    /**
     * 获取操作记录数量
     *
     * @param workOrderId
     * @param type
     * @return
     * @throws ServiceException
     */
    public long getCommentNum(long workOrderId, String type) throws ServiceException {
        StatisticsCountDTO statisticsCountDTO = new StatisticsCountDTO();
        statisticsCountDTO.setWorkOrderId(workOrderId);
        statisticsCountDTO.setType(type);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, statisticsCountDTO);
        RestfulResults<Long> restfulResults = oprRecordService.addCommentNum(statisticsCountDTO);
        checkRestfulResults(restfulResults);
        return restfulResults.getDatas();
    }
}
