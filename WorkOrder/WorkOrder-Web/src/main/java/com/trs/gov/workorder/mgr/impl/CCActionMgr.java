package com.trs.gov.workorder.mgr.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.common.utils.JsonUtils;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.interaction.DTO.OprRecordDTO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.CCDTO;
import com.trs.gov.workorder.DTO.CCListDTO;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.mgr.AbstractActionMgr;
import com.trs.gov.workorder.utils.CCUtils;
import com.trs.gov.workorder.utils.CommonInfo;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.gov.workorder.utils.TargetDTOUtilts;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工单-抄送
 */
@Component
@Slf4j
public class CCActionMgr extends AbstractActionMgr {
    @Autowired
    private CCMapper ccMapper;
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private CCUtils ccUtils;
    @Autowired
    private TargetDTOUtilts targetDTOUtilts;

    @Override
    public String key() {
        return OperateNameConstant.COPY_WORK_ORDER;
    }

    @Override
    public String desc() {
        return OperateNameConstant.getTypeDesc(OperateNameConstant.COPY_WORK_ORDER).get();
    }

    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO) {
        return (super.isSupportAction(supportActionDTO) ||
                workOrderRoleUtils.isWorkOrderRole(supportActionDTO,
                        WorkOrderRoleConstant.DEAL_UNIT_MASTER,
                        WorkOrderRoleConstant.DEAL_USER,
                        WorkOrderRoleConstant.CC_UNIT_MASTER,
                        WorkOrderRoleConstant.CC_USER,
                        WorkOrderRoleConstant.CR_USER,
                        WorkOrderRoleConstant.HOST_UNIT_MASTER,
                        WorkOrderRoleConstant.HOST_USER)) &&
                (supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_WAIT_ASSIGN ||
                        supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_DEALING);
    }

    @Override
    public void initParameterParse(BaseDTO dto) throws ServiceException {
        CCListDTO ccListDTO = (CCListDTO) dto;
        if (!StringUtils.isEmpty(ccListDTO.getCclist())) {
            List<CCDTO> ccDTOs = JsonUtils.toObject(ccListDTO.getCclist(), List.class, CCDTO.class);
            PreConditionCheck.checkArgument(ccDTOs != null, "抄送列表格式有误");
            for (CCDTO ccdto : ccDTOs) {
                ccdto.isValid();
                if (ccdto.getTargetUnitId()!=null){
                    ccListDTO.getUnitIdSet().add(ccdto.getTargetUnitId());
                }
                if (!StringUtils.isEmpty(ccdto.getTargetUsername())) {
                    ccListDTO.getUsernameSet().add(ccdto.getTargetUsername());
                }
            }
            ccDTOs = ccDTOs.stream().distinct().collect(Collectors.toList());
            targetDTOUtilts.removeRepeat(ccDTOs, false);
            ccListDTO.setCcDTOs(ccDTOs);
        }
        ccListDTO.getUnitIdSet().add(LoginInfoUtils.getLoginUnitId());
        ccListDTO.getUsernameSet().add(LoginInfoUtils.getLoginUser());
        ccListDTO.isValid();
    }

    @Override
    public RestfulResults startExecuteOneWorkOrder(BaseDTO dto) throws ServiceException {
        CCListDTO ccListDTO = (CCListDTO) dto;
        WorkOrderDO workOrderDO = workOrderMapper.selectById(ccListDTO.getWorkOrderId());
        if (workOrderDO == null) {
            throw new ServiceException("工单【" + ccListDTO.getWorkOrderId() + "】不存在");
        }

        Map<Long, UnitVO> unitVOMap = unitUtils.findUnit(ccListDTO.getUnitIdSet());
        for (UnitVO unitVO : unitVOMap.values()) {
            ccListDTO.getUsernameSet().add(unitVO.getUnitMaster());
        }
        CommonInfo info = new CommonInfo(unitVOMap, userExternalUtils.findUserListByUserName(ccListDTO.getUsernameSet()));

        ccListDTO.getNewCCDTOs().addAll(ccUtils.cc(workOrderDO, ccListDTO.getCcDTOs(), null, info));

        return RestfulResults.ok(new Report(desc(), desc()+"成功", Report.RESULT.SUCCESS));
    }

    @Override
    public void recordLogAfterExecute(BaseDTO dto) throws ServiceException {
        CCListDTO ccListDTO = (CCListDTO) dto;
        OprRecordDTO oprRecordDTO = new OprRecordDTO();
        BaseUtils.setUserInfoToDTO(oprRecordDTO);
        oprRecordDTO.setWorkOrderId(Long.valueOf(ccListDTO.getWorkOrderId()));
        oprRecordDTO.setOprKey(OperateNameConstant.COPY_WORK_ORDER);
        oprRecordDTO.setCrUser(new WorkOrderDO().getCrUser());
        if (!CollectionUtils.isEmpty(ccListDTO.getNewCCDTOs())) {
            for (CCDTO ccDTO : ccListDTO.getNewCCDTOs()) {
                oprRecordDTO.setAssignType(ccDTO.getType());
                oprRecordDTO.setGroupId(ccDTO.getGroupId());
                oprRecordDTO.setTargetUnitId(ccDTO.getTargetUnitId());
                oprRecordDTO.setTargetUser(ccDTO.getTargetUsername());
                oprRecordUtils.saveOprRecord(log, oprRecordDTO);
            }
        }
    }

    @Override
    public void sendMessage(BaseDTO baseDTO) {
        CCListDTO dto = (CCListDTO) baseDTO;
        if (CollectionUtils.isEmpty(dto.getNewCCDTOs())){
            return;
        }
        CreateNoticeDTO createNoticeDTO;
        try {
            createNoticeDTO = messageUtils.buildbaseMessage(Long.valueOf(dto.getWorkOrderId()));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送消息失败", e);
            return;
        }

        //清空现在工单所有的抄送人员
        if (createNoticeDTO.getOldCopyUser() != null) {
            createNoticeDTO.getOldCopyUser().clear();
        }
        //只给新增的抄送人员发消息
        List<TargetUserDTO> cclist = new ArrayList<>();
        for (CCDTO ccDTO : dto.getNewCCDTOs()) {
            cclist.add(new TargetUserDTO(ccDTO.getTargetUsername(), ccDTO.getTargetUnitId(), ccDTO.getGroupId(), ccDTO.getType()));
        }
        createNoticeDTO.setNewCopyUser(cclist);

        createNoticeDTO.setMessageConfigType(key());

        messageUtils.sendNoticeMessage(log, createNoticeDTO);
    }
}
