package com.trs.gov.workorder.mgr.impl;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.SupportActionDTO;
import com.trs.gov.workorder.DTO.WorkOrderActionDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.mgr.AbstractActionMgr;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 工单-解决
 */
@Component
@Slf4j
public class FinishActionMgr extends AbstractActionMgr {
    @Override
    public String key() {
        return OperateNameConstant.FINISH_WORK_ORDER;
    }

    @Override
    public String desc() {
        return OperateNameConstant.getTypeDesc(OperateNameConstant.FINISH_WORK_ORDER).get();
    }

    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO) {
        return (super.isSupportAction(supportActionDTO) ||
                workOrderRoleUtils.isWorkOrderRole(supportActionDTO,
                        WorkOrderRoleConstant.DEAL_UNIT_MASTER,
                        WorkOrderRoleConstant.DEAL_USER)) &&
                (supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_WAIT_ASSIGN ||
                        supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_DEALING);
    }

    @Override
    public void initParameterParse(BaseDTO baseDTO) throws ServiceException {
        baseDTO.isValid();
    }

    @Override
    public RestfulResults startExecuteOneWorkOrder(BaseDTO baseDTO) throws ServiceException {
        WorkOrderDO workOrderDO = commonConvert(baseDTO);
        workOrderDO.setStatus(WorkOrderConstant.STATUS_FINISHED);
        //单位负责人直接完成了工单，也算响应了
        if(workOrderDO.getActionTime()==null){
            WorkOrderActionDTO dto = (WorkOrderActionDTO) baseDTO;
            //存入响应状态，以便发送响应消息
            dto.setAction(true);
            workOrderDO.setActionTime(new Date());
        }
        workOrderDO.setFinishTime(new Date());

        return commonDoAction(workOrderDO);
    }

    @Override
    public void endExecuteOneWorkOrder(BaseDTO dto, WorkOrderVO workOrder) throws ServiceException {

    }
}
