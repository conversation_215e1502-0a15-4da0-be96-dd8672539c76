package com.trs.gov.workorder.mgr.condition;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.CCSearchDTO;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.gov.workorder.utils.external.OprRecordUtils;
import com.trs.gov.workorder.utils.external.UnitGroupUtils;
import com.trs.gov.workorder.utils.external.UnitUtils;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @ClassName：WorkOrderSearchType
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:34
 **/
@Component
public class BaseSearchMgr {
    @Autowired
    protected OprRecordUtils oprRecordUtils;
    @Autowired
    protected CCMapper ccMapper;
    @Autowired
    protected UnitGroupUtils unitGroupUtils;
    @Autowired
    protected UnitUtils unitUtils;

    public String getLoginUser(){
        return ContextHelper.getLoginUser().get();
    }

    /**
     * @Description  待我处理的查询条件
     * @Param [loginUnit, isUnit, workOrderStatus]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 17:24
     **/
    public Consumer<QueryWrapper<WorkOrderDO>> consumerMyDeal(UnitVO loginUnit, boolean isUnit, Integer... workOrderStatus) {
        String loginUsername = new WorkOrderDO().getCrUser();
        return m -> m.in(!ArrayUtils.isEmpty(workOrderStatus), "status", workOrderStatus)
                .eq("deal_unit_id", loginUnit.getId())
                .and(!isUnit, i -> i
                        .and(j -> j
                                .eq("deal_assign_type", AssignConstant.ASSIGN_TO_UNIT)
                                .eq(!loginUsername.equals(loginUnit.getUnitMaster()), "id", -1))
                        .or(j -> j
                                .eq("deal_assign_type", AssignConstant.ASSIGN_TO_USER)
                                .eq("deal_username", loginUsername)));
    }

    /**
     * @Description  主办是我的查询条件
     * @Param [loginUnit, isUnit, workOrderStatus]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 17:54
     **/
    public Consumer<QueryWrapper<WorkOrderDO>> consumerMyHost(UnitVO loginUnit, boolean isUnit, Integer... workOrderStatus) {
        return m -> m
                .in(!ArrayUtils.isEmpty(workOrderStatus), "status", workOrderStatus)
                .eq("host_unit_id", loginUnit.getId())
                .and(!isUnit, i -> i
                        .or(j -> j
                                .eq("host_assign_type", AssignConstant.ASSIGN_TO_USER)
                                .eq("host_username", getLoginUser()))
                        .or().gt(getLoginUser().equals(loginUnit.getUnitMaster()), "id", 0));
    }

    /**
     * //主办单位不可选，只能是指定到单位，没有个人选项了
     * @Description  主办和发起方是我的查询条件
     * @Param [loginUnit, isUnit, workOrderStatus]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 17:54
     **/
    public Consumer<QueryWrapper<WorkOrderDO>> consumerMyHostAndCrHost(UnitVO loginUnit, boolean isUnit, Integer... workOrderStatus) {
        return m -> m
                .in(!ArrayUtils.isEmpty(workOrderStatus), "status", workOrderStatus)
                .and(l ->l.eq("host_unit_id", loginUnit.getId()).eq("host_username", getLoginUser())
                        .or(k ->k.eq("cr_unit_id", loginUnit.getId())
                                .eq("cr_username",getLoginUser())))
                .and(!isUnit, i -> i
                        .or(j -> j
                                .eq("host_assign_type", AssignConstant.ASSIGN_TO_USER)
                                )
                        .or().gt(getLoginUser().equals(loginUnit.getUnitMaster()), "id", 0));
    }

    /**
     * @Description  抄送给我的工单列表
     * @Param [loginUnit, isUnit, workOrderStatus]
     * @return java.util.List<java.lang.Long>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/12/15 17:30
     **/
    public List<Long> queryCCToMe(UnitVO loginUnit, boolean isUnit, Integer... workOrderStatus) throws ServiceException {
        List<Long> loginGroupIds = unitGroupUtils.getLoginGroupIds();
        String loginUser = LoginInfoUtils.getLoginUser();
        QueryWrapper<CCDO> queryWrapper = new QueryWrapper<>();
        Consumer<QueryWrapper<CCDO>> consumerAll = m -> m.eq("type", AssignConstant.ASSIGN_TO_ALL);
        Consumer<QueryWrapper<CCDO>> consumerGroup = m -> m
                .and(j -> j
                        .eq("type", AssignConstant.ASSIGN_TO_GROUP)
                        .in("group_id", loginGroupIds));
        Consumer<QueryWrapper<CCDO>> consumerUnit = m -> m
                .eq("target_unit_id", loginUnit.getId())
                .and(!isUnit, i -> i
                        .and(j -> j
                            .eq("type", AssignConstant.ASSIGN_TO_UNIT)
                            .eq(!getLoginUser().equals(loginUnit.getUnitMaster()), "id", -1))
                        .or(j -> j
                            .eq("type", AssignConstant.ASSIGN_TO_USER)
                            .eq("target_username", getLoginUser())));
        queryWrapper
                .inSql(!ArrayUtils.isEmpty(workOrderStatus), "work_order_id", "select id from work_order where status in(" + StringUtils.join(workOrderStatus, ",") + ")")
                .and(i-> i
                        .and(loginUser.equals(loginUnit.getUnitMaster()), consumerAll)
                        .or(!CollectionUtils.isEmpty(loginGroupIds) && (isUnit || loginUser.equals(loginUnit.getUnitMaster())), consumerGroup)
                        .or(consumerUnit));
        List<CCDO> ccdos = ccMapper.selectList(queryWrapper);
        List<Long> ids = ccdos.stream().map(CCDO::getWorkOrderId).collect(Collectors.toList());
        if (ids.isEmpty()) {
            //避免sql中in()的情况
            ids.add(-1L);
        }
        return ids;
    }

    /**
     * 根据抄送查询工单id列表
     * @param ccSearchDTO
     * @return
     */
    public List<Long> queryWorkOrderIdsByCCSearch(CCSearchDTO ccSearchDTO) throws ServiceException {
        return queryWorkOrderIdsByCCSearch(ccSearchDTO.getCcUnitIdList());
    }

    public List<Long> queryWorkOrderIdsByCCSearch(String ccUnitIds) throws ServiceException {
        QueryWrapper<CCDO> queryWrapper = new QueryWrapper<>();
        List<Long> groupIds = unitGroupUtils.getGroupIdsByUnitIds(ccUnitIds);
        queryWrapper
                .or().in(!StringUtils.isEmpty(ccUnitIds), "target_unit_id", ccUnitIds.split(","))
                .or().in(!CollectionUtils.isEmpty(groupIds), "group_id", groupIds)
                .or().eq("type", AssignConstant.ASSIGN_TO_ALL);
        List<CCDO> ccdoList = ccMapper.selectList(queryWrapper);
        return ccdoList.stream().map(CCDO::getWorkOrderId).distinct().collect(Collectors.toList());
    }

    /**
     * 我创建、主办、受理或下属单位的工单查询条件
     * @return
     * @throws ServiceException
     */
    public Consumer<QueryWrapper<WorkOrderDO>> queryByMyUnit() throws ServiceException {
        Long loginUnitId = LoginInfoUtils.getLoginUnitId();
        List<Long> childrenUnitIds = unitUtils.findChildrenUnitIdsByUnitId(loginUnitId);
        return m -> m
                .or().eq("cr_unit_id", loginUnitId)
                .or().eq("host_unit_id", loginUnitId)
                .or().eq("deal_unit_id", loginUnitId)
                .or().in(!CollectionUtils.isEmpty(childrenUnitIds), "host_unit_id", childrenUnitIds);
    }
}
