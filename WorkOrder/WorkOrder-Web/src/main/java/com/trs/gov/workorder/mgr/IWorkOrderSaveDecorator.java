package com.trs.gov.workorder.mgr;

import com.trs.common.base.Reports;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.WorkOrderDTO;
import com.trs.gov.workorder.DTO.WorkOrderExportDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.service.ISupportKey;
import com.trs.user.DTO.UserDTO;
import com.trs.web.builder.base.RestfulResults;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 基础的动作业务接口
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-12 12:51
 * @version 1.0
 * @since 1.0
 */
public interface IWorkOrderSaveDecorator extends ISupportKey {
    /**
     * 初始化相关参数<BR>
     *
     * @param dto 请求参数
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:06
     */
    public void initParameterParse(WorkOrderDTO dto) throws ServiceException;

    /**
     * 进行权限校验<BR>
     *
     * @param user      用户
     * @param workOrder 工单
     * @param _sOprName 操作名
     * @param _sOprDesc 操作描述
     * @throws ServiceException 无权限的异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:07
     */
    public void validateRight(UserDTO user, WorkOrderVO workOrder, String _sOprName, String _sOprDesc) throws ServiceException;

    /**
     * 开始进行相关操作<BR>
     *
     * @param dto       请求参数
     * @return 操作报告
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:11
     */
    public RestfulResults startExecuteOneWorkOrder(WorkOrderDTO dto) throws ServiceException;

    /**
     * 结束进行相关操作<BR>
     *
     * @param dto       请求参数
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:11
     */
    public void endExecuteOneWorkOrder(BaseDTO dto) throws ServiceException;

    /**
     * 操作结束后记录日志等信息<BR>
     *
     * @param workOrder 工单
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:15
     */
    public void recordLogAfterExecute(WorkOrderDTO workOrder);

    /**
     * 返回相关结果，防止需要返回非Reports的结果<BR>
     *
     * @return 相关结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:11
     */
    public Object getExecuteResult() throws ServiceException;

    /**
     * 返回相关结果，防止需要返回非Reports的结果<BR>
     *
     * @param reports 相关运行保存
     * @return 相关结果
     * @throws ServiceException
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-12 13:14
     */
    public Object getExecuteResult(Reports reports) throws ServiceException;

    /**
     * 发送消息
     * @param baseDTO
     */
    public void sendMessage(BaseDTO baseDTO);

    /**
     * @Description  【老工单迁移的数据】校验各个工单类型各自的参数
     * @Param [dto]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 9:53
     **/
    public boolean checkParamsValid(WorkOrderExportDTO dto) throws ServiceException;

    /**
     * @Description  【老工单迁移的数据】保存 各自类型的工单
     * @Param [dto]
     * @return com.trs.gov.workorder.DO.WorkOrderDO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 9:53
     **/
    public void saveExportWorkOrder(WorkOrderExportDTO dto) throws ServiceException;
}
