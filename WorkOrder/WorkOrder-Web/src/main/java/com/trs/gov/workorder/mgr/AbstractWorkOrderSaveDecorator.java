package com.trs.gov.workorder.mgr;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.Reports;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.interaction.DTO.OprRecordDTO;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.DTO.TargetUserDTO;
import com.trs.gov.search.service.ISearch;
import com.trs.gov.workorder.DO.CCDO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.*;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.constant.CommonConstant;
import com.trs.gov.workorder.constant.FileConstant;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.dao.CCMapper;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.utils.LoginInfoUtils;
import com.trs.gov.workorder.utils.WorkOrderRoleUtils;
import com.trs.gov.workorder.utils.external.*;
import com.trs.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 抽象业务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-12 13:17
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public abstract class AbstractWorkOrderSaveDecorator implements IWorkOrderSaveDecorator {
    @Autowired
    protected WorkOrderMapper workOrderMapper;
    @Autowired
    protected CCMapper ccMapper;
    @Autowired
    protected MessageUtils messageUtils;
    @Autowired
    protected WorkOrderRoleUtils workOrderRoleUtils;
    @Autowired
    protected UserExternalUtils userExternalUtils;
    @Autowired
    protected UnitUtils unitUtils;
    @Autowired
    protected FileManagerUtils fileManagerUtils;
    @Autowired
    protected WorkOrderTypeUtils workOrderTypeUtils;
    @Autowired
    protected OprRecordUtils oprRecordUtils;

    @Reference(check = false, timeout = 60000)
    private ISearch search;

    @Override
    public String key() {
        return OperateNameConstant.UPDATE_WORK_ORDER;
    }

    @Override
    public String desc() {
        return OperateNameConstant.getTypeDesc(OperateNameConstant.UPDATE_WORK_ORDER).get();
    }

    @Override
    public boolean isSupportAction(SupportActionDTO supportActionDTO) {
        return (userExternalUtils.isAdminAndHasLoginUnit() ||
                workOrderRoleUtils.isWorkOrderRole(supportActionDTO,
                        WorkOrderRoleConstant.CR_USER)) &&
                (supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_WAIT_ASSIGN ||
                        supportActionDTO.getWorkOrderDO().getStatus() == WorkOrderConstant.STATUS_DEALING);
    }

    @Override
    public void validateRight(com.trs.user.DTO.UserDTO user, WorkOrderVO workOrder, String _sOprName, String _sOprDesc) throws ServiceException {

    }

    @Override
    public void endExecuteOneWorkOrder(BaseDTO dto) throws ServiceException{

    };

    @Override
    public Object getExecuteResult() throws ServiceException {
        return null;
    }

    @Override
    public Object getExecuteResult(Reports reports) throws ServiceException {
        return null;
    }

    public WorkOrderDO exportWorkOrderConvert(WorkOrderExportDTO dto){
        WorkOrderDO workOrderDO = new WorkOrderDO();
        BeanUtils.copyProperties(dto,workOrderDO);
        workOrderDO.setIsDelete(CommonConstant.NOT_DELETED);
        workOrderDO.setUpdateTime(new Date());
        workOrderDO.setReceiveTime(new Date());
        return workOrderDO;
    }

    public void saveWorkOrder(WorkOrderDO workOrderDO,WorkOrderExportDTO dto) throws ServiceException {
        workOrderDO.setExportFromOther(1);
        int insert = workOrderMapper.insert(workOrderDO);
        if(insert < 0){
            throw new ServiceException("保存工单数据出错");
        }
        //保存文件
        if(dto.getPiclist() != null && dto.getPiclist().size() > 0){
            fileManagerUtils.saveFile(workOrderDO.getId(), FileConstant.WORK_ORDER_FILE_TYPE, JSONObject.toJSONString(dto.getPiclist().toArray()), FileConstant.PICLIST_FILE_NAME);
        }
        if(dto.getFilelist() != null && dto.getFilelist().size() > 0){
            fileManagerUtils.saveFile(workOrderDO.getId(), FileConstant.WORK_ORDER_FILE_TYPE, JSONObject.toJSONString(dto.getFilelist().toArray()), FileConstant.FILELIST_FILE_NAME);
        }
        dto.setWorkOrderId(workOrderDO.getId());
    }

    /**
     * 通用WorkOrderDTO转换成WorkOrderDO
     * @param dto
     * @return
     */
    protected WorkOrderDO commonConvert(WorkOrderDTO dto) throws ServiceException {
        WorkOrderDO workOrderDO;
        Long id = dto.getId();
        String source = dto.getSource();
        if (id != null && id != 0) {
            workOrderDO = workOrderMapper.selectById(id);
            if (workOrderDO == null) {
                throw new ServiceException("工单【" + id + "】不存在");
            }

            //受理人有变动,投诉修改不更改受理人
            boolean isNewDeal = !dto.getWorkOrderTopTypeId().equals(WorkOrderConstant.TOP_TYPE_COMPLAINT) &&
                    (!ObjectUtils.nullSafeEquals(dto.getDealUnitId(), workOrderDO.getDealUnitId()) ||
                            !ObjectUtils.nullSafeEquals(dto.getDealAssignType(), workOrderDO.getDealAssignType()) ||
                            (AssignConstant.ASSIGN_TO_USER.equals(dto.getDealAssignType()) &&
                                    !ObjectUtils.nullSafeEquals(dto.getDealUsername(), workOrderDO.getDealUsername()) ));
            dto.setIsNewDeal(isNewDeal);

            dto.setIsCreate(false);
            source = workOrderDO.getSource();
        } else {
            workOrderDO = new WorkOrderDO();
            workOrderDO.setWorkOrderTopTypeName(workOrderTypeUtils.findWorkOrderTypeById(dto.getWorkOrderTopTypeId()).getTypeName());
            workOrderDO.setCrUsername(workOrderDO.getCrUser());
            workOrderDO.setCrTruename(userExternalUtils.getTrueNameByUserName(workOrderDO.getCrUser()));
            workOrderDO.setCrUnitId(LoginInfoUtils.getLoginUnitId());
            workOrderDO.setCrUnit(unitUtils.getLoginUnit().getUnitName());
            workOrderDO.setIsDelete(CommonConstant.NOT_DELETED);

            dto.setIsCreate(true);
        }
        BeanUtils.copyProperties(dto, workOrderDO);

        workOrderDO.setUpdateTime(new Date());
        workOrderDO.setReceiveTime(new Date());
        workOrderDO.setSource(source);

        return workOrderDO;
    }

    /**
     * 通用WorkOrder更新操作
     * @param workOrderDTO
     * @param workOrderDO
     * @throws Exception
     */
    protected void commonSaveOrUpdate(WorkOrderDTO workOrderDTO, WorkOrderDO workOrderDO) throws ServiceException {
        Long id = workOrderDO.getId();
        if (id != null && id != 0) {
            workOrderMapper.updateById(workOrderDO);
        } else {
            workOrderMapper.insert(workOrderDO);
            //保存id,方便保存操作记录使用
            workOrderDTO.setId(workOrderDO.getId());
        }
        saveWorkOrderToEs(workOrderDO);
        fileManagerUtils.saveFile(workOrderDO.getId(), FileConstant.WORK_ORDER_FILE_TYPE, workOrderDTO.getPiclist(), FileConstant.PICLIST_FILE_NAME);
        fileManagerUtils.saveFile(workOrderDO.getId(), FileConstant.WORK_ORDER_FILE_TYPE, workOrderDTO.getFilelist(), FileConstant.FILELIST_FILE_NAME);
    }

    /**
     *  保存工单到es
     * @param workOrderDO
     */
    private void saveWorkOrderToEs(WorkOrderDO workOrderDO) {
        try {
            WorkOrderDO newWorkOrderDO = workOrderMapper.selectById(workOrderDO.getId());
            com.trs.gov.search.DO.WorkOrderDO orderDO = new com.trs.gov.search.DO.WorkOrderDO();
            BeanUtils.copyProperties(newWorkOrderDO, orderDO);
            search.saveOrUpdateWorkOrder(orderDO);
        }catch (Exception e){
            log.error("保存工单id:{}到es失败", workOrderDO.getId(), e);
        }
    }

    @Override
    public void recordLogAfterExecute(WorkOrderDTO dto) {
        try {
            WorkOrderDO workOrderDO = workOrderMapper.selectById(dto.getId());
            if(workOrderDO==null){
                throw new ServiceException("工单【"+dto.getId()+"】未找到");
            }
            OprRecordDTO oprRecordDTO = new OprRecordDTO();
            BaseUtils.setUserInfoToDTO(oprRecordDTO);
            oprRecordDTO.setCrUser(new WorkOrderDO().getCrUser());
            oprRecordDTO.setCrUnitId(LoginInfoUtils.getLoginUnitId());
            oprRecordDTO.setWorkOrderId(dto.getId());
            //新增或编辑时更新了受理人
            if(dto.getIsCreate() || dto.getIsNewDeal()){
                oprRecordDTO.setTargetUnitId(dto.getDealUnitId());
                Integer dealAssignType = dto.getDealAssignType();
                if(dealAssignType!=null && (dealAssignType.equals(AssignConstant.ASSIGN_TO_UNIT) || dealAssignType.equals(AssignConstant.ASSIGN_TO_USER))){
                    oprRecordDTO.setAssignType(dealAssignType);
                }
                oprRecordDTO.setTargetUser(dto.getDealUsername());
            }
            if(dto.getIsCreate()){
                oprRecordDTO.setOprKey(OperateNameConstant.CREATE_WORK_ORDER);
            } else if(dto.getIsNewDeal()){
                oprRecordDTO.setOprKey(OperateNameConstant.UPDATE_WORK_ORDER_DEAL);
            } else {
                oprRecordDTO.setOprKey(OperateNameConstant.UPDATE_WORK_ORDER);
            }
            oprRecordUtils.saveOprRecord(log, oprRecordDTO);

            if(!CollectionUtils.isEmpty(dto.getNoticeDTOS())){
                for (NoticeDTO noticeDTO : dto.getNoticeDTOS()) {
                    oprRecordDTO.setOprKey(OperateNameConstant.CREATE_NOTICE);
                    oprRecordDTO.setAssignType(noticeDTO.getType());
                    oprRecordDTO.setGroupId(noticeDTO.getGroupId());
                    oprRecordDTO.setTargetUnitId(noticeDTO.getTargetUnitId());
                    oprRecordDTO.setTargetUser(noticeDTO.getTargetUsername());
                    oprRecordUtils.saveOprRecord(log, oprRecordDTO);
                }
            }

            if(!CollectionUtils.isEmpty(dto.getNewCCDTOs())){
                for (CCDTO ccDTO : dto.getNewCCDTOs()) {
                    oprRecordDTO.setOprKey(OperateNameConstant.COPY_WORK_ORDER);
                    oprRecordDTO.setAssignType(ccDTO.getType());
                    oprRecordDTO.setGroupId(ccDTO.getGroupId());
                    oprRecordDTO.setTargetUnitId(ccDTO.getTargetUnitId());
                    oprRecordDTO.setTargetUser(ccDTO.getTargetUsername());
                    oprRecordUtils.saveOprRecord(log, oprRecordDTO);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
            log.error("保存"+desc()+"时保存操作记录失败", e);
        }
    }

    @Override
    public void sendMessage(BaseDTO dto){
        WorkOrderDTO workOrderDTO = (WorkOrderDTO) dto;
        CreateNoticeDTO createNoticeDTO;
        try {
            createNoticeDTO = messageUtils.buildbaseMessage(workOrderDTO.getId());
        } catch (Exception e){
            e.printStackTrace();
            log.error("创建发送消息失败："+e);
            return;
        }
        createNoticeDTO.setUnitId(dto.getUnitId());
        if(workOrderDTO.getIsCreate()){
            createNoticeDTO.setMessageConfigType(OperateNameConstant.CREATE_WORK_ORDER);
        } else {
            createNoticeDTO.setMessageConfigType(OperateNameConstant.UPDATE_WORK_ORDER);
        }

        if(workOrderDTO.getWorkOrderTopTypeId().equals(WorkOrderConstant.TOP_TYPE_WORK_ORDER)){
            if(!CollectionUtils.isEmpty(workOrderDTO.getNewCCDTOs())){
                List<TargetUserDTO> ccList = new ArrayList<>();
                for (CCDTO ccDTO : workOrderDTO.getNewCCDTOs()) {
                    ccList.add(new TargetUserDTO(ccDTO.getTargetUsername(), ccDTO.getTargetUnitId(), ccDTO.getGroupId(), ccDTO.getType()));

                }
                createNoticeDTO.setNewCopyUser(ccList);
            }
            List<TargetUserDTO> ccList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(workOrderDTO.getOldCCDOs())){
                for (CCDO ccDO : workOrderDTO.getOldCCDOs()) {
                    ccList.add(new TargetUserDTO(ccDO.getTargetUsername(), ccDO.getTargetUnitId(), ccDO.getGroupId(), ccDO.getType()));
                }
            }
            createNoticeDTO.setOldCopyUser(ccList);
        } else if(workOrderDTO.getWorkOrderTopTypeId().equals(WorkOrderConstant.TOP_TYPE_NOTICE) && !CollectionUtils.isEmpty(workOrderDTO.getNoticeDTOS())){
            List<TargetUserDTO> noticeList = new ArrayList<>();
            for (NoticeDTO noticeDTO : workOrderDTO.getNoticeDTOS()) {
                noticeList.add(new TargetUserDTO(noticeDTO.getTargetUsername(), noticeDTO.getTargetUnitId(), noticeDTO.getGroupId(), noticeDTO.getType()));
            }
            createNoticeDTO.setNewCopyUser(noticeList);
        }

        messageUtils.sendNoticeMessage(log, createNoticeDTO);
    }
}
