package com.trs.gov.workorder.utils;

import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DO.WorkTimeDO;
import com.trs.gov.workorder.VO.ActionVO;
import com.trs.gov.workorder.constant.WorkOrderRoleConstant;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.dao.WorkTimeMapper;
import com.trs.gov.workorder.utils.external.UserExternalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class WorkTimeAccessUtils {
    @Autowired
    private WorkOrderRoleUtils workOrderRoleUtils;
    @Autowired
    private WorkTimeMapper workTimeMapper;
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private UserExternalUtils userExternalUtils;

    /**
     * 判断工时权限列表
     * @param workOrderId 只判断添加权限时必填
     * @param workTimeId 只判断添加权限时不填
     * @param oprkeys 操作
     * @return
     * @throws ServiceException
     */
    public List<ActionVO> getWorkTimeAccess(Long workOrderId, Long workTimeId, String... oprkeys) throws ServiceException {
        List<ActionVO> actionVOS = new ArrayList<>();
        if(!ArrayUtils.isEmpty(oprkeys)){
            for (String oprkey : oprkeys) {
                boolean access = workTimeAccess(oprkey, workOrderId, workTimeId);
                if(access){
                    ActionVO actionVO = new ActionVO(oprkey, OperateNameConstant.getTypeDesc(oprkey).orElse("未知操作"));
                    actionVOS.add(actionVO);
                }
            }
        }

        return actionVOS;
    }

    /**
     * 工时权限校验
     * @param oprKey
     * @param workOrderId
     * @param workTimeId
     * @return
     * @throws ServiceException
     */
    public boolean workTimeAccess(String oprKey, Long workOrderId, Long workTimeId) throws ServiceException {
        WorkOrderDO workOrderDO;
        WorkTimeDO workTimeDO = null;
        try {
            if(workTimeId!=null){
                workTimeDO = workTimeMapper.selectById(workTimeId);
                if (workTimeDO == null) {
                    throw new ServiceException("工时【" + workTimeId + "】不存在");
                }
            }
            if(workOrderId==null){
                workOrderId=workTimeDO.getWorkOrderId();
            }
            workOrderDO = workOrderMapper.selectById(workOrderId);
            if (workOrderDO == null) {
                throw new ServiceException("工单【" + workOrderId + "】不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取工时权限异常," + e.getMessage());
        }

        if(workOrderDO.getFinishTime()!=null && new Date().compareTo(TimeUtils.befOrAft(workOrderDO.getFinishTime(), 10, Calendar.DATE))>0){
//            throw new ServiceException("获取工时失败，工单完成10天后，无法操作工时");
            return false;
        }

        if(userExternalUtils.isAdmin()){
            return true;
        }
        switch (oprKey){
            case OperateNameConstant.MAN_HOUR_WORK_ORDER:
                break;
            case OperateNameConstant.UPDATE_MANHOUR:
            case OperateNameConstant.DELETE_MANHOUR:
                String loginUser = LoginInfoUtils.getLoginUser();
                Long loginUnitId = LoginInfoUtils.getLoginUnitId();
                if(!workOrderRoleUtils.isWorkOrderRole(workOrderDO, WorkOrderRoleConstant.CR_USER, WorkOrderRoleConstant.DEAL_UNIT_MASTER, WorkOrderRoleConstant.DEAL_USER) &&
                        !(loginUser.equals(workTimeDO.getCrUsername()) && loginUnitId.equals(workTimeDO.getCrUnitId()))) {
//                    throw new ServiceException("获取工时失败，没有权限");
                    return false;
                }
                break;
            default:
                throw new ServiceException("未知工时操作");
        }

        return true;
    }

    /**
     * @Description  Es中时间为UTC，需要做一次转换
     * @Param [inputDate]
     * @return java.lang.String
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/19 22:11
     **/
    public static Date dateAddHour(Date inputDate,int hour){
        if (inputDate !=null) {
            Calendar c = Calendar.getInstance();
            c.setTime(inputDate);
            c.add(Calendar.HOUR_OF_DAY, hour);
            return c.getTime();
        }
        return inputDate;
    }
}
