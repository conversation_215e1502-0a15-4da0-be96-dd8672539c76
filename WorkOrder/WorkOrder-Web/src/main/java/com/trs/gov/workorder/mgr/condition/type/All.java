package com.trs.gov.workorder.mgr.condition.type;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.constant.CommonConstant;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.mgr.condition.WorkOrderSearchTypeMgr;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @ClassName：AllDealed
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/12/15 16:51
 **/
@Component
public class All extends WorkOrderSearchTypeMgr{

    @Override
    public <T extends BaseDTO> Optional<Consumer<QueryWrapper<WorkOrderDO>>> buildCondition(Object keyWords, T dto) throws ServiceException {
        WorkOrderSearchDTO workOrderSearchDTO = (WorkOrderSearchDTO) dto;
        UnitVO loginUnit = unitUtils.getLoginUnit();
        if (!loginUnit.getId().equals(0L) &&
                (CommonConstant.YES.equals(workOrderSearchDTO.getIsAll()))) {
            return Optional.empty();
        }
        List<Long> relatedWorkOrderIdList = oprRecordUtils.getRelatedWorkOrderId(false, false,
                OperateNameConstant.CREATE_WORK_ORDER,
                OperateNameConstant.ASSIGN_WORK_ORDER,
                OperateNameConstant.ROLLBACK_WORK_ORDER,
                OperateNameConstant.UPDATE_WORK_ORDER_DEAL,
                OperateNameConstant.COPY_WORK_ORDER);
        Consumer<QueryWrapper<WorkOrderDO>> consumer1 = m -> m.in ("id", relatedWorkOrderIdList);

        Consumer<QueryWrapper<WorkOrderDO>> consumer2 = queryByMyUnit();

        Consumer<QueryWrapper<WorkOrderDO>> consumer = m -> m.and(i -> i
                .and(!CollectionUtils.isEmpty(relatedWorkOrderIdList), consumer1)
                .or(consumer2));

        return Optional.ofNullable(consumer);
    }

    @Override
    public String key() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL);
    }

    @Override
    public String desc() {
        return String.valueOf(WorkOrderConstant.SREARCH_ALL);
    }
}
