package com.trs.gov.workorder.mgr.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.base.Report;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.workorder.DO.WorkOrderDO;
import com.trs.gov.workorder.DO.WorkTimeDO;
import com.trs.gov.workorder.DTO.WorkTimeDTO;
import com.trs.gov.workorder.DTO.WorkTimeExportDTO;
import com.trs.gov.workorder.DTO.WorkTimeQueryDTO;
import com.trs.gov.workorder.VO.WorkTimeVO;
import com.trs.gov.workorder.dao.WorkOrderMapper;
import com.trs.gov.workorder.dao.WorkTimeMapper;
import com.trs.gov.workorder.utils.WorkOrderRoleUtils;
import com.trs.gov.workorder.utils.WorkTimeAccessUtils;
import com.trs.gov.workorder.utils.external.UnitUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 工时操作
 */
@Service
@Slf4j
public class WorkTimeMgr {
    @Autowired
    private WorkTimeMapper workTimeMapper;
    @Autowired
    private WorkOrderMapper workOrderMapper;
    @Autowired
    private WorkOrderRoleUtils workOrderRoleUtils;
    @Autowired
    private WorkTimeAccessUtils workTimeAccessUtils;
    @Autowired
    private UnitUtils unitUtils;

    public RestfulResults saveOrUpdateWorkTime(WorkTimeDTO workTimeDTO) throws ServiceException {
        WorkTimeDO workTimeDO;
        Long workTimeId = workTimeDTO.getWorkTimeId();
        String desc = workTimeId == null ? "添加" : "编辑";
        try {
            if (workTimeId != null) {
                workTimeDO = workTimeMapper.selectById(workTimeId);
                if (workTimeDO == null) {
                    return RestfulResults.error("工时【" + workTimeId + "】不存在");
                }
            } else {
                WorkOrderDO workOrderDO = workOrderMapper.selectById(workTimeDTO.getWorkOrderId());
                if (workOrderDO == null) {
                    return RestfulResults.error("工单【" + workTimeDTO.getWorkOrderId() + "】不存在");
                }
                workTimeDO = new WorkTimeDO();
                workTimeDO.setCrUsername(ContextHelper.getLoginUser().get());
                workTimeDO.setCrUnitId(Long.valueOf(ContextHelper.getLoginUnitId().get()));
            }
            BeanUtils.copyProperties(workTimeDTO, workTimeDO);
            workTimeDO.setWorkUnit(unitUtils.findUnitByUnitId(workTimeDO.getWorkUnitId()).getUnitName());

            if (workTimeId != null) {
                workTimeMapper.updateById(workTimeDO);
            } else {
                workTimeMapper.insert(workTimeDO);
            }
            return RestfulResults.ok(new Report(desc+"工时", desc+"工时成功", Report.RESULT.SUCCESS));
        } catch (Exception e) {
            e.printStackTrace();
            log.error(desc+"工时失败", e);
            return RestfulResults.error(desc+"工时失败," + e.getMessage());
        }
    }

    public RestfulResults<List<WorkTimeVO>> findWorkTime(WorkTimeQueryDTO dto) throws ServiceException {
        try {
            List<WorkTimeDO> workTimeDOList = workTimeMapper.selectList(
                    new QueryWrapper<WorkTimeDO>().in(!StringUtils.isEmpty(dto.getWorkOrderId()),"work_order_id", dto.getWorkOrderId().split(",")));
            List<WorkTimeVO> workTimeVOList = new ArrayList<>();
            if(!CollectionUtils.isEmpty(workTimeDOList)){
                for (WorkTimeDO workTimeDO : workTimeDOList) {
                    WorkTimeVO workTimeVO = new WorkTimeVO();
                    BeanUtils.copyProperties(workTimeDO, workTimeVO);
                    workTimeVO.setWorkTimeId(workTimeDO.getId());
                    workTimeVO.setActionList(workTimeAccessUtils.getWorkTimeAccess(workTimeDO.getWorkOrderId(), workTimeDO.getId(),
                            OperateNameConstant.UPDATE_MANHOUR, OperateNameConstant.DELETE_MANHOUR));
                    workTimeVOList.add(workTimeVO);
                }
            }

            return RestfulResults.ok(workTimeVOList);
        } catch (Exception e){
            e.printStackTrace();
            log.error("查找工时失败",e);
            return RestfulResults.error("查找工时失败："+e.getMessage());
        }
    }

    public RestfulResults deleteWorkTime(WorkTimeDTO dto) throws ServiceException {
        Long workTimeId = dto.getWorkTimeId();
        try {
            WorkTimeDO workTimeDO = workTimeMapper.selectById(workTimeId);
            if(workTimeDO==null){
                return RestfulResults.error("工时【"+workTimeId +"】不存在：");
            }
            //保存工单,方便保存操作记录和发送消息
            dto.setWorkOrderId(workTimeDO.getWorkOrderId());
            workTimeMapper.deleteById(workTimeId);
            return RestfulResults.ok(new Report("删除工时", "删除工时成功", Report.RESULT.SUCCESS));
        } catch (Exception e){
            e.printStackTrace();
            log.error("删除工时【"+workTimeId +"】失败,"+e);
            return RestfulResults.error("删除工时【"+workTimeId +"】失败："+e.getMessage());
        }
    }

    public Optional<WorkTimeVO> exportWorkTime(WorkTimeExportDTO dto) throws ServiceException {
        WorkTimeDO workTimeDO = new WorkTimeDO();
        BeanUtils.copyProperties(dto,workTimeDO);
        workTimeDO.setExportFromOther(1);
        workTimeDO.setCrUser(dto.getCrUsername());
        int insert = workTimeMapper.insert(workTimeDO);
        if(insert < 0){
            throw new ServiceException("同步工时记录【"+dto.getOldWorkTimeId()+"】失败!");
        }
        WorkTimeVO workTimeVO = new WorkTimeVO();
        BeanUtils.copyProperties(workTimeDO,workTimeVO);
        workTimeVO.setWorkTimeId(workTimeDO.getId());
        return Optional.of(workTimeVO);
    }
}
