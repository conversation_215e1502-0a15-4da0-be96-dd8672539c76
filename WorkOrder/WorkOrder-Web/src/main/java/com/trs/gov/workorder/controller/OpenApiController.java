package com.trs.gov.workorder.controller;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.workorder.DTO.OpenApiDTO;
import com.trs.gov.workorder.DTO.WorkOrderDTO;
import com.trs.gov.workorder.service.IOpenApiService;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * Description:
 *
 * @author: lv.bo
 * @create: 2025-08-07 16:28
 */
@RestController
@RequestMapping("/workorder/openapi")
public class OpenApiController {

    @Autowired
    private IOpenApiService openApiService;

    @PostMapping("/saveWorkOrder")
    @ApiOperation(value = "保存工单")
    public RestfulResults saveWorkOrder(HttpServletRequest request, OpenApiDTO dto) throws ServiceException {
        return openApiService.saveWorkOrder(request, dto);
    }
}
