<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.workorder.dao.NoticeReadMapper">

    <insert id="insertList" parameterType="list">
        INSERT INTO notice_read
            (
                `id`,
                `cr_time`,
                `cr_user`,
                `work_order_id`,
                `target_unit_id`,
                `target_username`,
                `status`,
                `export_from_other`
            )
        VALUES
            <foreach collection="list" item="noticeRead" separator=",">
                (
                    #{noticeRead.id},
                    #{noticeRead.crTime},
                    #{noticeRead.crUser},
                    #{noticeRead.workOrderId},
                    #{noticeRead.targetUnitId},
                    #{noticeRead.targetUsername},
                    #{noticeRead.status},
                    #{noticeRead.exportFromOther}
                )
            </foreach>
    </insert>

</mapper>