<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.workorder.dao.CCMapper">

    <insert id="insertList" parameterType="list">
        INSERT INTO cc
            (
                `id`,
                `cr_time`,
                `cr_user`,
                `cr_unit_id`,
                `cr_unit`,
                `cr_username`,
                `cr_truename`,
                `work_order_id`,
                `type`,
                `group_id`,
                `target_unit_id`,
                `target_unit`,
                `target_username`,
                `target_truename`,
                `status`,
                `export_from_other`
            )
        VALUES
            <foreach collection="list" item="ccdo" separator=",">
                (
                    #{ccdo.id},
                    #{ccdo.crTime},
                    #{ccdo.crUser},
                    #{ccdo.crUnitId},
                    #{ccdo.crUnit},
                    #{ccdo.crUsername},
                    #{ccdo.crTruename},
                    #{ccdo.workOrderId},
                    #{ccdo.type},
                    #{ccdo.groupId},
                    #{ccdo.targetUnitId},
                    #{ccdo.targetUnit},
                    #{ccdo.targetUsername},
                    #{ccdo.targetTruename},
                    #{ccdo.status},
                    #{ccdo.exportFromOther}
                )
            </foreach>
    </insert>

</mapper>