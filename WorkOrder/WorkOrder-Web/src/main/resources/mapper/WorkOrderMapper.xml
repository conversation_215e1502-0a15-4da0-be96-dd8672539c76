<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.workorder.dao.WorkOrderMapper">
  <resultMap id="BaseResultMap" type="com.trs.gov.workorder.VO.NoticeVO" autoMapping="true">
  </resultMap>

    <select id="queryNotice" resultType="com.trs.gov.workorder.VO.NoticeVO">
        SELECT
            wo.id work_order_id,
            wo.cr_unit_id,
            wo.cr_unit,
            wo.cr_username,
            wo.cr_truename,
            wo.content,
            wo.cr_time
            <if test="dto.type == 0">
                , nr.status
            </if>
        FROM
            work_order wo
            <if test="dto.type == 0">
                LEFT JOIN notice_read nr
                ON nr.work_order_id = wo.id
                and nr.target_unit_id = #{dto.unitId}
                and nr.target_username = #{dto.username}
            </if>
        where
            wo.id in(<include refid="queryWorkOrderId"></include>)
        ORDER BY
            wo.cr_time DESC
    </select>

    <sql id="queryWorkOrderId">
        SELECT
            DISTINCT wo.id work_order_id
        FROM
        work_order wo
        <if test="dto.type == 0">
            LEFT JOIN notice n ON n.work_order_id = wo.id
        </if>
        where 1=1
        <if test="dto.type == 0">
            and (
            n.type = 4
            <if test="groupIdList !=null and groupIdList.size!=0">
                or( n.type = 3
                and group_id in
                <foreach collection="groupIdList" item="groupId" open="(" close=")" separator=",">
                    #{groupId}
                </foreach> )
            </if>
            or( n.type = 2
            and n.target_unit_id = #{dto.unitId}
            AND ( n.target_username IS NULL OR n.target_username = #{dto.username} ) )
            or( n.type = 1
            and n.target_unit_id = #{dto.unitId}
            and n.target_username = #{dto.username})
            )
        </if>
        <if test="dto.type == 1">
            and wo.cr_unit_id = #{dto.unitId}
            and wo.cr_username = #{dto.username}
            and wo.work_order_top_type_id = 3
        </if>
        <if test="dto.crUnitIdList != null and dto.crUnitIdList != ''">
            and wo.cr_unit_id in (#{dto.crUnitIdList})
        </if>
        <if test="dto.crTimeStart != null">
            and wo.cr_time >= #{dto.crTimeStart}
        </if>
        <if test="dto.crTimeEnd != null">
            and wo.cr_time &lt;= #{dto.crTimeEnd}
        </if>
        <if test="dto.content != null and dto.content != ''">
            and wo.content like concat('%', #{dto.content}, '%')
        </if>
        ORDER BY
        wo.cr_time DESC
    </sql>

    <select id="queryNoticeReaded" resultType="com.trs.gov.workorder.VO.NoticeStatisticsVO">
        SELECT
        work_order_id,
        count( CASE WHEN status = 1 THEN 1 ELSE NULL END ) read_count
        FROM
        notice_read
        WHERE
        <if test="list != null and list.size != 0">
            work_order_id in(
            <foreach collection="list" item="workOrderId" separator=",">
                #{workOrderId}
            </foreach>)
        </if>
        GROUP BY
        work_order_id
    </select>

    <select id="queryAllWorkOrderTypeId" resultType="java.lang.Long">
        SELECT DISTINCT
            work_order_type_id type_id
        FROM
            work_order
        WHERE
            work_order_type_id IS NOT NULL
        UNION ALL
        SELECT DISTINCT
            work_order_top_type_id type_id
        FROM
            work_order
        WHERE
            work_order_top_type_id IS NOT NULL
    </select>

</mapper>