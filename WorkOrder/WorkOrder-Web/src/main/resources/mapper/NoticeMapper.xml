<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.workorder.dao.NoticeMapper">

    <insert id="insertList" parameterType="list">
        INSERT INTO notice
            (
                `id`,
                `cr_time`,
                `cr_user`,
                `work_order_id`,
                `type`,
                `group_id`,
                `target_unit_id`,
                `target_unit`,
                `target_username`,
                `target_truename`,
                `export_from_other`
            )
        VALUES
            <foreach collection="list" item="notice" separator=",">
                (
                    #{notice.id},
                    #{notice.crTime},
                    #{notice.crUser},
                    #{notice.workOrderId},
                    #{notice.type},
                    #{notice.groupId},
                    #{notice.targetUnitId},
                    #{notice.targetUnit},
                    #{notice.targetUsername},
                    #{notice.targetTruename},
                    #{notice.exportFromOther}
                )
            </foreach>
    </insert>

</mapper>