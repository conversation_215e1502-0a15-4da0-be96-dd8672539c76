# 基础镜像-JDK环境
FROM harbor.trscd.com.cn/trs/trs-openjdk-arthas:1.8.0_292-internal
# 相关标签信息
LABEL Author=trscd
LABEL Desc=工单系统-统计模块
LABEL Email=<EMAIL>
# 相关环境变量
ENV LANG=en_US.UTF-8 LC_CTYPE=en_US.UTF-8 LC_ALL=en_US.UTF-8
# 定义工作目录的参数值
ARG work_dir
# 设置环境变量
ENV work_dir=/TRS/APP/WorkOrder/StatisticsCenter-Web
# 设置工作目录
WORKDIR ${work_dir}
# 添加介质到工作目录中
COPY target/StatisticsCenter-Web-1.0.jar  ${work_dir}
# 需要开放的端口
EXPOSE 18086 28086
# 日志挂载的路径
VOLUME ["/TRS/LOGS/WorkOrder/StatisticsCenter-Web"]
# 启动脚本
CMD ["java", "-jar", "-Xms2g", "-Xmx2G", "-XX:+PrintGCDetails", "-XX:+HeapDumpOnOutOfMemoryError", "-XX:HeapDumpPath=/TRS/LOGS/WorkOrder/StatisticsCenter-Web/heapdump_pid_%p.hprof", "-Dfile.encoding=utf-8", "/TRS/APP/WorkOrder/StatisticsCenter-Web/StatisticsCenter-Web-1.0.jar"]

