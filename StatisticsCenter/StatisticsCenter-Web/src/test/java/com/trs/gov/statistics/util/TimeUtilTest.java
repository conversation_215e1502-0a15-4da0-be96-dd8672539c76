package com.trs.gov.statistics.util;

import com.trs.common.utils.TimeUtils;
import org.junit.Test;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class TimeUtilTest {
    @Test
    public void testT() {
        List<String> list = TimeUtils.findMonths("2020-01-01 00:00:00", "2020-12-01 00:00:01", "yyyy-MM-dd HH:mm:ss", TimeUtils.YYYYMMDD, false, false);
        System.out.println(list);
        System.out.println(TimeUtils.findTimePeriod(TimeUtils.stringToDate("2020-01-01"), new Date(), Calendar.DATE, "yyyy-MM-dd", false));
    }
}
