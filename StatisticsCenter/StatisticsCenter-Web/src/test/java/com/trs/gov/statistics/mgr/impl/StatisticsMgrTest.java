package com.trs.gov.statistics.mgr.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trs.gov.StatisticsCenterApplication;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.statistics.DTO.*;
import com.trs.gov.statistics.VO.StatusStatisticsVO;
import com.trs.gov.statistics.VO.WorkOrderTypeStatisticsVO;
import com.trs.gov.statistics.constant.TimeSearchFlag;
import com.trs.gov.statistics.mgr.IStatisticsMgr;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = StatisticsCenterApplication.class)
@Slf4j
public class StatisticsMgrTest {

    @Autowired
    private IStatisticsMgr mgr;

    @Test
    public void getWorkTimeStatistics() throws ServiceException {
        WorkTimeStatisticsDTO dto = WorkTimeStatisticsDTO.of(TimeSearchFlag.YEAR);
        dto.setUnitId("12,90,11");
        mgr.getWorkTimeStatistics(dto);
        mgr.getWorkTimeStatistics(WorkTimeStatisticsDTO.of(TimeSearchFlag.DAY, "2020-10-07", "2020-10-09"));
    }

    @Test
    public void getGradeStatistics() throws ServiceException {
        mgr.getGradeStatistics(GradeStatisticsDTO.of(TimeSearchFlag.YEAR));
    }

    @Test
    public void getUnitRankingList() throws ServiceException, JsonProcessingException {
        UnitRankingListStatisticsDTO dto = new UnitRankingListStatisticsDTO();
        dto.setOrderBy("star");
        dto.setTimeFlag(TimeSearchFlag.NONE);
        dto.isValid();
        RestfulResults results = RestfulResults.ok(mgr.getUnitRankingList(dto)).addMsg("成功获取数据！");
        ObjectMapper objectMapper = new ObjectMapper();
        System.out.println(objectMapper.writeValueAsString(results));
    }

    @Test
    public void getWorkOrderType() throws ServiceException, JsonProcessingException {
        WorkOrderTypeStatisticsDTO dto = new WorkOrderTypeStatisticsDTO();
        dto.setTimeFlag(TimeSearchFlag.NONE);
        List<WorkOrderTypeStatisticsVO> list = mgr.getWorkOrderType(dto);
        ObjectMapper objectMapper = new ObjectMapper();
        System.out.println(objectMapper.writeValueAsString(list));
    }

    @Test
    public void getStatusStatistics() throws ServiceException, JsonProcessingException {
        StatusStatisticsDTO dto = new StatusStatisticsDTO();
        List<StatusStatisticsVO> list = mgr.getStatusStatistics(dto);
        ObjectMapper objectMapper = new ObjectMapper();
        System.out.println(objectMapper.writeValueAsString(list));
    }
}