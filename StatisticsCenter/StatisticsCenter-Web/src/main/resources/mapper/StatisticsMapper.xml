<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.statistics.mapper.StatisticsMapper">

    <select id="getWorkTimeStatistics" resultType="com.trs.gov.statistics.DO.WorkTimeStatisticsDO">
        SELECT
        DATE_FORMAT( work_time.cr_time, #{dto.sqlFormat} ) AS time_str,
        SUM(work_time.working_time) AS value
        FROM `work_time` work_time
        <where>
            <![CDATA[ work_time.cr_time <= NOW() ]]>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND work_time.work_unit_id IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.startTime != null &amp;&amp; dto.startTime!=''">
                <![CDATA[ and work_time.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null &amp;&amp; dto.endTime!=''">
                <![CDATA[ and work_time.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
        GROUP BY time_str ORDER BY time_str
    </select>

    <select id="getGradeStatistics" resultType="com.trs.gov.statistics.DO.GradeStatisticsDO">
        SELECT
        grade.grade_unit_id AS unit_id,
        grade.grade_unit AS unit_name,
        AVG( grade.work_order_grade ) AS score
        FROM
        `grade` grade
        <where>
            <![CDATA[ grade.cr_time <= NOW() AND grade.grade_unit_id IS NOT NULL ]]>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND grade.grade_unit_id IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.startTime != null &amp;&amp; dto.startTime!=''">
                <![CDATA[ and grade.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null &amp;&amp; dto.endTime!=''">
                <![CDATA[ and grade.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
        GROUP BY grade.grade_unit_id ORDER BY score DESC
        <if test="dto.topNum != null">
            limit #{dto.topNum}
        </if>
    </select>

    <select id="getStatusStatistics" resultType="java.lang.Long">
        SELECT count(*) from work_order
        <where>
            <![CDATA[ work_order.cr_time <= NOW()]]>
            <if test="dto.startTime != null &amp;&amp; dto.startTime!=''">
                <![CDATA[ and work_order.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null &amp;&amp; dto.endTime!=''">
                <![CDATA[ and work_order.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.userName != null &amp;&amp; dto.userName!=''">
                <![CDATA[ and work_order.deal_username = #{dto.userName} ]]>
            </if>
            <if test="dto.status != null &amp;&amp; dto.status.length>0">
                <![CDATA[ and work_order.`status` IN ]]>
                <foreach collection="dto.status" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND work_order.deal_unit_id IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.workOrderTypeIds != null &amp;&amp; dto.workOrderTypeIds.length>0">
                AND work_order.work_order_top_type_id IN
                <foreach collection="dto.workOrderTypeIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
    </select>

    <select id="getGrowthTrend" resultType="java.lang.Long">
        SELECT count(*) from work_order
        <where>
            <![CDATA[ work_order.cr_time <= NOW()]]>
            <if test="dto.startTime != null">
                <![CDATA[ and work_order.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[ and work_order.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.userName != null">
                <![CDATA[ and work_order.deal_username = #{dto.userName} ]]>
            </if>
            <if test="dto.status != null &amp;&amp; dto.status.length>0">
                <![CDATA[ and work_order.`status` IN ]]>
                <foreach collection="dto.status" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND work_order.deal_unit_id IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.workOrderTypeIds != null &amp;&amp; dto.workOrderTypeIds.length>0">
                AND work_order.work_order_top_type_id IN
                <foreach collection="dto.workOrderTypeIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
    </select>

    <select id="getWorkOrderType" resultType="com.trs.gov.statistics.DO.WorkOrderTypeDO">
        select *
        from work_order_type;
    </select>


    <select id="getWorkOrderTypeStatistics" resultType="com.trs.gov.statistics.DO.WorkOrderTypeStatisticsDO">
        select
        count(*) as `value` ,
        work_order.work_order_type_id as `type`
        from
        work_order
        <where>
            1 = 1
            <if test="dto.topWorkOrderType != null">
                <![CDATA[ and work_order.work_order_top_type_id = #{dto.topWorkOrderType} ]]>
            </if>
            <if test="dto.startTime != null">
                <![CDATA[ and work_order.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[ and work_order.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND work_order.deal_unit_id IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
        group by `type`;
    </select>

    <select id="getOpsSiteStatistics" resultType="com.trs.gov.statistics.DO.OpsSiteStatisticsDO">
        SELECT
        count(*) AS `value`,
        site_relation.operation_unit_id AS unitId,
        unit.unit_name AS name
        FROM
        site_relation JOIN unit ON site_relation.operation_unit_id=unit.id
        GROUP BY operation_unit_id ORDER BY `value` DESC
        <if test="dto.topNum != null">
            limit #{dto.topNum}
        </if>
    </select>

    <select id="getOpsWorkOrderGrowthTrend" resultType="com.trs.gov.statistics.DO.OpsWorkOrderGrowthTrendDO">
        SELECT
        DATE_FORMAT( work_order.cr_time, '${dto.sqlFormat}' ) AS time_str,
        work_order.${dto.unitIdField} AS unit_id,
        unit.unit_name AS unit_name,
        count(1) AS `value`
        FROM `work_order` LEFT JOIN unit ON work_order.${dto.unitIdField} = unit.id
        <where>
            <![CDATA[ work_order.cr_time <= NOW() AND work_order.${dto.unitIdField}>0 ]]>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND work_order.${dto.unitIdField} IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.startTime != null">
                <![CDATA[ and work_order.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[ and work_order.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
        GROUP BY time_str,unit_id
    </select>

    <select id="getUnitWorkOrderNum" resultType="com.trs.gov.statistics.DO.UnitNumDO">
        SELECT
        work_order.${dto.unitIdField} AS unit_id,
        unit.unit_name AS name,
        COUNT( 1 ) AS `value`
        FROM
        `work_order` LEFT JOIN unit ON work_order.${dto.unitIdField}=unit.id
        <where>
            <![CDATA[ work_order.cr_time <= NOW() AND work_order.${dto.unitIdField}>0 ]]>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND work_order.${dto.unitIdField} IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.startTime != null">
                <![CDATA[ and work_order.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[ and work_order.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.status != null &amp;&amp; dto.status.length>0">
                <![CDATA[ and work_order.`status` IN ]]>
                <foreach collection="dto.status" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.workOrderTypeIds != null &amp;&amp; dto.workOrderTypeIds.length>0">
                AND work_order.work_order_top_type_id IN
                <foreach collection="dto.workOrderTypeIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
        GROUP BY unit_id
    </select>

    <select id="getUnitGradeNum" resultType="com.trs.gov.statistics.DO.UnitNumDO">
        SELECT
        grade.grade_unit_id AS unit_id,
        unit.unit_name AS name,
        COUNT( 1 ) AS `value`
        FROM
        `grade` LEFT JOIN unit ON grade.grade_unit_id=unit.id
        <where>
            <![CDATA[ grade.cr_time <= NOW() AND grade.grade_unit_id>0 ]]>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND grade.grade_unit_id IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.startTime != null">
                <![CDATA[ and grade.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[ and grade.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.workOrderTypeIds != null &amp;&amp; dto.workOrderTypeIds.length>0">
                AND grade.work_order_id
                IN (
                select work_order.id from work_order where work_order.work_order_top_type_id IN
                <foreach collection="dto.workOrderTypeIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
        GROUP BY unit_id
    </select>
    <select id="getUnitGradeValue" resultType="com.trs.gov.statistics.DO.GradeDO">
        SELECT
        grade.grade_unit_id AS unit_id,
        unit.unit_name AS name,
        AVG( grade.work_order_grade ) AS star,
        AVG( grade.work_order_grade ) AS attitudeGrade,
        AVG( grade.work_order_grade ) AS complete_time_grade,
        AVG( grade.complete_content ) AS complete_content_grade
        FROM
        `grade` LEFT JOIN unit ON grade.grade_unit_id=unit.id
        <where>
            <![CDATA[ grade.cr_time <= NOW() AND grade.grade_unit_id>0 ]]>
            <if test="dto.unitIds != null &amp;&amp; dto.unitIds.length>0">
                AND grade.grade_unit_id IN
                <foreach collection="dto.unitIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.startTime != null">
                <![CDATA[ and grade.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[ and grade.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.workOrderTypeIds != null &amp;&amp; dto.workOrderTypeIds.length>0">
                AND grade.work_order_id
                IN (
                select work_order.id from work_order where work_order.work_order_top_type_id IN
                <foreach collection="dto.workOrderTypeIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                )
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
        GROUP BY unit_id
    </select>
    <select id="getWorkOrderNum" resultType="java.lang.Long">
        SELECT COUNT( 1 ) AS `value` FROM `work_order`
        <where>
            <![CDATA[ work_order.cr_time <= NOW() ]]>
            <if test="dto.startTime != null">
                <![CDATA[ and work_order.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[ and work_order.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.status != null &amp;&amp; dto.status.length>0">
                <![CDATA[ and work_order.`status` IN ]]>
                <foreach collection="dto.status" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.workOrderTypeIds != null &amp;&amp; dto.workOrderTypeIds.length>0">
                AND work_order.work_order_top_type_id IN
                <foreach collection="dto.workOrderTypeIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
    </select>
    <select id="getUnitNum" resultType="java.lang.Long">
        SELECT COUNT( 1 ) AS `value` FROM `unit`
        <where>
            <![CDATA[ id>0 ]]>
            <if test="dto.startTime != null">
                <![CDATA[ and unit.cr_time >= #{dto.startTime} ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[ and unit.cr_time <= #{dto.endTime} ]]>
            </if>
            <if test="dto.status != null &amp;&amp; dto.status.length>0">
                <![CDATA[ and unit.`status` IN ]]>
                <foreach collection="dto.status" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="dto.unitTypes != null &amp;&amp; dto.unitTypes.length>0">
                <foreach collection="dto.unitTypes" item="id" index="index" open="AND (" close=")" separator=" or ">
                    FIND_IN_SET(#{id},unit.unit_type)
                </foreach>
            </if>
            <if test="dto.unitType != null">
                AND unit.unit_type = #{dto.unitType}
            </if>
            <if test="dto.where != null">
                and (${dto.where})
            </if>
        </where>
    </select>

</mapper>