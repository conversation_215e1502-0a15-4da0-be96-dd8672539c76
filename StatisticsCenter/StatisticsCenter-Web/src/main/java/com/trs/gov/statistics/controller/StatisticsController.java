package com.trs.gov.statistics.controller;

import com.trs.gov.statistics.DTO.*;
import com.trs.gov.statistics.VO.*;
import com.trs.gov.statistics.service.IStatisticsService;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.ApiOperation;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    @Autowired
    private IStatisticsService service;

    @ApiOperation(value = "6.1工单状态统计", notes = "6.1工单状态统计")
    @ResponseBody
    @RequestMapping("/workNum")
    public RestfulResults<List<StatusStatisticsVO>> getStatusStatistics(StatusStatisticsDTO dto) {
        return Try.of(() -> service.getStatusStatistics(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @ApiOperation(value = "6.4工时统计", notes = "6.4工时统计")
    @ResponseBody
    @RequestMapping("/workTimeStatistics")
    public RestfulResults<WorkTimeStatisticsVO> getWorkTimeStatistics(WorkTimeStatisticsDTO dto) {
        return Try.of(() -> service.getWorkTimeStatistics(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }


    @ApiOperation(value = "6.6系统工单走势图", notes = "6.6系统工单走势图")
    @ResponseBody
    @RequestMapping("/getOpsWorkOrderGrowthTrend")
    public RestfulResults<OpsWorkOrderGrowthTrendStatisticsVO> getOpsWorkOrderGrowthTrend(OpsWorkOrderGrowthTrendStatisticsDTO dto) {
        return Try.of(() -> service.getOpsWorkOrderGrowthTrend(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @ApiOperation(value = "6.7排行榜与得分统计", notes = "6.7排行榜与得分统计")
    @ResponseBody
    @RequestMapping("/getUnitRankingList")
    public RestfulResults<List<UnitRankingListStatisticsVO>> getUnitRankingList(UnitRankingListStatisticsDTO dto) {
        return Try.of(() -> service.getUnitRankingList(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @ApiOperation(value = "6.8工单类型统计", notes = "6.8工单类型统计")
    @ResponseBody
    @RequestMapping("/getWorkOrderType")
    public RestfulResults<List<WorkOrderTypeStatisticsVO>> getWorkOrderType(WorkOrderTypeStatisticsDTO dto) {
        return Try.of(() -> service.getWorkOrderType(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @ApiOperation(value = "6.9单位工单统计", notes = "6.9单位工单统计")
    @ResponseBody
    @RequestMapping("/getUnitWorkOrder")
    public RestfulResults<List<UnitWorkOrderStatisticsVO>> getUnitWorkOrder(UnitWorkOrderStatisticsDTO dto) {
        return Try.of(() -> service.getUnitWorkOrder(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }


    @ApiOperation(value = "8.1运维榜单", notes = "8.1运维榜单")
    @ResponseBody
    @RequestMapping("/gradeStatistics")
    public RestfulResults<List<GradeStatisticsVO>> getGradeStatistics(GradeStatisticsDTO dto) {
        return Try.of(() -> service.getGradeStatistics(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @ApiOperation(value = "10.1.1运维站点数", notes = "10.1.1运维站点数")
    @ResponseBody
    @RequestMapping("/getOpsSiteStatistics")
    public RestfulResults<List<OpsSiteStatisticsVO>> getOpsSiteStatistics(OpsSiteStatisticsDTO dto) {
        return Try.of(() -> service.getOpsSiteStatistics(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @ApiOperation(value = "10.1.2-10.1.4增长趋势统计", notes = "10.1.2-10.1.4增长趋势统计")
    @ResponseBody
    @RequestMapping("/getGrowthTrend")
    public RestfulResults<GrowthTrendStatisticsVO> getGrowthTrend(GrowthTrendStatisticsDTO dto) {
        return Try.of(() -> service.getGrowthTrend(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @ApiOperation(value = "10.1.5 统计类接口，联合一平台/平台支撑的统一运维监管", notes = "10.1.5 统计类接口，联合一平台/平台支撑的统一运维监管")
    @ResponseBody
    @RequestMapping("/getSystemInfo")
    public RestfulResults<SystemInfoVO> getSystemInfo(SystemInfoDTO dto) {
        return Try.of(() -> service.getSystemInfo(dto))
                .onFailure(err -> log.error("获取数据失败！", err))
                .getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

}
