package com.trs.gov.statistics.service;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.statistics.DTO.*;
import com.trs.gov.statistics.VO.*;
import com.trs.gov.statistics.mgr.IStatisticsMgr;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 统计服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-21 14:26
 * @version 1.0
 * @since 1.0
 */
@Service
@Slf4j
@Primary
public class StatisticsServiceImpl implements IStatisticsService {

    @Autowired
    private IStatisticsMgr mgr;

    /**
     * 状态统计服务<BR>
     *
     * @param dto 请求参数（以下参数均为可选）<br/>
     *            <ul>
     *              <li>workOrderTypeId 工单种类ID</li>
     *              <li>startDate       开始时间 （yyyy-MM-dd HH:mm:ss）</li>
     *              <li>endDate         结束时间 （yyyy-MM-dd HH:mm:ss）</li>
     *              <li>onlyMe          只看我的</li>
     *              <li>unitId          单位ID</li>
     *            </ul>
     * @return 状态统计数据列表
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 13:58
     */
    @Override
    public RestfulResults<List<StatusStatisticsVO>> getStatusStatistics(StatusStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getStatusStatistics(dto)).addMsg("成功获取数据！");
    }

    /**
     * 工时统计<BR>
     *
     * @param dto 请求参数
     * @return 工时统计结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-21 17:52
     */
    @Override
    public RestfulResults<WorkTimeStatisticsVO> getWorkTimeStatistics(WorkTimeStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getWorkTimeStatistics(dto)).addMsg("成功获取数据！");
    }

    /**
     * 系统工单走势图<BR>
     *
     * @param dto 请求参数
     * @return 相关结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-28 11:15
     */
    @Override
    public RestfulResults<OpsWorkOrderGrowthTrendStatisticsVO> getOpsWorkOrderGrowthTrend(OpsWorkOrderGrowthTrendStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getOpsWorkOrderGrowthTrend(dto)).addMsg("成功获取数据！");
    }

    /**
     * 得分统计<BR>
     *
     * @param dto 请求参数
     * @return 得分统计结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    @Override
    public RestfulResults<List<GradeStatisticsVO>> getGradeStatistics(GradeStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getGradeStatistics(dto)).addMsg("成功获取数据！");
    }


    /**
     * 工单增长趋势统计<BR>
     *
     * @param dto 请求参数
     * @return 工单增长趋势统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    @Override
    public RestfulResults<GrowthTrendStatisticsVO> getGrowthTrend(GrowthTrendStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getGrowthTrend(dto)).addMsg("成功获取数据！");
    }

    /**
     * 6.7排行榜与得分统计<BR>
     *
     * @param dto 请求参数
     * @return 相关榜单
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-29 14:01
     */
    @Override
    public RestfulResults<List<UnitRankingListStatisticsVO>> getUnitRankingList(UnitRankingListStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getUnitRankingList(dto)).addMsg("成功获取数据！");
    }

    /**
     * 工单类型统计<BR>
     *
     * @param dto 请求参数
     * @return 工单类型统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    @Override
    public RestfulResults<List<WorkOrderTypeStatisticsVO>> getWorkOrderType(WorkOrderTypeStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getWorkOrderType(dto)).addMsg("成功获取数据！");
    }

    /**
     * 单位工单统计<BR>
     *
     * @param dto 请求参数
     * @return 工单类型统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    @Override
    public RestfulResults<List<UnitWorkOrderStatisticsVO>> getUnitWorkOrder(UnitWorkOrderStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getUnitWorkOrder(dto)).addMsg("成功获取数据！");
    }

    /**
     * 运维站点统计<BR>
     *
     * @param dto 请求参数
     * @return 运维站点数据
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-27 19:48
     */
    @Override
    public RestfulResults<List<OpsSiteStatisticsVO>> getOpsSiteStatistics(OpsSiteStatisticsDTO dto) throws ServiceException {
        initUserInfo(dto);
        return RestfulResults.ok(mgr.getOpsSiteStatistics(dto)).addMsg("成功获取数据！");
    }

    /**
     * 获取系统信息<BR>
     *
     * @return 系统信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-30 15:54
     * @param dto
     */
    @Override
    public RestfulResults<SystemInfoVO> getSystemInfo(SystemInfoDTO dto) throws ServiceException {
        return RestfulResults.ok(mgr.getSystemInfo(dto)).addMsg("成功获取数据！");
    }

    private void initUserInfo(BaseDTO dto) throws ServiceException {
        BaseUtils.checkDTOIsValidAndHaveLoginInfo(dto);
        if (!CMyString.isEmpty(dto.getToken())) {
            ContextHelper.setToken(dto.getToken());
        }
        if (!CMyString.isEmpty(dto.getUnitId())) {
            ContextHelper.setLoginUnitId(dto.getUnitId());
        }
        if (!ContextHelper.getLoginUnitId().isPresent()) {
            ContextHelper.setLoginUnitId("0");
        }
    }
}
