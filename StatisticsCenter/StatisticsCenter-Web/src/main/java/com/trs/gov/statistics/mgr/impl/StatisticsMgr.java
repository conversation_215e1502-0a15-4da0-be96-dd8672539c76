package com.trs.gov.statistics.mgr.impl;

import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.core.util.ContextHelper;
import com.trs.gov.management.base.constant.UnitConstant;
import com.trs.gov.statistics.DO.*;
import com.trs.gov.statistics.DTO.*;
import com.trs.gov.statistics.VO.*;
import com.trs.gov.statistics.constant.SummaryFlag;
import com.trs.gov.statistics.constant.TimeSearchFlag;
import com.trs.gov.statistics.mapper.StatisticsMapper;
import com.trs.gov.statistics.mgr.IStatisticsMgr;
import com.trs.gov.statistics.util.TimeTools;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 统计业务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-21 14:37
 * @version 1.0
 * @since 1.0
 */
@Component
@Primary
@Slf4j
public class StatisticsMgr implements IStatisticsMgr {

    @Autowired
    private StatisticsMapper statisticsMapper;

    /**
     * 状态统计服务<BR>
     *
     * @param dto 请求参数（以下参数均为可选）<br/>
     *            <ul>
     *              <li>workOrderTypeId 工单种类ID</li>
     *              <li>startDate       开始时间 （yyyy-MM-dd HH:mm:ss）</li>
     *              <li>endDate         结束时间 （yyyy-MM-dd HH:mm:ss）</li>
     *              <li>onlyMe          只看我的</li>
     *              <li>unitId          单位ID</li>
     *            </ul>
     * @return 状态统计数据列表
     * @throws ServiceException 服务异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-09 13:58
     */
    @Override
    public List<StatusStatisticsVO> getStatusStatistics(StatusStatisticsDTO dto) throws ServiceException {
        StatusStatisticsSearchDTO searchDTO = new StatusStatisticsSearchDTO();
        if (dto.getWorkOrderTypeId() != null) {
            searchDTO.setWorkOrderTypeIds(CMyString.split(dto.getWorkOrderTypeId(), ","));
        }
        if (!CMyString.isEmpty(dto.getStartDate())) {
            searchDTO.setStartTime(dto.getStartDate());
        }
        if (!CMyString.isEmpty(dto.getEndDate())) {
            searchDTO.setEndTime(dto.getEndDate());
        }

        // 过滤处理单位
        if (!CMyString.isEmpty(dto.getDealUnitIdList())) {
            searchDTO.setUnitIds(CMyString.split(dto.getDealUnitIdList(), ","));
        }

        if (Optional.ofNullable(dto.getOnlyMe()).orElse(false)) {
            searchDTO.setUserName(ContextHelper.getLoginUser().orElseThrow(() -> new ServiceException("缺少用户信息")));
        }
        Set<Integer> status = WorkOrderConstant.statusName.keySet();
        List<StatusStatisticsVO> result = new ArrayList<>(status.size());
        for (Integer s : status) {
            searchDTO.setStatus(new Integer[]{s});
            Long v = statisticsMapper.getStatusStatistics(searchDTO);
            result.add(StatusStatisticsVO.of(s, WorkOrderConstant.statusName.getOrDefault(s, "未知状态"), v));
        }

        if (Optional.ofNullable(dto.getContainOverdue()).orElse(false)) {
            // 追加超期完成的数据
            searchDTO.setStatus(WorkOrderConstant.STATUS_LIST_FINISHED);
            searchDTO.setWhere(" work_order.expected_end_date IS NOT NULL AND work_order.expected_end_date<work_order.finish_time ");
            Long v = statisticsMapper.getStatusStatistics(searchDTO);
            result.add(StatusStatisticsVO.of(-999, "超期完成", v));
        }
        return result;
    }

    /**
     * 工时统计<BR>
     *
     * @param dto 请求参数
     * @return 工时统计结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-21 17:52
     */
    @Override
    public WorkTimeStatisticsVO getWorkTimeStatistics(WorkTimeStatisticsDTO dto) throws ServiceException {
        WorkTimeStatisticsSearchDTO searchDTO = WorkTimeStatisticsSearchDTO.of(dto.getSummaryFlag().getSqlFormat());
        if (!CMyString.isEmpty(dto.getUnitId())) {
            searchDTO.setUnitIds(CMyString.split(dto.getUnitId(), ","));
        }
        Tuple2<String, String> times = dto.getParamStartAndEndTime();
        searchDTO.setStartTime(times._1());
        searchDTO.setEndTime(times._2());
        List<WorkTimeStatisticsDO> list = statisticsMapper.getWorkTimeStatistics(searchDTO);
        WorkTimeStatisticsVO vo = new WorkTimeStatisticsVO();
        list.forEach(item -> {
            vo.getTimes().add(item.getTimeStr());
            vo.getValues().add(new BigDecimal(Optional.ofNullable(item.getValue()).orElse(0.0)).setScale(2, BigDecimal.ROUND_HALF_UP));
        });
        return vo;
    }

    /**
     * 系统工单走势图<BR>
     *
     * @param dto 请求参数
     * @return 相关结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-28 11:15
     */
    @Override
    public OpsWorkOrderGrowthTrendStatisticsVO getOpsWorkOrderGrowthTrend(OpsWorkOrderGrowthTrendStatisticsDTO dto) throws ServiceException {
        OpsWorkOrderGrowthTrendSearchDTO searchDTO = OpsWorkOrderGrowthTrendSearchDTO.of(dto.getSummaryFlag().getSqlFormat(), CMyString.showEmpty(dto.getUnitIdField(), "deal_unit_id"));
        if (!CMyString.isEmpty(dto.getUnitId())) {
            searchDTO.setUnitIds(CMyString.split(dto.getUnitId(), ","));
        }
        Tuple2<String, String> times = dto.getParamStartAndEndTime();
        searchDTO.setStartTime(times._1());
        searchDTO.setEndTime(times._2());
        List<OpsWorkOrderGrowthTrendDO> list = statisticsMapper.getOpsWorkOrderGrowthTrend(searchDTO);
        Map<String, List<Tuple2<String, Long>>> result = new HashMap<>(list.size());
        List<String> timeList = TimeTools.getTimeList(dto);
        Map<String, String> name = new HashMap<>(list.size());
        list.forEach(item -> {
            String key = "unit." + item.getUnitId();
            if (!name.containsKey(key)) {
                if (!CMyString.isEmpty(item.getUnitName())) {
                    name.put(key, item.getUnitName());
                }
            }
            List<Tuple2<String, Long>> vo;
            if (!result.containsKey(key)) {
                vo = timeList.stream().map(t -> {
                    if (CMyString.showEmpty(t).startsWith(item.getTimeStr())) {
                        return new Tuple2<>(t, item.getValue());
                    } else {
                        return new Tuple2<>(t, 0L);
                    }
                }).collect(Collectors.toList());
            } else {
                vo = result.get(key).stream().map(t -> {
                    if (CMyString.showEmpty(t._1()).startsWith(item.getTimeStr())) {
                        return t.update2(item.getValue());
                    }
                    return t;
                }).collect(Collectors.toList());
            }
            result.put(key, vo);
        });

        String[] date = new String[0];
        if (timeList != null && timeList.size() > 0) {
            date = new String[]{
                    timeList.get(0),
                    Integer.toString(timeList.size())
            };
        }
        return OpsWorkOrderGrowthTrendStatisticsVO.of(name.keySet().stream().map(key -> {
            OpsWorkOrderGrowthTrendStatisticsDataVO vo = new OpsWorkOrderGrowthTrendStatisticsDataVO();
            vo.setDept(name.get(key));
            vo.setData(result.get(key).stream().map(item -> new Object[]{item._1(), item._2()}).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList()), date);
    }

    /**
     * 得分统计<BR>
     *
     * @param dto 请求参数
     * @return 得分统计结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    @Override
    public List<GradeStatisticsVO> getGradeStatistics(GradeStatisticsDTO dto) throws ServiceException {
        GradeStatisticsSearchDTO searchDTO = new GradeStatisticsSearchDTO();
        if (!CMyString.isEmpty(dto.getUnitId())) {
            searchDTO.setUnitIds(CMyString.split(dto.getUnitId(), ","));
        }
        Tuple2<String, String> times = dto.getParamStartAndEndTime();
        searchDTO.setStartTime(times._1());
        searchDTO.setEndTime(times._2());
        searchDTO.setTopNum(Optional.ofNullable(dto.getTopNum()).orElse(10));
        List<GradeStatisticsDO> list = statisticsMapper.getGradeStatistics(searchDTO);
        return list.stream().map(item -> {
            GradeStatisticsVO vo = new GradeStatisticsVO();
            vo.setUnit(item.getUnitName());
            vo.setUnitId(item.getUnitId());
            BigDecimal b = new BigDecimal(Optional.ofNullable(item.getScore()).orElse(0.0));
            vo.setScore(b.setScale(1, BigDecimal.ROUND_HALF_UP));
            return vo;
        }).collect(Collectors.toList());
    }


    /**
     * 工单增长趋势统计<BR>
     *
     * @param dto 请求参数
     * @return 工单增长趋势统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    @Override
    public GrowthTrendStatisticsVO getGrowthTrend(GrowthTrendStatisticsDTO dto) throws ServiceException {
        GrowthTrendStatisticsSearchDTO searchDTO = new GrowthTrendStatisticsSearchDTO();
        if (!CMyString.isEmpty(dto.getWorkOrderTypeId())) {
            searchDTO.setWorkOrderTypeIds(CMyString.split(dto.getWorkOrderTypeId(), ","));
        }
        GrowthTrendStatisticsVO vo = new GrowthTrendStatisticsVO();
        List<String> list = TimeTools.getTimeList(dto);
        list.forEach(item -> {
            Tuple2<String, String> tuple2 = Try.of(() -> TimeTools.getParamStartAndEndTime(dto.getSummaryFlag().getTimeFlag(), item))
                    .onFailure(err -> log.error("timeFlag={},item={}获取起始日期失败！", dto.getTimeFlag(), item, err))
                    .getOrNull();
            if (tuple2 == null) {
                return;
            }
            searchDTO.setStartTime(tuple2._1());
            searchDTO.setEndTime(tuple2._2());
            Long value = statisticsMapper.getGrowthTrend(searchDTO);
            if (dto.getSummaryFlag() == SummaryFlag.MONTH) {
                vo.getTimes().add(TimeUtils.dateToString(TimeUtils.stringToDate(item, dto.getTimeFlag().getFormat()), "yyyy-MM"));
            } else {
                vo.getTimes().add(item);
            }
            vo.getValues().add(value);
            if (vo.getMaxValue() == null) {
                vo.setMaxValue(value);
            } else if (value > vo.getMaxValue()) {
                vo.setMaxValue(value);
            }
        });
        Long max = Optional.ofNullable(vo.getMaxValue()).orElse(0L);
        vo.setMaxValue(max + max / 10);
        return vo;
    }

    /**
     * 6.7排行榜与得分统计<BR>
     *
     * @param dto 请求参数
     * @return 相关榜单
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-29 14:01
     */
    @Override
    public List<UnitRankingListStatisticsVO> getUnitRankingList(UnitRankingListStatisticsDTO dto) throws ServiceException {
        UnitWorkOrderStatisticsSearchDTO searchDTO = new UnitWorkOrderStatisticsSearchDTO();
        if (!CMyString.isEmpty(dto.getWorkOrderTypeId())) {
            searchDTO.setWorkOrderTypeIds(CMyString.split(dto.getWorkOrderTypeId(), ","));
        }
        Tuple2<String, String> times = dto.getParamStartAndEndTime();
        searchDTO.setStartTime(times._1());
        searchDTO.setEndTime(times._2());
        searchDTO.setUnitIdField("deal_unit_id");

        // 查询收到的
        List<UnitNumDO> received = statisticsMapper.getUnitWorkOrderNum(searchDTO);

        // 查询完成的
        searchDTO.setStatus(WorkOrderConstant.STATUS_LIST_FINISHED);
        List<UnitNumDO> finish = statisticsMapper.getUnitWorkOrderNum(searchDTO);

        // 查询超期完成的
        searchDTO.setStatus(WorkOrderConstant.STATUS_LIST_FINISHED);
        searchDTO.setWhere(" ( work_order.expected_end_date IS NOT NULL AND work_order.expected_end_date < work_order.finish_time ) ");
        List<UnitNumDO> overdueFinish = statisticsMapper.getUnitWorkOrderNum(searchDTO);

        // 查询超期未完成的
        searchDTO.setStatus(WorkOrderConstant.STATUS_LIST_NOT_FINISHED);
        searchDTO.setWhere(" ( work_order.expected_end_date IS NOT NULL AND work_order.expected_end_date<NOW() ) ");
        List<UnitNumDO> overdueDoing = statisticsMapper.getUnitWorkOrderNum(searchDTO);

        List<UnitNumDO> overdue = new ArrayList<>(overdueFinish.size() + overdueDoing.size());
        overdue.addAll(overdueFinish);
        overdue.addAll(overdueDoing);

        // 查询评价数
        UnitGradeStatisticsSearchDTO gradeStatisticsSearchDTO = new UnitGradeStatisticsSearchDTO();
        if (!CMyString.isEmpty(dto.getWorkOrderTypeId())) {
            gradeStatisticsSearchDTO.setWorkOrderTypeIds(CMyString.split(dto.getWorkOrderTypeId(), ","));
        }
        gradeStatisticsSearchDTO.setStartTime(times._1());
        gradeStatisticsSearchDTO.setEndTime(times._2());
        List<UnitNumDO> grade = statisticsMapper.getUnitGradeNum(gradeStatisticsSearchDTO);

        // 查询评分
        List<GradeDO> star = statisticsMapper.getUnitGradeValue(gradeStatisticsSearchDTO);

        Map<String, UnitRankingListStatisticsVO> result = new HashMap<>(received.size() + finish.size() + overdue.size() + grade.size());

        received.forEach(item -> {
            String key = "unit." + item.getUnitId();
            UnitRankingListStatisticsVO vo = result.getOrDefault(key, UnitRankingListStatisticsVO.of(item.getName()));
            vo.setUnitId(item.getUnitId());
            vo.setWorknum(item.getValue());
            result.put(key, vo);
        });
        finish.forEach(item -> {
            String key = "unit." + item.getUnitId();
            UnitRankingListStatisticsVO vo = result.getOrDefault(key, UnitRankingListStatisticsVO.of(item.getName()));
            vo.setUnitId(item.getUnitId());
            vo.setFinishnum(item.getValue());
            result.put(key, vo);
        });
        overdue.forEach(item -> {
            String key = "unit." + item.getUnitId();
            UnitRankingListStatisticsVO vo = result.getOrDefault(key, UnitRankingListStatisticsVO.of(item.getName()));
            vo.setUnitId(item.getUnitId());
            // 超期是由两部分组成的，可能会有重复的单位ID，所以需要做累加
            vo.setExpirenum(Optional.ofNullable(vo.getExpirenum()).orElse(0L) + item.getValue());
            result.put(key, vo);
        });
        grade.forEach(item -> {
            String key = "unit." + item.getUnitId();
            UnitRankingListStatisticsVO vo = result.getOrDefault(key, UnitRankingListStatisticsVO.of(item.getName()));
            vo.setUnitId(item.getUnitId());
            vo.setCommentnum(item.getValue());
            result.put(key, vo);
        });
        star.forEach(item -> {
            String key = "unit." + item.getUnitId();
            UnitRankingListStatisticsVO vo = result.getOrDefault(key, UnitRankingListStatisticsVO.of(item.getName()));
            /**
             * 服务态度得分
             */
            BigDecimal attitudeGrade = new BigDecimal(Optional.ofNullable(item.getAttitudeGrade()).orElse(0.0)).setScale(2, BigDecimal.ROUND_HALF_UP);
            /**
             * 完成时间得分
             */
            BigDecimal completeTimeGrade = new BigDecimal(Optional.ofNullable(item.getCompleteTimeGrade()).orElse(0.0)).setScale(2, BigDecimal.ROUND_HALF_UP);
            /**
             * 完成内容得分
             */
            BigDecimal completeContentGrade = new BigDecimal(Optional.ofNullable(item.getCompleteContentGrade()).orElse(0.0)).setScale(2, BigDecimal.ROUND_HALF_UP);
            /**
             * 总平均分
             */
            BigDecimal starValue = attitudeGrade.add(completeTimeGrade).add(completeContentGrade).divide(new BigDecimal(3), 2, BigDecimal.ROUND_HALF_UP);
            vo.setStar(starValue.setScale(2, BigDecimal.ROUND_HALF_UP));
            vo.setAttitudeGrade(attitudeGrade);
            vo.setCompleteTimeGrade(completeTimeGrade);
            vo.setCompleteContentGrade(completeContentGrade);
            vo.setUnitId(item.getUnitId());
            result.put(key, vo);
        });
        return result.values().stream().sorted((a, b) -> {
            if ("expirenum".equalsIgnoreCase(dto.getOrderBy())) {
                return b.getExpirenum().compareTo(a.getExpirenum());
            }
            if ("finishnum".equalsIgnoreCase(dto.getOrderBy())) {
                return b.getFinishnum().compareTo(a.getFinishnum());
            }
            if ("commentnum".equalsIgnoreCase(dto.getOrderBy())) {
                return b.getCommentnum().compareTo(a.getCommentnum());
            }
            if ("star".equalsIgnoreCase(dto.getOrderBy())) {
                return b.getStar().compareTo(a.getStar());
            }
            return b.getWorknum().compareTo(a.getWorknum());
        }).collect(Collectors.toList());
    }

    /**
     * 工单类型统计<BR>
     *
     * @param dto 请求参数
     * @return 工单类型统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    @Override
    public List<WorkOrderTypeStatisticsVO> getWorkOrderType(WorkOrderTypeStatisticsDTO dto) throws ServiceException {
        List<WorkOrderTypeDO> typeList = statisticsMapper.getWorkOrderType();

        String topWorkOrderType = CMyString.showEmpty(dto.getTopWorkOrderType(), "1");

        Map<String, Long> idMap = typeList.stream().collect(Collectors.toMap(a -> Long.toString(a.getId()), item -> {
            if (topWorkOrderType.equals(Long.toString(item.getParentId()))) {
                return item.getId();
            } else {
                return item.getParentId();
            }
        }));

        Map<Long, WorkOrderTypeStatisticsVO> result = typeList.stream()
                .filter(item -> topWorkOrderType.equals(Long.toString(item.getParentId())))
                .collect(Collectors.toMap(WorkOrderTypeDO::getId, item -> WorkOrderTypeStatisticsVO
                        .of(Long.toString(item.getId()), item.getTypeName(), null)));
        result.put(-999L, WorkOrderTypeStatisticsVO.of("-999", "未分类", null));

        WorkOrderTypeStatisticsSearchDTO searchDTO = new WorkOrderTypeStatisticsSearchDTO();
        Tuple2<String, String> times = dto.getParamStartAndEndTime();
        searchDTO.setStartTime(times._1());
        searchDTO.setEndTime(times._2());
        searchDTO.setTopWorkOrderType(topWorkOrderType);
        List<WorkOrderTypeStatisticsDO> list = statisticsMapper.getWorkOrderTypeStatistics(searchDTO);
        list.forEach(item -> {
            Long parentId = idMap.getOrDefault(CMyString.showEmpty(item.getType()), -999L);
            if (!result.containsKey(parentId)) {
                parentId = -999L;
            }
            WorkOrderTypeStatisticsVO vo = result.get(parentId);
            Long oldValue = Long.valueOf(Optional.ofNullable(vo.getValue()).orElse("0"));
            Long incValue = Optional.ofNullable(item.getValue()).orElse(0L);
            vo.setValue(Long.toString(oldValue + incValue));
            result.put(parentId, vo);
        });

        return result.values().stream().filter(item -> item.getValue() != null)
                .sorted((a, b) -> Long.valueOf(Optional.ofNullable(b.getValue()).orElse("0"))
                        .compareTo(Long.valueOf(Optional.ofNullable(a.getValue()).orElse("0"))))
                .collect(Collectors.toList());
    }

    /**
     * 单位工单统计<BR>
     *
     * @param dto 请求参数
     * @return 工单类型统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    @Override
    public List<UnitWorkOrderStatisticsVO> getUnitWorkOrder(UnitWorkOrderStatisticsDTO dto) throws ServiceException {
        UnitWorkOrderStatisticsSearchDTO searchDTO = new UnitWorkOrderStatisticsSearchDTO();
        if (!CMyString.isEmpty(dto.getWorkOrderTypeId())) {
            searchDTO.setWorkOrderTypeIds(CMyString.split(dto.getWorkOrderTypeId(), ","));
        }
        Tuple2<String, String> times = dto.getParamStartAndEndTime();
        searchDTO.setStartTime(times._1());
        searchDTO.setEndTime(times._2());

        // 查询派发的
        searchDTO.setUnitIdField("cr_unit_id");
        List<UnitNumDO> paifa = statisticsMapper.getUnitWorkOrderNum(searchDTO);

        // 查询完成的
        searchDTO.setUnitIdField("deal_unit_id");
        searchDTO.setStatus(WorkOrderConstant.STATUS_LIST_FINISHED);
        List<UnitNumDO> finish = statisticsMapper.getUnitWorkOrderNum(searchDTO);

        Map<String, UnitWorkOrderStatisticsVO> result = new HashMap<>(paifa.size() + finish.size());

        paifa.forEach(item -> {
            String key = "unit." + item.getUnitId();
            UnitWorkOrderStatisticsVO vo = result.getOrDefault(key, UnitWorkOrderStatisticsVO.of(item.getName(), 0L, 0L));
            vo.setNewnum(item.getValue());
            result.put(key, vo);
        });
        finish.forEach(item -> {
            String key = "unit." + item.getUnitId();
            UnitWorkOrderStatisticsVO vo = result.getOrDefault(key, UnitWorkOrderStatisticsVO.of(item.getName(), 0L, 0L));
            vo.setFinishnum(item.getValue());
            result.put(key, vo);
        });
        return result.values().stream().sorted((a, b) -> {
            if ("NewNum".equalsIgnoreCase(dto.getOrderBy())) {
                return b.getNewnum().compareTo(a.getNewnum());
            } else if ("FinishNum".equalsIgnoreCase(dto.getOrderBy())) {
                return b.getFinishnum().compareTo(a.getFinishnum());
            }
            return Long.compare(b.getNewnum() + b.getFinishnum(), a.getNewnum() + a.getFinishnum());
        }).collect(Collectors.toList());
    }

    /**
     * 运维站点统计<BR>
     *
     * @param dto 请求参数
     * @return 运维站点数据
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-27 19:48
     */
    @Override
    public List<OpsSiteStatisticsVO> getOpsSiteStatistics(OpsSiteStatisticsDTO dto) throws ServiceException {
        List<OpsSiteStatisticsDO> list = statisticsMapper.getOpsSiteStatistics(dto);
        return list.stream().map(item -> {
            OpsSiteStatisticsVO vo = new OpsSiteStatisticsVO();
            BaseUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取系统信息<BR>
     *
     * @return 系统信息
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-30 15:54
     */
    @Override
    public SystemInfoVO getSystemInfo(SystemInfoDTO dto) throws ServiceException {
        SystemInfoVO vo = new SystemInfoVO();
        WorkOrderStatisticsSearchDTO searchDTO = new WorkOrderStatisticsSearchDTO();
        // 工单总量
        if (!CMyString.isEmpty(dto.getWorkOrderTypeId())) {
            searchDTO.setWorkOrderTypeIds(CMyString.split(dto.getWorkOrderTypeId(), ","));
        }
        vo.setWorkNum(statisticsMapper.getWorkOrderNum(searchDTO));
        // 今日新增
        Tuple2<String, String> times = TimeTools.getParamStartAndEndTime(TimeSearchFlag.DAY);
        searchDTO.setStartTime(times._1());
        searchDTO.setEndTime(times._2());
        vo.setTodayNewNum(statisticsMapper.getWorkOrderNum(searchDTO));

        UnitStatisticsSearchDTO unitDTO = new UnitStatisticsSearchDTO();
        if (!CMyString.isEmpty(dto.getUnitStatus())) {
            unitDTO.setStatus(CMyString.splitToInt(dto.getUnitStatus(), ","));
        }
        // 运维单位
        unitDTO.setUnitTypes(new String[]{
                UnitConstant.PARAM_OPERATION_ID
        });
        vo.setOpsUnitNum(statisticsMapper.getUnitNum(unitDTO));

        // 厂商单位
        unitDTO.setUnitTypes(new String[]{
                UnitConstant.PARAM_FACTORY_ID
        });
        vo.setFactoryUnitNum(statisticsMapper.getUnitNum(unitDTO));

        // 主办单位
        unitDTO.setUnitTypes(new String[]{
                UnitConstant.PARAM_HOSTUNIT_ID
        });
        vo.setHostUnitNum(statisticsMapper.getUnitNum(unitDTO));

        // 主管单位
        unitDTO.setUnitTypes(new String[]{
                UnitConstant.PARAM_MASTERUNIT_ID
        });
        vo.setMasterUnitNum(statisticsMapper.getUnitNum(unitDTO));

        return vo;
    }
}
