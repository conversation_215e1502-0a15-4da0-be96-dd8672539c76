package com.trs.gov.statistics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.statistics.DO.*;
import com.trs.gov.statistics.DTO.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-27 11:49
 * @version 1.0
 * @since 1.0
 */
@Component
public interface StatisticsMapper extends BaseMapper<WorkTimeStatisticsDO> {

    /**
     * 工单状态统计<BR>
     *
     * @param dto 检索参数
     * @return 数量
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-27 11:48
     */
    public Long getStatusStatistics(@Param("dto") StatusStatisticsSearchDTO dto);

    /**
     * 工时统计<BR>
     *
     * @param dto 检索参数
     * @return 检索的结果列表
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 15:10
     */
    public List<WorkTimeStatisticsDO> getWorkTimeStatistics(@Param("dto") WorkTimeStatisticsSearchDTO dto);

    /**
     * 得分统计<BR>
     *
     * @param dto 检索参数
     * @return 检索的结果列表
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 15:10
     */
    public List<GradeStatisticsDO> getGradeStatistics(@Param("dto") GradeStatisticsSearchDTO dto);

    /**
     * 工单增长趋势统计<BR>
     *
     * @param dto 请求参数
     * @return 工单增长趋势统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    public Long getGrowthTrend(@Param("dto") GrowthTrendStatisticsSearchDTO dto);

    /**
     * 工单类型统计<BR>
     *
     * @param dto 请求参数
     * @return 工单类型统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    public List<WorkOrderTypeStatisticsDO> getWorkOrderTypeStatistics(@Param("dto") WorkOrderTypeStatisticsSearchDTO dto);

    /**
     * 获取工单类型<BR>
     *
     * @return 工单类型列表
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2021-01-07 17:01
     */
    public List<WorkOrderTypeDO> getWorkOrderType();

    /**
     * 运维站点统计<BR>
     *
     * @param dto 请求参数
     * @return 运维站点数据
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-27 19:48
     */
    public List<OpsSiteStatisticsDO> getOpsSiteStatistics(@Param("dto") OpsSiteStatisticsDTO dto);

    /**
     * 系统工单走势图<BR>
     *
     * @param dto 请求参数
     * @return 相关结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-28 11:15
     */
    public List<OpsWorkOrderGrowthTrendDO> getOpsWorkOrderGrowthTrend(@Param("dto") OpsWorkOrderGrowthTrendSearchDTO dto);

    /**
     * 单位工单统计<BR>
     *
     * @param dto 请求参数
     * @return 工单类型统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    public List<UnitNumDO> getUnitWorkOrderNum(@Param("dto") UnitWorkOrderStatisticsSearchDTO dto);

    /**
     * 单位评价数统计<BR>
     *
     * @param dto 请求参数
     * @return 单位评价数统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 11:56
     */
    public List<UnitNumDO> getUnitGradeNum(@Param("dto") UnitGradeStatisticsSearchDTO dto);

    /**
     * 单位评分统计<BR>
     *
     * @param dto 请求参数
     * @return 单位评分统计
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-30 13:57
     */
    public List<GradeDO> getUnitGradeValue(@Param("dto") UnitGradeStatisticsSearchDTO dto);

    /**
     * 工单数量统计<BR>
     *
     * @param dto 请求参数
     * @return 工单数量
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-30 13:57
     */
    public Long getWorkOrderNum(@Param("dto") WorkOrderStatisticsSearchDTO dto);

    /**
     * 单位数量统计<BR>
     *
     * @param dto 请求参数
     * @return 单位数量
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-30 13:57
     */
    public Long getUnitNum(@Param("dto") UnitStatisticsSearchDTO dto);
}
