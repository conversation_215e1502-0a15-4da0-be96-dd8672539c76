package com.trs.gov.statistics.util;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.statistics.DTO.TimeDTO;
import com.trs.gov.statistics.constant.SummaryFlag;
import com.trs.gov.statistics.constant.TimeSearchFlag;
import org.junit.Test;

public class TimeToolsTest {

    @Test
    public void getTimeList() throws ServiceException {
        TimeDTO dto = new TimeDTO();
        dto.setStartDate("2019-01-01");
        dto.setEndDate("2020-01-01");
        dto.setTimeFlag(TimeSearchFlag.YEAR);
        dto.setSummaryFlag(SummaryFlag.YEAR);
        System.out.println(TimeTools.getTimeList(dto));
    }
}