package com.trs.gov.statistics.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class GrowthTrendStatisticsVO extends BaseVO {
    /**
     * 相关时间列表
     */
    private List<String> times = new ArrayList<>(0);

    /**
     * 相关值列表
     */
    private List<Long> values = new ArrayList<>(0);

    /**
     * 最大值
     */
    private Long maxValue;
}
