package com.trs.gov.statistics.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-28 16:09
 * @version 1.0
 * @since 1.0
 * 单位工单数量统计
 */
@Data
public class UnitWorkOrderStatisticsVO extends BaseVO {
    /**
     * 单位名称
     */
    private String dept;

    /**
     * 完成数
     */
    private Long finishnum;

    /**
     * 派发数
     */
    private Long newnum;

    public static UnitWorkOrderStatisticsVO of(String dept, Long finishnum, Long newnum) {
        UnitWorkOrderStatisticsVO vo = new UnitWorkOrderStatisticsVO();
        vo.setNewnum(newnum);
        vo.setDept(dept);
        vo.setFinishnum(finishnum);
        return vo;
    }
}
