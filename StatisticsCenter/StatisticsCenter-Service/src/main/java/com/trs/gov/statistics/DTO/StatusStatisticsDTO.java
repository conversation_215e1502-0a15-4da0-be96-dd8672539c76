package com.trs.gov.statistics.DTO;

import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

import java.util.Optional;

@Data
public class StatusStatisticsDTO extends BaseDTO {

    public static final String format = "yyyy-MM-dd HH:mm:ss";

    /**
     * 工单种类ID
     */
    private String workOrderTypeId = "1";

    /**
     * 开始时间 （yyyy-MM-dd HH:mm:ss）
     */
    private String startDate;

    /**
     * 结束时间 （yyyy-MM-dd HH:mm:ss）
     */
    private String endDate;

    private String dealUnitIdList;

    /**
     * 只看我的
     */
    private Boolean onlyMe;

    /**
     * 包含超期完成
     */
    private Boolean containOverdue;


    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (!CMyString.isEmpty(startDate)) {
            try {
                Optional.ofNullable(TimeUtils.stringToDate(startDate, format)).orElseThrow(() -> new ServiceException("没有得到相关日期！"));
            } catch (Exception e) {
                throw new ParamInvalidException("startDate需要是" + format + "格式！");
            }
        }
        if (!CMyString.isEmpty(endDate)) {
            try {
                Optional.ofNullable(TimeUtils.stringToDate(endDate, format)).orElseThrow(() -> new ServiceException("没有得到相关日期！"));
            } catch (Exception e) {
                throw new ParamInvalidException("endDate需要是" + format + "格式！");
            }
        }
        return true;
    }
}
