package com.trs.gov.statistics.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 工时统计的VO
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-21 17:45
 * @version 1.0
 * @since 1.0
 */
@Data
public class WorkTimeStatisticsVO extends BaseVO {

    /**
     * 相关时间列表
     */
    private List<String> times = new ArrayList<>(0);

    /**
     * 相关值列表
     */
    private List<BigDecimal> values = new ArrayList<>(0);
}
