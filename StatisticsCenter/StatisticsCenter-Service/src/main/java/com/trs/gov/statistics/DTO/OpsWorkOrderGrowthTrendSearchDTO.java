package com.trs.gov.statistics.DTO;

import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

@Data
public class OpsWorkOrderGrowthTrendSearchDTO extends SearchDTO {

    private String sqlFormat;

    /**
     * 单位ID字段
     * */
    private String unitIdField = "deal_unit_id";

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (CMyString.isEmpty(sqlFormat)) {
            throw new ParamInvalidException("日期参数不能为空！");
        }
        if (CMyString.isEmpty(unitIdField)) {
            throw new ParamInvalidException("单位ID字段不能为空！");
        }
        return super.isValid();
    }

    public static OpsWorkOrderGrowthTrendSearchDTO of(String sqlFormat) throws ServiceException {
        OpsWorkOrderGrowthTrendSearchDTO dto = new OpsWorkOrderGrowthTrendSearchDTO();
        dto.setSqlFormat(sqlFormat);
        dto.isValid();
        return dto;
    }

    public static OpsWorkOrderGrowthTrendSearchDTO of(String sqlFormat,String unitIdField) throws ServiceException {
        OpsWorkOrderGrowthTrendSearchDTO dto = new OpsWorkOrderGrowthTrendSearchDTO();
        dto.setSqlFormat(sqlFormat);
        dto.setUnitIdField(unitIdField);
        dto.isValid();
        return dto;
    }
}
