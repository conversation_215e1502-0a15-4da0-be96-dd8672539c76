package com.trs.gov.statistics.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class UnitRankingListStatisticsVO extends BaseVO {

    /**
     * 单位名称
     */
    private String dept;

    /**
     * 单位ID
     */
    private Long unitId;

    /**
     * 工单总数
     */
    private Long worknum;

    /**
     * 完成数
     */
    private Long finishnum;

    /**
     * 超期数
     */
    private Long expirenum;

    /**
     * 评价数
     */
    private Long commentnum;

    /**
     * 总平均分
     */
    private BigDecimal star;

    /**
     * 服务态度得分
     */
    private BigDecimal attitudeGrade;

    /**
     * 完成时间得分
     */
    private BigDecimal completeTimeGrade;

    /**
     * 完成内容得分
     */
    private BigDecimal completeContentGrade;

    public static UnitRankingListStatisticsVO of(String dept) {
        UnitRankingListStatisticsVO vo = new UnitRankingListStatisticsVO();
        vo.setDept(dept);
        vo.setWorknum(0L);
        vo.setFinishnum(0L);
        vo.setExpirenum(0L);
        vo.setCommentnum(0L);
        vo.setStar(new BigDecimal(0.0));
        vo.setAttitudeGrade(new BigDecimal(0.0));
        vo.setCompleteTimeGrade(new BigDecimal(0.0));
        vo.setCompleteContentGrade(new BigDecimal(0.0));
        return vo;
    }

}
