package com.trs.gov.statistics.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-09 13:40
 * @version 1.0
 * @since 1.0
 * 状态统计服务返回对象
 */
@Data
public class StatusStatisticsVO extends BaseVO {

    /**
     * 状态ID
     */
    private int status;

    /**
     * 状态名
     */
    private String name;

    /**
     * 数量
     */
    private Long value;


    public static StatusStatisticsVO of(int status, String name, Long value) {
        StatusStatisticsVO vo = new StatusStatisticsVO();
        vo.setStatus(status);
        vo.setName(name);
        vo.setValue(value);
        return vo;
    }
}
