package com.trs.gov.statistics.constant;

import com.trs.gov.core.util.CMyString;

import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 时间标识
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-21 18:03
 * @version 1.0
 * @since 1.0
 */
public enum TimeSearchFlag {
    NONE("不过滤时间", ""),
    DAY("统计时间段", "yyyy-MM-dd"),
    WEEK("统计周", "yyyy-MM-dd"),
    MONTH("统计月", "yyyy-MM-dd"),
    QUARTER("统计季度", "yyyy-MM-dd"),
    YEAR("统计年", "yyyy-MM-dd");

    /**
     * 时间格式
     */
    private String format;

    TimeSearchFlag(String desc, String format) {
        this.format = format;
    }

    public String getFormat() {
        return format;
    }

    public static Optional<TimeSearchFlag> findTimeSearchFlagByString(String time) {
        if (CMyString.isEmpty(time)) {
            return Optional.empty();
        }
        for (TimeSearchFlag flag : TimeSearchFlag.values()) {
            if (flag.toString().equals(time)) {
                return Optional.of(flag);
            }
        }
        return Optional.empty();
    }
}
