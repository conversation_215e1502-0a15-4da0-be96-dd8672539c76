package com.trs.gov.statistics.constant;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 数据汇总标识
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-26 14:02
 * @version 1.0
 * @since 1.0
 */
public enum SummaryFlag {

    DAY("按天汇总", "%Y-%m-%d", TimeSearchFlag.DAY),
    MONTH("按月汇总", "%Y-%m", TimeSearchFlag.MONTH),
    YEAR("按年汇总", "%Y", TimeSearchFlag.YEAR);


    /**
     * SQL时间格式
     */
    private String sqlFormat;

    private TimeSearchFlag timeFlag;

    public String getSqlFormat() {
        return sqlFormat;
    }

    public TimeSearchFlag getTimeFlag() {
        return timeFlag;
    }

    SummaryFlag(String desc, String sqlFormat, TimeSearchFlag timeFlag) {
        this.timeFlag = timeFlag;
        this.sqlFormat = sqlFormat;
    }
}
