package com.trs.gov.statistics.DTO;

import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

@Data
public class WorkTimeStatisticsSearchDTO extends SearchDTO {

    private String sqlFormat;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (CMyString.isEmpty(sqlFormat)) {
            throw new ParamInvalidException("日期参数不能为空！");
        }
        return true;
    }

    public static WorkTimeStatisticsSearchDTO of(String sqlFormat) throws ServiceException {
        WorkTimeStatisticsSearchDTO dto = new WorkTimeStatisticsSearchDTO();
        dto.sqlFormat = sqlFormat;
        dto.isValid();
        return dto;
    }

    public static WorkTimeStatisticsSearchDTO of(String sqlFormat, String where) throws ServiceException {
        WorkTimeStatisticsSearchDTO dto = new WorkTimeStatisticsSearchDTO();
        dto.sqlFormat = sqlFormat;
        dto.setWhere(where);
        dto.isValid();
        return dto;
    }
}
