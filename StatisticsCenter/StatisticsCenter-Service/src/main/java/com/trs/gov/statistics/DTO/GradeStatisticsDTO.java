package com.trs.gov.statistics.DTO;

import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.statistics.constant.TimeSearchFlag;
import lombok.Data;

@Data
public class GradeStatisticsDTO extends TimeDTO {

    /**
     * 获取前多少条数据（默认10）
     */
    private Integer topNum = 10;

    public static GradeStatisticsDTO of(String timeFlag) throws ServiceException {
        GradeStatisticsDTO dto = new GradeStatisticsDTO();
        dto.setTimeFlag(TimeSearchFlag.findTimeSearchFlagByString(timeFlag).orElseThrow(() -> new ParamInvalidException(timeFlag + "不存在！")));
        dto.isValid();
        return dto;
    }

    public static GradeStatisticsDTO of(String timeFlag, Integer topNum) throws ServiceException {
        GradeStatisticsDTO dto = new GradeStatisticsDTO();
        dto.setTimeFlag(TimeSearchFlag.findTimeSearchFlagByString(timeFlag).orElseThrow(() -> new ParamInvalidException(timeFlag + "不存在！")));
        dto.setTopNum(topNum);
        dto.isValid();
        return dto;
    }

    public static GradeStatisticsDTO of(TimeSearchFlag timeFlag) throws ServiceException {
        GradeStatisticsDTO dto = new GradeStatisticsDTO();
        dto.setTimeFlag(timeFlag);
        dto.isValid();
        return dto;
    }

    public static GradeStatisticsDTO of(TimeSearchFlag timeFlag, Integer topNum) throws ServiceException {
        GradeStatisticsDTO dto = new GradeStatisticsDTO();
        dto.setTimeFlag(timeFlag);
        dto.setTopNum(topNum);
        dto.isValid();
        return dto;
    }

    public static GradeStatisticsDTO of(String timeFlag, String startDate, String endDate) throws ServiceException {
        GradeStatisticsDTO dto = new GradeStatisticsDTO();
        dto.setTimeFlag(TimeSearchFlag.findTimeSearchFlagByString(timeFlag).orElseThrow(() -> new ParamInvalidException(timeFlag + "不存在！")));
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        dto.isValid();
        return dto;
    }

    public static GradeStatisticsDTO of(String timeFlag, String startDate, String endDate, Integer topNum) throws ServiceException {
        GradeStatisticsDTO dto = new GradeStatisticsDTO();
        dto.setTimeFlag(TimeSearchFlag.findTimeSearchFlagByString(timeFlag).orElseThrow(() -> new ParamInvalidException(timeFlag + "不存在！")));
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        dto.setTopNum(topNum);
        dto.isValid();
        return dto;
    }

    public static GradeStatisticsDTO of(TimeSearchFlag timeFlag, String startDate, String endDate) throws ServiceException {
        GradeStatisticsDTO dto = new GradeStatisticsDTO();
        dto.setTimeFlag(timeFlag);
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        dto.isValid();
        return dto;
    }

    public static GradeStatisticsDTO of(TimeSearchFlag timeFlag, String startDate, String endDate, Integer topNum) throws ServiceException {
        GradeStatisticsDTO dto = new GradeStatisticsDTO();
        dto.setTimeFlag(timeFlag);
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        dto.setTopNum(topNum);
        dto.isValid();
        return dto;
    }
}
