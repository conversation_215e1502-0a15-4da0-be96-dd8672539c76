package com.trs.gov.statistics.DTO;

import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.statistics.constant.TimeSearchFlag;
import lombok.Data;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-29 13:59
 * @version 1.0
 * @since 1.0
 * 单位排行榜DTO
 */
@Data
public class UnitRankingListStatisticsDTO extends TimeDTO {

    /**
     * 工单顶级Type
     */
    private String workOrderTypeId;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (getTimeFlag() == null) {
            setTimeFlag(TimeSearchFlag.NONE);
        }
        return super.isValid();
    }
}
