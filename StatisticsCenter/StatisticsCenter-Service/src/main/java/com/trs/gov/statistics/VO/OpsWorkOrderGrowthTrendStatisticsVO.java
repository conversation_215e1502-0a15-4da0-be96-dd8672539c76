package com.trs.gov.statistics.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class OpsWorkOrderGrowthTrendStatisticsVO extends BaseVO {
    /**
     * 存放每个单位的数据
     */
    private List<OpsWorkOrderGrowthTrendStatisticsDataVO> data = new ArrayList<>();
    /***
     * 存放[开始日期,日期数]
     * */
    private String[] date;

    public static OpsWorkOrderGrowthTrendStatisticsVO of(List<OpsWorkOrderGrowthTrendStatisticsDataVO> data, String[] date) {
        OpsWorkOrderGrowthTrendStatisticsVO vo = new OpsWorkOrderGrowthTrendStatisticsVO();
        vo.setData(data);
        vo.setDate(date);
        return vo;
    }
}
