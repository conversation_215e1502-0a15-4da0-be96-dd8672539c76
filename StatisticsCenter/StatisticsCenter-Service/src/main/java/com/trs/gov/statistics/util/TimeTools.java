package com.trs.gov.statistics.util;

import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.statistics.DTO.TimeDTO;
import com.trs.gov.statistics.constant.SummaryFlag;
import com.trs.gov.statistics.constant.TimeSearchFlag;
import io.vavr.Tuple2;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class TimeTools {

    public static Tuple2<String, String> getParamStartAndEndTime(TimeSearchFlag timeSearchFlag) throws ServiceException {
        return getParamStartAndEndTime(timeSearchFlag, null);
    }

    public static Tuple2<String, String> getParamStartAndEndTime(TimeSearchFlag timeSearchFlag, String date) throws ServiceException {

        if (timeSearchFlag == null) {
            throw new ParamInvalidException("时间标识异常");
        }

        String start;
        String end;

        Calendar calendar = Calendar.getInstance();

        if (!CMyString.isEmpty(date)) {
            calendar.setTime(TimeUtils.stringToDate(date, timeSearchFlag.getFormat()));
        }

        // 季度
        int quarter = calendar.get(Calendar.MONTH) / 3;

        switch (timeSearchFlag) {
            case DAY:
                calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
                start = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
                end = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                break;
            case WEEK:
                end = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
                calendar.add(Calendar.DATE, -7);
                start = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                break;
            case MONTH:
                calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
                start = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                calendar.add(Calendar.MONTH, 1);
                calendar.add(Calendar.SECOND, -1);
                end = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                break;
            case QUARTER:
                calendar.set(calendar.get(Calendar.YEAR), quarter * 3, 1, 0, 0, 0);
                start = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                calendar.add(Calendar.MONTH, 3);
                calendar.add(Calendar.SECOND, -1);
                end = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                break;
            case YEAR:
                calendar.set(calendar.get(Calendar.YEAR), 0, 1, 0, 0, 0);
                start = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                calendar.add(Calendar.YEAR, 1);
                calendar.add(Calendar.SECOND, -1);
                end = TimeUtils.dateToString(calendar.getTime(), TimeUtils.YYYYMMDD_HHMMSS);
                break;
            case NONE:
                start = null;
                end = null;
                break;
            default:
                throw new ParamInvalidException("时间标识异常");
        }
        return new Tuple2<>(start, end);
    }


    public static List<String> getTimeList(TimeDTO timeDTO) throws ServiceException {
        BaseUtils.checkDTO(timeDTO);
        TimeSearchFlag timeSearchFlag = timeDTO.getTimeFlag();
        SummaryFlag summaryFlag = timeDTO.getSummaryFlag();
        if (timeSearchFlag == null) {
            throw new ParamInvalidException("时间标识异常");
        }
        if (timeSearchFlag == TimeSearchFlag.NONE) {
            throw new ParamInvalidException("时间段划分时不能用NONE标识！");
        }
        if (summaryFlag == null) {
            throw new ParamInvalidException("汇总标识异常");
        }
        Tuple2<String, String> tuple2;
        if (!CMyString.isEmpty(timeDTO.getStartDate()) && !CMyString.isEmpty(timeDTO.getEndDate())) {
            tuple2 = new Tuple2<>(timeDTO.getStartDate() + " 00:00:00", timeDTO.getEndDate() + " 23:59:59");
        } else {
            Calendar calendar = Calendar.getInstance();
            if (!CMyString.isEmpty(timeDTO.getCustomTime())) {
                calendar.setTime(TimeUtils.stringToDate(timeDTO.getCustomTime(), timeSearchFlag.getFormat()));
            }
            tuple2 = getParamStartAndEndTime(timeSearchFlag, timeDTO.getCustomTime());
            if (TimeUtils.stringToDate(tuple2._2(), TimeUtils.YYYYMMDD_HHMMSS).after(new Date())) {
                tuple2 = tuple2.update2(TimeUtils.dateToString(new Date(), TimeUtils.YYYYMMDD_HHMMSS));
            }
        }
        switch (summaryFlag) {
            case YEAR:
                return TimeUtils.findTimePeriod(TimeUtils.stringToDate(tuple2._1(), TimeUtils.YYYYMMDD_HHMMSS), TimeUtils.stringToDate(tuple2._2(), TimeUtils.YYYYMMDD_HHMMSS), Calendar.YEAR, timeSearchFlag.getFormat(), false);
            case MONTH:
                return TimeUtils.findMonths(tuple2._1(), tuple2._2(), TimeUtils.YYYYMMDD_HHMMSS, timeSearchFlag.getFormat(), false, false);
            case DAY:
                return TimeUtils.findTimePeriod(TimeUtils.stringToDate(tuple2._1(), TimeUtils.YYYYMMDD_HHMMSS), TimeUtils.stringToDate(tuple2._2(), TimeUtils.YYYYMMDD_HHMMSS), Calendar.DATE, timeSearchFlag.getFormat(), false);
            default:
                throw new ParamInvalidException("暂不支持该操作");
        }
    }
}
