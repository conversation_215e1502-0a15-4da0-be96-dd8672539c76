package com.trs.gov.statistics.DTO;

import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import lombok.Data;

@Data
public class UnitWorkOrderStatisticsSearchDTO extends SearchDTO {

    /**
     * 单位ID字段
     */
    private String unitIdField;

    private Integer [] status;

    private String [] workOrderTypeIds;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (CMyString.isEmpty(unitIdField)) {
            throw new ParamInvalidException("单位ID字段不能为空！");
        }
        return super.isValid();
    }
}
