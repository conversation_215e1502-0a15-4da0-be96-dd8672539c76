package com.trs.gov.statistics.DTO;

import com.trs.common.utils.TimeUtils;
import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.statistics.constant.SummaryFlag;
import com.trs.gov.statistics.constant.TimeSearchFlag;
import com.trs.gov.statistics.util.TimeTools;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.Data;

import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 工时统计请求参数
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-21 17:56
 * @version 1.0
 * @since 1.0
 */
@Data
public class TimeDTO extends BaseDTO {

    /**
     * 时间标识
     */
    private TimeSearchFlag timeFlag;

    /**
     * 自定义相关年月
     */
    private String customTime;


    /**
     * 汇总标识
     */
    private SummaryFlag summaryFlag;

    /**
     * 开始日期（yyyy-MM-dd）
     */
    private String startDate;

    /**
     * 截止日期（yyyy-MM-dd）
     */
    private String endDate;

    public SummaryFlag getSummaryFlag() {
        if (summaryFlag == null) {
            switch (timeFlag) {
                case YEAR:
                case QUARTER:
                    summaryFlag = SummaryFlag.MONTH;
                    break;
                default:
                    summaryFlag = SummaryFlag.DAY;
            }
        }
        return summaryFlag;
    }

    /**
     * 获取开始和截止时间<BR>
     *
     * @return Tuple2<开始时间, 结束时间>
     * @throws ServiceException 异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-10-23 14:48
     */
    public Tuple2<String, String> getParamStartAndEndTime() throws ServiceException {
        String start = null;
        String end = null;
        if (!CMyString.isEmpty(getStartDate())) {
            start = getStartDate() + " 00:00:00";
        }
        if (!CMyString.isEmpty(getEndDate())) {
            end = getEndDate() + " 23:59:59";
        }
        if (CMyString.isEmpty(start) || CMyString.isEmpty(end)) {
            return TimeTools.getParamStartAndEndTime(getTimeFlag(), getCustomTime());
        }
        return new Tuple2<>(start, end);
    }

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if (timeFlag == null) {
            throw new ParamInvalidException("时间标识不能为空！");
        }
        switch (timeFlag) {
            case NONE:
            case YEAR:
            case MONTH:
            case QUARTER:
            case WEEK:
                break;
            case DAY:
                if (CMyString.isEmpty(startDate) || CMyString.isEmpty(endDate)) {
                    throw new ParamInvalidException("自定义统计时起止时间不能为空！");
                }
                break;
            default:
                throw new ParamInvalidException("未知标识！");
        }
        if (!CMyString.isEmpty(startDate)) {
            Optional.ofNullable(
                    Try.of(() -> TimeUtils.stringToDate(startDate, timeFlag.getFormat())).getOrElseThrow(err -> new ServiceException(err.getMessage(), err))
            ).orElseThrow(() -> new ParamInvalidException("startDate=[" + startDate + "],不是[" + timeFlag.getFormat() + "]格式"));
        }
        if (!CMyString.isEmpty(endDate)) {
            Optional.ofNullable(
                    Try.of(() -> TimeUtils.stringToDate(endDate, timeFlag.getFormat())).getOrElseThrow(err -> new ServiceException(err.getMessage(), err))
            ).orElseThrow(() -> new ParamInvalidException("endDate=[" + endDate + "],不是[" + timeFlag.getFormat() + "]格式"));
        }
        return true;
    }
}
