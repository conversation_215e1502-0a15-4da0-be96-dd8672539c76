package com.trs.gov.statistics.DTO;


import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.statistics.constant.TimeSearchFlag;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 工时统计请求参数
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-10-21 17:56
 * @version 1.0
 * @since 1.0
 */
public class WorkTimeStatisticsDTO extends TimeDTO {


    public static WorkTimeStatisticsDTO of(String timeFlag) throws ServiceException {
        WorkTimeStatisticsDTO dto = new WorkTimeStatisticsDTO();
        dto.setTimeFlag(TimeSearchFlag.findTimeSearchFlagByString(timeFlag).orElseThrow(() -> new ParamInvalidException(timeFlag + "不存在！")));
        dto.isValid();
        return dto;
    }

    public static WorkTimeStatisticsDTO of(TimeSearchFlag timeFlag) throws ServiceException {
        WorkTimeStatisticsDTO dto = new WorkTimeStatisticsDTO();
        dto.setTimeFlag(timeFlag);
        dto.isValid();
        return dto;
    }

    public static WorkTimeStatisticsDTO of(String timeFlag, String startDate, String endDate) throws ServiceException {
        WorkTimeStatisticsDTO dto = new WorkTimeStatisticsDTO();
        dto.setTimeFlag(TimeSearchFlag.findTimeSearchFlagByString(timeFlag).orElseThrow(() -> new ParamInvalidException(timeFlag + "不存在！")));
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        dto.isValid();
        return dto;
    }

    public static WorkTimeStatisticsDTO of(TimeSearchFlag timeFlag, String startDate, String endDate) throws ServiceException {
        WorkTimeStatisticsDTO dto = new WorkTimeStatisticsDTO();
        dto.setTimeFlag(timeFlag);
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        dto.isValid();
        return dto;
    }
}
