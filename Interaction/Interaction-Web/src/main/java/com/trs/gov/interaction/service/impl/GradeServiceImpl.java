package com.trs.gov.interaction.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.base.Report;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.interaction.DTO.GradeDTO;
import com.trs.gov.interaction.DTO.GradeExportDTO;
import com.trs.gov.interaction.DTO.OprRecordDTO;
import com.trs.gov.interaction.VO.GradeVO;
import com.trs.gov.interaction.mgr.GradeMgr;
import com.trs.gov.interaction.mgr.OprRecordMgr;
import com.trs.gov.interaction.service.GradeService;
import com.trs.gov.message.DTO.CreateNoticeDTO;
import com.trs.gov.message.service.SendNoticeMesssageService;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.service.IMessageService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/21 23:34
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class GradeServiceImpl implements GradeService {
    @Autowired
    private GradeMgr gradeMgr;
    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    @Autowired
    private OprRecordMgr oprRecordMgr;

    @Reference(check = false, timeout = 60000)
    private IMessageService iMessageService;
    @Reference(check = false, timeout = 60000)
    private SendNoticeMesssageService noticeMesssageService;
    @Override
    public RestfulResults<List<GradeVO>> getGradeList(GradeDTO dto) {
        List<GradeVO> gradeList;
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService,dto);
            //调用业务方法
            gradeList = gradeMgr.getGradeList(dto);
        } catch (Exception e) {
            log.error("获取数据异常",e);
            return RestfulResults.error("获取异常,异常信息:" + e.getMessage());
        }
        return RestfulResults.ok(gradeList).addMsg("获取评价成功").addTotalCount(Long.parseLong(String.valueOf(gradeList.size())));
    }

    @Override
    public RestfulResults<Report> saveGrade(String gradeInfos) throws ServiceException {
        //获取评价DTO集合
        List<GradeDTO> gradeDTOS = JSONObject.parseArray(gradeInfos, GradeDTO.class);
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, gradeDTOS.get(0));
        gradeMgr.saveAllGrade(gradeDTOS);
        try {
            //发送通知消息
            WorkOrderSearchDTO workOrderSearchDTO = new WorkOrderSearchDTO();
            workOrderSearchDTO.setId(gradeDTOS.get(0).getWorkOrderId());
            workOrderSearchDTO.setToken(gradeDTOS.get(0).getToken());
            workOrderSearchDTO.setUnitId(gradeDTOS.get(0).getUnitId());
            BaseUtils.setUserInfoToDTO(workOrderSearchDTO);
            CreateNoticeDTO createNoticeDTO = iMessageService.buildbaseMessage(workOrderSearchDTO);
            createNoticeDTO.setMessageConfigType(OperateNameConstant.APPRAISE_WORK_ORDER);
            BaseUtils.setUserInfoToDTO(createNoticeDTO);
            RestfulResults restfulResults = noticeMesssageService.sendNoticeMessage(createNoticeDTO);
            if (!restfulResults.getCode().equals("200")){
                log.error(restfulResults.getMsg());
            }
            //保存操作记录
            OprRecordDTO oprRecordDTO = new OprRecordDTO();
            oprRecordDTO.setCrUnitId(Long.parseLong(gradeDTOS.get(0).getUnitId()));
            oprRecordDTO.setCrUser(gradeDTOS.get(0).getCrUser());
            oprRecordDTO.setOprKey(OperateNameConstant.APPRAISE_WORK_ORDER);
            oprRecordDTO.setWorkOrderId(gradeDTOS.get(0).getWorkOrderId());
            oprRecordMgr.saveOprRecord(oprRecordDTO);
        }
        catch (Exception e){
           log.error(e.getMessage(),e);
        }
        return RestfulResults.ok(new Report("评价","保存评价成功")).addMsg("保存成功");
    }

    @Override
    public GradeVO exportGrade(GradeExportDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        dto.isValid();
        return gradeMgr.exportGrade(dto).orElseThrow(() -> new ServiceException("同步得分数据【"+dto.getOldGradeId()+"】失败！"));
    }
}
