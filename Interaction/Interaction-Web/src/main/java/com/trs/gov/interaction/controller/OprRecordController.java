package com.trs.gov.interaction.controller;

import com.trs.common.base.Report;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.interaction.DTO.OperRecordDTO;
import com.trs.gov.interaction.DTO.OprRecordDTO;
import com.trs.gov.interaction.DTO.QueryWorkOrderIdsDTO;
import com.trs.gov.interaction.DTO.StatisticsCountDTO;
import com.trs.gov.interaction.VO.OperRecordVO;
import com.trs.gov.interaction.VO.OprRecodDeptVO;
import com.trs.gov.interaction.VO.RollBackWorkOrderVO;
import com.trs.gov.interaction.service.OprRecordService;
import com.trs.web.builder.base.RestfulResults;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.vavr.control.Try;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 回复相关控制器
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-23 17:37
 * @version 1.0
 * @since 1.0
 */
@Api(value = "工单操作记录类",tags = "OprRecordController")
@RestController
@RequestMapping("/interaction/record")
public class OprRecordController {

    @Autowired
    private OprRecordService service;

    @PostMapping("/reply")
    public RestfulResults<Report> reply(OprRecordDTO dto) {
        return Try.of(() -> {
            BaseUtils.checkDTO(dto);
            if (!OperateNameConstant.REPLY_WORK_ORDER.equals(dto.getOprKey())) {
                throw new ParamInvalidException("oprKey异常，该接口只支持回复操作！");
            }
            return service.saveOprRecord(dto);
        }).getOrElseGet(err -> RestfulResults.error(err.getMessage()));
    }

    @ApiOperation(value = "获取工单的评价单位列表")
    @ApiImplicitParam(name = "workOrderId",value = "工单Id",required = true,type = "long")
    @PostMapping("/dept")
    public RestfulResults<List<OprRecodDeptVO>> getOperUnits(Long workOrderId){
        return service.getOperUnits(workOrderId);
    }

    @ApiOperation(value = "获取工单的操作记录")
    @ApiImplicitParam(name = "workOrderId",value = "工单Id",required = true,type = "long")
    @PostMapping("/log")
    public RestfulResults<List<OperRecordVO>> getOperRecordByWorkOrderId(OperRecordDTO operRecordDTO){
        return service.getOperRecordByWorkOrderId(operRecordDTO);
    }

    @ApiOperation(value = "获取首页所有工单最新动态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "workOrderId",value = "工单Id",required = true,type = "long"),
            @ApiImplicitParam(name = "siteId",value = "站点id",required = false,type = "long")
    })
    @PostMapping("/alllog")
    public RestfulResults<List<OperRecordVO>> getAllOperRecord(OperRecordDTO operRecordDTO){
        return service.getAllOperRecord(operRecordDTO);
    }


    @PostMapping("/replyCount")
    public RestfulResults<Long> addCommentNum(StatisticsCountDTO statisticsCountDTO){
        return service.addCommentNum(statisticsCountDTO);
    }
    @PostMapping("/ids")
    RestfulResults<List<Long>> listWorkOrderIdByUserAndUnitAndType(QueryWorkOrderIdsDTO dto){
        return service.listWorkOrderIdByUserAndUnitAndType(dto);
    }

    @ApiOperation(value = "获取工单的回退列表")
    @ApiImplicitParam(name = "workOrderId",value = "工单Id",required = true,type = "long")
    @PostMapping("/rollBack")
    public RestfulResults<List<RollBackWorkOrderVO>> listRollBackWorkOrderLog(OprRecordDTO oprRecordDTO){
        return service.listRollBackWorkOrderLog(oprRecordDTO);
    }

    @ApiOperation(value = "获取所有工单的最新动态")
    @PostMapping("/listNewestTrends")
    public RestfulResults<List<OperRecordVO>> listNewestTrends(OperRecordDTO dto){
        return service.listNewestTrends(dto);
    }



}
