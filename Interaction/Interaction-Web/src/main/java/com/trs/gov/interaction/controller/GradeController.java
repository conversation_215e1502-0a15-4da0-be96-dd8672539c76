package com.trs.gov.interaction.controller;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.GradeDTO;
import com.trs.gov.interaction.VO.GradeVO;
import com.trs.gov.interaction.service.impl.GradeServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020/9/21 23:33
 * @version 1.0
 * @since  1.0
 */
@RestController
@RequestMapping("/interaction/grade")
public class GradeController {

    @Autowired
    private GradeServiceImpl gradeService;

    /**
     * 获取评价列表<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 23:36
     * @param  dto  请求参数
     * @throws
     * @return
     */
    @RequestMapping(value = "getGradeList",method = {RequestMethod.GET})
    public RestfulResults<List<GradeVO>> getGradeList(GradeDTO dto){
        return gradeService.getGradeList(dto);
    }

    /**
     * 保存评价<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/21 23:38
     * @param  gradeInfos  请求参数
     * @throws
     * @return
     */
    @RequestMapping(value = "saveGrade",method = {RequestMethod.POST})
    RestfulResults<Report> saveGrade(String gradeInfos) throws ServiceException {
        return gradeService.saveGrade(gradeInfos);
    }
}
