package com.trs.gov.interaction.mgr;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.OprRecordDTO;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 操作业务类接口
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-23 18:53
 * @version 1.0
 * @since 1.0
 */
public interface IOprRecordMgr {
    /**
     * 保存操作数据<BR>
     *
     * @param dto 请求参数
     * @return 保存结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     */
    Report saveOprRecord(OprRecordDTO dto) throws ServiceException;
}
