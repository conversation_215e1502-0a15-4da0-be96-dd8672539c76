package com.trs.gov.interaction.mgr;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.file.util.FileUtils;
import com.trs.gov.interaction.DO.OprRecordDO;
import com.trs.gov.interaction.DTO.*;
import com.trs.gov.interaction.VO.GradeVO;
import com.trs.gov.interaction.VO.OperRecordVO;
import com.trs.gov.interaction.VO.OprRecodDeptVO;
import com.trs.gov.interaction.VO.RollBackWorkOrderVO;
import com.trs.gov.interaction.mapper.OprRecordMapper;
import com.trs.gov.interaction.service.GradeService;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.management.DTO.GroupUnitListSearchDTO;
import com.trs.gov.management.DTO.UnitGroupSearchDTO;
import com.trs.gov.management.DTO.UnitSearchDTO;
import com.trs.gov.management.VO.UnitGroupVO;
import com.trs.gov.management.VO.UnitVO;
import com.trs.gov.management.VO.UserCountVO;
import com.trs.gov.management.service.SiteRelationService;
import com.trs.gov.management.service.UnitGroupRelationService;
import com.trs.gov.management.service.UnitGroupService;
import com.trs.gov.management.service.UnitService;
import com.trs.gov.workorder.DTO.WorkOrderSearchDTO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import com.trs.gov.workorder.constant.WorkOrderConstant;
import com.trs.gov.workorder.service.IWorkOrderService;
import com.trs.user.DTO.UserDTO;
import com.trs.user.VO.UserVO;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import com.trs.web.restful.RestfulJsonHelper;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 操作记录业务类
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-23 18:52
 * @version 1.0
 * @since 1.0
 */
@Component
@Slf4j
@Primary
public class OprRecordMgr implements IOprRecordMgr {

    @Autowired
    private OprRecordMapper mapper;

    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileService;

    @Reference(check = false, timeout = 60000)
    private UnitService unitService;

    @Reference(check = false, timeout = 60000)
    private IUserService iUserService;

    @Reference(check = false, timeout = 60000)
    private SiteRelationService siteRelationService;

    @Reference(check = false, timeout = 60000)
    private IWorkOrderService iWorkOrderService;

    @Reference(check = false, timeout = 60000)
    private GradeService gradeService;

    @Reference(check = false, timeout = 60000)
    private UnitGroupService unitGroupService;

    @Reference(check = false, timeout = 60000)
    private UnitGroupRelationService unitGroupRelationService;

    /**
     * 保存操作数据<BR>
     *
     * @param dto 请求参数
     * @return 保存结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     */
    @Override
    public Report saveOprRecord(OprRecordDTO dto) throws ServiceException {
        BaseUtils.checkDTO(dto);
        checkUserAndUnit(dto);
        OprRecordDO oprRecordDO = null;
        try {
            oprRecordDO = new OprRecordDO();
            BaseUtils.copyProperties(dto, oprRecordDO);
            mapper.insert(oprRecordDO);
            if (!CMyString.isEmpty(dto.getPicList())) {
                FileUtils.saveFile(fileService, dto.getPicList(), OprRecordDO.OBJ_TYPE, Long.toString(oprRecordDO.getId()), "picList");
            }
            if (!CMyString.isEmpty(dto.getFileList())) {
                FileUtils.saveFile(fileService, dto.getFileList(), OprRecordDO.OBJ_TYPE, Long.toString(oprRecordDO.getId()), "fileList");
            }
        } catch (Exception e) {
            log.error("保存操作记录失败!", e);
            Long id = oprRecordDO.getId();
            if (id != null) {
                Try.of(() -> {
                    FileUtils.deleteFile(fileService, OprRecordDO.OBJ_TYPE, Long.toString(id), "picList");
                    return 0;
                }).onFailure(err -> log.warn("清除文件出错！", err));

                Try.of(() -> {
                    FileUtils.deleteFile(fileService, OprRecordDO.OBJ_TYPE, Long.toString(id), "fileList");
                    return 0;
                }).onFailure(err -> log.warn("清除文件出错！", err));

                Try.of(() -> mapper.deleteById(id)).onFailure(err -> log.warn("删除记录", err));
            }
            throw new ServiceException("保存操作记录失败!", e);
        }
        return new Report("操作记录", "成功添加操作记录！");
    }

    public List<Long> getUnitsByGroupId(Long groupId) throws Exception {
        List<Long> result = new ArrayList<>();
        GroupUnitListSearchDTO groupUnitListSearchDTO = new GroupUnitListSearchDTO();
        groupUnitListSearchDTO.setGroupId(String.valueOf(groupId));
        BaseUtils.setUserInfoToDTO(groupUnitListSearchDTO);
        RestfulResults<List<UserCountVO>> groupUnitInfoListByGroupId = unitGroupService.getGroupUnitInfoListByGroupIdNotRight(groupUnitListSearchDTO);
        checkRestfulResults(groupUnitInfoListByGroupId);
        result.addAll(groupUnitInfoListByGroupId.getDatas().stream().map(UserCountVO::getUnitId).collect(Collectors.toList()));
        return result;
    }

    /**
     * @return com.trs.web.builder.base.RestfulResults<java.util.List < com.trs.gov.interaction.VO.OprRecodDeptVO>>
     * @Description 获取单位列表
     * @Param [workOrderId]  //抄送需要被评价，但是 工单的操作记录 里面 不展示
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/25 16:22
     **/
    public RestfulResults<List<OprRecodDeptVO>> getOperUnits(Long workOrderId) {
        List<OprRecodDeptVO> deptVOList = new ArrayList<>();
        try {
            List<OprRecordDO> oprRecordDOList = mapper.selectList((new QueryWrapper<OprRecordDO>())
                    .eq("work_order_id", workOrderId)
                    .in("ope_record_type", Arrays.asList(OperateNameConstant.CREATE_WORK_ORDER,
                            OperateNameConstant.REPLY_WORK_ORDER,OperateNameConstant.ASSIGN_WORK_ORDER,OperateNameConstant.ROLLBACK_WORK_ORDER,
                            OperateNameConstant.UPDATE_WORK_ORDER,OperateNameConstant.UPDATE_WORK_ORDER_DEAL,
                            OperateNameConstant.FINISH_WORK_ORDER,OperateNameConstant.APPRAISE_WORK_ORDER, OperateNameConstant.COPY_WORK_ORDER,
                            OperateNameConstant.REOPEN_WORK_ORDER))
                    .isNotNull("assign_type"));
            if (oprRecordDOList == null) {
                return RestfulResults.ok(deptVOList).addTotalCount(0L).addMsg("工单[" + workOrderId + "]暂无任何相关数据!");
            }
            List<Long> unitIds = new ArrayList<>();
            for (OprRecordDO oprRecordDO : oprRecordDOList) {
                if(oprRecordDO.getOprKey().equals(OperateNameConstant.COPY_WORK_ORDER)){
                    if(oprRecordDO.getAssignType().equals(AssignConstant.ASSIGN_TO_ALL)){
                        //如果是抄送给全部单位  则返回全部即可
                        List<OprRecodDeptVO> allDept = mapper.allDistinctUnit();
                        return RestfulResults.ok(allDept == null?new ArrayList<OprRecodDeptVO>():allDept).addMsg("获取所有单位的抄送记录成功!");
                    }else if (oprRecordDO.getAssignType().equals(AssignConstant.ASSIGN_TO_GROUP)){
                        List<Long> unitsByGroupId = getUnitsByGroupId(oprRecordDO.getGroupId());
                        if(!CollectionUtils.isEmpty(unitsByGroupId)){
                            unitIds.addAll(unitsByGroupId);
                        }
                    }else if (oprRecordDO.getAssignType().equals(AssignConstant.ASSIGN_TO_UNIT)
                            || oprRecordDO.getAssignType().equals(AssignConstant.ASSIGN_TO_USER)){
                        unitIds.add(oprRecordDO.getTargetUnitId());
                    }
                }else {
                    if (oprRecordDO.getTargetUnitId() != null ){
                        unitIds.add(oprRecordDO.getTargetUnitId());
                    }
                }
            }
            if(CollectionUtils.isEmpty(unitIds)){
                return RestfulResults.ok(new ArrayList<OprRecodDeptVO>()).addMsg("可评价的单位列表为空!");
            }
            String unitIdsStr = unitIds.stream().map(a->String.valueOf(a)).collect(Collectors.joining(","));
            UnitSearchDTO unitSearchDTO = new UnitSearchDTO();
            unitSearchDTO.setRpcTag(true);
            unitSearchDTO.setId(unitIdsStr);
            RestfulResults<List<UnitVO>> listRestfulResults = unitService.queryUnitList(unitSearchDTO);
            if (listRestfulResults.getCode().equals("500")) {
                throw new ServiceException("系统异常,未成功从其他服务获取到单位列表信息!");
            }
            List<UnitVO> datas = listRestfulResults.getDatas();
            if (datas == null) {
                return RestfulResults.ok(deptVOList).addTotalCount(0L).addMsg("工单[" + workOrderId + "]暂无可评价相关数据!");
            }
            datas.forEach(a -> {
                OprRecodDeptVO oprRecodDeptVO = new OprRecodDeptVO();
                oprRecodDeptVO.setUnitId(a.getId());
                oprRecodDeptVO.setUnitName(a.getUnitName());
                deptVOList.add(oprRecodDeptVO);
            });
        } catch (Exception e) {
            log.error("获取数据异常！", e);
        }
        return RestfulResults.ok(deptVOList).addTotalCount(Long.valueOf(deptVOList.size())).addMsg("获取数据成功!");
    }

    /**
     * @Description  校验  用户 和 单位是否存在
     * @Param [dto]
     * @return void
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/7 18:05
     **/
    public void checkUserAndUnit(OprRecordDTO dto) throws ServiceException {
        if(!CMyString.isEmpty(dto.getTargetUser())){
            getUserVO(dto.getTargetUser());
        }
        if(dto.getTargetUnitId()!=null){
            getUnitVO(String.valueOf(dto.getTargetUnitId()));
        }
        if (dto.getGroupId() != null){
            getGroupVO(dto.getGroupId());
        }
    }

    /**
     * @return com.trs.web.builder.base.RestfulResults<java.util.List < com.trs.gov.interaction.VO.OperRecordVO>>
     * @Description 获取工单记录失败!
     * @Param [workOrderId]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/25 16:53
     **/
    public RestfulResults<List<OperRecordVO>> getOperRecordByWorkOrderId(OperRecordDTO operRecordDTO) throws ServiceException {
        List<OperRecordVO> operRecordVOList = new ArrayList<>();
        Optional<Page<OperRecordVO>> resultOptionals = listOperRecordVO(operRecordDTO);

        //操作都没有相关数据,则评价列表更没有
        if (!resultOptionals.isPresent()) {
            return RestfulResults.ok(operRecordVOList)
                    .addPageNum(operRecordDTO.getPageNum())
                    .addPageSize(operRecordDTO.getPageSize())
                    .addTotalCount(0L).addMsg("工单["+operRecordDTO.getWorkOrderId()+"]的操作记录数据为空!");
        }
        Page<OperRecordVO> operRecordVOPage = resultOptionals.get();
        return RestfulResults.ok(operRecordVOPage.getRecords())
                .addMsg("获取工单["+operRecordDTO.getWorkOrderId()+"]的操作记录数据成功!")
                .addTotalCount(operRecordVOPage.getTotal())
                .addPageSize((int) operRecordVOPage.getSize())
                .addPageNum((int) operRecordVOPage.getCurrent());
    }

    /**
     * @return com.trs.web.builder.base.RestfulResults<java.util.List < com.trs.gov.interaction.VO.OperRecordVO>>
     * @Description 首页最新动态
     * @Param [operRecordDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/28 11:14
     **/
    public RestfulResults<List<OperRecordVO>> getAllOperRecord(OperRecordDTO operRecordDTO) throws ServiceException {
        List<OperRecordVO> recordVOList = new ArrayList<>();
        operRecordDTO.setSearchAll(true);
        Optional<Page<OperRecordVO>> resultOptionals = listOperRecordVO(operRecordDTO);
        if (!resultOptionals.isPresent()) {
            return RestfulResults.ok(recordVOList).addPageSize(operRecordDTO.getPageSize()).addPageNum(operRecordDTO.getPageNum()).addTotalCount(0L).addMsg("站点下的动态数据为空!");
        }
        Page<OperRecordVO> operRecordVOPage = resultOptionals.get();
        return RestfulResults.ok(operRecordVOPage.getRecords()).addMsg("获取数据成功!").addTotalCount(operRecordVOPage.getTotal()).addPageSize((int) operRecordVOPage.getSize()).addPageNum((int) operRecordVOPage.getCurrent());
    }

    /**
     * @return java.util.Optional<com.baomidou.mybatisplus.extension.plugins.pagination.Page < com.trs.gov.interaction.VO.OperRecordVO>>
     * @Description 构造返回内容
     * @Param [operRecordDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/28 11:18
     **/
    public Optional<Page<OperRecordVO>> listOperRecordVO(OperRecordDTO operRecordDTO) throws ServiceException {
        //初始化分页
        Page<OperRecordVO> resultPage = new Page<>();
        List<OperRecordVO> recordVOList = new ArrayList<>();
        QueryWrapper<OprRecordDO> wrapper = new QueryWrapper<>();
        List<WorkOrderVO> workOrderVOS = null;
        if(operRecordDTO.isSearchAll()){
            workOrderVOS = listWorkOrderVO(operRecordDTO.getSiteId());
            if(workOrderVOS == null || workOrderVOS.size() == 0){
                return Optional.empty();
            }
            List<Long> workOrderIds = workOrderVOS.stream().map(WorkOrderVO::getId).collect(Collectors.toList());
            //如果站点中不存在该单位，直接查询结果为不存在【即id = -1】
            wrapper.in(workOrderIds != null && workOrderIds.size() > 0,"work_order_id",workOrderIds)
                    .orderByDesc("cr_time");
        }else{
            wrapper.in("ope_record_type", Arrays.asList(OperateNameConstant.CREATE_WORK_ORDER,
                    OperateNameConstant.REPLY_WORK_ORDER,OperateNameConstant.ASSIGN_WORK_ORDER,OperateNameConstant.ROLLBACK_WORK_ORDER,
                    OperateNameConstant.UPDATE_WORK_ORDER,OperateNameConstant.UPDATE_WORK_ORDER_DEAL,
                    OperateNameConstant.FINISH_WORK_ORDER,OperateNameConstant.APPRAISE_WORK_ORDER,
                    OperateNameConstant.REOPEN_WORK_ORDER))
                    .eq("work_order_id", operRecordDTO.getWorkOrderId())
                    .orderByAsc("cr_time");
        }
        //设置分页
        Page<OprRecordDO> page = new Page<>(operRecordDTO.getPageNum(), operRecordDTO.getPageSize());
        resultPage.setSize(operRecordDTO.getPageSize());
        resultPage.setPages(operRecordDTO.getPageNum());
        Page<OprRecordDO> oprRecordDOPage = mapper.selectPage(page, wrapper);
        //如果为空,直接返回
        List<OprRecordDO> records = oprRecordDOPage.getRecords();
        resultPage.setTotal(oprRecordDOPage.getTotal());
        if (records == null || records.size() == 0) {
            return Optional.empty();
        }
        //获取工单数据列表和对应单位列表
        List<UnitVO> unitVOS = listUnit(oprRecordDOPage);
        for (OprRecordDO oprRecordDO : records) {
            //构造返回结果
            OperRecordVO operRecordVO = new OperRecordVO();
            //拷贝到VO
            BaseUtils.copyProperties(oprRecordDO, operRecordVO);
            if (operRecordDTO.isSearchAll()) {
                WorkOrderVO workOrder = getWorkOrder(workOrderVOS,oprRecordDO.getWorkOrderId());
                operRecordVO.setWorkOrderId(workOrder.getId());
                operRecordVO.setWorkOrderTopTypeName(workOrder.getWorkOrderTopTypeName());
                operRecordVO.setWorkOrderCrTime(workOrder.getCrTime());
                operRecordVO.setWorkOrderContent(workOrder.getContent());
                operRecordVO.setPriority(workOrder.getPriority());
            }
            String s = OperateNameConstant.getTypeDesc(oprRecordDO.getOprKey()).orElseThrow(() -> new ServiceException("不存在该操作类型[" + operRecordVO.getOprKey() + "]!"));
            operRecordVO.setOperateDesc(s.replace("完成","解决"));
            UnitVO unitVO = getUnitVO(unitVOS,oprRecordDO.getCrUnitId());
            operRecordVO.setCrUnitName(unitVO.getUnitName());
            //存储用户相关信息
            UserVO userVO = getUserVO(oprRecordDO.getCrUser()).orElseThrow(() -> new ServiceException("数据库中无用户[" + oprRecordDO.getCrUser() + "]的相关数据!"));
            operRecordVO.setAvatar(userVO.getAvatar());
            operRecordVO.setCrUserTrueName(userVO.getTrueName());
            if(StringUtils.isEmpty(operRecordVO.getTargetUser())){
                operRecordVO.setTargetUser(null);
            }
            if (StringUtils.isNotEmpty(operRecordVO.getTargetUser())) {
                UserVO targetUserDTO = getUserVO(operRecordVO.getTargetUser()).orElseThrow(() -> new ServiceException("数据库中无用户[" + oprRecordDO.getTargetUser() + "]的相关数据!"));
                operRecordVO.setTargetUserTrueName(targetUserDTO.getTrueName());
            }
            if(oprRecordDO.getTargetUnitId() != null){
                UnitVO unit = getUnitVO(String.valueOf(oprRecordDO.getTargetUnitId())).orElseThrow(() -> new ServiceException("系统异常,获取用户单位信息失败!"));
                operRecordVO.setTargetUnit(unit.getUnitName());
            }
            if (oprRecordDO.getGroupId() != null){
                UnitGroupVO unitGroupVO = getGroupVO(oprRecordDO.getGroupId()).orElseThrow(() -> new ServiceException("系统异常！获取分组【" + oprRecordDO.getGroupId() + "】信息,失败!"));
                operRecordVO.setGroupName(unitGroupVO.getGroupName());
            }
            if (!operRecordDTO.isSearchAll()) {
                ObjDTO one = ObjDTO.of(OprRecordDO.OBJ_TYPE, Long.toString(oprRecordDO.getId()), "fileList");
                ObjDTO two = ObjDTO.of(OprRecordDO.OBJ_TYPE, Long.toString(oprRecordDO.getId()), "picList");
                BaseUtils.setUserInfoToDTO(one, two);
                List<FileVO> fileList = fileService.getFileListOfObj(one);
                List<FileVO> picList = fileService.getFileListOfObj(two);
                operRecordVO.setFileList(fileList);
                operRecordVO.setPicList(picList);
                if(operRecordVO.getOprKey().equals(OperateNameConstant.APPRAISE_WORK_ORDER)){
                    Optional<List<GradeVO>> gradeVOS = listGradeVO(operRecordVO.getWorkOrderId(), operRecordVO.getCrUser());
                    if(gradeVOS.isPresent()){
                        operRecordVO.setGradeList(gradeVOS.get());
                    }
                }
            }
            recordVOList.add(operRecordVO);
            //构建用户信息（ 用户名+联系方式）
            for (OperRecordVO vo: recordVOList) {
                if (com.trs.common.utils.StringUtils.isNullOrEmpty(vo.getCrUser()) == false){
                    vo.setCrUserTrueName(buildUserInfo(vo.getCrUser()));
                }
                if (com.trs.common.utils.StringUtils.isNullOrEmpty(vo.getTargetUser()) == false){
                    vo.setTargetUserTrueName(buildUserInfo(vo.getTargetUser()));
                }
            }
        }
        resultPage.setRecords(recordVOList);
        return Optional.ofNullable(resultPage);
    }

    /**
     * 通过用户名查询用户信息，并构建 用户名+联系方式
     * @param userName
     * @return
     */
    public String buildUserInfo(String userName) throws ServiceException {
        UserVO userVO = findUserByUserName(userName);
        return userVO.getTrueName()+" "+userVO.getPhone();
    }

    /**
     * 根据用户名获取用户
     *
     * @param userName
     * @return
     * @throws ServiceException
     */
    public UserVO findUserByUserName(String userName) throws ServiceException {
        if (com.trs.common.utils.StringUtils.isEmpty(userName)) {
            throw new ServiceException("用户名不能为空：" + userName);
        }
        UserDTO userDTO = new UserDTO();
        userDTO.setUserName(userName);
        UserVO userVO = iUserService.getBaseUserInfoByUserName(userDTO);
        if (userVO == null) {
            throw new ServiceException("用户名未找到：" + userName);
        }
        return userVO;
    }

    /**
     * @Description  是否是投诉类型得工单
     * @Param [workOrderId]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 13:28
     **/
    public boolean isComplaint(Long workOrderId) throws ServiceException {
        Long workOrderTopId = mapper.getWorkOrderTopId(workOrderId);
        if(workOrderTopId == null){
            throw new ServiceException("workOrderTopId不能为空!");
        }
        if(workOrderTopId.equals(WorkOrderConstant.TOP_TYPE_COMPLAINT)){
            return true;
        }
        return false;
    }

    /**
     * @Description  获取单位ids的详情
     * @Param [page]
     * @return java.util.List<com.trs.gov.management.VO.UnitVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/12 15:50
     **/
    public List<UnitVO> listUnit(Page<OprRecordDO> page){
        List<OprRecordDO> records = page.getRecords();
        List<UnitVO> datas = null;
        try {
            String ids = records.stream().map(a -> String.valueOf(a.getCrUnitId())).collect(Collectors.joining(","));
            UnitSearchDTO unitSearchDTO = new UnitSearchDTO();
            unitSearchDTO.setRpcTag(true);
            unitSearchDTO.setId(ids);
            unitSearchDTO.setPageNum(0);
            unitSearchDTO.setPageSize((int)page.getSize());
            UserUtils.checkDTOAndLoadUserInfoByDTO(iUserService, unitSearchDTO);
            //获取数据
            RestfulResults<List<UnitVO>> listRestfulResults = unitService.queryUnitList(unitSearchDTO);
            checkRestfulResults(listRestfulResults);
            datas = listRestfulResults.getDatas();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return datas;
    }

    /**
     * @Description  获取工单的创建抄送主办受理为当前登录单位和用户的工单id
     * @Param []
     * @return java.util.List<java.lang.Long>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/12 15:58
     **/
    public List<WorkOrderVO> listWorkOrderVO(Long siteId){
        List<WorkOrderVO> datas =null;
        try {
            WorkOrderSearchDTO workOrderSearchDTO = new WorkOrderSearchDTO();
            workOrderSearchDTO.setType(WorkOrderConstant.SREARCH_ALL);
            workOrderSearchDTO.setPageNum(0);
            if(siteId != null && !siteId.equals(0L)){
                workOrderSearchDTO.setSiteIdList(String.valueOf(siteId));
            }
            workOrderSearchDTO.setContainNotice(false);
            workOrderSearchDTO.setPageSize(10000);
            UserUtils.checkDTOAndLoadUserInfoByDTO(iUserService, workOrderSearchDTO);
            RestfulResults<List<WorkOrderVO>> listRestfulResults = iWorkOrderService.searchWorkOrder(workOrderSearchDTO);
            checkRestfulResults(listRestfulResults);
            datas = listRestfulResults.getDatas();
            return datas;
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return datas;
    }


    /**
     * @return com.trs.web.builder.base.RestfulResults<java.lang.Void>
     * @Description
     * @Param [workOrderId]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/27 11:18
     **/
    public RestfulResults<Long> addCommentNum(StatisticsCountDTO statisticsCountDTO) {
        Integer count = 0;
        try {
            count = mapper.selectCount(new QueryWrapper<OprRecordDO>().eq("work_order_id", statisticsCountDTO.getWorkOrderId())
                    .eq("ope_record_type", statisticsCountDTO.getType()));
        } catch (Exception e) {
            log.error("添加数据异常！", e);
        }
        return RestfulResults.ok(Long.valueOf(String.valueOf(count)));
    }

    /**
     * @return com.trs.web.builder.base.RestfulResults<java.util.List < com.trs.gov.interaction.VO.RollBackWorkOrderVO>>
     * @Description 获取工单记录
     * @Param [oprRecordDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/28 18:03
     **/
    public RestfulResults<List<RollBackWorkOrderVO>> listRollBackWorkOrderLog(OprRecordDTO oprRecordDTO) throws ServiceException {
        List<RollBackWorkOrderVO> result = new ArrayList<>();
        QueryWrapper<OprRecordDO> oprRecordDOQueryWrapper = new QueryWrapper<>();
        oprRecordDOQueryWrapper.eq("work_order_id", oprRecordDTO.getWorkOrderId());
        oprRecordDOQueryWrapper.in("ope_record_type",Arrays.asList(OperateNameConstant.CREATE_WORK_ORDER,
                OperateNameConstant.REPLY_WORK_ORDER, OperateNameConstant.ROLLBACK_WORK_ORDER, OperateNameConstant.ASSIGN_WORK_ORDER,
                OperateNameConstant.UPDATE_WORK_ORDER_DEAL));
        oprRecordDOQueryWrapper.orderByAsc("cr_time");
        oprRecordDOQueryWrapper.and(wrapper -> wrapper.isNotNull("target_user").or().isNotNull("target_unit_id"));
        List<OprRecordDO> oprRecordDOList = listDistic(mapper.selectList(oprRecordDOQueryWrapper));
        if (result == null || oprRecordDOList.size() == 0) {
            return RestfulResults.ok(result).addMsg("查询成功! 但数据为空!");
        }
        boolean isComplaint = isComplaint(oprRecordDTO.getWorkOrderId());
        for (OprRecordDO oprRecordDO : oprRecordDOList) {
            RollBackWorkOrderVO rollBackWorkOrderVO = new RollBackWorkOrderVO();
            BaseUtils.copyProperties(oprRecordDO, rollBackWorkOrderVO);
            UnitVO unitVO = getUnitVO(String.valueOf(oprRecordDO.getTargetUnitId())).orElseThrow(() -> new ServiceException("系统异常,获取用户单位信息失败!"));
            //在回退节点增加创建工单节点
            if(oprRecordDO.getOprKey().equals(OperateNameConstant.CREATE_WORK_ORDER) && !isComplaint){
                RollBackWorkOrderVO tempRollBack = new RollBackWorkOrderVO();
                BaseUtils.copyProperties(oprRecordDO, tempRollBack);
                UserVO userVO = getUserVO(oprRecordDO.getCrUser()).orElseThrow(() -> new ServiceException("数据库中无用户[" + oprRecordDO.getTargetUser() + "]的相关数据!"));
                UnitVO tempUnitVo = getUnitVO(String.valueOf(oprRecordDO.getCrUnitId())).orElseThrow(() -> new ServiceException("系统异常,获取用户单位信息失败!"));
                tempRollBack.setTargetUser(userVO.getUserName());
                tempRollBack.setTargetUserTrueName(userVO.getTrueName());
                tempRollBack.setTargetUnitId(oprRecordDO.getCrUnitId());
                tempRollBack.setTargetUnitName(tempUnitVo.getUnitName());
                tempRollBack.setTarget(false);
                result.add(tempRollBack);
            }
            rollBackWorkOrderVO.setTargetUnitName(unitVO.getUnitName());
            //如果目标人为空则是指定到单位的
            if(oprRecordDO.getAssignType().equals(AssignConstant.ASSIGN_TO_UNIT)){
                rollBackWorkOrderVO.setUnit(true);
                result.add(rollBackWorkOrderVO);
                continue;
            }else if (oprRecordDO.getAssignType().equals(AssignConstant.ASSIGN_TO_USER)){
                UserVO userVO = getUserVO(oprRecordDO.getTargetUser()).orElseThrow(() -> new ServiceException("数据库中无用户[" + oprRecordDO.getTargetUser() + "]的相关数据!"));
                rollBackWorkOrderVO.setOprDesc(OperateNameConstant.getTypeDesc(oprRecordDO.getOprKey()).orElseThrow(() -> new ServiceException("不存在该操作类型[" + oprRecordDO.getOprKey() + "]!")));
                rollBackWorkOrderVO.setTargetUser(userVO.getTrueName());
                result.add(rollBackWorkOrderVO);
            }
        }
        return RestfulResults.ok(result).addTotalCount(Long.valueOf(result.size())).addMsg("获取数据成功!");
    }


    /**
     * @return java.util.Optional<com.trs.gov.management.VO.UnitVO>
     * @Description 根据单位id获取单位
     * @Param [unitId]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/25 17:48
     **/
    public Optional<UnitVO> getUnitVO(String unitId) throws ServiceException {
        Try<Optional<UnitVO>> tyrResult = Try.of(() -> {
            UnitSearchDTO unitSearchDTO = new UnitSearchDTO();
            unitSearchDTO.setRpcTag(true);
            unitSearchDTO.setId(unitId);
            UserUtils.checkDTOAndLoadUserInfoByDTO(iUserService, unitSearchDTO);
            List<UnitVO> datas = unitService.queryUnitList(unitSearchDTO).getDatas();
            log.info("错误",unitService.queryUnitList(unitSearchDTO).getMsg());
            return Optional.ofNullable(datas.get(0));
        });
        if (tyrResult.isFailure()) {
            log.error("获取单位信息失败! ", tyrResult.getCause());
            throw new ServiceException("系统异常,从其它服务【unitService】查询单位【" + unitId + "】的数据失败!");
        }
        return tyrResult.get();
    }

    /**
     * @Description  查询单位详细信息
     * @Param [unitVOS, unitId]
     * @return com.trs.gov.management.VO.UnitVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/30 15:26
     **/
    public UnitVO getUnitVO(List<UnitVO> unitVOS,Long unitId) throws ServiceException {
        for (UnitVO unitVO : unitVOS) {
            if(unitId.equals(unitVO.getId())){
                return unitVO;
            }
        }
        throw new ServiceException("系统异常,获取单位【" + unitId + "】的详细信息失败!");
    }

    /**
     * @return java.util.Optional<com.trs.user.VO.UserVO>
     * @Description 根据用户名获取用户
     * @Param [username]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/25 17:48
     **/
    public Optional<UserVO> getUserVO(String username) throws ServiceException {
        Try<Optional<UserVO>> tyrResult = Try.of(() -> {
            UserDTO userDTO = new UserDTO();
            userDTO.setUserName(username);
            UserVO userVO = iUserService.getBaseUserInfoByUserName(userDTO);
            return Optional.ofNullable(userVO);
        });
        if (tyrResult.isFailure()) {
            log.error("获取用户信息失败! ", tyrResult.getCause());
            throw new ServiceException("系统异常,从其它服务【iUserService】查询用户【" + username + "】的数据失败!");
        }
        return tyrResult.get();
    }

    /**
     * @Description  获取groupID获取Name信息
     * @Param [groupId]
     * @return java.util.Optional<com.trs.gov.management.VO.UnitGroupVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/8 10:12
     **/
    public Optional<UnitGroupVO> getGroupVO(Long groupId) throws ServiceException {
        Try<Optional<UnitGroupVO>> tyrResult = Try.of(() -> {
            UnitGroupSearchDTO unitGroupSearchDTO = new UnitGroupSearchDTO();
            unitGroupSearchDTO.setId(String.valueOf(groupId));
            BaseUtils.setUserInfoToDTO(unitGroupSearchDTO);
            RestfulResults<List<UnitGroupVO>> listRestfulResults = unitGroupService.queryUnitGroupNotRight(unitGroupSearchDTO);
            checkRestfulResults(listRestfulResults);
            return Optional.ofNullable(listRestfulResults.getDatas().get(0));
        });
        if (tyrResult.isFailure()) {
            log.error("获取用户信息失败! ", tyrResult.getCause());
            throw new ServiceException("系统异常,从其它服务【unitGroupService】查询分组【" + groupId + "】的数据失败!");
        }
        return tyrResult.get();
    }

    /**
     * @return java.util.Optional<com.trs.gov.workorder.VO.WorkOrderDetailVO>
     * @Description 根据工单Id获取工单详情
     * @Param [workOrderId]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/27 14:00
     **/
    public WorkOrderVO getWorkOrder(List<WorkOrderVO> workOrderVOS,Long workOrderId) throws ServiceException {
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            if(workOrderVO.getId().equals(workOrderId)){
                return workOrderVO;
            }
        }
        throw new ServiceException("系统异常,获取工单【" + workOrderId + "】的详细信息失败!");
    }

    /**
     * @Description  获取评价列表
     * @Param [workOrderId]
     * @return java.util.Optional<java.util.List<com.trs.gov.interaction.VO.GradeVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/29 16:07
     **/
    public Optional<List<GradeVO>> listGradeVO(Long workOrderId,String username) throws ServiceException {
        GradeDTO gradeDTO = new GradeDTO();
        gradeDTO.setWorkOrderId(workOrderId);
        gradeDTO.setCrUser(username);
        RestfulResults<List<GradeVO>> gradeList = gradeService.getGradeList(gradeDTO);
        if (gradeList.getCode().equals("500")) {
            throw new ServiceException("获取评价列表失败!");
        }
        List<GradeVO> datas = gradeList.getDatas();
        if(datas == null || datas.size() == 0){
            return Optional.empty();
        }
        return Optional.ofNullable(gradeList.getDatas());
    }

    /**
     * @Description 排重，如果包含就无操作
     * @Param [oprRecordDOS]
     * @return java.util.List<com.trs.gov.interaction.DO.OprRecordDO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/12 15:46
     **/
    public List<OprRecordDO> listDistic(List<OprRecordDO> oprRecordDOS){
        List<OprRecordDO> result = new ArrayList<>();
        if(oprRecordDOS == null || oprRecordDOS.size() == 0){
            return result;
        }
        for (OprRecordDO oprRecordDO : oprRecordDOS) {
            if(result != null && result.size() != 0){
                if(!isContainRecord(result,oprRecordDO)){
                    result.add(oprRecordDO);
                }
            }else { //第一个直接加入
                result.add(oprRecordDO);
            }
        }
        return result;
    }

    /**
     * @Description  是否存在id
     * @Param [ids, id]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/12 15:48
     **/
    public boolean isExistId(List<Long> ids,Long id) throws ServiceException {
        if(id == null){
            throw new ServiceException("单位id不能为空!");
        }
        if(ids == null || ids.size() ==0){
            return false;
        }
        for (Long aLong : ids) {
            if(aLong.equals(id)){
                return true;
            }
        }
        return false;
    }

    /**
     * @Description s是否包含目标用户
     * @Param [result, targetUser, targetUnitId]
     * @return boolean
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/12 15:45
     **/
    public boolean isContainRecord(List<OprRecordDO> result,OprRecordDO recordDO){
        for (OprRecordDO oprRecordDO : result) {
            if(recordDO.getAssignType().equals(AssignConstant.ASSIGN_TO_UNIT)
                    &&recordDO.getTargetUnitId().equals(oprRecordDO.getTargetUnitId())){
                return true;
            }else if(recordDO.getAssignType().equals(AssignConstant.ASSIGN_TO_USER)
                    && recordDO.getTargetUnitId().equals(oprRecordDO.getTargetUnitId()) && recordDO.getTargetUser().equals(oprRecordDO.getTargetUser())){
                return true;
            }
        }
        return false;
    }

    public void checkRestfulResults(RestfulResults restfulResults) throws ServiceException {
        if (!restfulResults.getCode().equals(RestfulJsonHelper.STATUS_CODE.OK.getValue())) {
            throw new ServiceException(restfulResults.getMsg());
        }
    }

    private List<Long> getGroupByUnitId(Long unitId) throws ServiceException {
        List<Long> groupIdList = unitGroupRelationService.getGroupByUnitId(String.valueOf(unitId));
        return groupIdList !=null ? groupIdList : new ArrayList<>();
    }

    public List<Long> listWorkOrderIdByUserAndUnitAndType(QueryWorkOrderIdsDTO dto) throws ServiceException {
        Try<List<Long>> of = Try.of(() ->{
            if(dto.getCrUnit() == null && CMyString.isEmpty(dto.getCrUser())){
                // 先 获取分组
                dto.setGroupIds(getGroupByUnitId(dto.getTargetUnit()));
                //  判断当前登录用户是否是管理员[如果搜不到单位和用户的绑定关系即Null,肯定不是单位的负责人]
                Boolean isMaster = mapper.isMasterByUserNameAndUnitId(dto);
                dto.setIsMaster(Objects.isNull(isMaster)?0:isMaster?1:0);
            }
            return mapper.listWorkOrderIdsByUserInfo(dto);
        });
        if(of.isFailure()){
            log.error("系统异常",of.getCause());
            throw new ServiceException("获取数据出错!"+of.getCause());
        }
        return of.get();
    }

    @Transactional(rollbackFor = Exception.class)
    public Optional<OperRecordVO> exportOperRecord(OprRecordExportDTO dto) throws ServiceException{
        OprRecordDO oprRecordDO = new OprRecordDO();
        BeanUtils.copyProperties(dto,oprRecordDO);
        oprRecordDO.setExportFromOther(1);
        int insert = mapper.insert(oprRecordDO);
        if(insert < 1){
            throw new ServiceException("老操作数据["+dto.getOldRecordId()+"]保存失败!");
        }
        if(dto.getPicList() != null && dto.getPicList().size() > 0){
            try {
                FileUtils.saveFile(fileService, JSONObject.toJSONString(dto.getPicList().toArray()), UnitDO.OBJ_TYPE, Long.toString(oprRecordDO.getId()), "picList");
            } catch (Exception e) {
                throw new ServiceException(e.getMessage(),e);
            }
        }
        if(dto.getFileList() != null && dto.getFileList().size() > 0){
            try {
                FileUtils.saveFile(fileService, JSONObject.toJSONString(dto.getFileList().toArray()), UnitDO.OBJ_TYPE, Long.toString(oprRecordDO.getId()), "fileList");
            } catch (Exception e) {
                throw new ServiceException(e.getMessage(),e);
            }
        }
        OperRecordVO operRecordVO = new OperRecordVO();
        BeanUtils.copyProperties(oprRecordDO,operRecordVO);
        return Optional.of(operRecordVO);
    }


    /**
     * @Description  获取工单最新状态
     * @Param [dto]
     * @return java.util.List<com.trs.gov.interaction.VO.OperRecordVO>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/2/7 14:04
     **/
    public Tuple2<Long,List<OperRecordVO>> listNewestTrends(OperRecordDTO dto) throws ServiceException {
        PreConditionCheck.checkArgument(!com.trs.common.utils.StringUtils.isEmpty(dto.getKeyWords()),"查询名称不能为空");
        List<WorkOrderVO> workOrderVOS = mapper.listWorkOrderByKeyWords(dto.getKeyWords());
        if (CollectionUtils.isEmpty(workOrderVOS)){
            return Tuple.of(0L,Collections.emptyList());
        }
        QueryWrapper<OprRecordDO> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("cr_time");
        wrapper.in("work_order_id",workOrderVOS.stream().map(WorkOrderVO::getId).collect(Collectors.toList()));
        Page<OprRecordDO> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<OprRecordDO> oprRecordDOPage = mapper.selectPage(page, wrapper);
        List<OprRecordDO> records = oprRecordDOPage.getRecords();
        List<UnitVO> unitVOS = listUnit(oprRecordDOPage);
        if (CollectionUtils.isEmpty(records)){
            return Tuple.of(0L,Collections.emptyList());
        }

        List<OperRecordVO> resultVOS = new ArrayList<>();
        try {
            for (OprRecordDO record : records) {
                OperRecordVO recordVO = new OperRecordVO();
                BaseUtils.copyProperties(record, recordVO);
                WorkOrderVO workOrder = getWorkOrder(workOrderVOS,record.getWorkOrderId());
                recordVO.setWorkOrderId(workOrder.getId());
                recordVO.setWorkOrderTopTypeName(workOrder.getWorkOrderTopTypeName());
                recordVO.setWorkOrderCrTime(workOrder.getCrTime());
                recordVO.setWorkOrderContent(workOrder.getContent());
                recordVO.setPriority(workOrder.getPriority());
                recordVO.setSitename(workOrder.getSitename());
                String s = OperateNameConstant.getTypeDesc(record.getOprKey()).orElseThrow(() -> new ServiceException("不存在该操作类型[" + record.getOprKey() + "]!"));
                recordVO.setOperateDesc(s.replace("完成","解决"));
                UnitVO unitVO = getUnitVO(unitVOS,record.getCrUnitId());
                recordVO.setCrUnitName(unitVO.getUnitName());
                //存储用户相关信息
                UserVO userVO = getUserVO(record.getCrUser()).orElseThrow(() -> new ServiceException("数据库中无用户[" + record.getCrUser() + "]的相关数据!"));
                recordVO.setAvatar(userVO.getAvatar());
                recordVO.setCrUserTrueName(userVO.getTrueName());
                if(StringUtils.isEmpty(recordVO.getTargetUser())){
                    recordVO.setTargetUser(null);
                }
                if (StringUtils.isNotEmpty(record.getTargetUser())) {
                    UserVO targetUserDTO = getUserVO(record.getTargetUser()).orElseThrow(() -> new ServiceException("数据库中无用户[" + record.getTargetUser() + "]的相关数据!"));
                    recordVO.setTargetUserTrueName(targetUserDTO.getTrueName());
                }
                if(record.getTargetUnitId() != null){
                    UnitVO unit = getUnitVO(String.valueOf(record.getTargetUnitId())).orElseThrow(() -> new ServiceException("系统异常,获取用户单位信息失败!"));
                    recordVO.setTargetUnit(unit.getUnitName());
                }
                if (recordVO.getGroupId() != null){
                    UnitGroupVO unitGroupVO = getGroupVO(recordVO.getGroupId()).orElseThrow(() -> new ServiceException("系统异常！获取分组【" + record.getGroupId() + "】信息,失败!"));
                    recordVO.setGroupName(unitGroupVO.getGroupName());
                }
                resultVOS.add(recordVO);
            }
            return Tuple.of(oprRecordDOPage.getTotal(),resultVOS);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Tuple.of(0L,Collections.emptyList());
        }

    }

}





























