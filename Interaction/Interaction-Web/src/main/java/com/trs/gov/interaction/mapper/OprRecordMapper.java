package com.trs.gov.interaction.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.gov.interaction.DO.OprRecordDO;
import com.trs.gov.interaction.DTO.QueryWorkOrderIdsDTO;
import com.trs.gov.interaction.VO.OprRecodDeptVO;
import com.trs.gov.workorder.VO.WorkOrderVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface OprRecordMapper extends BaseMapper<OprRecordDO> {
    /**
     * @Description  根据id获取工单的顶级Id名字
     * @Param [workOrderId]
     * @return java.lang.Long
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/1/7 13:40
     **/
    @Select("select work_order_top_type_id from work_order where id = #{id}")
    Long getWorkOrderTopId(@Param("id")Long workOrderId);

    Boolean isMasterByUserNameAndUnitId(@Param("dto") QueryWorkOrderIdsDTO dto);

    List<Long> listWorkOrderIdsByUserInfo(@Param("dto") QueryWorkOrderIdsDTO dto);

    List<OprRecodDeptVO> allDistinctUnit();

    List<WorkOrderVO> listWorkOrderByIds(@Param("ids")List<Long> workOrderIds);

    List<WorkOrderVO> listWorkOrderByKeyWords(@Param("keyWords")String keyWords);
}
