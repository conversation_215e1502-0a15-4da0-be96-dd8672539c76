package com.trs.gov.interaction.service.impl;

import com.trs.common.base.PreConditionCheck;
import com.trs.common.base.Report;
import com.trs.gov.core.constant.AssignConstant;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.interaction.DTO.*;
import com.trs.gov.interaction.VO.OperRecordVO;
import com.trs.gov.interaction.VO.OprRecodDeptVO;
import com.trs.gov.interaction.VO.RollBackWorkOrderVO;
import com.trs.gov.interaction.mgr.OprRecordMgr;
import com.trs.gov.interaction.service.OprRecordService;
import com.trs.user.service.IUserService;
import com.trs.user.utils.UserUtils;
import com.trs.web.builder.base.RestfulResults;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 回复记录服务
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-23 18:50
 * @version 1.0
 * @since 1.0
 */
@Service
@Primary
@Slf4j
public class OprRecordServiceImpl implements OprRecordService {

    @Autowired
    private OprRecordMgr mgr;

    @Reference(check = false, timeout = 60000)
    private IUserService userService;

    /**
     * 获取操作记录(工单详情部分)<BR>
     *
     * @param operRecordDTO 工单id
     * @return 查询结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     */
    @Override
    public RestfulResults<List<OperRecordVO>> getOperRecordByWorkOrderId(OperRecordDTO operRecordDTO) {
        try {
            PreConditionCheck.checkNotNull(operRecordDTO.getWorkOrderId(), "工单Id不能为空!");
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService, operRecordDTO);
            return mgr.getOperRecordByWorkOrderId(operRecordDTO);
        } catch (Exception e) {
            log.error("获取操作记录异常！", e);
            return RestfulResults.error(e.getMessage());
        }
    }

    /**
     * 保存操作数据<BR>
     *
     * @param dto 请求参数
     * @return 保存结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     */
    @Override
    public RestfulResults<Report> saveOprRecord(OprRecordDTO dto) throws ServiceException {
        dto.isValid();
        if (dto.getAssignType().intValue() == AssignConstant.ASSIGN_TO_USER){
            if(dto.getTargetUnitId() == null){
                throw new ServiceException("指定类型为【1】的操作记录目标单位ID不能为空!");
            }
            if(CMyString.isEmpty(dto.getTargetUser())){
                throw new ServiceException("指定类型为【1】的操作记录目标用户名不能为空!");
            }
        }else if (dto.getAssignType().intValue() == AssignConstant.ASSIGN_TO_UNIT){
            if(dto.getTargetUnitId() == null){
                throw new ServiceException("指定类型为【2】的操作记录目标单位ID不能为空!");
            }
        }else if (dto.getAssignType().intValue() == AssignConstant.ASSIGN_TO_GROUP){
            if(dto.getGroupId() == null){
                throw new ServiceException("指定类型为【3】的操作记录组织ID不能为空!");
            }
        }
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        return RestfulResults.ok(mgr.saveOprRecord(dto)).addMsg("操作成功！");
    }

    /**
     * 获取参与工单的的单位<BR>
     *
     * @param workOrderId 工单id
     * @return 查询结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     */
    @Override
    public RestfulResults<List<OprRecodDeptVO>> getOperUnits(Long workOrderId) {
        try {
            PreConditionCheck.checkNotNull(workOrderId, "工单Id不能为空!");
            return mgr.getOperUnits(workOrderId);
        } catch (Exception e) {
            log.error("获取操作记录异常！", e);
            return RestfulResults.error(e.getMessage());
        }
    }

    /**
     * 获取所有工单和对应操作记录信息(最新动态部分)<BR>
     *
     * @param operRecordDTO 站点id
     * @return 查询结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     */
    @Override
    public RestfulResults<List<OperRecordVO>> getAllOperRecord(OperRecordDTO operRecordDTO) {
        try {
            UserUtils.checkDTOAndLoadUserInfoByDTO(userService,operRecordDTO);
            return mgr.getAllOperRecord(operRecordDTO);
        } catch (Exception e) {
            log.error("获取最新动态失败！", e);
            return RestfulResults.error("获取最新动态失败! " + e.getMessage());
        }
    }

    /**
     * 给工单添加回复数量<BR>
     *
     * @param statisticsCountDTO 工单id
     * @return 保存结果
     * @throws ServiceException 服务异常
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     */
    @Override
    public RestfulResults<Long> addCommentNum(StatisticsCountDTO statisticsCountDTO) {
        try {
            boolean isValid = statisticsCountDTO.isValid();
            return mgr.addCommentNum(statisticsCountDTO);
        } catch (Exception e) {
            log.error("统计数量失败！", e);
            return RestfulResults.error("统计数量失败!" + e.getMessage());
        }
    }

    /**
     * @return com.trs.web.builder.base.RestfulResults<com.trs.gov.interaction.VO.RollBackWorkOrderVO>
     * @Description 回退列表
     * @Param [oprRecordDTO]
     * <AUTHOR> E-mail:<EMAIL>
     * @Date 2020/9/28 17:49
     **/
    @Override
    public RestfulResults<List<RollBackWorkOrderVO>> listRollBackWorkOrderLog(OprRecordDTO oprRecordDTO) {
        try {
            if (oprRecordDTO.getWorkOrderId() == null || oprRecordDTO.getWorkOrderId() <= 0L) {
                throw new ParamInvalidException("工单ID不能为空!");
            }
            return mgr.listRollBackWorkOrderLog(oprRecordDTO);
        } catch (Exception e) {
            log.error("获取回退列表失败！", e);
            return RestfulResults.error("获取回退列表失败! " + e.getMessage());
        }
    }

    /**
     * @Description 根据目标用户信息和操作类型获取工单的ids
     * @Param [queryWorkOrderIdsDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/13 11:25
     **/
    @Override
    public RestfulResults<List<Long>> listWorkOrderIdByUserAndUnitAndType(QueryWorkOrderIdsDTO queryWorkOrderIdsDTO) {
        try {
            boolean isValid = queryWorkOrderIdsDTO.isValid();
            return RestfulResults.ok(mgr.listWorkOrderIdByUserAndUnitAndType(queryWorkOrderIdsDTO));
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return RestfulResults.error(e.getMessage());
        }
    }

    /**
     * @Description  导入评价的操作记录
     * @Param [dto]
     * @return com.trs.gov.interaction.VO.OperRecordVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 17:37
     **/
    @Override
    public OperRecordVO exportOperRecord(OprRecordExportDTO dto) throws ServiceException {
        UserUtils.checkDTOAndLoadUserInfoByDTO(userService, dto);
        dto.isValid();
        return mgr.exportOperRecord(dto).orElseThrow(() -> new ServiceException("同步回复内容数据【"+dto.getOldRecordId()+"】失败！"));
    }

    /**
     * @Description  大屏展示的最新动态
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.interaction.VO.OperRecordVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/2/7 11:31
     **/
    @Override
    public RestfulResults<List<OperRecordVO>> listNewestTrends(OperRecordDTO dto){
        return Try.of(()-> {
            Tuple2<Long, List<OperRecordVO>> longListTuple2 = mgr.listNewestTrends(dto);
            return RestfulResults.ok(longListTuple2._2).addMsg("获取数据成功!").addPageNum(dto.getPageNum()).addPageSize(dto.getPageSize()).addTotalCount(longListTuple2._1);
        }).onFailure(err->log.error(err.getMessage(),err))
        .getOrElse(RestfulResults.error("系统异常，获取数据失败!"));
    }
}
