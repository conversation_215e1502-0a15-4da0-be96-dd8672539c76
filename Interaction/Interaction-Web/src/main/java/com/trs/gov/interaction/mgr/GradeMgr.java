package com.trs.gov.interaction.mgr;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.trs.common.utils.StringUtils;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.BaseUtils;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.DTO.ObjDTO;
import com.trs.gov.file.VO.FileVO;
import com.trs.gov.file.service.IFileManagerService;
import com.trs.gov.file.util.FileUtils;
import com.trs.gov.interaction.DO.GradeDO;
import com.trs.gov.interaction.DO.OprRecordDO;
import com.trs.gov.interaction.DTO.GradeDTO;
import com.trs.gov.interaction.DTO.GradeExportDTO;
import com.trs.gov.interaction.DTO.OprRecordDTO;
import com.trs.gov.interaction.VO.GradeVO;
import com.trs.gov.interaction.mapper.GradeMapper;
import com.trs.gov.interaction.mapper.OprRecordMapper;
import com.trs.gov.management.DO.UnitDO;
import com.trs.gov.message.service.SendNoticeMesssageService;
import com.trs.gov.workorder.DTO.WorkOrderActionDTO;
import com.trs.gov.workorder.service.IMessageService;
import com.trs.gov.workorder.service.IWorkOrderService;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class GradeMgr {
    @Autowired
    private OprRecordMapper oprRecordMapper;

    @Autowired
    private GradeMapper gradeMapper;

    @Autowired
    private OprRecordMgr oprRecordMgr;

    @Reference(check = false, timeout = 60000)
    private IFileManagerService fileService;

    @Reference(check = false, timeout = 60000)
    private SendNoticeMesssageService noticeMesssageService;

    @Reference(check = false, timeout = 60000)
    private IMessageService iMessageService;

    @Reference(check = false, timeout = 60000)
    private IWorkOrderService workOrderService;

    public List<GradeVO> getGradeList(GradeDTO dto) throws ServiceException {
        List<GradeVO> list = new ArrayList<>();
        //获取评价列表
        List<GradeDO> gradeDOS = gradeMapper.selectList(new QueryWrapper<GradeDO>()
                    .eq("work_order_id", dto.getWorkOrderId())
                    .eq(!StringUtils.isEmpty(dto.getCrUser()),"cr_user",dto.getCrUser()));
        for (GradeDO gradeDO : gradeDOS) {
            GradeVO gradeVO = new GradeVO();
            BaseUtils.copyProperties(gradeDO, gradeVO);
            //获取附加信息
            ObjDTO one = ObjDTO.of(GradeDO.OBJ_TYPE, Long.toString(gradeDO.getId()), "fileList");
            ObjDTO two = ObjDTO.of(GradeDO.OBJ_TYPE, Long.toString(gradeDO.getId()), "picList");
            BaseUtils.setUserInfoToDTO(one, two);
            List<FileVO> fileList = fileService.getFileListOfObj(one);
            List<FileVO> picList = fileService.getFileListOfObj(two);
            gradeVO.setFileList(fileList);
            gradeVO.setPicList(picList);
            list.add(gradeVO);
        }
        return list;
    }

    public void saveGrade(GradeDTO dto) throws ServiceException {
        GradeDO gradeDO = new GradeDO();
        try {
            BaseUtils.copyProperties(dto, gradeDO);
            BigDecimal workOrderGrade = BigDecimal.valueOf(dto.getAttitude()).add(BigDecimal.valueOf(dto.getCompleteContent())).add(BigDecimal.valueOf(dto.getCompleteTime())).divide(BigDecimal.valueOf(3), 1, BigDecimal.ROUND_DOWN);
            gradeDO.setWorkOrderGrade(workOrderGrade);
            gradeMapper.insert(gradeDO);
            if (!CMyString.isEmpty(dto.getPicList())&&!JSONObject.parseArray(dto.getPicList()).isEmpty()&&!JSONObject.parseArray(dto.getPicList()).getJSONObject(0).isEmpty()) {
                FileUtils.saveFile(fileService, dto.getPicList(), GradeDO.OBJ_TYPE, Long.toString(gradeDO.getId()), "picList");
            }
            if (!CMyString.isEmpty(dto.getFileList())&&!JSONObject.parseArray(dto.getFileList()).isEmpty()&&!JSONObject.parseArray(dto.getFileList()).getJSONObject(0).isEmpty()) {
                FileUtils.saveFile(fileService, dto.getFileList(), GradeDO.OBJ_TYPE, Long.toString(gradeDO.getId()), "fileList");
            }
            //评价状态
            WorkOrderActionDTO workOrderActionDTO = new WorkOrderActionDTO();
            workOrderActionDTO.setWorkOrderId(dto.getWorkOrderId());
            workOrderActionDTO.setOprKey(OperateNameConstant.APPRAISE_WORK_ORDER);
            BaseUtils.setUserInfoToDTO(workOrderActionDTO);
            workOrderService.doWorkOrderAction(OperateNameConstant.APPRAISE_WORK_ORDER,workOrderActionDTO);
        } catch (Exception e) {
            log.error("评价" + dto.getGradeUnit() + "单位失败!", e);
            Long id = gradeDO.getId();
            if (id != null) {
                Try.of(() -> {
                    FileUtils.deleteFile(fileService, OprRecordDO.OBJ_TYPE, Long.toString(id), "picList");
                    return 0;
                }).onFailure(err -> log.warn("清除文件出错！", err));

                Try.of(() -> {
                    FileUtils.deleteFile(fileService, OprRecordDO.OBJ_TYPE, Long.toString(id), "fileList");
                    return 0;
                }).onFailure(err -> log.warn("清除文件出错！", err));

                Try.of(() -> gradeMapper.deleteById(id)).onFailure(err -> log.warn("删除评价", err));
            }
            throw new ServiceException("评价【" + dto.getGradeUnit() + "】单位失败!", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveAllGrade(List<GradeDTO> gradeDTOS) throws ServiceException{
        //保存所有评价
        for (GradeDTO gradeDTO : gradeDTOS) {
            BaseUtils.checkDTO(gradeDTO);
            BaseUtils.setUserInfoToDTO(gradeDTO);
            this.saveGrade(gradeDTO);
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public Optional<GradeVO> exportGrade(GradeExportDTO dto) throws ServiceException {
        GradeDO gradeDO = new GradeDO();
        BaseUtils.copyProperties(dto,gradeDO);
        BigDecimal workOrderGrade = BigDecimal.valueOf(dto.getAttitude()).add(BigDecimal.valueOf(dto.getCompleteContent())).add(BigDecimal.valueOf(dto.getCompleteTime())).divide(BigDecimal.valueOf(3), 1, BigDecimal.ROUND_DOWN);
        gradeDO.setWorkOrderGrade(workOrderGrade);
        gradeDO.setExportFromOther(1);
        int insert = gradeMapper.insert(gradeDO);
        if(insert < 1 ){
            throw new ServiceException("同步得分失败!");
        }
        if(dto.getPicList() != null && dto.getPicList().size() > 0){
            try {
                FileUtils.saveFile(fileService, JSONObject.toJSONString(dto.getPicList().toArray()), UnitDO.OBJ_TYPE, Long.toString(gradeDO.getId()), "picList");
            } catch (Exception e) {
                throw new ServiceException(e.getMessage(),e);
            }
        }
        if(dto.getFileList() != null && dto.getFileList().size() > 0){
            try {
                FileUtils.saveFile(fileService, JSONObject.toJSONString(dto.getFileList().toArray()), UnitDO.OBJ_TYPE, Long.toString(gradeDO.getId()), "fileList");
            } catch (Exception e) {
                throw new ServiceException(e.getMessage(),e);
            }
        }
        Integer integer = oprRecordMapper.selectCount(new QueryWrapper<OprRecordDO>().eq("ope_record_type", OperateNameConstant.APPRAISE_WORK_ORDER).eq("work_order_id", dto.getWorkOrderId()).eq("cr_user", dto.getCrUser()));
        //新工单中一条操作记录对应多条评价，所以如果存在就不再创建
        if(integer < 1){
            //保存操作记录
            OprRecordDTO oprRecordDTO = new OprRecordDTO();
            oprRecordDTO.setCrUnitId(dto.getCrUnitId());
            oprRecordDTO.setCrUser(dto.getCrUser());
            oprRecordDTO.setOprKey(OperateNameConstant.APPRAISE_WORK_ORDER);
            oprRecordDTO.setWorkOrderId(dto.getWorkOrderId());
            oprRecordDTO.setCrTime(dto.getCrTime());
            oprRecordDTO.setOption(dto.getContent());
            oprRecordDTO.setExportFromOther(1);
            oprRecordMgr.saveOprRecord(oprRecordDTO);
        }
        GradeVO gradeVO = new GradeVO();
        BaseUtils.copyProperties(gradeDO,gradeVO);
        return Optional.of(gradeVO);

    }

}
