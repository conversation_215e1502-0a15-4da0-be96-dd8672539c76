<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.gov.interaction.mapper.OprRecordMapper">

    <select id="listWorkOrderIdsByUserInfo" resultType="java.lang.Long">
        select opr.work_order_id from opr_record opr
        where
        <choose>
            <when test="dto.crUser != null and dto.crUser != '' and dto.crUnit != null ">
                opr.cr_user = #{dto.crUser} and opr.cr_unit_id = #{dto.crUnit}
            </when>
            <otherwise>
                 (
                 ( opr.assign_type = 4  <if test="dto.assignType == 1 and dto.isMaster == 0">  and opr.id = -1 </if> )
                or (opr.assign_type = 3
                <choose>
                    <when test="dto.groupIds != null and dto.groupIds.size() > 0">
                        and opr.group_id in
                        <foreach collection="dto.groupIds" item="id" index="index" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    </when>
                    <otherwise>
                        and opr.id = -1
                    </otherwise>
                </choose>
                <if test="dto.assignType == 1 and dto.isMaster == 0">  and opr.id = -1 </if>
                )
                or (opr.assign_type = 2 and opr.target_unit_id = #{dto.targetUnit} <if test="dto.assignType == 1 and dto.isMaster == 0">  and opr.id = -1 </if>)

                or (opr.assign_type = 1 and opr.target_unit_id = #{dto.targetUnit} <if test="dto.assignType == 1">  and opr.target_user = #{dto.targetUser} </if>)
                )
            </otherwise>
        </choose>
        <if test="dto.oprRecordType != null and dto.oprRecordType.size() > 0">
            and opr.ope_record_type in
            <foreach collection="dto.oprRecordType" item="id" index="index" open="(" close=")" separator=",">
               #{id}
            </foreach>
        </if>

    </select>

    <select id="isMasterByUserNameAndUnitId" resultType="boolean">
        select is_master from user_unit_mapping where unit_id = #{dto.targetUnit} and user_name = #{dto.targetUser}
    </select>

    <select id="allDistinctUnit" resultType="com.trs.gov.interaction.VO.OprRecodDeptVO">
        select distinct unit_id as unitId,unit_name as unitName from user_unit_mapping
    </select>

    <select id="listWorkOrderByIds" resultType="com.trs.gov.workorder.VO.WorkOrderVO">
        select id,
        work_order_top_type_id as workOrderTopTypeId,
        work_order_top_type_name as workOrderTopTypeName,
        cr_time as crTime,
        content as content,
        priority
        from work_order
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="listWorkOrderByKeyWords" resultType="com.trs.gov.workorder.VO.WorkOrderVO">
        select id,
        work_order_top_type_id as workOrderTopTypeId,
        work_order_top_type_name as workOrderTopTypeName,
        cr_time as crTime,
        content as content,
        sitename as siteName,
        priority
        from work_order
        where
        <if test="keyWords !=null and keyWords != ''">
            work_order.sitename REGEXP #{keyWords}
        </if>
    </select>


</mapper>