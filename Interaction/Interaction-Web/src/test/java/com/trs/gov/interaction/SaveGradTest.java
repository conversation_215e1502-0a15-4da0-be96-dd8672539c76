package com.trs.gov.interaction;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.InteractionApplication;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.GradeExportDTO;
import com.trs.gov.interaction.VO.GradeVO;
import com.trs.gov.interaction.mapper.GradeMapper;
import com.trs.gov.interaction.service.impl.GradeServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * @ClassName：SaveGradTest
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/11/27 16:06
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = InteractionApplication.class)
@Slf4j
public class SaveGradTest {

    @Autowired
    private GradeServiceImpl service;
    @Autowired
    private GradeMapper mapper;

    @Test
    public void saveGrad() throws ServiceException {
        GradeExportDTO gradeExportDTO = new GradeExportDTO();
        gradeExportDTO.setOldGradeId("我老评分的ID");
        gradeExportDTO.setWorkOrderId(151L);
        gradeExportDTO.setGradeUnitId(999L);
        gradeExportDTO.setGradeUnit("我是评分的单位999");
        gradeExportDTO.setAttitude(4.5);
        gradeExportDTO.setCompleteTime(6.5);
        gradeExportDTO.setCompleteContent(7.5);
        gradeExportDTO.setCrUser("杨鑫");
        gradeExportDTO.setCrUnitId(225L);
        gradeExportDTO.setCrTime(new Date());
        GradeVO gradeVO = service.exportGrade(gradeExportDTO);
        System.out.println(JSONObject.toJSONString(gradeVO));
        System.out.println(gradeVO.getId());
//        mapper.deleteById(gradeVO.getId());
//        System.out.println("删除成功");

    }
}
