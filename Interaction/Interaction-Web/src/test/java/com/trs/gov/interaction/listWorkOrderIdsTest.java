package com.trs.gov.interaction;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.InteractionApplication;
import com.trs.gov.interaction.DTO.QueryWorkOrderIdsDTO;
import com.trs.gov.interaction.VO.OprRecodDeptVO;
import com.trs.gov.interaction.mapper.OprRecordMapper;
import com.trs.gov.interaction.service.impl.OprRecordServiceImpl;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName：listWorkOrderIdsTest
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/13 14:15
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = InteractionApplication.class)
@Slf4j
public class listWorkOrderIdsTest {
    @Autowired
    private OprRecordServiceImpl oprRecordService;
    @Autowired
    private OprRecordMapper oprRecordMapper;

    @Test
    public void distinctUnit(){
        List<OprRecodDeptVO> deptVOList = oprRecordMapper.allDistinctUnit();
        System.out.println(JSONObject.toJSONString(deptVOList));
    }

    @Test
    public void listWorOrderIds(){
        QueryWorkOrderIdsDTO queryWorkOrderIdsDTO = new QueryWorkOrderIdsDTO();
        queryWorkOrderIdsDTO.setTargetUser("杨鑫");
        queryWorkOrderIdsDTO.setTargetUnit(11L);
        queryWorkOrderIdsDTO.setOprRecordType(Arrays.asList("workorder.add.create","workorder.handel.xiugai","workorder.handel.jiaoban","workorder.handel.huitui","workorder.handel.xinzengchaosong"));

        RestfulResults<List<Long>> listRestfulResults = oprRecordService.listWorkOrderIdByUserAndUnitAndType(queryWorkOrderIdsDTO);
        List<Long> longs = listRestfulResults.getDatas();
        System.out.println(JSONObject.toJSONString(longs));

    }
}
