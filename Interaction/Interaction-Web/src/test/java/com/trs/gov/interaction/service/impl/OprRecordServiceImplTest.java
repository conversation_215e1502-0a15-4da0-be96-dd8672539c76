package com.trs.gov.interaction.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.InteractionApplication;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.OperRecordDTO;
import com.trs.gov.interaction.DTO.OprRecordExportDTO;
import com.trs.gov.interaction.VO.OperRecordVO;
import com.trs.gov.interaction.mapper.OprRecordMapper;
import com.trs.gov.interaction.service.OprRecordService;
import com.trs.web.builder.base.RestfulResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = InteractionApplication.class)
@Slf4j
public class OprRecordServiceImplTest{
    @Reference(check = false, timeout = 60000)
    private OprRecordService service;
    @Autowired
    private OprRecordMapper mapper;

    @Test
    public void saveComments() throws ServiceException {
        OprRecordExportDTO oprRecordExportDTO = new OprRecordExportDTO();
        oprRecordExportDTO.setOldRecordId("我是老的ID");
        oprRecordExportDTO.setWorkOrderId(999L);
        oprRecordExportDTO.setCrUser("杨鑫");
        oprRecordExportDTO.setCrUnitId(888L);
        oprRecordExportDTO.setCrTime(new Date());
        oprRecordExportDTO.setOption("我是评论的内容！我是评论的内容！我是评论的内容！");
        oprRecordExportDTO.setOprKey("workorder.handel.huifu");
        OperRecordVO operRecordVO = service.exportOperRecord(oprRecordExportDTO);
        System.out.println(JSONObject.toJSONString(operRecordVO));
        System.out.println(operRecordVO.getId());
//        mapper.deleteById(operRecordVO.getId());

    }

    @Test
    public void listNewestTrends(){
        OperRecordDTO dto = new OperRecordDTO();
        dto.setKeyWords("贵州省");
        RestfulResults<List<OperRecordVO>> listRestfulResults = service.listNewestTrends(dto);
        System.out.println(listRestfulResults.getDatas().toString());
    }
}