package com.trs.gov.interaction;

import com.alibaba.fastjson.JSONObject;
import com.trs.gov.file.VO.FileVO;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName：Test
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/11/27 17:56
 **/
public class Test {
    public static void main(String[] args) {
        FileVO fileVO = new FileVO();
        fileVO.setFiledesc("描述");
        fileVO.setFileext("txt");
        fileVO.setFilename("测试");
        fileVO.setFilesize(13215L);
        fileVO.setUrl("http://baidu.com");
        FileVO fileVO1 = new FileVO();
        fileVO1.setFiledesc("描述");
        fileVO1.setFileext("txt");
        fileVO1.setFilename("测试");
        fileVO1.setFilesize(13215L);
        fileVO1.setUrl("http://baidu.com");
        List<FileVO> fileVOS = Arrays.asList(fileVO, fileVO1);
        System.out.println(JSONObject.toJSONString(fileVOS));
        System.out.println("---------------------------------区别--------------------");
        FileVO[] fileVOS1 = new FileVO[]{fileVO,fileVO1};
        System.out.println(JSONObject.toJSONString(fileVOS1));
        System.out.println("---------------------------------区别--------------------");
        System.out.println(JSONObject.toJSONString(fileVOS.toArray()));

//        JSONObject.toJSONString(dto.getPicList().toArray())
//        ObjectMapper mapper = new ObjectMapper();

    }
}
