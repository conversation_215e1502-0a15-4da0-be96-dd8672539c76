package com.trs.gov.interaction.DO;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-10 11:21
 * @version 1.0
 * @since 1.0
 * 评论回复对象
 */
@Data
@TableName("opr_record")
public class OprRecordDO extends BaseDO {

    public static final String OBJ_TYPE = "opr_record";

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id", required = true)
    @Column(name = "work_order_id")
    @TableField("work_order_id")
    private Long workOrderId;

    /**
     * 创建单位id
     */
    @ApiModelProperty(value = "创建单位id", required = true)
    @Column(name = "cr_unit_id")
    @TableField("cr_unit_id")
    private Long crUnitId;

    /**
     * 操作类型id
     */
    @ApiModelProperty(value = "操作类型", required = true)
    @Column(name = "ope_record_type")
    @TableField("ope_record_type")
    private String oprKey;

    /**
     * 评论或回复内容
     */
    @ApiModelProperty(value = "评论或回复内容", required = true)
    @Column(name = "`option`")
    @TableField("`option`")
    private String option;

    /**
     * 目标用户
     */
    @ApiModelProperty(value = "目标用户", required = true)
    @Column(name = "target_user")
    @TableField("target_user")
    private String targetUser;

    /**
     * 目标单位ID
     */
    @ApiModelProperty(value = "目标单位", required = true)
    @Column(name = "target_unit_id")
    @TableField("target_unit_id")
    private Long targetUnitId;

    /**
     * 是否指定到单位和人： 0都不指定，1指定到人，2指定到单位 3.指定到分组 4.全部
     */
    @ApiModelProperty(value = "是否指定到单位", required = true)
    @Column(name = "assign_type")
    @TableField("assign_type")
    private Integer assignType;

    @ApiModelProperty(value = "分组ID", required = true)
    @Column(name = "group_id")
    @TableField("group_id")
    private Long groupId;


}
