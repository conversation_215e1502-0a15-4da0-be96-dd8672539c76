package com.trs.gov.interaction.service;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.*;
import com.trs.gov.interaction.VO.OperRecordVO;
import com.trs.gov.interaction.VO.OprRecodDeptVO;
import com.trs.gov.interaction.VO.RollBackWorkOrderVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 操作记录服务接口
 */
public interface OprRecordService {

    /**
     * 获取操作记录(工单详情部分)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     * @param  operRecordDTO  工单id
     * @throws ServiceException 服务异常
     * @return 查询结果
     */
    RestfulResults<List<OperRecordVO>> getOperRecordByWorkOrderId(OperRecordDTO operRecordDTO);

    /**
     * 保存操作数据<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     * @param   dto 请求参数
     * @throws  ServiceException 服务异常
     * @return  保存结果
     */
    RestfulResults<Report> saveOprRecord(OprRecordDTO dto) throws ServiceException;

    /**
     * 获取参与工单的的单位<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     * @param   workOrderId 工单id
     * @throws  ServiceException 服务异常
     * @return  查询结果
     */
    RestfulResults<List<OprRecodDeptVO>> getOperUnits(Long workOrderId);

    /**
     * 获取所有工单和对应操作记录信息(最新动态部分)<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     * @param   operRecordDTO 站点id
     * @throws  ServiceException 服务异常
     * @return  查询结果
     */
    RestfulResults<List<OperRecordVO>> getAllOperRecord(OperRecordDTO operRecordDTO);

    /**
     * 给工单添加回复数量<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:43
     * @param   statisticsCountDTO 工单id
     * @throws  ServiceException 服务异常
     * @return  保存结果
     */
    RestfulResults<Long> addCommentNum(StatisticsCountDTO statisticsCountDTO);

    /**
     * @Description  回退记录
     * @Param [oprRecordDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.interaction.VO.RollBackWorkOrderVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/9/28 21:49
     **/
    RestfulResults<List<RollBackWorkOrderVO>> listRollBackWorkOrderLog(OprRecordDTO oprRecordDTO);


    /**
     * @Description  根据目标用户信息和操作类型获取工单的ids
     * @Param [queryWorkOrderIdsDTO]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<java.lang.Long>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/10/13 11:25
     **/
    RestfulResults<List<Long>> listWorkOrderIdByUserAndUnitAndType(QueryWorkOrderIdsDTO queryWorkOrderIdsDTO);
    /**
     * @Description  导入评论
     * @Param []
     * @return com.trs.gov.interaction.VO.OperRecordVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 16:58
     **/
    public OperRecordVO exportOperRecord(OprRecordExportDTO dto)throws ServiceException;

    /**
     * @Description  大屏展示的运维最新动态
     * @Param [dto]
     * @return com.trs.web.builder.base.RestfulResults<java.util.List<com.trs.gov.interaction.VO.OperRecordVO>>
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2021/2/7 11:30
     **/
    RestfulResults<List<OperRecordVO>> listNewestTrends(OperRecordDTO dto);

}
