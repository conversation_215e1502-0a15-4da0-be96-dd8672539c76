package com.trs.gov.interaction.VO;

import com.trs.gov.core.VO.BaseVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName：RollBackWorkOrderVO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/28 17:37
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RollBackWorkOrderVO extends BaseVO {
    /**
     * 工单Id
     **/
    private Long workOrderId;
    /**
     * 操作类型
     **/
    private String oprKey;
    /**
     * 操作类型描述
     **/
    private String oprDesc;
    /**
     * 工单Id
     **/
    private Long targetUnitId;
    /**
     * 单位名称
     **/
    private String targetUnitName;
    /**
     * 真实用户
     **/
    private String targetUser;
    /**
     * 真实姓名
     **/
    private String targetUserTrueName;

    /**
     * 是否是单位经手
     **/
    private boolean isUnit = false;
    /**
     * 是否是受理人
     **/
    private boolean isTarget = true;



}
