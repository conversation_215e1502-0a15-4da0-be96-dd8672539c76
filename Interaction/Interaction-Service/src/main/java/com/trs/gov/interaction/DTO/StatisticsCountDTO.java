package com.trs.gov.interaction.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName：StatisticsCountDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/27 14:19
 **/
@Data
public class StatisticsCountDTO extends BaseDTO {
    /**
     * 工单ID
     **/
    private Long workOrderId;
    /**
     * 工单某个操作类型的Id
     **/
    private String type;

    @Override
    public boolean isValid() throws ServiceException {
        if(workOrderId == null){
            throw new ParamInvalidException("工单Id不能为空!");
        }
        if(StringUtils.isEmpty(type)){
            throw new ParamInvalidException("想要统计的类型不能为空!");
        }
        return true;
    }
}
