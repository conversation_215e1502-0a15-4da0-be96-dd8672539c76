package com.trs.gov.interaction.DO;

import com.baomidou.mybatisplus.annotation.TableName;
import com.trs.gov.core.DO.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-10 11:21
 * @version 1.0
 * @since 1.0
 * 工单评价对象
 */
@Data
@TableName("grade")
public class GradeDO extends BaseDO {


    public static final String OBJ_TYPE = "grade";
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称", required = true)
    @Column(name = "grade_unit")
    private String gradeUnit;

    /**
     * 评价单位id
     */
    @ApiModelProperty(value = "评价单位id", required = true)
    @Column(name = "grade_unit_id")
    private Long gradeUnitId;

    /**
     * 评分内容
     */
    @ApiModelProperty(value = "评分内容", required = true)
    @Column(name = "content")
    private String content;

    /**
     * 服务态度得分
     */
    @ApiModelProperty(value = "服务态度得分", required = true)
    @Column(name = "attitude")
    private Double attitude;

    /**
     * 完成时间得分
     */
    @ApiModelProperty(value = "完成时间得分", required = true)
    @Column(name = "complete_time")
    private Double completeTime;

    /**
     * 完成内容得分
     */
    @ApiModelProperty(value = "完成内容得分", required = true)
    @Column(name = "complete_content")
    private Double completeContent;

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id", required = true)
    @Column(name = "work_order_id")
    private Long workOrderId;

    /**
     * 工单得分
     */
    @ApiModelProperty(value = "工单得分", required = true)
    @Column(name = "work_order_grade")
    private BigDecimal workOrderGrade;

}
