package com.trs.gov.interaction.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.VO.FileVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 评分的迁移请求参数
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-11-20 16:14
 * @version 1.0
 * @since 1.0
 */
@Data
public class GradeExportDTO extends BaseDTO {

    /**
     * 工单id
     */
    private Long workOrderId;

    /**
     * 旧系统的评分ID
     */
    private String oldGradeId;

    /**
     * 评价单位
     */
    private String gradeUnit;

    /**
     * 评价单位id
     */
    private Long gradeUnitId;

    /**
     * 评价内容
     */
    private String content;

    /**
     * 服务态度得分
     */
    private Double attitude;

    /**
     * 完成时间得分
     */
    private Double completeTime;

    /**
     * 完成内容得分
     */
    private Double completeContent;

    /**
     * 图片文件
     */
    private List<FileVO> picList;

    /**
     * 其它附件
     */
    private List<FileVO> fileList;

    /**
     * 创建人
     */
    private String crUser;
    /**
     * 创建人得单位Id 【原本没有，添加原因:1.人和单位指定一个界面   2.不传crUnitId任何地方都查不出来，就没有导入得意义】
     */
    private Long crUnitId;
    /**
     * 创建时间
     */
    private Date crTime;

    @Override
    public boolean isValid() throws ServiceException {
        if (workOrderId == null || workOrderId <= 0L) {
            throw new ParamInvalidException("工单id为空");
        }
        if(CMyString.isEmpty(oldGradeId)){
            throw new ParamInvalidException("老评分数据的Id不能为空!");
        }
        if(CMyString.isEmpty(gradeUnit)){
            throw new ParamInvalidException("被评分的单位名称不能为空!");
        }
        if(gradeUnitId == null){
            throw new ParamInvalidException("被评分的单位ID不能为空!");
        }
        if(attitude == null){
            throw new ParamInvalidException("服务态度得分不能为空!");
        }
        if(completeTime == null){
            throw new ParamInvalidException("完成时间得分不能为空!");
        }
        if(completeContent == null){
            throw new ParamInvalidException("完成内容得分不能为空!");
        }
        if(CMyString.isEmpty(crUser)){
            throw new ParamInvalidException("创建人不能为空!");
        }
        if(crUnitId == null){
            throw new ParamInvalidException("创建单位Id不能为空!");
        }
        if (crTime == null){
            throw new ParamInvalidException("创建时间不能为空!");
        }
        //内容可以没有

        return true;

    }
}
