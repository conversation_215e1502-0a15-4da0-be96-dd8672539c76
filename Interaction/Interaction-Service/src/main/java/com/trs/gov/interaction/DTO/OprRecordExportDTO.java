package com.trs.gov.interaction.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import com.trs.gov.file.VO.FileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OprRecordExportDTO extends BaseDTO {

    /**
     * 创建人
     */
    private String crUser;
    /**
     * 创建单位
     */
    private Long crUnitId;

    /**
     * 旧系统的操作记录ID
     * */
    private String oldRecordId;

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id", required = true)
    private Long workOrderId;

    /**
     * 操作类型id
     */
    @ApiModelProperty(value = "操作类型", required = true)
    private String oprKey;

    /**
     * 评论或回复内容
     */
    @ApiModelProperty(value = "评论或回复内容", required = true)
    private String option;

    /**
     * 目标用户
     */
    @ApiModelProperty(value = "目标用户", required = true)
    private String targetUser;

    /**
     * 目标单位ID
     */
    @ApiModelProperty(value = "目标单位", required = true)
    private Long targetUnitId;

    /**
     * 是否指定到单位和人： 0都不指定，1指定到人，2指定到单位，默认为零
     */
    @ApiModelProperty(value = "是否指定到单位", required = true)
    private Integer assignType = 0;

    /**
     * 图片文件
     */
    private List<FileVO> picList;

    /**
     * 其它附件
     */
    private List<FileVO> fileList;

    /**
     * 创建时间
     */
    private Date crTime;

    /**
     * 检测DTO是否有效，即必传参数是否传了<BR>
     *
     * @return 检测结果(参数没有异常时返回true)
     * @throws ServiceException 相关异常
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * 创建时间：2020-09-10 15:49
     */
    @Override
    public boolean isValid() throws ServiceException {
        if(CMyString.isEmpty(oldRecordId)){
            throw new ServiceException("老操作记录的id不能为空!");
        }
        if(workOrderId == null){
            throw new ServiceException("工单id不能为空!");
        }
        if(crUnitId == null){
            throw new ServiceException("创建单位id不能为空!");
        }
        if(CMyString.isEmpty(oprKey)){
            throw new ServiceException("操作类型key不能为空!");
        }
        if(CMyString.isEmpty(crUser)){
            throw new ServiceException("评价用户不能为空!");
        }
        if(CMyString.isEmpty(option)){
            throw new ServiceException("评论内容不能为空!");
        }
        if(crTime == null){
            throw new ServiceException("评价时间不能为空!");
        }
        return true;
    }
}
