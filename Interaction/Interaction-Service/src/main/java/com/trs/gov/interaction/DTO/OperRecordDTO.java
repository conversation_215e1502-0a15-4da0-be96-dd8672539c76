package com.trs.gov.interaction.DTO;

import com.trs.gov.core.DTO.BasePageDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

/**
 * @ClassName：OperRecordDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/9/25 18:22
 **/
@Data
public class OperRecordDTO extends BasePageDTO {
    /**
     * 工单Id
     */
    private Long workOrderId;
/**
     * 站点ID
     */
    private Long siteId;

    private String keyWords;

    private boolean searchAll = false;

    private boolean isExistUnitId = false;

    @Override
    public boolean isValid() throws ServiceException {
        return true;
    }
}
