package com.trs.gov.interaction.VO;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.gov.core.VO.BaseVO;
import com.trs.gov.file.VO.FileVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
public class GradeVO extends BaseVO {
    /**
     * 单位名称
     */
    private Long id;
    /**
     * 单位名称
     */
    private String gradeUnit;

    /**
     * 评价单位
     */
    private Long gradeUnitId;

    /**
     * 评价人姓名
     */
    private String crUser;

    /**
     * 评价时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date crTime;

    /**
     * 评分内容
     */
    private String content;

    /**
     * 服务态度得分
     */
    private Double attitude;

    /**
     * 完成时间得分
     */
    private Double completeTime;

    /**
     * 完成内容得分
     */
    private Double completeContent;

    /**
     * 工单得分
     */
    private BigDecimal workOrderGrade;

    /**
     * 图片列表
     */
    private List<FileVO> picList;

    /**
     * 附件列表
     */
    private List<FileVO> fileList;

}
