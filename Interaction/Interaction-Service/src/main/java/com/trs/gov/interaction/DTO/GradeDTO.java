package com.trs.gov.interaction.DTO;


import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

@Data
public class GradeDTO extends BaseDTO {

    /**
     * 工单id
     */
    private Long workOrderId;

    /**
     * 评价单位
     */
    private String gradeUnit;

    /**
     * 评价单位id
     */
    private Long gradeUnitId;

    /**
     * 评价内容
     */
    private String content;

    /**
     * 服务态度得分
     */
    private Double attitude;

    /**
     * 完成时间得分
     */
    private Double completeTime;

    /**
     * 完成内容得分
     */
    private Double completeContent;

    /**
     * 图片文件
     */
    private String picList;

    /**
     * 其它附件
     */
    private String fileList;

    /**
     * 创建人
     */
    private String crUser;
    @Override
    public boolean isValid() throws ServiceException {
        if (workOrderId==null){
            throw new ParamInvalidException("工单id为空");
        }
        return true;
    }
}
