package com.trs.gov.interaction.service;

import com.trs.common.base.Report;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.interaction.DTO.GradeDTO;
import com.trs.gov.interaction.DTO.GradeExportDTO;
import com.trs.gov.interaction.VO.GradeVO;
import com.trs.web.builder.base.RestfulResults;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> lan.xin E-mail: <EMAIL>
 * 创建时间：2020-09-09 19:28
 * @version 1.0
 * @since 1.0
 * 评价服务接口
 */
public interface GradeService {

    /**
     * 获取评价列表<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:39
     * @param   dto  请求参数
     * @throws  ServiceException 服务异常
     * @return  查询结果
     */
    RestfulResults<List<GradeVO>> getGradeList(GradeDTO dto) ;
    /**
     * 保存评价<BR>
     * <AUTHOR> lan.xin E-mail: <EMAIL>
     * 创建时间：2020/9/9 20:40
     * @param   gradeInfos 请求参数
     * @throws  ServiceException 服务异常
     * @return  保存结果
     */
    RestfulResults<Report> saveGrade(String gradeInfos) throws ServiceException;

    /**
     * @Description  同步得分机制
     * @Param [dto]
     * @return com.trs.gov.interaction.VO.GradeVO
     * <AUTHOR> E-mail:<EMAIL>
     * @Date  2020/11/27 15:22
     **/
    public GradeVO exportGrade(GradeExportDTO dto) throws ServiceException;
}
