package com.trs.gov.interaction.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.exception.ServiceException;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：QueryWorkOrderIdsDTO
 * @Description : TODO
 * <AUTHOR> YangXin
 * @Date 2020/10/13 11:23
 **/
@Data
public class QueryWorkOrderIdsDTO extends BaseDTO {

    /**
     * 组织Ids
     */
    private List<Long> groupIds;

    //0-否，1-是
    private Integer isMaster;

    //1-指定到我，2-指定到单位
    private Integer assignType;

    /**
     * 操作类型
     */
    private List<String> oprRecordType;
    /**
     * 目标人
     **/
    private String targetUser;

    /**
     * 目标单位
     **/
    private Long targetUnit;

    /**
     * 创建人
     **/
    private String crUser;

    /**
     * 创建单位
     **/
    private Long crUnit;


    @Override
    public boolean isValid() throws ServiceException {
        if (assignType == null){
            throw new ServiceException("assignType不能为空!");
        }
        if(oprRecordType == null || oprRecordType.size() == 0){
            throw new ServiceException("操作类型不能为空!");
        }
        return true;
    }
}
