package com.trs.gov.interaction.VO;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.trs.gov.core.VO.BaseVO;
import com.trs.gov.file.VO.FileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
@Data
public class OperRecordVO extends BaseVO {


    /**
     * 工单id
     */
    private Long workOrderId;

    /**
     * 操作记录id
     */
    private Long id;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 创建人
     */
    private String crUser;
    /**
     * 创建人真实姓名
     */
    private String crUserTrueName;

    /**
     * 创建单位id
     */
    private Long crUnitId;
    /**
     * 创建单位名字
     */
    private String crUnitName;

    /**
     * 操作类型
     */
    private String oprKey;
    /**
     * 操作类型描述
     */
    private String operateDesc;

    /**
     * 评论或回复内容
     */
    private String option;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date crTime;

    /**
     * 目标用户
     */
    private String targetUser;
    /**
     * 目标用户
     */
    private String targetUserTrueName;

    /**
     * 目标单位
     */
    private String targetUnit;
    /**
     * 是否指定到单位和人： 0都不指定，1指定到人，2指定到单位,默认为0
     */
    private Integer assignType = 0;

    /**
     * 分组ID
     */
    private Long groupId;
    /**
     * 分组 名字
     */
    private String groupName;


    /**
     * 图片列表
     */
    private List<FileVO> picList;

    /**
     * 附件列表
     */
    private List<FileVO> fileList;
    /**
     * 顶级工单分类名字
     */
    private String workOrderTopTypeName;
    /**
     * 工单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date workOrderCrTime;
    /**
     * 工单内容
     */
    private String workOrderContent;

    /**
     * 紧急程度
     */
    private String priority;
    /**
     * 评价列表
     */
    private List<GradeVO> gradeList = new ArrayList<>();
    
    /**
     * 站点
     */
    @ApiModelProperty(value = "站点")
    private String sitename;

}
