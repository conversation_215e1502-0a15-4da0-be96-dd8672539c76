package com.trs.gov.interaction.DTO;

import com.trs.gov.core.DTO.BaseDTO;
import com.trs.gov.core.constant.OperateNameConstant;
import com.trs.gov.core.exception.ParamInvalidException;
import com.trs.gov.core.exception.ServiceException;
import com.trs.gov.core.util.CMyString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * 回复信息
 *
 * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
 * 创建时间：2020-09-23 17:12
 * @version 1.0
 * @since 1.0
 */
@Data
public class OprRecordDTO extends BaseDTO {

    /**
     * 创建人
     */
    private String crUser;
    /**
     * 创建单位
     */
    private Long crUnitId;

    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id", required = true)
    private Long workOrderId;

    /**
     * 操作类型id
     */
    @ApiModelProperty(value = "操作类型", required = true)
    private String oprKey;

    /**
     * 评论或回复内容
     */
    @ApiModelProperty(value = "评论或回复内容", required = true)
    private String option;

    /**
     * 目标用户
     */
    @ApiModelProperty(value = "目标用户", required = true)
    private String targetUser;

    /**
     * 目标单位ID
     */
    @ApiModelProperty(value = "目标单位", required = true)
    private Long targetUnitId;

    /**
     * 是否指定到单位和人： 0都不指定，1指定到人，2指定到单位，默认为零
     */
    @ApiModelProperty(value = "是否指定到单位", required = true)
    private Integer assignType = 0;

    /**
     * 图片文件
     */
    private String picList;
    /**
     *  同步的数据需要使用
     **/
    private Date crTime;
    /**
     * 是否是从其他地方迁移过来的数据
     * */
    private Integer exportFromOther;
    /**
     * 其它附件
     */
    private String fileList;

    /**
     * 分组ID
     */
    private Long groupId;

    public Long getCrUnitId() {
        if (crUnitId == null || crUnitId <= 0L) {
            return Long.valueOf(Optional.ofNullable(getUnitId()).orElse("0"));
        }
        return crUnitId;
    }

    public void setCrUnitId(Long crUnitId) {
        this.crUnitId = crUnitId;
    }

    @Override
    public boolean isValid() throws ServiceException {
        if (workOrderId == null || workOrderId <= 0L) {
            throw new ParamInvalidException("工单ID[" + workOrderId + "]异常！");
        }
        if (CMyString.isEmpty(oprKey)) {
            throw new ParamInvalidException("操作类型不能为空！");
        }
        if (!OperateNameConstant.getTypeDesc(oprKey).isPresent()) {
            throw new ParamInvalidException("不存在相关操作！oprKey=" + oprKey);
        }
        if (getCrUnitId() == null || getCrUnitId() <= 0L) {
            throw new ParamInvalidException("单位ID不能为空！");
        }
        if (assignType == null) {
            throw new ParamInvalidException("指定类型assignType不能为空！");
        }
        return true;
    }
}
